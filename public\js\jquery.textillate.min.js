"use strict";function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(c){function r(t){return/In/.test(t)||0<=c.inArray(t,c.fn.textillate.defaults.inEffects)}function u(t){return/Out/.test(t)||0<=c.inArray(t,c.fn.textillate.defaults.outEffects)}function s(t){return"true"!==t&&"false"!==t?t:"true"===t}function f(t){var e=t.attributes||[],i={};return e.length&&c.each(e,function(t,e){var n=e.nodeName.replace(/delayscale/,"delayScale");/^data-in-*/.test(n)?(i.in=i.in||{},i.in[n.replace(/data-in-/,"")]=s(e.nodeValue)):/^data-out-*/.test(n)?(i.out=i.out||{},i.out[n.replace(/data-out-/,"")]=s(e.nodeValue)):/^data-*/.test(n)&&(i[n.replace(/data-/,"")]=s(e.nodeValue))}),i}function d(t,l,i){var o=t.length;o?(l.shuffle&&(t=function(t){for(var e,n,i=t.length;i;e=parseInt(Math.random()*i),n=t[--i],t[i]=t[e],t[e]=n);return t}(t)),l.reverse&&(t=t.toArray().reverse()),c.each(t,function(t,e){var s=c(e);function a(){r(l.effect)?"typeOut"===l.effect?s.css("display","inline-block"):s.css("visibility","visible"):u(l.effect)&&("typeOut"===l.effect?s.css("display","none"):s.css("visibility","hidden")),!(o-=1)&&i&&i()}var n=l.sync?l.delay:l.delay*t*l.delayScale;s.text()?setTimeout(function(){var t,e,n,i;t=s,e=l.effect,n=a,i=0,"clipIn"===e?(t.css("width","auto"),i=t.width(),t.css("overflow","hidden"),t.css("width","0"),t.css("visibility","visible"),t.animate({width:i+.3*parseFloat(t.css("font-size"))},1200,function(){setTimeout(function(){n&&n()},100)})):"clipOut"===e?t.animate({width:"2px"},1200,function(){setTimeout(function(){n&&n()},100)}):"typeIn"===e?t.addClass("sp-title-animated "+e).show():t.addClass("sp-title-animated "+e).css("visibility","visible").show(),"typeIn"!==e&&"typeOut"!==e||!jQuery("html").hasClass("ua-edge")&&!jQuery("html").hasClass("ua-ie")||(t.removeClass("sp-title-animated "+e).css("visibility","visible"),n&&n()),t.one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oAnimationEnd AnimationEnd",function(){t.removeClass("sp-title-animated "+e),n&&n()})},n):a()})):i&&i()}var a=function(t,e){var l=this,o=c(t);l.init=function(){l.$texts=o.find(e.selector),l.$texts.length||(l.$texts=c('<ul class="texts"><li>'+o.html()+"</li></ul>"),o.html(l.$texts)),l.$texts.hide(),l.$current=c('<span class="sp-textillate">').html(l.$texts.find(":first-child").html()).prependTo(o),r(e.in.effect)?l.$current.css("visibility","hidden"):u(e.out.effect)&&l.$current.css("visibility","visible"),l.setOptions(e),l.timeoutRun=null,setTimeout(function(){l.options.autoStart&&l.start()},l.options.initialDelay)},l.setOptions=function(t){l.options=t},l.triggerEvent=function(t){var e=c.Event(t+".tlt");return o.trigger(e,l),e},l.in=function(t,e){t=t||0;var n,i=l.$texts.find(":nth-child("+((t||0)+1)+")"),s=c.extend(!0,{},l.options,i.length?f(i[0]):{}),a=l.$current.closest(".sp-animated-texts-wrapper");i.addClass("current"),l.triggerEvent("inAnimationBegin"),o.attr("data-active",i.data("id")),"line"==l.options.length?l.$current.html(i.html()).lettering("lines"):l.$current.html(i.html()).lettering("words"),"char"==l.options.type&&l.$current.find('[class^="word"]').css({display:"inline-block","-webkit-transform":"translate3d(0,0,0)","-moz-transform":"translate3d(0,0,0)","-o-transform":"translate3d(0,0,0)",transform:"translate3d(0,0,0)"}).each(function(){c(this).lettering()}),n=l.$current.find('[class^="'+l.options.length+'"]').css("display","inline-block"),r(s.in.effect)?"typeIn"===s.in.effect?n.css("display","none"):n.css("visibility","hidden"):u(s.in.effect)&&n.css("visibility","visible"),"typeIn"!==s.in.effect&&"clipIn"!==s.in.effect||void 0!==a.attr("style")&&-1!==a.attr("style").indexOf("width")||l.$current.closest(".sp-animated-texts-wrapper").css("width","auto"),l.currentIndex=t,d(n,s.in,function(){l.triggerEvent("inAnimationEnd"),s.in.callback&&s.in.callback(),e&&e(l)})},l.out=function(t){var e=l.$texts.find(":nth-child("+((l.currentIndex||0)+1)+")"),n=l.$current.find('[class^="'+l.options.length+'"]'),i=c.extend(!0,{},l.options,e.length?f(e[0]):{});l.triggerEvent("outAnimationBegin"),d(n,i.out,function(){e.removeClass("current"),l.triggerEvent("outAnimationEnd"),o.removeAttr("data-active"),i.out.callback&&i.out.callback(),t&&t(l)})},l.start=function(t){setTimeout(function(){l.triggerEvent("start"),function e(n){l.in(n,function(){var t=l.$texts.children().length;n+=1,!l.options.loop&&t<=n?(l.options.callback&&l.options.callback(),l.triggerEvent("end")):(n%=t,l.timeoutRun=setTimeout(function(){l.out(function(){e(n)})},l.options.minDisplayTime))})}(t||0)},l.options.initialDelay)},l.stop=function(){l.timeoutRun&&(clearInterval(l.timeoutRun),l.timeoutRun=null)},l.init()};c.fn.textillate=function(i,s){return this.each(function(){var t=c(this),e=t.data("textillate"),n=c.extend(!0,{},c.fn.textillate.defaults,f(this),"object"==_typeof(i)&&i);e?"string"==typeof i?e[i].apply(e,[].concat(s)):e.setOptions.call(e,n):t.data("textillate",e=new a(this,n))})},c.fn.textillate.defaults={selector:".texts",loop:!0,minDisplayTime:2e3,initialDelay:0,in:{effect:"fadeInLeftBig",delayScale:1.5,delay:50,sync:!1,reverse:!1,shuffle:!1,callback:function(){}},out:{effect:"hinge",delayScale:1.5,delay:50,sync:!1,reverse:!1,shuffle:!1,callback:function(){}},autoStart:!0,inEffects:[],outEffects:["hinge"],callback:function(){},type:"char"}}(jQuery);