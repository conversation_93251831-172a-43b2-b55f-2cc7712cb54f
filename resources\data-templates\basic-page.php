<?php

$seedprod_recovery = '
{
  "show_header_template": true,
  "show_footer_template": true,
  "no_conflict_mode": false,
  "no_index": false,
  "seo_title": "",
  "seo_description": "",
  "favicon": "",
  "social_thumbnail": "",
  "enable_recaptcha": false,
  "template_id": 71,
  "post_title": "",
  "post_name": "",
  "post_status": "draft",
  "show_powered_by_link": false,
  "facebook_app_id": "",
  "affiliate_url": "",
  "header_scripts": "",
  "body_scripts": "",
  "footer_scripts": "",
  "conversion_scripts": "",
  "disable_default_excluded_urls": false,
  "include_exclude_type": "0",
  "include_list": "",
  "exclude_list": "",
  "bypass_phrase": "",
  "bypass_expires": "2",
  "bypass_cookie": false,
  "access_by_ip": "",
  "access_by_role": [],
  "redirect_login_page": false,
  "redirect_mode": false,
  "redirect_url": "",
  "domain_mapping_status": false,
  "domain_mapping": "",
  "domain_mapping_force_https": false,
  "exclude_list_404": "",
  "email_integration_id": "",
  "is_new": false,
  "page_type": "lp",
  "document": {
    "sections": [
      {
        "id": "ks8a8s",
        "type": "section",
        "rows": [
          {
            "id": "vw3kvy",
            "type": "row",
            "colType": "1-col",
            "cols": [
              {
                "id": "yiin6t",
                "type": "col",
                "blocks": [
                  {
                    "id": "grs238",
                    "elType": "block",
                    "type": "header",
                    "settings": {
                      "headerTxt": "Recovery Mode",
                      "tag": "h1",
                      "textColor": "",
                      "bgColor": "",
                      "beforeIcon": "",
                      "afterIcon": "",
                      "marginTop": "",
                      "marginBottom": "",
                      "marginLeft": "",
                      "marginRight": "",
                      "marginSync": false,
                      "marginTop_mobile": "",
                      "marginBottom_mobile": "",
                      "marginLeft_mobile": "",
                      "marginRight_mobile": "",
                      "marginSync_mobile": false,
                      "marginTop_tablet": "",
                      "marginBottom_tablet": "",
                      "marginLeft_tablet": "",
                      "marginRight_tablet": "",
                      "marginSync_tablet": false,
                      "customClass": "",
                      "customAttributes": "",
                      "paddingTop": "",
                      "paddingBottom": "",
                      "paddingLeft": "",
                      "paddingRight": "",
                      "paddingSync": true,
                      "paddingTop_mobile": "",
                      "paddingBottom_mobile": "",
                      "paddingLeft_mobile": "",
                      "paddingRight_mobile": "",
                      "paddingSync_mobile": true,
                      "paddingTop_tablet": "",
                      "paddingBottom_tablet": "",
                      "paddingLeft_tablet": "",
                      "paddingRight_tablet": "",
                      "paddingSync_tablet": true,
                      "borderRadius": "",
                      "border": "",
                      "borderStyle": "",
                      "borderSize": "",
                      "borderColor": "",
                      "shadow": "",
                      "textShadow": "",
                      "textshadowColor": "",
                      "textshadowHorizontal": 0,
                      "textshadowVertical": 0,
                      "textshadowBlur": 3,
                      "textshadowSpread": 0,
                      "textshadowPosition": "outline",
                      "font": "",
                      "fontVariant": "",
                      "fontSize": "",
                      "fontSize_tablet": "",
                      "fontSize_mobile": "",
                      "lineHeight": "",
                      "lineHeight_tablet": "",
                      "lineHeight_mobile": "",
                      "letterSpacing": "",
                      "letterSpacing_tablet": "",
                      "letterSpacing_mobile": "",
                      "typographyBold": "",
                      "typographyItalic": "",
                      "typographyUnderline": "",
                      "align": "center",
                      "align_tablet": "center",
                      "align_mobile": "center",
                      "typographyLetterCase": "",
                      "hideOnDesktop": false,
                      "hideOnTablet": false,
                      "hideOnMobile": false,
                      "entranceAnimation": ""
                    },
                    "widget_settings": ""
                  },
                  {
                    "id": "wwf59h",
                    "elType": "block",
                    "type": "spacer",
                    "settings": {
                      "height": "20",
                      "height_mobile": 40,
                      "height_tablet": 10
                    },
                    "widget_settings": ""
                  },
                  {
                    "id": "ssrgnk",
                    "elType": "block",
                    "type": "text",
                    "settings": {
                      "editortxt": "code",
                      "txt": "<p style=\"text-align: center;\">Your page\'s code has become invalid. Please choose <strong>Revisions</strong> on the bottom left to access the last known working state of this page.</p>",
                      "textColor": "",
                      "bgColor": "",
                      "beforeIcon": "",
                      "afterIcon": "",
                      "marginTop": "",
                      "marginBottom": "",
                      "marginLeft": "",
                      "marginRight": "",
                      "marginSync": false,
                      "marginTop_mobile": "",
                      "marginBottom_mobile": "",
                      "marginLeft_mobile": "",
                      "marginRight_mobile": "",
                      "marginSync_mobile": false,
                      "marginTop_tablet": "",
                      "marginBottom_tablet": "",
                      "marginLeft_tablet": "",
                      "marginRight_tablet": "",
                      "marginSync_tablet": false,
                      "customClass": "",
                      "customAttributes": "",
                      "paddingTop": "",
                      "paddingBottom": "",
                      "paddingLeft": "",
                      "paddingRight": "",
                      "paddingSync": true,
                      "paddingTop_mobile": "",
                      "paddingBottom_mobile": "",
                      "paddingLeft_mobile": "",
                      "paddingRight_mobile": "",
                      "paddingSync_mobile": true,
                      "paddingTop_tablet": "",
                      "paddingBottom_tablet": "",
                      "paddingLeft_tablet": "",
                      "paddingRight_tablet": "",
                      "paddingSync_tablet": true,
                      "borderRadius": "",
                      "border": "",
                      "borderStyle": "",
                      "borderSize": "",
                      "borderColor": "",
                      "shadow": "",
                      "shadowColor": "",
                      "shadowHorizontal": 0,
                      "shadowVertical": 0,
                      "shadowBlur": 3,
                      "shadowSpread": 0,
                      "shadowPosition": "outline",
                      "textShadow": "",
                      "textshadowColor": "",
                      "textshadowHorizontal": 0,
                      "textshadowVertical": 0,
                      "textshadowBlur": 3,
                      "textshadowSpread": 0,
                      "textshadowPosition": "outline",
                      "font": "",
                      "fontVariant": "",
                      "fontSize": "",
                      "fontSize_mobile": "",
                      "fontSize_tablet": "",
                      "lineHeight": "",
                      "lineHeight_mobile": "",
                      "lineHeight_tablet": "",
                      "letterSpacing": "",
                      "letterSpacing_mobile": "",
                      "letterSpacing_tablet": "",
                      "typographyBold": "",
                      "typographyItalic": "",
                      "typographyUnderline": "",
                      "align": "left",
                      "align_mobile": "left",
                      "align_tablet": "left",
                      "typographyLetterCase": "",
                      "entranceAnimation": "",
                      "hideOnDesktop": false,
                      "hideOnMobile": false,
                      "hideOnTablet": false,
                      "version": 2
                    },
                    "widget_settings": ""
                  }
                ],
                "settings": {
                  "bgStyle": "s",
                  "bgGradient": {
                    "type": "linear",
                    "position": "center",
                    "angle": 0,
                    "color1": "",
                    "color1location": 0,
                    "color2": "",
                    "color2location": 100
                  },
                  "colWidth": "",
                  "bgColor": "",
                  "bgImage": "",
                  "bgPosition": "",
                  "bgDimming": 0,
                  "bgDimmingOverlay": "",
                  "bgCustomDimming": 0,
                  "bgCustomDimmingOverlay": "",
                  "bgCustomXPositionUnit": "px",
                  "bgCustomXPosition": "",
                  "bgCustomYPositionUnit": "px",
                  "bgCustomYPosition": "",
                  "bgCustomAttachment": "",
                  "bgCustomRepeat": "",
                  "bgCustomSize": "",
                  "bgCustomSizeWidthUnit": "%",
                  "bgCustomSizeWidth": "100",
                  "marginTop": "",
                  "marginBottom": "",
                  "marginLeft": "",
                  "marginRight": "",
                  "marginSync": false,
                  "marginTop_mobile": "",
                  "marginBottom_mobile": "",
                  "marginLeft_mobile": "",
                  "marginRight_mobile": "",
                  "marginSync_mobile": false,
                  "marginTop_tablet": "",
                  "marginBottom_tablet": "",
                  "marginLeft_tablet": "",
                  "marginRight_tablet": "",
                  "marginSync_tablet": false,
                  "customClass": "",
                  "customAttributes": "",
                  "shadow": "",
                  "shadowColor": "",
                  "shadowHorizontal": 0,
                  "shadowVertical": 0,
                  "shadowBlur": 3,
                  "shadowSpread": 0,
                  "shadowPosition": "outline",
                  "paddingTop": "",
                  "paddingBottom": "",
                  "paddingLeft": "",
                  "paddingRight": "",
                  "paddingSync": true,
                  "paddingTop_mobile": "",
                  "paddingBottom_mobile": "",
                  "paddingLeft_mobile": "",
                  "paddingRight_mobile": "",
                  "paddingSync_mobile": true,
                  "paddingTop_tablet": "",
                  "paddingBottom_tablet": "",
                  "paddingLeft_tablet": "",
                  "paddingRight_tablet": "",
                  "paddingSync_tablet": true,
                  "borderRadiusTL": "",
                  "borderRadiusTR": "",
                  "borderRadiusBL": "",
                  "borderRadiusBR": "",
                  "borderRadiusSync": true,
                  "borderTop": "0",
                  "borderBottom": "0",
                  "borderLeft": "0",
                  "borderRight": "0",
                  "borderSync": true,
                  "borderStyle": "solid",
                  "alignType": "s",
                  "display": "",
                  "flexDirection": "column",
                  "justifyContent": "",
                  "alignItems": "",
                  "cssPosition": "",
                  "cssPosition_mobile": "",
                  "cssPosition_tablet": "",
                  "offsetTop": "",
                  "offsetRight": "",
                  "offsetBottom": "",
                  "offsetLeft": "",
                  "offsetTop_mobile":"",
                  "offsetRight_mobile":"",
                  "offsetBottom_mobile":"",
                  "offsetLeft_mobile":"",
                  "offsetTop_tablet":"",
                  "offsetRight_tablet":"",
                  "offsetBottom_tablet":"",
                  "offsetLeft_tablet":"",
                  "zIndex": "",
                  "zIndex_mobile": "",
                  "zIndex_tablet": "",
                  "overflow": "",
                  "overflow_mobile": "",
                  "overflow_tablet": "",
                  "shapeDivider": "t",
                  "bottomShapeType": "",
                  "bottomShapeColor": "",
                  "bottomshapewidth": 100,
                  "bottomshapeheight": "",
                  "bottomflip": false,
                  "bottomfront": false,
                  "topShapeType": "",
                  "topShapeColor": "",
                  "topshapewidth": 100,
                  "topshapeheight": "",
                  "topflip": false,
                  "topfront": false,
                  "entranceAnimation": "",
                  "hideOnDesktop": false,
                  "hideOnMobile": false,
                  "hideOnTablet": false
                }
              }
            ],
            "settings": {
              "bgStyle": "s",
              "bgGradient": {
                "type": "linear",
                "position": "center",
                "angle": 0,
                "color1": "",
                "color1location": 0,
                "color2": "",
                "color2location": 100
              },
              "colGutter": 0,
              "width": "",
              "bgColor": "",
              "bgImage": "",
              "bgPosition": "",
              "marginTop": "",
              "shadow": "",
              "paddingTop": "0",
              "paddingBottom": "0",
              "paddingLeft": "0",
              "paddingRight": "0",
              "paddingSync": true,
              "borderRadiusTL": "",
              "borderRadiusTR": "",
              "borderRadiusBL": "",
              "borderRadiusBR": "",
              "borderRadiusSync": true,
              "borderTop": "0",
              "borderBottom": "0",
              "borderLeft": "0",
              "borderRight": "0",
              "borderSync": true,
              "borderStyle": "solid",
              "contentWidth": 2,
              "bgDimming": 0,
              "bgDimmingOverlay": "",
              "bgCustomDimming": 0,
              "bgCustomDimmingOverlay": "",
              "bgCustomXPositionUnit": "px",
              "bgCustomXPosition": "",
              "bgCustomYPositionUnit": "px",
              "bgCustomYPosition": "",
              "bgCustomAttachment": "",
              "bgCustomRepeat": "",
              "bgCustomSize": "",
              "bgCustomSizeWidthUnit": "%",
              "bgCustomSizeWidth": "100",
              "marginBottom": "",
              "marginLeft": "",
              "marginRight": "",
              "marginSync": false,
              "marginTop_mobile": "",
              "marginBottom_mobile": "",
              "marginLeft_mobile": "",
              "marginRight_mobile": "",
              "marginSync_mobile": false,
              "customClass": "",
              "customAttributes": "",
              "shadowColor": "",
              "shadowHorizontal": 0,
              "shadowVertical": 0,
              "shadowBlur": 3,
              "shadowSpread": 0,
              "shadowPosition": "outline",
              "paddingTop_mobile": "",
              "paddingBottom_mobile": "",
              "paddingLeft_mobile": "",
              "paddingRight_mobile": "",
              "paddingSync_mobile": true,
              "hideOnDesktop": false,
              "hideOnMobile": false,
              "entranceAnimation": "",
              "alignType": "s",
              "display": "",
              "flexDirection": "row",
              "justifyContent": "",
              "alignItems": "",
              "cssPosition": "",
              "cssPosition_mobile": "",
              "cssPosition_tablet": "",
              "offsetTop": "",
              "offsetRight": "",
              "offsetBottom": "",
              "offsetLeft": "",
              "offsetTop_mobile":"",
              "offsetRight_mobile":"",
              "offsetBottom_mobile":"",
              "offsetLeft_mobile":"",
              "offsetTop_tablet":"",
              "offsetRight_tablet":"",
              "offsetBottom_tablet":"",
              "offsetLeft_tablet":"",
              "zIndex": "",
              "zIndex_mobile": "",
              "zIndex_tablet": "",
              "overflow": "",
              "overflow_mobile": "",
              "overflow_tablet": "",
              "shapeDivider": "t",
              "bottomShapeType": "",
              "bottomShapeColor": "",
              "bottomshapewidth": 100,
              "bottomshapeheight": "",
              "bottomflip": false,
              "bottomfront": false,
              "topShapeType": "",
              "topShapeColor": "",
              "topshapewidth": 100,
              "topshapeheight": "",
              "topflip": false,
              "topfront": false,
              "marginTop_tablet": "",
              "marginBottom_tablet": "",
              "marginLeft_tablet": "",
              "marginRight_tablet": "",
              "marginSync_tablet": false,
              "paddingTop_tablet": "",
              "paddingBottom_tablet": "",
              "paddingLeft_tablet": "",
              "paddingRight_tablet": "",
              "paddingSync_tablet": true,
              "hideOnTablet": false
            }
          }
        ],
        "settings": {
          "bgStyle": "s",
          "bgGradient": {
            "type": "linear",
            "position": "center",
            "angle": 0,
            "color1": "",
            "color1location": 0,
            "color2": "",
            "color2location": 100
          },
          "contentWidth": 1,
          "width": "1000",
          "bgColor": "",
          "bgImage": "",
          "bgPosition": "",
          "marginTop": "",
          "shadow": "",
          "paddingTop": "10",
          "paddingBottom": "10",
          "paddingLeft": "10",
          "paddingRight": "10",
          "paddingSync": true,
          "borderRadius": "",
          "borderTop": "0",
          "borderBottom": "0",
          "borderLeft": "0",
          "borderRight": "0",
          "borderSync": true,
          "borderStyle": "solid",
          "particleBg": false,
          "particleStyle": "default",
          "customParticlesJSON": "",
          "particlesOpacity": "0.5",
          "particlesFlowDirection": "left",
          "particlesAdvanceSettings": false,
          "particlesDotColor": "",
          "numParticles": "",
          "sizeParticles": "",
          "moveParticlesSpeed": 10,
          "particlesHoverEffect": true,
          "bgDimming": 0,
          "bgDimmingOverlay": "",
          "bgCustomDimming": 0,
          "bgCustomDimmingOverlay": "",
          "bgCustomXPositionUnit": "px",
          "bgCustomXPosition": "",
          "bgCustomYPositionUnit": "px",
          "bgCustomYPosition": "",
          "bgCustomAttachment": "",
          "bgCustomRepeat": "",
          "bgCustomSize": "",
          "bgCustomSizeWidthUnit": "%",
          "bgCustomSizeWidth": "100",
          "marginBottom": "",
          "marginLeft": "",
          "marginRight": "",
          "marginSync": false,
          "marginTop_mobile": "",
          "marginBottom_mobile": "",
          "marginLeft_mobile": "",
          "marginRight_mobile": "",
          "marginSync_mobile": false,
          "customClass": "",
          "customAttributes": "",
          "shadowColor": "",
          "shadowHorizontal": 0,
          "shadowVertical": 0,
          "shadowBlur": 3,
          "shadowSpread": 0,
          "shadowPosition": "outline",
          "paddingTop_mobile": "",
          "paddingBottom_mobile": "",
          "paddingLeft_mobile": "",
          "paddingRight_mobile": "",
          "paddingSync_mobile": true,
          "borderRadiusTL": "",
          "borderRadiusTR": "",
          "borderRadiusBL": "",
          "borderRadiusBR": "",
          "borderRadiusSync": true,
          "shapeDivider": "t",
          "bottomShapeType": "",
          "bottomShapeColor": "",
          "bottomshapewidth": 100,
          "bottomshapeheight": "",
          "bottomflip": false,
          "bottomfront": false,
          "topShapeType": "",
          "topShapeColor": "",
          "topshapewidth": 100,
          "topshapeheight": "",
          "topflip": false,
          "topfront": false,
          "entranceAnimation": "",
          "hideOnDesktop": false,
          "hideOnMobile": false,
          "marginTop_tablet": "",
          "marginBottom_tablet": "",
          "marginLeft_tablet": "",
          "marginRight_tablet": "",
          "marginSync_tablet": false,
          "paddingTop_tablet": "",
          "paddingBottom_tablet": "",
          "paddingLeft_tablet": "",
          "paddingRight_tablet": "",
          "paddingSync_tablet": true,
          "hideOnTablet": false
        }
      }
    ],
    "settings": {
      "bgStyle": "s",
      "bgGradient": {
        "type": "linear",
        "position": "center",
        "angle": 0,
        "color1": "",
        "color1location": 0,
        "color2": "",
        "color2location": 100
      },
      "bgColor": "#FFFFFF",
      "bgImage": "",
      "bgDimming": 0,
      "bgOverlayColor": "",
      "bgPosition": "cover",
      "buttonColor": "#000000",
      "headerColor": "#000000",
      "linkColor": "#FF0000",
      "linkDarkerColor": "#cc0000",
      "textColor": "#272727",
      "textFont": "sans-serif",
      "textFontVariant": "400",
      "headerFont": "sans-serif",
      "headerFontVariant": "400",
      "contentPosition": "1",
      "customCss": "",
      "headCss": "#sp-page{color:#272727} #sp-page .sp-header-tag-h1,#sp-page .sp-header-tag-h2,#sp-page .sp-header-tag-h3,#sp-page .sp-header-tag-h4,#sp-page .sp-header-tag-h5,#sp-page .sp-header-tag-h6{color:#000000}#sp-page h1,#sp-page h2,#sp-page h3,#sp-page h4,#sp-page h5,#sp-page h6{color:#000000; font-family:sans-serif;font-weight:400;font-style:normal} #sp-page a{color:#FF0000} #sp-page a:hover{color:#cc0000}#sp-page .btn{background-color:#000000}body{background-color:#FFFFFF !important; background-image:;}",
      "mobileCss": ".sp-mobile-view .sp-headline-block-grs238, .sp-mobile-view  #sp-grs238, .sp-mobile-view  #grs238 {text-align:center !important;}.sp-mobile-view #sp-wwf59h {height:40px !important;}.sp-mobile-view .sp-text-wrapper-ssrgnk, .sp-mobile-view  #sp-ssrgnk, .sp-mobile-view  #ssrgnk {text-align:left !important;}",
      "placeholderCss": "",
      "useVideoBg": false,
      "mobileVisibilityCss": "",
      "desktopVisibilityCss": "",
      "tabletVisibilityCss": "",
      "tabletCss": ".sp-tablet-view .sp-headline-block-grs238, .sp-tablet-view  #sp-grs238, .sp-tablet-view  #grs238 {text-align:center !important;}.sp-tablet-view #sp-wwf59h {height:10px !important;}.sp-tablet-view .sp-text-wrapper-ssrgnk, .sp-tablet-view  #sp-ssrgnk, .sp-tablet-view  #ssrgnk {text-align:left !important;}"
    }
  }
}';

$seedprod_basic_lpage = '
{
  "show_header_template": true,
  "show_footer_template": true,
  "no_conflict_mode": false,
  "no_index": false,
  "seo_title": "",
  "seo_description": "",
  "favicon": "",
  "social_thumbnail": "",
  "enable_recaptcha": false,
  "template_id":"",
  "post_title":"",
  "post_name":"",
  "post_status":"",
  "show_powered_by_link":false,
  "facebook_app_id":"",
  "affiliate_url":"",
  "header_scripts":"",
  "body_scripts":"",
  "footer_scripts":"",
  "conversion_scripts":"",
  "disable_default_excluded_urls": false,
  "include_exclude_type": "0",
  "include_list": "",
  "exclude_list": "",
  "bypass_phrase": "",
  "bypass_expires": "2",
  "bypass_cookie": false,
  "access_by_ip":"",
  "access_by_role":[],
  "redirect_login_page":false,
  "redirect_mode":false,
  "redirect_url":"",
  "domain_mapping_status":false,
  "domain_mapping":"",
  "domain_mapping_force_https":false,
  "exclude_list_404":"",
  "email_integration_id":"",
  "document": {
    "sections": [],
    "settings": {
        "bgStyle": "s",
        "bgGradient": { "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
        "bgColor":"#FFFFFF",
        "bgImage":"",
        "bgDimming":0,
        "bgCustomDimming":0,
        "bgCustomDimmingOverlay":"",
        "bgCustomXPositionUnit":"px",
        "bgCustomXPosition":"",
        "bgCustomYPositionUnit":"px",
        "bgCustomYPosition":"",
        "bgCustomAttachment":"",
        "bgCustomRepeat":"",
        "bgCustomSize":"",
        "bgCustomSizeWidthUnit":"%",
        "bgCustomSizeWidth":"100",
        "bgOverlayColor":"",
        "bgPosition":"cover",
        "buttonColor":"#000000",
        "headerColor":"#000000",
        "linkColor":"#FF0000",
        "linkDarkerColor":"#cc0000",
        "textColor":"#272727",
        "textFont":"\'Helvetica Neue\', Arial, sans-serif",
        "textFontVariant":"400",
        "headerFont":"\'Helvetica Neue\', Arial, sans-serif",
        "headerFontVariant":"400",
        "contentPosition":"1",
        "customCss":"",
        "headCss":"",
        "mobileCss":"",
        "tabletCss":"",
        "mobileVisibilityCss":"",
        "tabletVisibilityCss":"",
        "desktopVisibilityCss":"",
        "placeholderCss":"",
        "useVideoBg":false,
        "useVideoBgUrl":"",
        "useSlideshowBg": false,
        "useSlideshowImgs": [""]
    }
  }
}';

$seedprod_current_content = '
[
  {
     "id":"ks8a8s",
     "type":"section",
     "rows":[
        {
           "id":"vw3kvy",
           "type":"row",
           "colType":"1-col",
           "cols":[
              {
                 "id":"iw5h09",
                 "type":"col",
                 "blocks":[
                    {
                       "id":"jzzxne",
                       "elType":"block",
                       "type":"text",
                       "settings":{
                          "txt":"current_content",
                          "textColor":"",
                          "bgColor":"",
                          "beforeIcon":"",
                          "afterIcon":"",
                          "marginTop":"",
                          "marginBottom": "",
                          "marginLeft": "",
                          "marginRight": "",
                          "marginSync": false,
                          "marginTop_mobile":"",
                          "marginBottom_mobile": "",
                          "marginLeft_mobile": "",
                          "marginRight_mobile": "",
                          "marginSync_mobile": false,
                          "marginTop_tablet":"",
                          "marginBottom_tablet": "",
                          "marginLeft_tablet": "",
                          "marginRight_tablet": "",
                          "marginSync_tablet": false,
                          "customClass": "",
                          "customAttributes": "",
                          "paddingTop":"",
                          "paddingBottom":"",
                          "paddingLeft":"",
                          "paddingRight":"",
                          "paddingSync":true,
                          "paddingTop_mobile":"",
                          "paddingBottom_mobile":"",
                          "paddingLeft_mobile":"",
                          "paddingRight_mobile":"",
                          "paddingSync_mobile":true,
                          "paddingTop_tablet":"",
                          "paddingBottom_tablet":"",
                          "paddingLeft_tablet":"",
                          "paddingRight_tablet":"",
                          "paddingSync_tablet":true,
                          "borderRadius":"",
                          "border":"",
                          "borderStyle":"",
                          "borderSize":"",
                          "borderColor":"",
                          "shadow":"",
                          "textShadow":"",
                          "font":"",
                          "fontVariant":"",
                          "fontSize":"",
                          "fontSize_mobile":"",
                          "fontSize_tablet":"",
                          "lineHeight":"",
                          "lineHeight_mobile":"",
                          "lineHeight_tablet":"",
                          "letterSpacing":"",
                          "letterSpacing_mobile":"",
                          "letterSpacing_tablet":"",
                          "typographyBold":"",
                          "typographyItalic":"",
                          "typographyUnderline":"",
                          "align":"left",
                          "typographyLetterCase":"",
                          "hideOnDesktop": false,
                          "hideOnMobile": false,
                          "hideOnTablet": false
                       }
                    }
                 ],
                 "settings":{
                    "bgStyle":"s",
                    "bgGradient":{
                       "type":"linear",
                       "position":"center",
                       "angle":0,
                       "color1":"",
                       "color1location":0,
                       "color2":"",
                       "color2location":100
                    },
                    "colWidth":"",
                    "bgColor":"",
                    "bgImage":"",
                    "bgPosition":"",
                    "marginTop":"",
                    "marginBottom": "",
                    "marginLeft": "",
                    "marginRight": "",
                    "marginSync": false,
                    "customClass": "",
                    "customAttributes": "",
                    "shadow":"",
                    "paddingTop":"",
                    "paddingBottom":"",
                    "paddingLeft":"",
                    "paddingRight":"",
                    "paddingSync":true,
                    "borderRadiusTL":"",
                    "borderRadiusTR":"",
                    "borderRadiusBL":"",
                    "borderRadiusBR":"",
                    "borderRadiusSync":true,
                    "borderTop":"0",
                    "borderBottom":"0",
                    "borderLeft":"0",
                    "borderRight":"0",
                    "borderSync":true,
                    "borderStyle":"solid"
                 }
              }
           ],
           "settings":{
              "bgStyle":"s",
              "bgGradient":{
                 "type":"linear",
                 "position":"center",
                 "angle":0,
                 "color1":"",
                 "color1location":0,
                 "color2":"",
                 "color2location":100
              },
              "colGutter":0,
              "width":"",
              "bgColor":"",
              "bgImage":"",
              "bgPosition":"",
              "marginTop":"",
              "marginBottom": "",
              "marginLeft": "",
              "marginRight": "",
              "marginSync": false,
              "customClass": "",
              "customAttributes": "",
              "shadow":"",
              "paddingTop":"",
              "paddingBottom":"",
              "paddingLeft":"",
              "paddingRight":"",
              "paddingSync":true,
              "borderRadiusTL":"",
              "borderRadiusTR":"",
              "borderRadiusBL":"",
              "borderRadiusBR":"",
              "borderRadiusSync":true,
              "borderTop":"0",
              "borderBottom":"0",
              "borderLeft":"0",
              "borderRight":"0",
              "borderSync":true,
              "borderStyle":"solid"
           }
        }
     ],
     "settings":{
        "bgStyle":"s",
        "bgGradient":{
           "type":"linear",
           "position":"center",
           "angle":0,
           "color1":"",
           "color1location":0,
           "color2":"",
           "color2location":100
        },
        "contentWidth":1,
        "width":"",
        "bgColor":"",
        "bgImage":"",
        "bgPosition":"",
        "marginTop":"",
        "marginBottom": "",
        "marginLeft": "",
        "marginRight": "",
        "marginSync": false,
        "customClass": "",
        "customAttributes": "",
        "shadow":"",
        "paddingTop":"",
        "paddingBottom":"",
        "paddingLeft":"",
        "paddingRight":"",
        "paddingSync":true,
        "borderRadius":"",
        "borderTop":"0",
        "borderBottom":"0",
        "borderLeft":"0",
        "borderRight":"0",
        "borderSync":true,
        "borderStyle":"solid",
        "borderRadiusTL":"",
        "borderRadiusTR":"",
        "borderRadiusBL":"",
        "borderRadiusBR":"",
        "borderRadiusSync":true
     }
  }
]';

$seedprod_lite_block_templates = '
  {
    "document":{
        "bgStyle": "s",
        "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
        "bgColor":"#FFFFFF",
        "bgImage":"",
        "bgDimming":0,
        "bgCustomDimming":0,
        "bgCustomDimmingOverlay":"",
        "bgCustomXPositionUnit":"px",
        "bgCustomXPosition":"",
        "bgCustomYPositionUnit":"px",
        "bgCustomYPosition":"",
        "bgCustomAttachment":"",
        "bgCustomRepeat":"",
        "bgCustomSize":"",
        "bgOverlayColor":"",
        "bgPosition":"cover",
        "buttonColor":"#000000",
        "headerColor":"#000000",
        "linkColor":"#FF0000",
        "linkDarkerColor":"#cc0000",
        "textColor":"#272727",
        "textFont":"\'Helvetica Neue\', Arial, sans-serif",
        "textFontVariant":"400",
        "headerFont":"\'Helvetica Neue\', Arial, sans-serif",
        "headerFontVariant":"400",
        "contentPosition":"1",
        "customCss":"",
        "headCss":"",
        "mobileCss":"",
        "tabletCss":"",
        "placeholderCss":"",
        "useVideoBg":false,
        "useVideoBgUrl":"",
        "useSlideshowBg": false,
        "useSlideshowImgs": [""]
    },
    "row":{
        "bgStyle": "s",
        "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
        "colGutter": 0,
        "contentWidth":2,
        "width":"",
        "bgColor":"",
        "bgImage":"",
        "bgDimming":0,
        "bgDimmingOverlay":"",
        "bgPosition":"",
        "bgCustomDimming":0,
        "bgCustomDimmingOverlay":"",
        "bgCustomXPositionUnit":"px",
        "bgCustomXPosition":"",
        "bgCustomYPositionUnit":"px",
        "bgCustomYPosition":"",
        "bgCustomAttachment":"",
        "bgCustomRepeat":"",
        "bgCustomSize":"",
        "bgCustomSizeWidthUnit":"%",
        "bgCustomSizeWidth":"100",
        "marginTop": "",
        "marginBottom": "",
        "marginLeft": "",
        "marginRight": "",
        "marginSync": false,
        "marginTop_mobile": "",
        "marginBottom_mobile": "",
        "marginLeft_mobile": "",
        "marginRight_mobile": "",
        "marginSync_mobile": false,
        "marginTop_tablet": "",
        "marginBottom_tablet": "",
        "marginLeft_tablet": "",
        "marginRight_tablet": "",
        "marginSync_tablet": false,
        "customClass": "",
        "customAttributes": "",
        "shadow":"",
        "shadowColor":"",
        "shadowHorizontal":0,
        "shadowVertical":0,
        "shadowBlur":3,
        "shadowSpread":0,
        "shadowPosition":"outline",
        "paddingTop": "",
        "paddingBottom": "",
        "paddingLeft": "",
        "paddingRight": "",
        "paddingSync": true,
        "paddingTop_mobile": "",
        "paddingBottom_mobile": "",
        "paddingLeft_mobile": "",
        "paddingRight_mobile": "",
        "paddingSync_mobile": true,
        "paddingTop_tablet": "",
        "paddingBottom_tablet": "",
        "paddingLeft_tablet": "",
        "paddingRight_tablet": "",
        "paddingSync_tablet": true,
        "borderRadiusTL": "",
        "borderRadiusTR": "",
        "borderRadiusBL": "",
        "borderRadiusBR": "",
        "borderRadiusSync":true,
        "borderTop": "0",
        "borderBottom": "0",
        "borderLeft": "0",
        "borderRight": "0",
        "borderSync": true,
        "borderStyle":"solid",
        "entranceAnimation":"",
        "alignType":"s",
        "display":"",
        "flexDirection":"row",
        "justifyContent":"",
        "alignItems":"",
        "cssPosition":"",
        "cssPosition_mobile":"",
        "cssPosition_tablet":"",
        "offsetTop":"",
        "offsetRight":"",
        "offsetBottom":"",
        "offsetLeft":"",
        "offsetTop_mobile":"",
        "offsetRight_mobile":"",
        "offsetBottom_mobile":"",
        "offsetLeft_mobile":"",
        "offsetTop_tablet":"",
        "offsetRight_tablet":"",
        "offsetBottom_tablet":"",
        "offsetLeft_tablet":"",
        "zIndex":"",
        "zIndex_mobile": "",
        "zIndex_tablet": "",
        "overflow":"",
        "overflow_mobile":"",
        "overflow_tablet":"",
        "shapeDivider":"t",
        "bottomShapeType":"",
        "bottomShapeColor":"",
        "bottomshapewidth":100,
        "bottomshapeheight":"",
        "bottomflip":false,
        "bottomfront":false,
        "topShapeType":"",
        "topShapeColor":"",
        "topshapewidth":100,
        "topshapeheight":"",
        "topflip":false,
        "topfront":false,
        "hideOnDesktop": false,
        "hideOnMobile": false,
        "hideOnTablet": false
    },
    "section":{
        "particleBg":false,
        "particleStyle":"default",
        "customParticlesJSON":"",
        "particlesOpacity":"0.5",
        "particlesFlowDirection":"left",
        "particlesAdvanceSettings":false,
        "particlesDotColor":"",
        "numParticles":"",
        "sizeParticles":"",
        "moveParticlesSpeed":10,
        "particlesHoverEffect":true,
        "bgStyle": "s",
        "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
        "contentWidth":1,
        "width":"",
        "bgColor":"",
        "bgImage":"",
        "bgPosition":"",
        "bgDimming":0,
        "bgDimmingOverlay":"",
        "bgCustomDimming":0,
        "bgCustomDimmingOverlay":"",
        "bgCustomXPositionUnit":"px",
        "bgCustomXPosition":"",
        "bgCustomYPositionUnit":"px",
        "bgCustomYPosition":"",
        "bgCustomAttachment":"",
        "bgCustomRepeat":"",
        "bgCustomSize":"",
        "bgCustomSizeWidthUnit":"%",
        "bgCustomSizeWidth":"100",
        "marginTop": "",
        "marginBottom": "",
        "marginLeft": "",
        "marginRight": "",
        "marginSync": false,
        "marginTop_mobile": "",
        "marginBottom_mobile": "",
        "marginLeft_mobile": "",
        "marginRight_mobile": "",
        "marginSync_mobile": false,
        "marginTop_tablet": "",
        "marginBottom_tablet": "",
        "marginLeft_tablet": "",
        "marginRight_tablet": "",
        "marginSync_tablet": false,
        "customClass": "",
        "customAttributes": "",
        "shadow":"",
        "shadowColor":"",
        "shadowHorizontal":0,
        "shadowVertical":0,
        "shadowBlur":3,
        "shadowSpread":0,
        "shadowPosition":"outline",
        "paddingTop": "",
        "paddingBottom": "",
        "paddingLeft": "",
        "paddingRight": "",
        "paddingSync": true,
        "paddingTop_mobile": "",
        "paddingBottom_mobile": "",
        "paddingLeft_mobile": "",
        "paddingRight_mobile": "",
        "paddingSync_mobile": true,
        "paddingTop_tablet": "",
        "paddingBottom_tablet": "",
        "paddingLeft_tablet": "",
        "paddingRight_tablet": "",
        "paddingSync_tablet": true,
        "borderRadiusTL": "",
        "borderRadiusTR": "",
        "borderRadiusBL": "",
        "borderRadiusBR": "",
        "borderRadiusSync":true,
        "borderTop": "0",
        "borderBottom": "0",
        "borderLeft": "0",
        "borderRight": "0",
        "borderSync": true,
        "borderStyle":"solid",
        "shapeDivider":"t",
        "bottomShapeType":"",
        "bottomShapeColor":"",
        "bottomshapewidth":100,
        "bottomshapeheight":"",
        "bottomflip":false,
        "bottomfront":false,
        "topShapeType":"",
        "topShapeColor":"",
        "topshapewidth":100,
        "topshapeheight":"",
        "topflip":false,
        "topfront":false,
        "cssPosition": "",
        "cssPosition_mobile": "",
        "cssPosition_tablet": "",
        "offsetTop": "",
        "offsetRight": "",
        "offsetBottom": "",
        "offsetLeft": "",
        "offsetTop_mobile":"",
        "offsetRight_mobile":"",
        "offsetBottom_mobile":"",
        "offsetLeft_mobile":"",
        "offsetTop_tablet":"",
        "offsetRight_tablet":"",
        "offsetBottom_tablet":"",
        "offsetLeft_tablet":"",
        "zIndex": "",
        "zIndex_mobile": "",
        "zIndex_tablet": "",
        "overflow": "",
        "overflow_mobile": "",
        "overflow_tablet": "",
        "entranceAnimation":"",
        "hideOnDesktop": false,
        "hideOnMobile": false,
        "hideOnTablet": false
    },
    "col":{
      "bgStyle": "s",
      "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
      "colWidth":"",
      "bgColor":"",
      "bgImage":"",
      "bgPosition":"",
      "bgDimming":0,
      "bgDimmingOverlay":"",
      "bgCustomDimming":0,
      "bgCustomDimmingOverlay":"",
      "bgCustomXPositionUnit":"px",
      "bgCustomXPosition":"",
      "bgCustomYPositionUnit":"px",
      "bgCustomYPosition":"",
      "bgCustomAttachment":"",
      "bgCustomRepeat":"",
      "bgCustomSize":"",
      "bgCustomSizeWidthUnit":"%",
      "bgCustomSizeWidth":"100",
      "marginTop": "",
      "marginBottom": "",
      "marginLeft": "",
      "marginRight": "",
      "marginSync": false,
      "marginTop_mobile": "",
      "marginBottom_mobile": "",
      "marginLeft_mobile": "",
      "marginRight_mobile": "",
      "marginSync_mobile": false,
      "marginTop_tablet": "",
      "marginBottom_tablet": "",
      "marginLeft_tablet": "",
      "marginRight_tablet": "",
      "marginSync_tablet": false,
      "customClass": "",
      "customAttributes": "",
      "shadow":"",
      "shadowColor":"",
      "shadowHorizontal":0,
      "shadowVertical":0,
      "shadowBlur":3,
      "shadowSpread":0,
      "shadowPosition":"outline",
      "paddingTop": "",
      "paddingBottom": "",
      "paddingLeft": "",
      "paddingRight": "",
      "paddingSync": true,
      "paddingTop_mobile": "",
      "paddingBottom_mobile": "",
      "paddingLeft_mobile": "",
      "paddingRight_mobile": "",
      "paddingSync_mobile": true,
      "paddingTop_tablet": "",
      "paddingBottom_tablet": "",
      "paddingLeft_tablet": "",
      "paddingRight_tablet": "",
      "paddingSync_tablet": true,
      "borderRadiusTL": "",
      "borderRadiusTR": "",
      "borderRadiusBL": "",
      "borderRadiusBR": "",
      "borderRadiusSync":true,
      "borderTop": "0",
      "borderBottom": "0",
      "borderLeft": "0",
      "borderRight": "0",
      "borderSync": true,
      "borderStyle":"solid",
      "alignType":"s",
      "display":"",
      "flexDirection":"column",
      "justifyContent":"",
      "alignItems":"",
      "cssPosition":"",
      "cssPosition_mobile":"",
      "cssPosition_tablet":"",
      "offsetTop":"",
      "offsetRight":"",
      "offsetBottom":"",
      "offsetLeft":"",
      "offsetTop_mobile":"",
      "offsetRight_mobile":"",
      "offsetBottom_mobile":"",
      "offsetLeft_mobile":"",
      "offsetTop_tablet":"",
      "offsetRight_tablet":"",
      "offsetBottom_tablet":"",
      "offsetLeft_tablet":"",
      "zIndex":"",
      "zIndex_mobile": "",
      "zIndex_tablet": "",
      "overflow":"",
      "overflow_mobile":"",
      "overflow_tablet":"",
      "shapeDivider":"t",
      "bottomShapeType":"",
      "bottomShapeColor":"",
      "bottomshapewidth":100,
      "bottomshapeheight":"",
      "bottomflip":false,
      "bottomfront":false,
      "topShapeType":"",
      "topShapeColor":"",
      "topshapewidth":100,
      "topshapeheight":"",
      "topflip":false,
      "topfront":false,
      "entranceAnimation":"",
      "hideOnDesktop": false,
      "hideOnMobile": false,
      "hideOnTablet": false
  },
  "icon":{
      "linktype": "custom",
      "link": "",
      "openNewWindow": false,
      "noFollow": false,
      "icon":"fas fa-angle-double-down",
      "color":"",
      "fontSize":"48",
      "align":"center",
      "align_mobile":"center",
      "align_tablet":"center",
      "verticalalign": "left",
      "textShadow":"",
      "textshadowColor":"",
      "textshadowHorizontal":0,
      "textshadowVertical":0,
      "textshadowBlur":3,
      "textshadowSpread":0,
      "textshadowPosition":"outline",
      "marginTop": "",
      "marginBottom": "",
      "marginLeft": "",
      "marginRight": "",
      "marginSync": false,
      "marginTop_mobile": "",
      "marginBottom_mobile": "",
      "marginLeft_mobile": "",
      "marginRight_mobile": "",
      "marginSync_mobile": false,
      "marginTop_tablet": "",
      "marginBottom_tablet": "",
      "marginLeft_tablet": "",
      "marginRight_tablet": "",
      "marginSync_tablet": false,
      "customClass": "",
      "customAttributes": "",
      "paddingTop": "",
      "paddingBottom": "",
      "paddingLeft": "",
      "paddingRight": "",
      "paddingSync": true,
      "paddingTop_mobile": "",
      "paddingBottom_mobile": "",
      "paddingLeft_mobile": "",
      "paddingRight_mobile": "",
      "paddingSync_mobile": true,
      "paddingTop_tablet": "",
      "paddingBottom_tablet": "",
      "paddingLeft_tablet": "",
      "paddingRight_tablet": "",
      "paddingSync_tablet": true,
      "entranceAnimation":"",
      "hideOnDesktop": false,
      "hideOnMobile": false,
      "hideOnTablet": false
  },
  "starrating":{
    "label":"",
    "scale":"5",
    "rating":"5",
    "iconSize":"24",
    "spaceBetween":"",
    "align":"center",
    "align_mobile":"center",
    "align_tablet":"center",
    "color":"#fdd835",
    "textColor":"",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "textShadow":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "businessreviews":{
    "skin":"default",
    "reviewsource":"googleplaces",
    "googleplaceid":"",
    "yelpbusinessid":"",
    "languagecode":"",
    "reloadreview":"hour",
    "items": [{ "img": "", "txt": "\"This is the greatest thing since sliced bread!\"", "name": "John Smith", "title": "CEO" }],
    "align": "center",
    "datealign": "center",
    "namealign": "center",
    "layout":"grid",
    "slidetoshow":1,
    "gridcolumn":3,
    "filterby":"default",
    "minimumrating":"no",
    "reviewerimage":true,
    "imageposition":"abovename",
    "imageSize":40,
    "reviewername":true,
    "reviewerlinkname":true,
    "reviewernamecolor":"#9E9E9E",
    "reviewdate":true,
    "reviewdatetype":"relative",
    "reviewdateColor":"#9E9E9E",
    "reviewrating":true,
    "stariconstyle":"default",
    "iconSize":10,
    "iconColor":"#FB8C00",
    "unmaskediconColor":"#CCCCCC",
    "reviewtext":true,
    "reviewtextColor":"#444444",
    "reviewtextlength":25,
    "readmoretext":"Read more...",
    "readmoreColor":"#444444",
    "navColor":"d",
    "commentBubble":true,
    "bubbleColor": "",
    "autoPlay":true,
    "slidetoshow":1,
    "numofreviews":3,
    "slideshow":1,
    "speed":5,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "textColor": "#444444",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": true,
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "dateColor": "#9E9E9E",
    "datefont": "",
    "datefontVariant": "",
    "datefontSize": "",
    "datefontSize_mobile": "",
    "datefontSize_tablet": "",
    "datelineHeight": "",
    "datelineHeight_mobile": "",
    "datelineHeight_tablet": "",
    "dateletterSpacing": "",
    "dateletterSpacing_mobile": "",
    "dateletterSpacing_tablet": "",
    "datetypographyBold": "",
    "datetypographyItalic": true,
    "datetypographyUnderline": "",
    "datetypographyLetterCase": "",
    "nameColor": "#444444",
    "namefont": "",
    "namefontVariant": "",
    "namefontSize": "12",
    "namefontSize_mobile": "",
    "namefontSize_tablet": "",
    "namelineHeight": "",
    "namelineHeight_mobile": "",
    "namelineHeight_tablet": "",
    "nameletterSpacing": "",
    "nameletterSpacing_mobile": "",
    "nameletterSpacing_tablet": "",
    "nametypographyBold": "",
    "nametypographyItalic": "",
    "nametypographyUnderline": "",
    "nametypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "testimonial":{
    "items": [{ "show_image": true, "img": "", "srcset": "", "txt": "\"This is the greatest thing since sliced bread!\"", "name": "John Smith", "title": "CEO" }],
    "align": "left",
    "navColor":"d",
    "commentBubble":true,
    "bubbleColor": "",
    "autoPlay":true,
    "slidetoshow":1,
    "slideshow":1,
    "speed":5,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "textColor": "#444444",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": true,
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "nameColor": "",
    "namefont": "",
    "namefontVariant": "",
    "namefontSize": "",
    "namefontSize_mobile": "",
    "namefontSize_tablet": "",
    "namelineHeight": "",
    "namelineHeight_mobile": "",
    "namelineHeight_tablet": "",
    "nameletterSpacing": "",
    "nameletterSpacing_mobile": "",
    "nameletterSpacing_tablet": "",
    "nametypographyBold": "",
    "nametypographyItalic": "",
    "nametypographyUnderline": "",
    "nametypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "imagecarousel":{
    "showLightboxGallery":false,
    "items": [
      {
        "img": "",
        "name": "",
        "title": "",
        "srcset": "",
        "show_header": false,
        "header": "Header 1",
        "subheader": "Subheader 1",
        "show_button": false,
        "button_text": "Button 1",
        "button_link": "",
        "link_open_new_window": false,
        "link_no_follow": false
      }
    ],
    "bannerButtonBorderRadius": "",
    "bannerButtonBgColor": "",
    "bannerButtonStyle": "flat",
    "bannerButtonSize": 3,
    "bannerButtonShadow": "",
    "bannerButtonLineHeight": "",
    "bannerbuttonfont": "",
    "bannerbuttonfontVariant": "",
    "bannerbuttonfontSize": "16",
    "bannerbuttonfontSize_mobile": "",
    "bannerbuttonfontSize_tablet": "",
    "bannerbuttonlineHeight": "",
    "bannerbuttonlineHeight_mobile": "",
    "bannerbuttonlineHeight_tablet": "",
    "bannerbuttonletterSpacing": "",
    "bannerbuttonletterSpacing_mobile": "",
    "bannerbuttonletterSpacing_tablet": "",
    "bannerbuttontypographyBold": "",
    "bannerbuttontypographyItalic": "",
    "bannerbuttontypographyUnderline": "",
    "bannerbuttontypographyLetterCase": "",
    "bannerbuttonalign": "center",
    "bannerButtonPaddingTop": "15",
    "bannerButtonPaddingLeft": "35",
    "bannerButtonBorderRadius": "",
    "bannerButtonBeforeIcon": "",
    "bannerButtonAfterIcon": "",
    "headerVerticalOrientation": 50,
    "headerHorizontalOrientation": 50,
    "align": "center",
    "captionalign":"center",
    "headertextalign":"center",
    "headertextalign_mobile":"center",
    "headertextalign_tablet":"center",
    "subheadertextalign":"center",
    "subheadertextalign_mobile":"center",
    "subheadertextalign_tablet":"center",
    "carouselbuttonalign":"center",
    "carouselbuttonalign_mobile":"center",
    "carouselbuttonalign_tablet":"center",
    "carouselHeaderAnimation":"",
    "carouselSubHeaderAnimation":"",
    "carouselButtonAnimation":"",
    "showCaption": false,
    "showSliderNav": true,
    "navColor":"d",
    "slidetoshow":1,
    "slideshow":1,
    "autoPlay":true,
    "speed":5,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "textColor": "",
    "captionColor": "",
    "captionfont": "",
    "captionfontVariant": "",
    "captionfontSize": "",
    "captionfontSize_mobile": "",
    "captionfontSize_tablet": "",
    "captionlineHeight": "",
    "captionlineHeight_mobile": "",
    "captionlineHeight_tablet": "",
    "captionletterSpacing": "",
    "captionletterSpacing_mobile": "",
    "captionletterSpacing_tablet": "",
    "captiontypographyBold": "",
    "captiontypographyItalic": "",
    "captiontypographyUnderline": "",
    "captiontypographyLetterCase": "",
    "bannerHeaderColor": "",
    "bannerHeaderfont": "",
    "bannerHeaderfontVariant": "",
    "bannerHeaderfontSize": "",
    "bannerHeaderfontSize_mobile": "",
    "bannerHeaderfontSize_tablet": "",
    "bannerHeaderlineHeight": "",
    "bannerHeaderlineHeight_mobile": "",
    "bannerHeaderlineHeight_tablet": "",
    "bannerHeaderletterSpacing": "",
    "bannerHeaderletterSpacing_mobile": "",
    "bannerHeaderletterSpacing_tablet": "",
    "bannerHeadertypographyBold": "",
    "bannerHeadertypographyItalic": "",
    "bannerHeadertypographyUnderline": "",
    "bannerHeadertypographyLetterCase": "",
    "bannerSubheaderColor": "",
    "bannerSubheaderfont": "",
    "bannerSubheaderfontVariant": "",
    "bannerSubheaderfontSize": "",
    "bannerSubheaderfontSize_mobile": "",
    "bannerSubheaderfontSize_tablet": "",
    "bannerSubheaderlineHeight": "",
    "bannerSubheaderlineHeight_mobile": "",
    "bannerSubheaderlineHeight_tablet": "",
    "bannerSubheaderletterSpacing": "",
    "bannerSubheaderletterSpacing_mobile": "",
    "bannerSubheaderletterSpacing_tablet": "",
    "bannerSubheadertypographyBold": "",
    "bannerSubheadertypographyItalic": "",
    "bannerSubheadertypographyUnderline": "",
    "bannerSubheadertypographyLetterCase": "",
    "bannerButtonfont": "",
    "bannerButtonfontVariant": "",
    "bannerButtonfontSize": "",
    "bannerButtonfontSize_mobile": "",
    "bannerButtonfontSize_tablet": "",
    "bannerButtonlineHeight": "",
    "bannerButtonlineHeight_mobile": "",
    "bannerButtonlineHeight_tablet": "",
    "bannerButtonletterSpacing": "",
    "bannerButtonletterSpacing_mobile": "",
    "bannerButtonletterSpacing_tablet": "",
    "bannerButtontypographyBold": "",
    "bannerButtontypographyItalic": "",
    "bannerButtontypographyUnderline": "",
    "bannerButtontypographyLetterCase": "",
    "shadow": "",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "imageOverlayColor": "",
    "imageOverlayOpacity": "0.5",
    "blockTemplateId": false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnTablet": false,
    "hideOnMobile": false
  },
  "feature":{
    "showLightboxGallery":false,
    "tag": "h2",
    "icon":"",
    "iconColor":"",
    "textColor": "",
    "headerColor": "",
    "iconFontSize":"38",
    "headerTxt": "My Feature or Benefit",
    "layout": "image",
    "txt": "Description",
    "width": "",
    "height": "",
    "align": "left",
    "verticalalign": "left",
    "altTxt": "",
    "src": "",
    "unit":"px",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "imageBorderRadius": "3",
    "radiusunit":"px",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync": true,
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "beforeaftertoggle":{
    "beforeText":"Before",
    "afterText":"After",
    "beforesrc": "",
    "aftersrc": "",
    "orientation":"horizontal",
    "moveOnHover":false,
    "overlayColor":"",
    "initialOffset":50,
    "blankImagesText":"Please add Before/After Images",
    "loadingBeforeAfter":"Loading...",
    "handleColor":"",
    "handleThickness":"3",
    "circleWidth":"50",
    "circleRadius":"50",
    "triangleSize":"10",
    "tag": "h2",
    "icon":"fas fa-check",
    "iconColor":"",
    "labelColor": "",
    "bgColor":"",
    "textColor": "",
    "iconFontSize":"38",
    "headerTxt": "My Feature or Benefit",
    "layout": "image",
    "txt": "Description",
    "width": "200",
    "height": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "labelalign": "left",
    "labelalign_mobile": "left",
    "labelalign_tablet": "left",
    "altTxt": "",
    "src": "",
    "unit":"px",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "labelfont": "",
    "labelfontVariant": "",
    "labelfontSize": "",
    "labelfontSize_mobile": "",
    "labelfontSize_tablet": "",
    "labellineHeight": "",
    "labellineHeight_mobile": "",
    "labellineHeight_tablet": "",
    "labelletterSpacing": "",
    "labelletterSpacing_mobile": "",
    "labelletterSpacing_tablet": "",
    "labeltypographyBold": "",
    "labeltypographyItalic": "",
    "labeltypographyUnderline": "",
    "labeltypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "contenttoggle":{
    "txt1":"Heading 1",
    "txt2":"Heading 2",
    "contenttype1":"content",
    "contenttype2":"content",
    "content1":"This is your first content.",
    "content2":"This is your second content.",
    "templateparts":"",
    "templateresult":"",
    "templateparts2":"",
    "templateresult2":"",
    "showToggleContent":true,
    "heading1Color": "",
    "heading2Color": "",
    "content1Color": "",
    "content2Color": "",
    "lefttogglebgColor": "",
    "righttogglebgColor": "",
    "switcherbgColor": "",
    "switchstyle":"round",
    "switcherSize":"small",
    "heading1font": "",
    "heading1fontVariant": "",
    "heading1fontSize": "16",
    "heading1fontSize_mobile": "",
    "heading1fontSize_tablet": "",
    "heading1lineHeight": "",
    "heading1lineHeight_mobile": "",
    "heading1lineHeight_tablet": "",
    "heading1letterSpacing": "",
    "heading1letterSpacing_mobile": "",
    "heading1letterSpacing_tablet": "",
    "heading1typographyBold": true,
    "heading1typographyItalic": "",
    "heading1typographyUnderline": "",
    "heading1typographyLetterCase": "",
    "heading2font": "",
    "heading2fontVariant": "",
    "heading2fontSize": "16",
    "heading2fontSize_mobile": "",
    "heading2fontSize_tablet": "",
    "heading2lineHeight": "",
    "heading2lineHeight_mobile": "",
    "heading2lineHeight_tablet": "",
    "heading2letterSpacing": "",
    "heading2letterSpacing_mobile": "",
    "heading2letterSpacing_tablet": "",
    "heading2typographyBold": true,
    "heading2typographyItalic": "",
    "heading2typographyUnderline": "",
    "heading2typographyLetterCase": "",
    "content1font": "",
    "content1fontVariant": "",
    "content1fontSize": "",
    "content1fontSize_mobile": "",
    "content1fontSize_tablet": "",
    "content1lineHeight": "",
    "content1lineHeight_mobile": "",
    "content1lineHeight_tablet": "",
    "content1letterSpacing": "",
    "content1letterSpacing_mobile": "",
    "content1letterSpacing_tablet": "",
    "content1typographyBold": "",
    "content1typographyItalic": "",
    "content1typographyUnderline": "",
    "content1typographyLetterCase": "",
    "content2font": "",
    "content2fontVariant": "",
    "content2fontSize": "",
    "content2fontSize_mobile": "",
    "content2fontSize_tablet": "",
    "content2lineHeight": "",
    "content2lineHeight_mobile": "",
    "content2lineHeight_tablet": "",
    "content2letterSpacing": "",
    "content2letterSpacing_mobile": "",
    "content2letterSpacing_tablet": "",
    "content2typographyBold": "",
    "content2typographyItalic": "",
    "content2typographyUnderline": "",
    "content2typographyLetterCase": "",
    "tag": "h2",
    "icon":"fas fa-check",
    "bgColor":"",
    "textColor": "",
    "align": "left",
    "labelalign": "left",
    "align_mobile": "left",
    "labelalign_mobile": "left",
    "align_tablet": "left",
    "labelalign_tablet": "left",
    "src": "",
    "unit":"px",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "labelfont": "",
    "labelfontVariant": "",
    "labelfontSize": "",
    "labelfontSize_mobile": "",
    "labelfontSize_tablet": "",
    "labellineHeight": "",
    "labellineHeight_mobile": "",
    "labellineHeight_tablet": "",
    "labelletterSpacing": "",
    "labelletterSpacing_mobile": "",
    "labelletterSpacing_tablet": "",
    "labeltypographyBold": "",
    "labeltypographyItalic": "",
    "labeltypographyUnderline": "",
    "labeltypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "iconfeature":{
    "tag": "h2",
    "icon":"fas fa-check",
    "iconColor":"",
    "headerColor": "",
    "textColor": "",
    "iconFontSize":"38",
    "iconGap":"20",
    "headerTxt": "My Feature or Benefit",
    "layout": "icon",
    "txt": "Description",
    "width": "200",
    "height": "",
    "align": "left",
    "align_tablet": "center",
    "align_mobile": "center",
    "verticalalign": "left",
    "altTxt": "",
    "src": "",
    "unit":"px",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "header":{
    "headerTxt": "My Awesome Headline",
    "tag": "h1",
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "headerTxtaiprompt":"",
    "headerTxtairesult":"",
    "headerTxtchangetoneto":"",
    "headerTxtchangelanguageto":"",
    "headerTxtshow_modal_openai":false,
    "headerTxtshow_generate_text":true,
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_tablet": "",
    "fontSize_mobile": "",
    "lineHeight": "",
    "lineHeight_tablet": "",
    "lineHeight_mobile": "",
    "letterSpacing": "",
    "letterSpacing_tablet": "",
    "letterSpacing_mobile": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_tablet": "center",
    "align_mobile": "center",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnTablet": false,
    "hideOnMobile": false,
    "entranceAnimation":""
  },
  "animatedheadline":{
    "beforeHeaderTxt": "Before text",
    "headerTxt": "My Awesome Headline",
    "headerBiggerTxt": "My Awesome\nBigger Headline",
    "afterHeaderTxt": "After text",
    "styleType":"highlight",
    "shapeType":"circle",
    "animationType":"typing",
    "infiniteLoop":true,
    "animationDuration":1200,
    "animationDelay":8000,
    "strokeColor":"",
    "tag": "h1",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "seedprodgallery":{
    "items": [{ "img": "", "imgdata":[], "imagesid":[], "name": "Tab 1", "title": "CEO" }],
    "singleitems": [{ "img": "","imgdata":[],"imagesid":[], "name": "" }],
    "align": "center",
    "captionalign":"center",
    "bgOverlay": false,
    "overlay_title":"",
    "overlay_desc":"",
    "galleryType":"single",
    "galleryLayout":"grid",
    "galleryimg":"",
    "galleryimgobj":[],
    "galleryimgid":[],
    "galleryimgdata":[],
    "rowHeight":"100",
    "navColor":"d",
    "lazyLoad":true,
    "columns":3,
    "colSpacing":"1",
    "galleryLink":"",
    "customurl":"",
    "aspect_ratio":"1:1",
    "image_size":"medium",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "textColor": "",
    "tabBgColor": "",
    "tabPaddingTop": "4",
    "tabPaddingLeft": "10",
    "tabMarginLeft": "5",
    "tabborderRadius": "",
    "tabborderWidth": "",
    "tabBorderSync": false,
    "tabBorderTop": "",
    "tabBorderRight": "",
    "tabBorderBottom": "2",
    "tabBorderLeft": "",
    "tabborderColor": "",
    "tabHoverColor": "",
    "hoverBgColor": "",
    "tabActiveColor": "",
    "activeBgColor": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "tabShadow": "",
    "tabshadow": "",
    "tabshadowColor":"",
    "tabshadowHorizontal":0,
    "tabshadowVertical":0,
    "tabshadowBlur":3,
    "tabshadowSpread":0,
    "tabshadowPosition":"outline",
    "captionColor": "",
    "captionfont": "",
    "captionfontVariant": "",
    "captionfontSize": "",
    "captionfontSize_mobile": "",
    "captionfontSize_tablet": "",
    "captionlineHeight": "",
    "captionlineHeight_mobile": "",
    "captionlineHeight_tablet": "",
    "captionletterSpacing": "",
    "captionletterSpacing_mobile": "",
    "captionletterSpacing_tablet": "",
    "captiontypographyBold": "",
    "captiontypographyItalic": "",
    "captiontypographyUnderline": "",
    "captiontypographyLetterCase": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "overlayColor": "",
    "overlayfont": "",
    "overlayfontVariant": "",
    "overlayfontSize": "",
    "overlayfontSize_mobile": "",
    "overlayfontSize_tablet": "",
    "overlaylineHeight": "",
    "overlaylineHeight_mobile": "",
    "overlaylineHeight_tablet": "",
    "overlayletterSpacing": "",
    "overlayletterSpacing_mobile": "",
    "overlayletterSpacing_tablet": "",
    "overlaytypographyBold": "",
    "overlaytypographyItalic": "",
    "overlaytypographyUnderline": "",
    "overlaytypographyLetterCase": "",
    "headerColor":"",
    "headerOpenColor":"",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "overlayColor":"#ffffff",
    "overlayBgColor":"#000000",
    "shadow": "",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "imagePadding": "",
    "blockTemplateId": false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "seedprodbasicgallery":{
    "items": [{ "img": "","imgdata":[],"imagesid":[], "name": "", "title": "CEO" }],
    "singleitems": [{ "img": "","imgdata":[],"imagesid":[], "name": "" }],
    "order_by":"",
    "align": "center",
    "captionalign":"center",
    "bgOverlay": true,
    "overlay_title":"",
    "overlay_desc":"",
    "galleryType":"single",
    "lightboxEffect":"no",
    "galleryLayout":"grid",
    "galleryimg":"",
    "galleryimgobj":[],
    "galleryimgid":[],
    "galleryimgdata":[],
    "rowHeight":"100",
    "navColor":"d",
    "lazyLoad":true,
    "columns":3,
    "colSpacing":"1",
    "galleryLink":"none",
    "customurl":"",
    "aspect_ratio":"1:1",
    "image_size":"medium",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "textColor": "",
    "captionColor": "",
    "captionfont": "",
    "captionfontVariant": "",
    "captionfontSize": "",
    "captionfontSize_mobile": "",
    "captionfontSize_tablet": "",
    "captionlineHeight": "",
    "captionlineHeight_mobile": "",
    "captionlineHeight_tablet": "",
    "captionletterSpacing": "",
    "captionletterSpacing_mobile": "",
    "captionletterSpacing_tablet": "",
    "captiontypographyBold": "",
    "captiontypographyItalic": "",
    "captiontypographyUnderline": "",
    "captiontypographyLetterCase": "",
    "shadow": "",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "imagePadding": "",
    "blockTemplateId": false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "stripepayment":{
    "payment_amount": "",
    "payment_description": "",
    "stripe_token": "",
    "success_url": "",
    "payment_currency": "USD",
    "action": "link",
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "btnTxt": "Pay Now",
    "btnStyle": "",
    "btnSubTxt": "",
    "btnSubTextSize": "",
    "textColor": "",
    "bgColor": "",
    "buttonTextColor":"",
    "bgColorHover":"",
    "buttonTextHoverColor":"",
    "buttonNormalStyle":"s",
    "buttonNormalBGtype":"radial",
    "buttonNormalBGposition":"",
    "buttonHoverStyle":"s",
    "buttonHoverBGtype":"radial",
    "buttonHoverBGposition":"",
    "btnStyleHover":"",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "btnSize":"4",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "4",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "22",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "button":{
    "action": "link",
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "btnDynamicTxt": "",
    "btnTxt": "Call To Action",
    "btnStyle": "",
    "btnSubTxt": "",
    "btnSubTextSize": "",
    "textColor": "",
    "bgColor": "",
    "buttonTextColor":"",
    "bgColorHover":"",
    "buttonTextHoverColor":"",
    "buttonNormalStyle":"s",
    "buttonNormalBGtype":"radial",
    "buttonNormalBGposition":"",
    "buttonHoverStyle":"s",
    "buttonHoverBGtype":"radial",
    "buttonHoverBGposition":"",
    "btnStyleHover":"",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "btnSize":"4",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "22",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "text":{
    "editortxt":"code",
    "txt": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam commodo velit ex, non ultrices leo auctor at. Integer blandit ex velit, vel aliquam sem tempor eu. Pellentesque sem tortor, elementum et nisi sed, convallis pharetra lorem. Aenean rhoncus rhoncus ex, in dictum massa dictum et. Morbi at nisl fermentum, condimentum tortor a, laoreet leo. Curabitur laoreet diam a metus tincidunt, sed dapibus orci venenatis.",
    "txtaiprompt":"",
    "txtairesult":"",
    "txtchangetoneto":"",
    "txtchangelanguageto":"",
    "txtshow_modal_openai":false,
    "txtshow_generate_text":true,
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "facebooklike":{
    "buttonType":"like",
    "layoutType":"standard",
    "buttonSize":"small",
    "shareButton":false,
    "targetUrl":"currentpage",
    "targetUrlFormat":"plainpermalink",     
    "customlinkurl":"",
    "align": "left",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "facebookcomments":{
    "orderBy" :"social",
    "lazyLoad" :false,
    "colorScheme" : "dark",
    "numComments" : "5",
    "targetUrl":"currentpage",
    "targetUrlFormat":"plainpermalink",     
    "customlinkurl":"",
    "align": "center",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "facebookpage":{
    "layoutPage":"timeline",
    "multiLayoutPage":["timeline"],
    "smallHeader" : false,
    "coverPhoto" : false,
    "profilePhoto" : true,
    "hideCTA" : true,
    "height" : "500",   
    "customlinkurl":"https://www.facebook.com/seedprodwp/",
    "align": "center",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "facebookembed":{
    "embedType":"post",
    "fullPost" : true,
    "fullScreen" : true,
    "autoPlay" : true,
    "captions" : true,
    "parentComment" : true,
    "height" : "500",   
    "postlinkurl":"https://www.facebook.com/seedprodwp/posts/1788470497975496",
    "videolinkurl":"https://business.facebook.com/seedprodwp/videos/226940179165922/",
    "commentlinkurl":"https://www.facebook.com/zuck/posts/10102577175875681?comment_id=1193531464007751&reply_comment_id=654912701278942",
    "align": "center",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "twitterembedtweet":{
    "align": "left",
    "tweeterid":"1385250840898850816",
    "colorScheme":"light",
    "width":"350",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "lang":"en",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "twitterembedtimeline":{
    "align": "left",
    "tweeterid":"seedprod",
    "colorScheme":"light",
    "width":"350",
    "height":"350",
    "hideHeader": false,
    "hideFooter": true,
    "hideBorders": false,
    "transparent": false,
    "hideScrollbar": false,
    "showReplies":false,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "lang":"en",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "sitelogo":{
    "unit":"px",
    "height":"",
    "width":"",
    "align": "left",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "2",
    "paddingBottom": "2",
    "paddingLeft": "0",
    "paddingRight": "0",
    "paddingSync": true,
    "paddingTop_mobile": "2",
    "paddingBottom_mobile": "2",
    "paddingLeft_mobile": "0",
    "paddingRight_mobile": "0",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "2",
    "paddingBottom_tablet": "2",
    "paddingLeft_tablet": "0",
    "paddingRight_tablet": "0",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "twittertweetbutton":{
    "tweetUrl":"https://twitter.com/seedprod",
    "buttonSize":"small",
    "tweetText":"",
    "tweetHashTag":"",
    "viaHandle":"",
    "relatedTweet":"",
    "align": "left",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "lang":"en",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "twitterfollowbutton":{
    "screenName":"seedprod",
    "buttonSize":"small",
    "showScreenName":true,
    "showCount":false, 
    "align": "left",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "lang":"en",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "alertbox":{
    "showIcon":true,
    "icon":"fas fa-check",
    "alertType":"info",
    "dismissbtn":true,
    "headerTxt": "Title",
    "description": "This is description area.",
    "tag": "h1",
    "bgColor":"",
    "alertStyles":"style3",
    "textColor": "",
    "descColor": "",
    "dismissColor": "",
    "bgTitleColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "18",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "true",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "textfont": "",
    "textfontVariant": "",
    "textfontSize": "14",
    "textfontSize_mobile": "",
    "textfontSize_tablet": "",
    "textlineHeight": "",
    "textlineHeight_mobile": "",
    "textlineHeight_tablet": "",
    "textletterSpacing": "",
    "textletterSpacing_mobile": "",
    "textletterSpacing_tablet": "",
    "texttypographyBold": "",
    "texttypographyItalic": "",
    "texttypographyUnderline": "",
    "textalign": "left",
    "textalign_mobile": "left",
    "textalign_tablet": "left",
    "texttypographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false,
    "entranceAnimation":""
  },
  "enviragallery":{
    "formId":"",
    "limit":"20",
    "textColor":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "contactform":{
    "formId":"",
    "formTitle": "false",
    "formDescription": "false",
    "textColor":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "pushnotification":{
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "giveaway":{
    "giveawayId":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "mypaykit":{
    "giveawayId":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "image":{
    "linktype":"custom",
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "unit":"px",
    "dynamicImgShortcode": "",
    "dynamicImgSrc": "",
    "srcaiimageprompt":"",
    "srcaiimageresult":"",
    "srcaieditimageprompt":"",
    "srcaiimage":"",
    "srcaigenerated":false,
    "srcshow_modal_openai":false,
    "srcshow_generate_text":true,
    "srccodegenerated":true,
    "src": "",
    "altTxt": "",
    "link": "",
    "width": "",
    "height": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "imagePadding": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "shadow": "",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "blockTemplateId":false,
    "radiusunit":"px",
    "imghiddenwidth": "",
    "imghiddenheight": "",
    "imgSetSource": "",
    "borderRadiusSync":true,
    "shadowColor":"#000000",
    "shadowSpread":"-20",
    "shadowHorizontal":"0",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "shadowVertical":"20",
    "shadowBlur":"20",
    "shadowPosition":"outline",
    "objectfit":"fill",
    "rotateimage":0,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "divider":{
    "style": "solid",
    "width": "90",
    "height": "1",
    "color": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "10",
    "paddingBottom": "10",
    "paddingLeft": "0",
    "paddingRight": "0",
    "paddingSync": false,
    "paddingTop_mobile": "10",
    "paddingBottom_mobile": "10",
    "paddingLeft_mobile": "0",
    "paddingRight_mobile": "0",
    "paddingSync_mobile": false,
    "paddingTop_tablet": "10",
    "paddingBottom_tablet": "10",
    "paddingLeft_tablet": "0",
    "paddingRight_tablet": "0",
    "paddingSync_tablet": false,
    "shadow": "",
    "addelement":"",
    "elementText":"SeedProd",
    "elementTextTag":"span",
    "elementIcon":"fas fa-check",
    "textColor": "",
    "iconColor": "",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "iconHeight": "15",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "spacer": {
    "height": 60,
    "height_mobile": 40,
    "height_tablet": 10
  },
  "accordion":{
    "spaceBetween":"24",
    "items": [{"txt": "Accordion 1", "details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam commodo velit ex, non ultrices leo auctor at."}, {"txt": "Accordion 2", "details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam commodo velit ex, non ultrices leo auctor at."}],
    "bgColor": "",
    "dividerColor": "",
    "textColor": "",
    "iconColor": "",
    "openIconColor": "",
    "closedIcon": "fas fa-plus",
    "openIcon": "fas fa-minus",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "headerColor":"",
    "headerOpenColor":"",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "anchor": {
    "name": ""
  },
  "searchform": {
    "align": "center",
    "buttonType":"icon",
    "buttonIconType":"icon1",
    "placeholder":"Search...",
    "buttonText":"Search",
    "size":36,
    "width":"",
    "bgColor":"",
    "searchColor":"",
    "buttonBgColor":"",
    "buttonColor":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "googlemaps": {
    "location": "West Palm Beach, FL",
    "zoom": "10",
    "width": "100",
    "height": "",
    "align": "center",
    "shadow":"",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "customhtml":{
    "code": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "shortcode":{
    "shortcode": "",
    "shortcode_placeholder": "",
    "render_shortcode_preview": false,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "templatetag":{
    "templatetag": "",
    "templatetag_placeholder": "",
    "render_templatetag_preview": false,
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "hideOnDesktop": false,
    "hideOnMobile": false
  },
  "seedprodtemplateparts":{
    "templateparts" :"",
    "templateresult":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "login":{
    "align": "left",
    "redirectUrl": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "rowSpacing": "10",
    "linkColor": "",
    "linkHoverColor": "",
    "loggedInText": "Logged in as {user}",
    "labelRememberme": "Remember Me",
    "labelLostpass": "Lost your password?",
    "labelTextUser": "Username or Email Address",
    "labelTextUserPlaceholder": "",
    "labelTextPass": "Password",
    "labelTextPassPlaceholder": "",
    "labelSpacing": "5",
    "labelTextColor": "",
    "labelTextFont": "",
    "labelTextSize": "",
    "labelTextFontVariant": "",
    "fieldTextColor": "",
    "fieldSize": "md",
    "fieldWidth": "100",
    "fieldTextFontSize": "",
    "fieldPaddingVertical": "6",
    "fieldPaddingHorizontal": "10",
    "fieldTextFont": "",
    "fieldTextFontVariant": "",
    "fieldBGColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "1",
    "fieldborderBottom": "1",
    "fieldborderLeft": "1",
    "fieldborderRight": "1",
    "fieldBorderRadius": "3",
    "btnText": "Log In",
    "btnTextFontSize": "15",
    "btnTextFont": "",
    "btnTextFontVariant": "",
    "btnTextColor": "",
    "btnSize": "1",
    "btnBgColor": "",
    "btnPaddingVertical": "8",
    "btnPaddingHorizontal": "12",
    "btnBorderRadius": "3",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "video":{
    "type":"youtube",
    "code": "",
    "youtubeUrl": "https://www.youtube.com/watch?v=MUXoqm3VpRo",
    "width": "100",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "shadow":"",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "videopopup":{
    "source": "youtube",
    "code": "",
    "youtubeUrl": "https://www.youtube.com/watch?v=MUXoqm3VpRo",
    "vimeoUrl": "https://vimeo.com/569325254",
    "externalUrl": false,
    "customVideoUrl": "",
    "videofileWidth": "",
    "videofileHeight": "",
    "videofileMimeType": "",
    "videofileTitle": "",
    "videofileLength": "",
    "videofileSize": "",
    "customVideoId": "",
    "startTime": "",
    "endTime": "",
    "autoplayVideo": false,
    "playOnMobile": false,
    "muteVideo": false,
    "loopVideo": false,
    "showPlayerControls": true,
    "showDownloadButton": false,
    "preloadVideo": "metadata",
    "modestBranding": false,
    "privacyMode": false,
    "lazyLoadVideo": false,
    "showRelatedVideos": "currentChannel",
    "posterImage": "",
    "showIntroTitle": true,
    "showIntroPortrait": true,
    "showIntroByLine": true,
    "controlsColor": "",
    "enableImageOverlay": false,
    "enableTeaserVideo": false,
    "imageOverlay": "",
    "overlayImageWidth": "100",
    "overlayImageHeight": "100",
    "overlayImageUnit": "%",
    "aspectRatio": "16 / 9",
    "showOverlayPlayIcon": true,
    "overlayPlayIcon": "fas fa-play",
    "teaserVideoPlayIcon": "fas fa-play",
    "iconOpacity": "5",
    "iconFontSize": "100",
    "teaserVideoIconFontSize": "80",
    "overlayPlayIconColor": "",
    "teaserVideoPlayIconColor": "#E53935",
    "hideTeaserVideoPlayIcon": false,
    "wrapperWidth": "100",
    "enableLightbox": false,
    "enableStickyVideo": false,
    "enableBanner": true,
    "bannerText": "Click to hear audio",
    "bannerBackgroundColor": "#E53935",
    "bannerTextColor": "#FFFFFF",
    "bannertextfont": "",
    "bannertextfontVariant": "",
    "bannertextfontSize": "",
    "bannertextfontSize_mobile": "",
    "bannertextfontSize_tablet": "",
    "bannertextlineHeight": "",
    "bannertextlineHeight_mobile": "",
    "bannertextlineHeight_tablet": "",
    "bannertextletterSpacing": "",
    "bannertextletterSpacing_mobile": "",
    "bannertextletterSpacing_tablet": "",
    "bannertexttypographyBold": "",
    "bannertexttypographyItalic": "",
    "bannertexttypographyUnderline": "",
    "bannertextalign": "center",
    "bannertexttypographyLetterCase": "",
    "bannerTextIcon": "fas fa-volume-up",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "shadow":"",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "countdown":{
    "countdownType":"",
    "vtHours":"0",
    "vtMinutes":"30",
    "vtSeconds":"0",
    "action":1,
    "thankyouTxt": "",
    "redirectUrl": "",
    "size":"md",
    "timezone": "' . seedprod_lite_get_default_timezone() . '",
    "endDate": "",
    "endTime": "",
    "endTimestamp": "",
    "bgColor": "",
    "labelColor": "",
    "dayTxt":"Days",
    "hourTxt":"Hours",
    "minuteTxt":"Minutes",
    "secondTxt":"Seconds",
    "spaceBetween":"20",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "typographyLetterCase": "uppercase",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "bulletlist":{
    "layout": "v",
    "spaceBetween":"10",
    "spaceBetween_mobile":"10",
    "spaceBetween_tablet":"10",
    "items": [{ "icon": "fas fa-check", "txt": "Item 1" }],
    "textColor": "",
    "iconColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "businesshours":{
    "rowSpacing":"6",
    "items": [{ "icon": "fas fa-check","txt": "Monday", "daytxt": "Monday" , "timetxt":"9:AM - 6:PM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Tuesday", "daytxt": "Tuesday" , "timetxt":"9:AM - 6:PM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Wednesday", "daytxt": "Wednesday" , "timetxt":"9:AM - 6:PM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Thursday", "daytxt": "Thursday" , "timetxt":"9:AM - 6:PM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Friday", "daytxt": "Friday" , "timetxt":"9:AM - 6:PM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Saturday", "daytxt": "Saturday" , "timetxt":"9:AM  - 11:AM" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Sunday", "daytxt": "Sunday" , "timetxt":"Closed" , "styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" }],
    "textColor": "",
    "timeColor":"",
    "iconColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "12",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "timealign": "center",
    "timefont": "",
    "timefontVariant": "",
    "timefontSize": "12",
    "timefontSize_mobile": "",
    "timefontSize_tablet": "",
    "timelineHeight": "",
    "timelineHeight_mobile": "",
    "timelineHeight_tablet": "",
    "timeletterSpacing": "",
    "timeletterSpacing_mobile": "",
    "timeletterSpacing_tablet": "",
    "timetypographyBold": "",
    "timetypographyItalic": "",
    "timetypographyUnderline": "",
    "timetypographyLetterCase": "",
    "divider":false,
    "dividerStyle":"solid",
    "dividerWeight":"1",
    "stripEffect":false,
    "stripOddColor":"#cccccc",
    "stripEvenColor":"#ffffff",
    "borderRadius": "1",
    "border": "",
    "borderStyle": "solid",
    "borderSize": "",
    "borderColor": "",
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "bgDimming":0,
    "bgDimmingOverlay":"",
    "bgCustomDimming":0,
    "bgCustomDimmingOverlay":"",
    "bgCustomXPositionUnit":"px",
    "bgCustomXPosition":"",
    "bgCustomYPositionUnit":"px",
    "bgCustomYPosition":"",
    "bgCustomAttachment":"",
    "bgCustomRepeat":"",
    "bgCustomSize":"",
    "bgCustomSizeWidthUnit":"%",
    "bgCustomSizeWidth":"100",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "teammembers":{
    "showSocialIcons":true,
    "socialiconshape":"",
    "socialiconalign":"center",
    "socialiconalign_mobile":"center",
    "socialiconalign_tablet":"center",
    "socialiconwidth":"15",
    "socialiconcolor":"",
    "socialborderRadiusSync":true,
    "socialborderRadiusTL":"",
    "socialborderRadiusTR":"",
    "socialborderRadiusBL":"",
    "socialborderRadiusBR":"",
    "imghiddenwidth": "",
    "imghiddenheight": "",
    "rowSpacing":"6",
    "items": [{ "icon": "fab fa-facebook-f","txt": "Facebook", "txtlink":"#", "bgcolor":"", "color":"" },
              { "icon": "fab fa-twitter","txt": "Twitter", "txtlink":"#", "bgcolor":"", "color":"" }
              ],
    "imagesrc": "",
    "imgaltTxt":"",
    "imgwidth": "100",
    "imgMarginBottom": "",
    "nameMarginBottom": "",
    "designationMarginBottom": "",
    "descriptionMarginBottom": "",
    "separatorMarginBottom": "",
    "height": "",
    "unit": "%",
    "imagePosition":"top",
    "membername": "John Doe",
    "showDesignation": true,
    "designation": "CEO",
    "showDescription": true,
    "description": "Enter description text here.",
    "showSeperator": true,
    "seperatorPosition": "belowname",
    "seperatorWidth": 100,
    "seperatorThickness": 1,
    "seperatorStyle":"solid",
    "seperatorColor": "#cccccc",
    "seperatoralign":"center",
    "seperatoralign_mobile":"center",
    "seperatoralign_tablet":"center",
    "textColor": "",
    "timeColor":"",
    "iconColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "17",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "left",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "designationalign": "center",
    "designationalign_mobile": "left",
    "designationalign_tablet": "center",
    "designationfont": "",
    "designationfontVariant": "",
    "designationfontSize": "12",
    "designationfontSize_mobile": "",
    "designationfontSize_tablet": "",
    "designationlineHeight": "",
    "designationlineHeight_mobile": "",
    "designationlineHeight_tablet": "",
    "designationletterSpacing": "",
    "designationletterSpacing_mobile": "",
    "designationletterSpacing_tablet": "",
    "designationtypographyBold": "",
    "designationtypographyItalic": "",
    "designationtypographyUnderline": "",
    "designationtypographyLetterCase": "",
    "descriptionalign": "center",
    "descriptionalign_mobile": "left",
    "descriptionalign_tablet": "center",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "12",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptiontypographyLetterCase": "",
    "imageshape":"rounded",
    "imageborderRadiusSync":true,
    "imageborderRadiusTL":"",
    "imageborderRadiusTR":"",
    "imageborderRadiusBL":"",
    "imageborderRadiusBR":"",
    "imageborderStyle":"solid",
    "imageborderWeight":"1",
    "imageborderColor": "",
    "tag":"h3",
    "divider":false,
    "dividerStyle":"solid",
    "dividerWeight":"1",
    "stripEffect":false,
    "stripOddColor":"#cccccc",
    "stripEvenColor":"#ffffff",
    "borderRadius": "1",
    "border": "",
    "borderStyle": "solid",
    "borderSize": "",
    "borderColor": "",
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "bgDimming":0,
    "bgDimmingOverlay":"",
    "bgCustomDimming":0,
    "bgCustomDimmingOverlay":"",
    "bgCustomXPositionUnit":"px",
    "bgCustomXPosition":"",
    "bgCustomYPositionUnit":"px",
    "bgCustomYPosition":"",
    "bgCustomAttachment":"",
    "bgCustomRepeat":"",
    "bgCustomSize":"",
    "bgCustomSizeWidthUnit":"%",
    "bgCustomSizeWidth":"100",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "pricelist":{
    "rowSpacing":"6",
    "items": [{ "icon": "fas fa-check","txt": "Item 1","desc": "Description",  "price":"$20" ,"discountprice":"$20" ,"pricelink":"#","img": "" ,"srcset": "", "discount":false, "styleday":false, "daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Item 2","desc": "Description",  "price":"$20" ,"discountprice":"$20" ,"pricelink":"#","img": "" ,"srcset": "", "discount":false,"styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" },
              { "icon": "fas fa-check","txt": "Item 3","desc": "Description", "price":"$20" ,"discountprice":"$20" ,"pricelink":"#","img": "" ,"srcset": "", "discount":false,"styleday":false,"daycolor":"#ccc","timecolor":"#ccc","bgcolor":"#fff" }],
    "textColor": "",
    "descColor":"",
    "priceColor":"",
    "discountColor":"",
    "imageshape":"",
    "imagewidth":"150",
    "imageposition":"left",
    "overallalign":"left",
    "verticalalign":"center",
    "overallalign_mobile":"center",
    "overallalign_tablet":"center",
    "anchorType":"div",
    "titlepriceseperator":"dotted",
    "verticalalign_mobile":"center",
    "verticalalign_tablet":"center",
    "priceposition":"rightofheading",
    "linkbox":false,
    "imageborderRadiusSync":true,
    "imageborderRadiusTL":"",
    "imageborderRadiusTR":"",
    "imageborderRadiusBL":"",
    "imageborderRadiusBR":"",
    "timeColor":"#",
    "iconColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "5",
    "paddingBottom": "5",
    "paddingLeft": "5",
    "paddingRight": "5",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "pricealign": "center",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "12",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricetypographyLetterCase": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "12",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "descalign": "center",
    "descfont": "",
    "descfontVariant": "",
    "descfontSize": "12",
    "descfontSize_mobile": "",
    "descfontSize_tablet": "",
    "desclineHeight": "",
    "desclineHeight_mobile": "",
    "desclineHeight_tablet": "",
    "descletterSpacing": "",
    "descletterSpacing_mobile": "",
    "descletterSpacing_tablet": "",
    "desctypographyBold": "",
    "desctypographyItalic": "",
    "desctypographyUnderline": "",
    "desctypographyLetterCase": "",
    "typographyLetterCase": "",
    "timealign": "center",
    "timefont": "",
    "timefontVariant": "",
    "timefontSize": "12",
    "timefontSize_mobile": "",
    "timefontSize_tablet": "",
    "timelineHeight": "",
    "timelineHeight_mobile": "",
    "timelineHeight_tablet": "",
    "timeletterSpacing": "",
    "timeletterSpacing_mobile": "",
    "timeletterSpacing_tablet": "",
    "timetypographyBold": "",
    "timetypographyItalic": "",
    "timetypographyUnderline": "",
    "timetypographyLetterCase": "",
    "divider":false,
    "dividerStyle":"solid",
    "dividerWeight":"1",
    "stripEffect":false,
    "stripOddColor":"#cccccc",
    "stripEvenColor":"#ffffff",
    "borderRadius": "1",
    "border": "",
    "borderStyle": "solid",
    "borderSize": "",
    "borderColor": "",
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "bgDimming":0,
    "bgDimmingOverlay":"",
    "bgCustomDimming":0,
    "bgCustomDimmingOverlay":"",
    "bgCustomXPositionUnit":"px",
    "bgCustomXPosition":"",
    "bgCustomYPositionUnit":"px",
    "bgCustomYPosition":"",
    "bgCustomAttachment":"",
    "bgCustomRepeat":"",
    "bgCustomSize":"",
    "bgCustomSizeWidthUnit":"%",
    "bgCustomSizeWidth":"100",
    "blockTemplateId":false,
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "tabbedlayout":{
    "layout": "h",
    "spaceBetween":"6",
    "items": [{ "icon": "fas fa-check", "txt": "Tab 1", "content": "Tab Content"  }],
    "textColor": "",
    "hideTabIcons":true,
    "tabColor":"",
    "tabBGColor":"",
    "iconColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "6",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "blockTemplateId":false,
    "headeralign":"left",
    "headerColor":"",
    "headerOpenColor":"",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "activetabColor":"#333333",
    "activetabBGColor":"",
    "contentpaddingTop": "",
    "contentpaddingBottom": "",
    "contentpaddingLeft": "",
    "contentpaddingRight": "",
    "contentpaddingSync": true,
    "tabpaddingTop": "",
    "tabpaddingBottom": "",
    "tabpaddingLeft": "",
    "tabpaddingRight": "",
    "tabpaddingSync": true,
    "tabBorderRadius":"4",
    "tabBorderStyle":"solid",
    "tabBorderColor":"",
    "tabBorderSync":"true",
    "tabBorderTop": "",
    "tabBorderRight": "",
    "tabBorderBottom": "",
    "tabBorderLeft": "",
    "textBGColor":"",
    "tabContentBorderRadius":"",
    "tabContentBorderStyle":"solid",
    "tabContentBorderColor":"",
    "tabContentBorderSync":"true",
    "tabContentBorderTop": "",
    "tabContentBorderRight": "",
    "tabContentBorderBottom": "",
    "tabContentBorderLeft": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "nav":{
    "layout": "h",
    "menutype":"s",
    "mobileMenu":false,
    "divider":"",
    "navmenu":"",
    "spaceBetween":"20",
    "items": [{ "url": "", "txt": "About","openNewWindow":"","noFollow":"" }],
    "textColor": "",
    "textHoverColor": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_tablet": "center",
    "align_mobile": "center",
    "typographyLetterCase": "",
    "menuBackgroundColor": "",
    "submenuTextColor": "",
    "submenuHoverColor": "",
    "submenuBorderRadius": "6",
    "submenuLineHeight": "",
    "submenupaddingTop": "4",
    "submenupaddingBottom": "4",
    "submenupaddingLeft": "4",
    "submenupaddingRight": "4",
    "submenupaddingSync": true,
    "submenuBorderWidth": "",
    "submenuBorderColor": "",
    "subnavshadow": "6",
    "subnavshadowColor":"",
    "subnavshadowHorizontal":0,
    "subnavshadowVertical":0,
    "subnavshadowBlur":3,
    "subnavshadowSpread":0,
    "subnavshadowPosition":"outline",
    "subnavColor": "",
    "subnavHoverColor": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "form":{
    "items": []
  },
  "optinform":{
    "align": "center",
    "items": [
      { "type": "email", "label":"Enter Your Email","txt":"Email","width":"100","required": true,"display": true},
      { "type": "name", "label":"Enter Your Name","txt":"Name","width":"100","required": false, "display": true},
      { "type": "optin_confirm", "label":"Optin Confirmation","txt":"Optin Confirmation","width":"100","required": true,"display": false}
    ],
    "action":"1",
    "redirectUrl":"",
    "thankyouTxt":"Thank You, we\'ll be in touch soon.",
    "btnSize":"3",
    "inputSize":"",
    "bgColor":"",
    "btnWidth":"100",
    "btnTxt":"Submit",
    "btnSubTxt":"",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "inputPadding": "8",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "1",
    "fieldborderBottom": "1",
    "fieldborderLeft": "1",
    "fieldborderRight": "1",
    "fieldborderSync": true,
    "buttonFontSize": "",
    "beforeIcon":"",
    "afterIcon":"",
    "shadow": "",
    "borderRadius": "3",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "typographyLetterCase": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "gap": false,
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "progressbar":{
    "txt": "My Text",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "width": "60",
    "color": "",
    "outerColor": "#F7F7F7",
    "borderRadius": "3",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "typographyLetterCase": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "socialprofiles":{
    "iconStyle":"1",
    "iconPadding": 15,
    "iconSize": 24,
    "iconColor": "",
    "size": "md",
    "items": [{ "type": "facebook", "label":"","url":"","icon":"", "color":"" },{ "type": "twitter", "label":"","url":"","icon":"", "color":"" },{ "type": "instagram", "label":"","url":"","icon":"", "color":"" },{ "type": "youtube", "label":"","url":"","icon":"", "color":"" }],
    "spaceBetween": "10",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "socialsharing":{
    "size": "sm",
    "items": [{ "type": "facebook", "label":"", "meta":"", "url":"" },{ "type": "twitter", "label":"", "meta":"", "url":"" }],
    "spaceBetween": "20",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "pricingtable":{
    "planName": "Enter Your Plan Name",
    "price": "100",
    "currencySymbol": "$",
    "customCurrencySymbol": "",
    "currencyPosition": "before",
    "showRegularPrice": true,
    "regularPrice": "150",
    "regularPriceLabel": "Normally",
    "period": "/month",
    "description": "Perfect For Beginners",
    "showTopButton": true,
    "topButtonText": "Call To Action",
    "topButtonLink": "",
    "topButtonLinkOpenNewWindow": false,
    "topButtonLinkNoFollow": false,
    "topButtonStyle": "flat",
    "topButtonBgColor": "",
    "topButtonShadow": "",
    "topButtonLineHeight": "",
    "topbuttonfont": "",
    "topbuttonfontVariant": "",
    "topbuttonfontSize": "16",
    "topbuttonfontSize_mobile": "",
    "topbuttonfontSize_tablet": "",
    "topbuttonlineHeight": "",
    "topbuttonlineHeight_mobile": "",
    "topbuttonlineHeight_tablet": "",
    "topbuttonletterSpacing": "",
    "topbuttonletterSpacing_mobile": "",
    "topbuttonletterSpacing_tablet": "",
    "topbuttontypographyBold": "",
    "topbuttontypographyItalic": "",
    "topbuttontypographyUnderline": "",
    "topbuttontypographyLetterCase": "",
    "topbuttonalign": "center",
    "topButtonSize":"3",
    "topButtonPaddingTop": "15",
    "topButtonPaddingLeft": "35",
    "topButtonBorderRadius": "",
    "topButtonBeforeIcon": "",
    "topButtonAfterIcon": "",
    "featuresHeader": "",
    "featuresList": [{ "icon": "fas fa-check", "txt": "Features List Item #1" }, { "icon": "fas fa-check", "txt": "Features List Item #2" }],
    "featuresListLayout": "v",
    "featuresListSpaceBetween":"10",
    "featuresListIconColor":"10",
    "featureslistalign": "center",
    "featureslistfontSize": "16",
    "featureslisttypographyBold": "",
    "featureslisttypographyItalic": "",
    "featureslisttypographyUnderline": "",
    "featureslisttypographyLetterCase": "",
    "featureslistfontVariant": "",
    "featureslistfontSize_mobile": "",
    "featureslistfontSize_tablet": "",
    "featureslistlineHeight": "1.5",
    "featureslistlineHeight_mobile": "",
    "featureslistlineHeight_tablet": "",
    "featureslistletterSpacing": "",
    "featureslistletterSpacing_mobile": "",
    "featureslistletterSpacing_tablet": "",
    "featuresListTextColor": "",
    "featuresListBackgroundColor": "#ffffff",
    "featuresListPadding": "10",
    "showBottomButton": true,
    "bottomButtonText": "Call To Action",
    "bottomButtonLink": "",
    "bottomButtonLinkOpenNewWindow": false,
    "bottomButtonLinkNoFollow": false,
    "bottomButtonStyle": "flat",
    "bottomButtonBgColor": "",
    "bottomButtonShadow": "",
    "bottomButtonLineHeight": "",
    "bottombuttonfont": "",
    "bottombuttonfontVariant": "",
    "bottombuttonfontSize": "16",
    "bottombuttonfontSize_mobile": "",
    "bottombuttonfontSize_tablet": "",
    "bottombuttonlineHeight": "3",
    "bottombuttonlineHeight_mobile": "",
    "bottombuttonlineHeight_tablet": "",
    "bottombuttonletterSpacing": "",
    "bottombuttonletterSpacing_mobile": "",
    "bottombuttonletterSpacing_tablet": "",
    "bottombuttontypographyBold": "",
    "bottombuttontypographyItalic": "",
    "bottombuttontypographyUnderline": "",
    "bottombuttontypographyLetterCase": "",
    "bottombuttonalign": "center",
    "bottomButtonSize":"3",
    "bottomButtonPaddingTop": "15",
    "bottomButtonPaddingLeft": "35",
    "bottomButtonBorderRadius": "",
    "bottomButtonBeforeIcon": "",
    "bottomButtonAfterIcon": "",
    "headerBackgroundColor": "#f3f3f3",
    "headerPadding": "20",
    "plannamealign": "center",
    "plannamefont": "",
    "plannamefontVariant": "",
    "plannamefontSize": "22",
    "plannamefontSize_mobile": "",
    "plannamefontSize_tablet": "",
    "plannamelineHeight": "",
    "plannamelineHeight_mobile": "",
    "plannamelineHeight_tablet": "",
    "plannameletterSpacing": "",
    "plannameletterSpacing_mobile": "",
    "plannameletterSpacing_tablet": "",
    "plannametypographyBold": "",
    "plannametypographyItalic": "",
    "plannametypographyUnderline": "",
    "plannametypographyLetterCase": "",
    "planNameTextColor": "",
    "regularpricealign": "center",
    "regularpricefont": "",
    "regularpricefontVariant": "",
    "regularpricefontSize": "16",
    "regularpricefontSize_mobile": "",
    "regularpricefontSize_tablet": "",
    "regularpricelineHeight": "",
    "regularpricelineHeight_mobile": "",
    "regularpricelineHeight_tablet": "",
    "regularpriceletterSpacing": "",
    "regularpriceletterSpacing_mobile": "",
    "regularpriceletterSpacing_tablet": "",
    "regularpricetypographyBold": "",
    "regularpricetypographyItalic": "",
    "regularpricetypographyUnderline": "",
    "regularpricetypographyLetterCase": "",
    "regularPriceTextColor": "#9E9E9E",
    "descriptionalign": "center",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "16",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptiontypographyLetterCase": "",
    "descriptionTextColor": "",
    "pricealign": "center",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "70",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": true,
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricetypographyLetterCase": "",
    "priceTextColor": "",
    "pricesuperscriptalign": "center",
    "pricesuperscriptfont": "",
    "pricesuperscriptfontVariant": "",
    "pricesuperscriptfontSize": "14",
    "pricesuperscriptfontSize_mobile": "",
    "pricesuperscriptfontSize_tablet": "",
    "pricesuperscriptlineHeight": "",
    "pricesuperscriptlineHeight_mobile": "",
    "pricesuperscriptlineHeight_tablet": "",
    "pricesuperscriptletterSpacing": "",
    "pricesuperscriptletterSpacing_mobile": "",
    "pricesuperscriptletterSpacing_tablet": "",
    "pricesuperscripttypographyBold": "",
    "pricesuperscripttypographyItalic": "",
    "pricesuperscripttypographyUnderline": "",
    "pricesuperscripttypographyLetterCase": "",
    "priceSuperScriptTextColor": "",
    "priceSuperScriptTop": "-2.5",
    "priceSuperScriptTop_tablet": "",
    "priceSuperScriptTop_mobile": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "4",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "blockBorderRadius": "",
    "blockBorderTop": "1",
    "blockBorderBottom": "1",
    "blockBorderLeft": "1",
    "blockBorderRight": "1",
    "blockBorderSync": true,
    "blockBorderStyle":"solid",
    "blockBorderColor": "#f3f3f3",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wcaddtocart":{
    "productId": "",
    "directToCheckout": false,
    "btnTxt": "Add To Cart",
    "btnStyle": "",
    "btnSubTxt": "",
    "btnSubTextSize": "",
    "textColor": "#ffffff",
    "bgColor": "",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "btnSize":"4",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "22",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wccart":{
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "headerColor": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "align": "",
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "btnShadow": "",
    "btnLineHeight": "",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "1",
    "fieldborderRight": "1",
    "fieldborderBottom": "1",
    "fieldborderLeft": "1",
    "fieldborderSync": "true",
    "alertInfoHlColor": "",
    "alertErrorHlColor": "",
    "alertSuccessHlColor": "",
    "cartFieldBorderColor": "",
    "cartborderTop": "1",
    "cartborderRight": "1",
    "cartborderBottom": "1",
    "cartborderLeft": "1",
    "cartborderSync": "true",
    "cartBorderRadius": "",
    "cartBgColor": "",
    "cartTextColor": "",
    "cartFont": "",
    "cartFontVariant": "",
    "cartHeaderBgColor": "",
    "cartHeaderFont": "",
    "cartHeaderFontVariant": "",
    "cartHeaderColor": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wccheckout":{
    "css": "",
    "layoutColumns": "2",
    "headerColor": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "align": "",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "1",
    "fieldborderRight": "1",
    "fieldborderBottom": "1",
    "fieldborderLeft": "1",
    "fieldborderSync": "true",
    "borderRadius": "",
    "labelColor": "",
    "labelfont": "",
    "labelfontVariant": "",
    "labelfontSize": "",
    "labelfontSize_mobile": "",
    "labelfontSize_tablet": "",
    "labellineHeight": "",
    "labellineHeight_mobile": "",
    "labellineHeight_tablet": "",
    "labelletterSpacing": "",
    "labelletterSpacing_mobile": "",
    "labelletterSpacing_tablet": "",
    "labeltypographyBold": "",
    "labeltypographyItalic": "",
    "labeltypographyUnderline": "",
    "labeltypographyLetterCase": "",
    "labelalign": "",
    "rowSpacing": "6",
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "btnShadow": "",
    "btnLineHeight": "",
    "alertInfoHlColor": "",
    "alertErrorHlColor": "",
    "alertSuccessHlColor": "",
    "cartFieldBorderColor": "",
    "cartborderTop": "1",
    "cartborderRight": "1",
    "cartborderBottom": "1",
    "cartborderLeft": "1",
    "cartborderSync": "true",
    "cartBorderRadius": "",
    "cartBgColor": "",
    "cartTextColor": "",
    "cartFont": "",
    "cartFontVariant": "",
    "cartHeaderBgColor": "",
    "cartHeaderFont": "",
    "cartHeaderFontVariant": "",
    "cartHeaderColor": "",
    "paymentBgColor": "",
    "paymentTextColor": "#333",
    "paymentFont": "",
    "paymentFontVariant": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wccustomproductsgrid":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "all_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor":"",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "3",
    "imageBorderStyle": "solid",
    "imageBorderColor": "#ccc",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "archiveproducts":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "all_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor":"",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "3",
    "imageBorderStyle": "solid",
    "imageBorderColor": "#ccc",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wcfeaturedproductsgrid":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "featured_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wcsaleproductsgrid":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "sale_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "saleColor": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wcbestsellingproductsgrid":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "best_selling_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wcrecentproductsgrid":{
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "recent_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wctopratedproductsgrid":{
    "entranceAnimation":"",
    "columns": "4",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "top_rated_products",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "wpwidgetblock":{
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "fontVariant": "",
    "titleBottomMargin": "",
    "entranceAnimation":"",
    "widgetdataoptions": "",
    "widgetresult": "",
    "widgetdata": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "columns": "4",
    "titleColor":"",
    "titleBottomMargin":"16",
    "contentColor":"",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "contentalign": "",
    "contentfont": "",
    "contentfontVariant": "",
    "contentfontSize": "",
    "contentfontSize_mobile": "",
    "contentfontSize_tablet": "",
    "contentlineHeight": "",
    "contentlineHeight_mobile": "",
    "contentlineHeight_tablet": "",
    "contentletterSpacing": "",
    "contentletterSpacing_mobile": "",
    "contentletterSpacing_tablet": "",
    "contenttypographyBold": "",
    "contenttypographyItalic": "",
    "contenttypographyUnderline": "",
    "contenttypographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "posts":{
    "skin": "classic",
    "skinlayout":"gridlayout",
    "slidetoshow":"2",
    "imageposition":"left",
    "autoplay":true,
    "speed":"100",
    "badge": true,
    "carouselalign": "center",
    "navlayout":"bottom",
    "navigation":"both",
    "carouselColor":"",
    "featuredimgheight":"",
    "badgetaxonomy": "category",
    "avatar": true,
    "masonary": false,
    "imagePosition":"left",
    "columns": "1",
    "pagination": false,
    "queryType": "custom",
    "queryByPostType": true,
    "queryPostType": [],
    "queryByCategory": false,
    "queryCategory": [],
    "queryByTags": false,
    "queryTags": [],
    "queryByAuthors": false,
    "queryAuthors": [],
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "manualQuery": "post_type=post&posts_per_page=3&order=desc&post_status=publish",
    "numberPerPages": 10,
    "showFeaturedImage": false,
    "showTitle": true,
    "titleHtmlTag": "h4",
    "showMetaOptions": false,
    "showDateModifiedMeta": true,
    "showAuthorMeta": true,
    "showDateMeta": true,
    "showTimeMeta": true,
    "showCommentCountMeta": true,
    "metaSeparator": ",",
    "showExcerpt": true,
    "excerptLength": 20,
    "showReadMore": true,
    "readMoreText": "Continue Reading →",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "contentBorderRadius": "",
    "contentBorderTop": "",
    "contentBorderBottom": "",
    "contentBorderLeft": "",
    "contentBorderRight": "",
    "contentBorderSync": true,
    "contentBorderStyle":"solid",
    "contentBorderColor": "",
    "contentpaddingTop": "",
    "contentpaddingBottom": "",
    "contentpaddingLeft": "",
    "contentpaddingRight": "",
    "contentpaddingSync": true,
    "contentBackgroundColor": "",
    "contentshadow": "",
    "contentshadowColor":"",
    "contentshadowHorizontal":0,
    "contentshadowVertical":0,
    "contentshadowBlur":3,
    "contentshadowSpread":0,
    "contentshadowPosition":"outline",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "blockBorderRadius": "",
    "blockBorderTop": "",
    "blockBorderBottom": "",
    "blockBorderLeft": "",
    "blockBorderRight": "",
    "blockBorderSync": true,
    "blockBorderStyle":"solid",
    "blockBorderColor": "",
    "imageShadow": "",
    "radiusunit":"px",
    "borderRadiusSync":true,
    "imageBorderRadiusSync": true,
    "imageBorderRadiusTopRight": "",
    "imageBorderRadiusTopLeft": "",
    "imageBorderRadiusBottomRight": "",
    "imageBorderRadiusBottomLeft": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "",
    "imageBorderBottom": "",
    "imageBorderLeft": "",
    "imageBorderRight": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "imageBorderSync": true,
    "imagePadding": "",
    "titlealign": "left",
    "titlealign_mobile": "center",
    "titlealign_tablet": "center",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": true,
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titletypographyLetterCase": "",
    "titleTextColor": "",
    "metatextalign": "left",
    "metatextalign_mobile": "left",
    "metatextalign_tablet": "left",
    "metatextfont": "",
    "metatextfontVariant": "",
    "metatextfontSize": "",
    "metatextfontSize_mobile": "",
    "metatextfontSize_tablet": "",
    "metatextlineHeight": "",
    "metatextlineHeight_mobile": "",
    "metatextlineHeight_tablet": "",
    "metatextletterSpacing": "",
    "metatextletterSpacing_mobile": "",
    "metatextletterSpacing_tablet": "",
    "metatexttypographyBold": "",
    "metatexttypographyItalic": "",
    "metatexttypographyUnderline": "",
    "metatexttypographyLetterCase": "",
    "metatextTextColor": "",
    "excerptalign": "left",
    "excerptalign_mobile": "left",
    "excerptalign_tablet": "left",
    "excerptfont": "",
    "excerptfontVariant": "",
    "excerptfontSize": "",
    "excerptfontSize_mobile": "",
    "excerptfontSize_tablet": "",
    "excerptlineHeight": "",
    "excerptlineHeight_mobile": "",
    "excerptlineHeight_tablet": "",
    "excerptletterSpacing": "",
    "excerptletterSpacing_mobile": "",
    "excerptletterSpacing_tablet": "",
    "excerpttypographyBold": "",
    "excerpttypographyItalic": "",
    "excerpttypographyUnderline": "",
    "excerpttypographyLetterCase": "",
    "excerptTextColor": "",
    "readmoretextalign": "left",
    "readmoretextalign_mobile": "left",
    "readmoretextalign_tablet": "left",
    "readmoretextfont": "",
    "readmoretextfontVariant": "",
    "readmoretextfontSize": "",
    "readmoretextfontSize_mobile": "",
    "readmoretextfontSize_tablet": "",
    "readmoretextlineHeight": "",
    "readmoretextlineHeight_mobile": "",
    "readmoretextlineHeight_tablet": "",
    "readmoretextletterSpacing": "",
    "readmoretextletterSpacing_mobile": "",
    "readmoretextletterSpacing_tablet": "",
    "readmoretexttypographyBold": "",
    "readmoretexttypographyItalic": "",
    "readmoretexttypographyUnderline": "",
    "readmoretexttypographyLetterCase": "",
    "readmoreTextColor": "",
    "paginationalign": "left",
    "paginationalign_mobile": "left",
    "paginationalign_tablet": "left",
    "paginationfont": "",
    "paginationfontVariant": "",
    "paginationfontSize": "",
    "paginationfontSize_mobile": "",
    "paginationfontSize_tablet": "",
    "paginationlineHeight": "",
    "paginationlineHeight_mobile": "",
    "paginationlineHeight_tablet": "",
    "paginationletterSpacing": "",
    "paginationletterSpacing_mobile": "",
    "paginationletterSpacing_tablet": "",
    "paginationtypographyBold": "",
    "paginationtypographyItalic": "",
    "paginationtypographyUnderline": "",
    "paginationtypographyLetterCase": "",
    "paginationTextColor": "",
    "entranceAnimation":"",
    "taxonomyfont": "",
    "taxonomyfont_mobile": "",
    "taxonomyfont_tablet": "",
    "taxonomyfontVariant": "",
    "taxonomyfontSize": "",
    "taxonomyfontSize_mobile": "",
    "taxonomyfontSize_tablet": "",
    "taxonomylineHeight": "",
    "taxonomylineHeight_mobile": "",
    "taxonomylineHeight_tablet": "",
    "taxonomyletterSpacing": "",
    "taxonomyletterSpacing_mobile": "",
    "taxonomyletterSpacing_tablet": "",
    "taxonomytypographyBold": "",
    "taxonomytypographyItalic": "",
    "taxonomytypographyUnderline": "",
    "taxonomytypographyLetterCase": "",
    "taxonomyTextColor": "",
    "taxonomypaddingTop": "",
    "taxonomypaddingBottom": "",
    "taxonomypaddingLeft": "",
    "taxonomypaddingRight": "",
    "taxonomypaddingSync": true,
    "taxonomyBottomSpacing": "",
    "taxBorderRadiusTop": "",
    "taxBorderRadiusBottom": "",
    "taxBorderRadiusLeft": "",
    "taxBorderRadiusRight": "",
    "taxBorderRadiusSync": true,
    "taxBgColor": "",
    "taxColor": "",
    "postpaddingTop": "",
    "postpaddingBottom": "",
    "postpaddingLeft": "",
    "postpaddingRight": "",
    "postpaddingSync": true,
    "postBackgroundColor": "",
    "dividerBorderColor": "",
    "spaceBottom": "",
    "postshadow": "",
    "postshadowColor":"",
    "postshadowHorizontal":0,
    "postshadowVertical":0,
    "postshadowBlur":3,
    "postshadowSpread":0,
    "postshadowPosition":"outline",
    "imageMarginTop": "",
    "imageMarginBottom": "",
    "imageMarginLeft": "",
    "imageMarginRight": "",
    "imageMarginSync": true,
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "posttitle":{
    "headerTxt": "[seedprod tag=\"the_title\"]",
    "tag": "h1",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "archivetitle":{
    "headerTxt": "[seedprod tag=\"the_archive_title\"]",
    "tag": "h1",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "postcontent":{
    "entranceAnimation":"",
    "editortxt":"code",
    "txt": "[seedprod tag=\"the_content\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": ""
  },
  "postexcerpt":{
    "entranceAnimation":"",
    "txt": "[seedprod tag=\"the_excerpt\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": ""
  },
  "postfeaturedimage":{
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "unit":"px",
    "src": "",
    "altTxt": "",
    "link": "",
    "width": "",
    "height": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "imagePadding": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "postauthorbox":{
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "profilePictureToggle": true,
    "displayNameToggle": true,
    "biographyToggle": true,
    "htmlTag": "div",
    "link": "none",
    "namefont": "",
    "namefontVariant": "",
    "namefontSize": "",
    "namefontSize_mobile": "",
    "namefontSize_tablet": "",
    "namelineHeight": "",
    "namelineHeight_mobile": "",
    "namelineHeight_tablet": "",
    "nameletterSpacing": "",
    "nameletterSpacing_mobile": "",
    "nameletterSpacing_tablet": "",
    "nametypographyBold": "",
    "nametypographyItalic": "",
    "nametypographyUnderline": "",
    "namealign": "",
    "nametypographyLetterCase": "",
    "nameColor": "",
    "biographyfont": "",
    "biographyfontVariant": "",
    "biographyfontSize": "",
    "biographyfontSize_mobile": "",
    "biographyfontSize_tablet": "",
    "biographylineHeight": "",
    "biographylineHeight_mobile": "",
    "biographylineHeight_tablet": "",
    "biographyletterSpacing": "",
    "biographyletterSpacing_mobile": "",
    "biographyletterSpacing_tablet": "",
    "biographytypographyBold": "",
    "biographytypographyItalic": "",
    "biographytypographyUnderline": "",
    "biographyalign": "",
    "biographytypographyLetterCase": "",
    "biographyColor": "",
    "backgroundColor": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "borderStyle":"solid",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "authorbio":{
    "entranceAnimation":"",
    "editortxt":"code",
    "txt": "[seedprod tag=\"the_author_meta(description)\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "postcomments": {
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "contentPolicy": "",
    "txt": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "borderStyle":"solid",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "postnavigation":{
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "navigationColor": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "postinfo": {
    "items": [{ "type": "author", "avatar": true, "linktoauthor": true, "avatarSize": "24", "date_format": "default", "custom_date_format": "F j, Y", "time_format": "default", "custom_time_format": "g:i a", "terms_taxonomy" : "", "iconType": "default", "icon": "" }, { "type": "date", "avatar": false, "avatarSize": "20", "date_format": "default", "custom_date_format": "F j, Y", "time_format": "default", "custom_time_format": "g:i a", "terms_taxonomy" : "", "iconType": "default", "icon": "" }, { "type": "time", "avatar": false, "avatarSize": "20", "date_format": "default", "custom_date_format": "F j, Y", "time_format": "default", "custom_time_format": "g:i a", "terms_taxonomy" : "", "iconType": "default", "icon": "" }, { "type": "comments", "avatar": false, "avatarSize": "20", "date_format": "default", "custom_date_format": "F j, Y", "time_format": "default", "custom_time_format": "g:i a", "terms_taxonomy" : "", "iconType": "default", "icon": "" }],
    "spaceBetween": "8",
    "alignment":"left",
    "divider": "",
    "textColor": "",
    "textfont": "",
    "textfontVariant": "",
    "textfontSize": "",
    "textfontSize_mobile": "",
    "textfontSize_tablet": "",
    "textlineHeight": "",
    "textlineHeight_mobile": "",
    "textlineHeight_tablet": "",
    "textletterSpacing": "",
    "textletterSpacing_mobile": "",
    "textletterSpacing_tablet": "",
    "texttypographyBold": "",
    "texttypographyItalic": "",
    "texttypographyUnderline": "",
    "texttypographyLetterCase": "",
    "iconColor": "",
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "borderStyle":"solid",
    "showIcons": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "menucart": {
    "customClass": "",
    "customAttributes": "",
    "hideOnEmpty": false,
    "showSubtotal": true,
    "align":"left",
    "align_mobile": "left",
    "align_tablet": "left",
    "badgeColor": "#df2a4a",
    "badgeTextColor": "#ffffff",
    "textColor": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "producttitle":{
    "headerTxt": "[seedprod_wc tag=\"the_title\"]",
    "tag": "h1",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productfeaturedimage":{
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "unit":"px",
    "src": "",
    "altTxt": "",
    "link": "",
    "width": "",
    "height": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "imagePadding": "",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "imageBorderSync": true,
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productprice":{
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "textColor": "",
    "saleTextColor": "",
    "salefont": "",
    "salefontVariant": "",
    "salefontSize": "",
    "salefontSize_mobile": "",
    "salefontSize_tablet": "",
    "salelineHeight": "",
    "salelineHeight_mobile": "",
    "salelineHeight_tablet": "",
    "saleletterSpacing": "",
    "saleletterSpacing_mobile": "",
    "saleletterSpacing_tablet": "",
    "saletypographyBold": "",
    "saletypographyItalic": "",
    "saletypographyUnderline": "",
    "saletypographyLetterCase": "",
    "stacked": false,
    "spaceBetween": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "customClass": "",
    "customAttributes": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "addtocart":{
    "action": "link",
    "btnTxt": "Add To Cart",
    "btnStyle": "",
    "textColor": "",
    "bgColor": "",
    "buttonTextColor":"",
    "bgColorHover":"",
    "buttonTextHoverColor":"",
    "buttonNormalStyle":"s",
    "buttonNormalBGtype":"radial",
    "buttonNormalBGposition":"",
    "buttonHoverStyle":"s",
    "buttonHoverBGtype":"radial",
    "buttonHoverBGposition":"",
    "btnStyleHover":"",
    "btnPaddingTop": "8",
    "btnPaddingLeft": "12",
    "btnSize":"2",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "4",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "15",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "",
    "align_mobile": "",
    "typographyLetterCase": "",
    "quantityfont": "",
    "quantityfontVariant": "",
    "quantityfontSize": "22",
    "quantityfontSize_mobile": "",
    "quantityfontSize_tablet": "",
    "quantitylineHeight": "",
    "quantitylineHeight_mobile": "",
    "quantitylineHeight_tablet": "",
    "quantityletterSpacing": "",
    "quantityletterSpacing_mobile": "",
    "quantityletterSpacing_tablet": "",
    "quantitytypographyBold": "",
    "quantitytypographyItalic": "",
    "quantitytypographyUnderline": "",
    "quantityalign": "",
    "quantitytypographyLetterCase": "",
    "spaceBetween": "10",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productmeta":{
    "metaView": "inline",
    "spaceBetween": "",
    "alignment":"left",
    "divider": ",",
    "textColor": "",
    "textfont": "",
    "textfontVariant": "",
    "textfontSize": "",
    "textfontSize_mobile": "",
    "textfontSize_tablet": "",
    "textlineHeight": "",
    "textlineHeight_tablet": "",
    "textlineHeight_mobile": "",
    "textletterSpacing": "",
    "textletterSpacing_tablet": "",
    "textletterSpacing_mobile": "",
    "texttypographyBold": "",
    "texttypographyItalic": "",
    "texttypographyUnderline": "",
    "texttypographyLetterCase": "",
    "linkColor": "",
    "linkfont": "",
    "linkfontVariant": "",
    "linkfontSize": "",
    "linkfontSize_mobile": "",
    "linkfontSize_tablet": "",
    "linklineHeight": "",
    "linklineHeight_mobile": "",
    "linklineHeight_tablet": "",
    "linkletterSpacing": "",
    "linkletterSpacing_mobile": "",
    "linkletterSpacing_tablet": "",
    "linktypographyBold": "",
    "linktypographyItalic": "",
    "linktypographyUnderline": "",
    "linktypographyLetterCase": "",
    "dividerColor": "",
    "dividerfont": "",
    "dividerfontVariant": "",
    "dividerfontSize": "",
    "dividerfontSize_mobile": "",
    "dividerfontSize_tablet": "",
    "dividerlineHeight": "",
    "dividerlineHeight_mobile": "",
    "dividerlineHeight_tablet": "",
    "dividerletterSpacing": "",
    "dividerletterSpacing_mobile": "",
    "dividerletterSpacing_tablet": "",
    "dividertypographyBold": "",
    "dividertypographyItalic": "",
    "dividertypographyUnderline": "",
    "dividertypographyLetterCase": "",
    "bgStyle": "s",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "bgColor":"",
    "bgImage":"",
    "bgPosition":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync":true,
    "borderTop": "0",
    "borderBottom": "0",
    "borderLeft": "0",
    "borderRight": "0",
    "borderSync": true,
    "borderStyle":"solid",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productcontent":{
    "editortxt":"code",
    "txt": "[seedprod_wc tag=\"the_content\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "shortdescription":{
    "editortxt":"code",
    "txt": "[seedprod_wc tag=\"short_description\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productdatatabs":{
    "txt":"[sp_product_data_tabs]",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "bgColor": "",
    "bgImage": "",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "showNormalSettings": false,
    "showActiveSettings": false,
    "tabsNormalTextColor": "",
    "tabsActiveTextColor": "",
    "tabsNormalBackgroundColor": "",
    "tabsActiveBackgroundColor": "",
    "tabsNormalBorderColor": "",
    "tabsActiveBorderColor": "",
    "tabalign": "left",
    "tabfont": "",
    "tabfontVariant": "",
    "tabfontSize": "",
    "tabfontSize_mobile": "",
    "tabfontSize_tablet": "",
    "tablineHeight": "",
    "tablineHeight_mobile": "",
    "tablineHeight_tablet": "",
    "tabletterSpacing": "",
    "tabletterSpacing_mobile": "",
    "tabletterSpacing_tablet": "",
    "tabtypographyBold": "",
    "tabtypographyItalic": "",
    "tabtypographyUnderline": "",
    "tabtypographyLetterCase": "",
    "tabsBorderRadius": "",
    "panelTextColor": "",
    "panelfont": "",
    "panelfontVariant": "",
    "panelfontSize": "",
    "panelfontSize_mobile": "",
    "panelfontSize_tablet": "",
    "panellineHeight": "",
    "panellineHeight_mobile": "",
    "panellineHeight_tablet": "",
    "panelletterSpacing": "",
    "panelletterSpacing_mobile": "",
    "panelletterSpacing_tablet": "",
    "paneltypographyBold": "",
    "paneltypographyItalic": "",
    "paneltypographyUnderline": "",
    "paneltypographyLetterCase": "",
    "panelHeadingTextColor": "",
    "panelheadingfont": "",
    "panelheadingfontVariant": "",
    "panelheadingfontSize": "",
    "panelheadingfontSize_mobile": "",
    "panelheadingfontSize_tablet": "",
    "panelheadinglineHeight": "",
    "panelheadinglineHeight_mobile": "",
    "panelheadinglineHeight_tablet": "",
    "panelheadingletterSpacing": "",
    "panelheadingletterSpacing_mobile": "",
    "panelheadingletterSpacing_tablet": "",
    "panelheadingtypographyBold": "",
    "panelheadingtypographyItalic": "",
    "panelheadingtypographyUnderline": "",
    "panelheadingtypographyLetterCase": "",
    "panelborderRadius": "",
    "panelborderRadiusTL": "",
    "panelborderRadiusTR": "",
    "panelborderRadiusBL": "",
    "panelborderRadiusBR": "",
    "panelborderRadiusSync":true,
    "panelborderTop": "",
    "panelborderBottom": "",
    "panelborderLeft": "",
    "panelborderRight": "",
    "panelborderSync": true,
    "panelborderStyle":"solid",
    "panelborder": "",
    "panelborderStyle": "",
    "panelborderSize": "",
    "panelborderColor": "",
    "panelshadow": "",
    "panelshadowColor":"",
    "panelshadowHorizontal":0,
    "panelshadowVertical":0,
    "panelshadowBlur":3,
    "panelshadowSpread":0,
    "panelshadowPosition":"outline",
    "panelpaddingTop": "",
    "panelpaddingBottom": "",
    "panelpaddingLeft": "",
    "panelpaddingRight": "",
    "entranceAnimation":"",
    "panelpaddingSync": true,
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productgalleryimages":{
    "txt":"[sp_product_gallery_images]",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "bgColor": "",
    "bgImage": "",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "showNormalSettings": false,
    "showActiveSettings": false,
    "thumbnailsborderRadius": "",
    "thumbnailsborderRadiusTL": "",
    "thumbnailsborderRadiusTR": "",
    "thumbnailsborderRadiusBL": "",
    "thumbnailsborderRadiusBR": "",
    "thumbnailsborderRadiusSync":true,
    "thumbnailsborderTop": "0",
    "thumbnailsborderBottom": "0",
    "thumbnailsborderLeft": "0",
    "thumbnailsborderRight": "0",
    "thumbnailsborderSync": true,
    "thumbnailsborderStyle": "solid",
    "thumbnailsborder": "",
    "thumbnailsborderStyle": "",
    "thumbnailsborderSize": "",
    "thumbnailsborderColor": "",
    "imageborderRadius": "",
    "imageborderRadiusTL": "",
    "imageborderRadiusTR": "",
    "imageborderRadiusBL": "",
    "imageborderRadiusBR": "",
    "imageborderRadiusSync":true,
    "imageborderTop": "0",
    "imageborderBottom": "0",
    "imageborderLeft": "0",
    "imageborderRight": "0",
    "imageborderSync": true,
    "imageborderStyle": "solid",
    "imageborder": "",
    "imageborderStyle": "",
    "imageborderSize": "",
    "imageborderColor": "",
    "zoomButtonPositionRight": "1",
    "zoomButtonPositionTop": "1",
    "zoomButtonFontSize": "16",
    "zoomButtonColor": "",
    "zoomButtonBackgroundColor": "",
    "zoomButtonBorderRadius": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "additionalinformation":{
    "bgColor": "",
    "bgImage": "",
    "bgGradient":{ "type": "linear","position": "center", "angle": 0,"color1":"","color1location":0,"color2":"","color2location":100 },
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "headeralign": "",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "headerTextColor": "",
    "font":"",
    "fontVariant":"",
    "fontSize":"",
    "fontSize_mobile":"",
    "fontSize_tablet":"",
    "lineHeight":"",
    "lineHeight_mobile":"",
    "lineHeight_tablet":"",
    "letterSpacing":"",
    "letterSpacing_mobile":"",
    "letterSpacing_tablet":"",
    "typographyBold":"",
    "typographyItalic":"",
    "typographyUnderline":"",
    "align":"left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase":"",
    "textColor":"",
    "customClass": "",
    "customAttributes": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false,
    "showHeader": true
  },
  "productrelated":{
    "columns": "1",
    "pagination": false,
    "limit": "1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "product_related",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": "",
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titlealign": "",
    "titletypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor":"",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "upsells":{
    "columns": "1",
    "pagination": false,
    "limit": "-1",
    "showItemsCount": false,
    "showOrderBy": false,
    "selectProductIds": false,
    "productIds": [],
    "selectProductSkus": false,
    "productSkus": [],
    "selectCategories": false,
    "productCategories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "productTags": [],
    "queryTagsOperator": "IN",
    "selectGroup": false,
    "productGroups": "",
    "selectByVisibility": false,
    "productVisibility": "",
    "queryByAttribute": false,
    "queryAttribute": "",
    "queryAttributeTerms": [],
    "queryTermsOperator": "IN",
    "querySource": "upsells",
    "queryInclude": "",
    "queryIncludeBy": "",
    "queryIncludeTerm": "",
    "queryExclude": "",
    "queryExcludeBy": "",
    "queryExcludeTerm": "",
    "queryOrderBy": [],
    "queryOrder": "ASC",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": "",
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titlealign": "",
    "titletypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "",
    "imageBorderStyle": "solid",
    "imageBorderColor": "",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productrating":{
    "txt": "[sp_product_rating]",
    "label":"",
    "scale":"5",
    "rating":"5",
    "iconSize":"24",
    "spaceBetween":"",
    "align":"",
    "align_mobile": "",
    "align_tablet": "",
    "emptyStarColor":"",
    "color":"",
    "textColor":"",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "textfont": "",
    "textfontVariant": "",
    "textfontSize": "",
    "textfontSize_mobile": "",
    "textfontSize_tablet": "",
    "textlineHeight": "",
    "textlineHeight_mobile": "",
    "textlineHeight_tablet": "",
    "textletterSpacing": "",
    "textletterSpacing_mobile": "",
    "textletterSpacing_tablet": "",
    "texttypographyBold": "",
    "texttypographyItalic": "",
    "texttypographyUnderline": "",
    "texttypographyLetterCase": "",
    "textShadow":"",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "productstock":{
    "editortxt":"code",
    "txt": "[sp_product_stock]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "saleColor":"",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "imageShadow": "",
    "imageBorderRadius": "3",
    "imageBorderStyle": "solid",
    "imageBorderColor": "#ccc",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "hotspot":{
    "src": "",
    "altTxt": "",
    "width": "",
    "unit": "px",
    "imghiddenwidth": "",
    "imghiddenheight": "",
    "objectfit": "",
    "items": [{
      "icon": "",
      "tooltipContent": "Add Your Tooltip Text Here",
      "view": 1,
      "label": "",
      "linktype": "custom",
      "link": "",
      "openNewWindow": false,
      "noFollow": false,
      "customIconSize": false,
      "iconSize": 24,
      "iconColor": "#DD4A1F",
      "iconPosition": "left",
      "horizontalOrientation": 50,
      "verticalOrientation": 50,
      "customTooltipProperties": false,
      "tooltipTextWrap": false,
      "show_adv_link": false,
      "showAdvancedSettings": false
    }],
    "hotspotAnimation": "softbeat",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "tooltipPosition": "top",
    "tooltipPosition_mobile": "top",
    "tooltipPosition_tablet": "top",
    "tooltipTrigger": "hover",
    "tooltipAnimation": "fade",
    "tooltipDuration": 200,
    "showTooltipArrow": true,
    "tooltipMaxWidth": 400,
    "radiusunit":"px",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync": true,
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "borderRadius": "",
    "borderRadiusTL": "",
    "borderRadiusTR": "",
    "borderRadiusBL": "",
    "borderRadiusBR": "",
    "borderRadiusSync": true,
    "imagePadding": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "shadow": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "counter": {
    "startNumber": "0",
    "endNumber": "50",
    "numberPrefix": "",
    "numberSuffix": "",
    "animationDuration": "1000",
    "thousandsSeparator": false,
    "separator": "default",
    "title": "Customers",
    "titlealign": "left",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": "",
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titletypographyLetterCase": "",
    "titleTextColor": "",
    "titleshadow": "",
    "titleshadowColor":"",
    "titleshadowHorizontal":0,
    "titleshadowVertical":0,
    "titleshadowBlur":3,
    "titleshadowSpread":0,
    "titleshadowPosition":"outline",
    "numberalign": "left",
    "numberfont": "",
    "numberfontVariant": "",
    "numberfontSize": "72",
    "numberfontSize_mobile": "",
    "numberfontSize_tablet": "",
    "numberlineHeight": "1",
    "numberlineHeight_mobile": "",
    "numberlineHeight_tablet": "",
    "numberletterSpacing": "",
    "numberletterSpacing_mobile": "",
    "numberletterSpacing_tablet": "",
    "numbertypographyBold": "",
    "numbertypographyItalic": "",
    "numbertypographyUnderline": "",
    "numbertypographyLetterCase": "",
    "numberTextColor": "",
    "numbershadow": "",
    "numbershadowColor":"",
    "numbershadowHorizontal":0,
    "numbershadowVertical":0,
    "numbershadowBlur":3,
    "numbershadowSpread":0,
    "numbershadowPosition":"outline",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadsgrid":{
    "columns": "4",
    "pagination": false,
    "number": "20",
    "selectDownloadIds": false,
    "ids": [],
    "selectCategories": false,
    "categories": [],
    "queryCategoriesOperator": "IN",
    "selectTags": false,
    "tags": [],
    "queryTagsOperator": "IN",
    "orderBy": "post_date",
    "order": "ASC",
    "relation": "",
    "price": true,
    "excerpt": true,
    "full_content": false,
    "buy_button": true,
    "thumbnails": true,
    "align": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": "",
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titlealign": "",
    "titletypographyLetterCase": "",
    "titleColor": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptionalign": "",
    "descriptiontypographyLetterCase": "",
    "descriptionColor": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricealign": "",
    "pricetypographyLetterCase": "",
    "priceColor": "",
    "buttonStyle": "flat",
    "buttonBgColor": "",
    "buttonShadow": "",
    "buttonLineHeight": "",
    "buttonfont": "",
    "buttonfontVariant": "",
    "buttonfontSize": "",
    "buttonfontSize_mobile": "",
    "buttonfontSize_tablet": "",
    "buttonlineHeight": "",
    "buttonlineHeight_mobile": "",
    "buttonlineHeight_tablet": "",
    "buttonletterSpacing": "",
    "buttonletterSpacing_mobile": "",
    "buttonletterSpacing_tablet": "",
    "buttontypographyBold": "",
    "buttontypographyItalic": "",
    "buttontypographyUnderline": "",
    "buttonalign": "",
    "buttontypographyLetterCase": "",
    "buttonSize":"4",
    "buttonPaddingTop": "16",
    "buttonPaddingLeft": "20",
    "buttonBorderRadius": "",
    "paginationfont": "",
    "paginationfontVariant": "",
    "paginationfontSize": "",
    "paginationfontSize_mobile": "",
    "paginationfontSize_tablet": "",
    "paginationlineHeight": "",
    "paginationlineHeight_mobile": "",
    "paginationlineHeight_tablet": "",
    "paginationletterSpacing": "",
    "paginationletterSpacing_mobile": "",
    "paginationletterSpacing_tablet": "",
    "paginationtypographyBold": "",
    "paginationtypographyItalic": "",
    "paginationtypographyUnderline": "",
    "paginationalign": "",
    "paginationtypographyLetterCase": "",
    "paginationPageColor": "",
    "paginationLinkColor": "",
    "imageShadow": "",
    "imageBorderRadius": "3",
    "imageBorderStyle": "solid",
    "imageBorderColor": "#ccc",
    "imageBorderTop": "1",
    "imageBorderBottom": "1",
    "imageBorderLeft": "1",
    "imageBorderRight": "1",
    "imageBorderSync": true,
    "imagePadding": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "eddcart":{
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "align": "",
    "headerColor": "",
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "btnShadow": "",
    "btnLineHeight": "",
    "cartFieldBorderColor": "#eee",
    "cartborderTop": "1",
    "cartborderRight": "1",
    "cartborderBottom": "1",
    "cartborderLeft": "1",
    "cartborderSync": "true",
    "cartBorderRadius": "",
    "cartBgColor": "",
    "productfont": "",
    "productfontVariant": "",
    "productfontSize": "",
    "productfontSize_mobile": "",
    "productfontSize_tablet": "",
    "productlineHeight": "",
    "productlineHeight_mobile": "",
    "productlineHeight_tablet": "",
    "productletterSpacing": "",
    "productletterSpacing_mobile": "",
    "productletterSpacing_tablet": "",
    "producttypographyBold": "",
    "producttypographyItalic": "",
    "producttypographyUnderline": "",
    "producttypographyLetterCase": "",
    "productalign": "",
    "cartProductColor": "",
    "pricefont": "",
    "pricefontVariant": "",
    "pricefontSize": "",
    "pricefontSize_mobile": "",
    "pricefontSize_tablet": "",
    "pricelineHeight": "",
    "pricelineHeight_mobile": "",
    "pricelineHeight_tablet": "",
    "priceletterSpacing": "",
    "priceletterSpacing_mobile": "",
    "priceletterSpacing_tablet": "",
    "pricetypographyBold": "",
    "pricetypographyItalic": "",
    "pricetypographyUnderline": "",
    "pricetypographyLetterCase": "",
    "pricealign": "",
    "cartPriceColor": "",
    "totalfont": "",
    "totalfontVariant": "",
    "totalfontSize": "",
    "totalfontSize_mobile": "",
    "totalfontSize_tablet": "",
    "totallineHeight": "",
    "totallineHeight_mobile": "",
    "totallineHeight_tablet": "",
    "totalletterSpacing": "",
    "totalletterSpacing_mobile": "",
    "totalletterSpacing_tablet": "",
    "totaltypographyBold": "",
    "totaltypographyItalic": "",
    "totaltypographyUnderline": "",
    "totaltypographyLetterCase": "",
    "totalalign": "",
    "cartTotalColor": "",
    "linkfont": "",
    "linkfontVariant": "",
    "linkfontSize": "",
    "linkfontSize_mobile": "",
    "linkfontSize_tablet": "",
    "linklineHeight": "",
    "linklineHeight_mobile": "",
    "linklineHeight_tablet": "",
    "linkletterSpacing": "",
    "linkletterSpacing_mobile": "",
    "linkletterSpacing_tablet": "",
    "linktypographyBold": "",
    "linktypographyItalic": "",
    "linktypographyUnderline": "",
    "linktypographyLetterCase": "",
    "linkalign": "",
    "cartLinkColor": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "eddcheckout":{
    "css": "",
    "headerColor": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "align": "",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "1",
    "fieldborderRight": "1",
    "fieldborderBottom": "1",
    "fieldborderLeft": "1",
    "fieldborderSync": "true",
    "fieldBorderRadius": "",
    "borderRadius": "",
    "labelColor": "",
    "labelfont": "",
    "labelfontVariant": "",
    "labelfontSize": "",
    "labelfontSize_mobile": "",
    "labelfontSize_tablet": "",
    "labellineHeight": "",
    "labellineHeight_mobile": "",
    "labellineHeight_tablet": "",
    "labelletterSpacing": "",
    "labelletterSpacing_mobile": "",
    "labelletterSpacing_tablet": "",
    "labeltypographyBold": "",
    "labeltypographyItalic": "",
    "labeltypographyUnderline": "",
    "labeltypographyLetterCase": "",
    "labelalign": "",
    "rowSpacing": "6",
    "btnStyle": "flat",
    "btnBgColor": "",
    "btnBorderRadius": "",
    "btnShadow": "",
    "btnLineHeight": "",
    "alertInfoHlColor": "",
    "alertErrorHlColor": "",
    "alertSuccessHlColor": "",
    "headerfont": "",
    "headerfontVariant": "",
    "headerfontSize": "",
    "headerfontSize_mobile": "",
    "headerfontSize_tablet": "",
    "headerlineHeight": "",
    "headerlineHeight_mobile": "",
    "headerlineHeight_tablet": "",
    "headerletterSpacing": "",
    "headerletterSpacing_mobile": "",
    "headerletterSpacing_tablet": "",
    "headertypographyBold": "",
    "headertypographyItalic": "",
    "headertypographyUnderline": "",
    "headertypographyLetterCase": "",
    "headeralign": "",
    "itemfont": "",
    "itemfontVariant": "",
    "itemfontSize": "",
    "itemfontSize_mobile": "",
    "itemfontSize_tablet": "",
    "itemlineHeight": "",
    "itemlineHeight_mobile": "",
    "itemlineHeight_tablet": "",
    "itemletterSpacing": "",
    "itemletterSpacing_mobile": "",
    "itemletterSpacing_tablet": "",
    "itemtypographyBold": "",
    "itemtypographyItalic": "",
    "itemtypographyUnderline": "",
    "itemtypographyLetterCase": "",
    "itemalign": "",
    "itemColor": "",
    "cartFieldBorderColor": "",
    "cartborderTop": "1",
    "cartborderRight": "1",
    "cartborderBottom": "1",
    "cartborderLeft": "1",
    "cartborderSync": "true",
    "cartBgColor": "",
    "linkfont": "",
    "linkfontVariant": "",
    "linkfontSize": "",
    "linkfontSize_mobile": "",
    "linkfontSize_tablet": "",
    "linklineHeight": "",
    "linklineHeight_mobile": "",
    "linklineHeight_tablet": "",
    "linkletterSpacing": "",
    "linkletterSpacing_mobile": "",
    "linkletterSpacing_tablet": "",
    "linktypographyBold": "",
    "linktypographyItalic": "",
    "linktypographyUnderline": "",
    "linktypographyLetterCase": "",
    "linkalign": "",
    "linkColor": "",
    "titlefont": "",
    "titlefontVariant": "",
    "titlefontSize": "",
    "titlefontSize_mobile": "",
    "titlefontSize_tablet": "",
    "titlelineHeight": "",
    "titlelineHeight_mobile": "",
    "titlelineHeight_tablet": "",
    "titleletterSpacing": "",
    "titleletterSpacing_mobile": "",
    "titleletterSpacing_tablet": "",
    "titletypographyBold": "",
    "titletypographyItalic": "",
    "titletypographyUnderline": "",
    "titletypographyLetterCase": "",
    "titlealign": "",
    "titleColor": "",
    "descriptionfont": "",
    "descriptionfontVariant": "",
    "descriptionfontSize": "",
    "descriptionfontSize_mobile": "",
    "descriptionfontSize_tablet": "",
    "descriptionlineHeight": "",
    "descriptionlineHeight_mobile": "",
    "descriptionlineHeight_tablet": "",
    "descriptionletterSpacing": "",
    "descriptionletterSpacing_mobile": "",
    "descriptionletterSpacing_tablet": "",
    "descriptiontypographyBold": "",
    "descriptiontypographyItalic": "",
    "descriptiontypographyUnderline": "",
    "descriptiontypographyLetterCase": "",
    "descriptionalign": "",
    "descriptionColor": "",
    "totalfont": "",
    "totalfontVariant": "",
    "totalfontSize": "",
    "totalfontSize_mobile": "",
    "totalfontSize_tablet": "",
    "totallineHeight": "",
    "totallineHeight_mobile": "",
    "totallineHeight_tablet": "",
    "totalletterSpacing": "",
    "totalletterSpacing_mobile": "",
    "totalletterSpacing_tablet": "",
    "totaltypographyBold": "",
    "totaltypographyItalic": "",
    "totaltypographyUnderline": "",
    "totaltypographyLetterCase": "",
    "totalalign": "",
    "totalColor": "",
    "paymentborderTop": "1",
    "paymentborderRight": "1",
    "paymentborderBottom": "1",
    "paymentborderLeft": "1",
    "paymentborderSync": "true",
    "paymentFieldBorderColor": "",
    "paymentBorderRadius": "",
    "paymentBgColor": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "eddbuynowbutton":{
    "productId": "",
    "directToCheckout": false,
    "btnTxt": "Buy Now",
    "showPrice": true,
    "btnStyle": "",
    "btnSubTxt": "",
    "btnSubTextSize": "",
    "textColor": "#ffffff",
    "bgColor": "",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "btnSize":"4",
    "beforeIcon": "",
    "afterIcon": "",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "",
    "fieldborderRight": "",
    "fieldborderBottom": "",
    "fieldborderLeft": "",
    "fieldborderSync": "true",
    "fieldBorderRadius": "",
    "rowSpacing": "6",
    "fieldWidth": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "22",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadtitle":{
    "headerTxt": "[seedprod_edd tag=\"the_title\"]",
    "tag": "h1",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "textShadow": "",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "center",
    "align_mobile": "center",
    "align_tablet": "center",
    "typographyLetterCase": "",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadfeaturedimage":{
    "link": "",
    "openNewWindow": false,
    "noFollow": false,
    "unit":"px",
    "src": "",
    "altTxt": "",
    "link": "",
    "width": "",
    "height": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "imagePadding": "",
    "align": "",
    "align_mobile": "",
    "align_tablet": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "imageBorderRadius": "",
    "imageBorderTop": "0",
    "imageBorderBottom": "0",
    "imageBorderLeft": "0",
    "imageBorderRight": "0",
    "imageBorderSync": true,
    "imageBorderStyle":"solid",
    "imageBorderColor": "",
    "blockTemplateId":false,
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadprice":{
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "typographyLetterCase": "",
    "textColor": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "customClass": "",
    "customAttributes": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadcontent":{
    "editortxt":"code",
    "txt": "[seedprod_edd tag=\"the_content\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "eddaddtocart":{
    "directToCheckout": false,
    "btnTxt": "Buy Now",
    "showPrice": true,
    "btnStyle": "",
    "btnSubTxt": "",
    "btnSubTextSize": "",
    "textColor": "#ffffff",
    "bgColor": "",
    "btnPaddingTop": "16",
    "btnPaddingLeft": "20",
    "btnSize":"4",
    "fieldBGColor": "",
    "fieldTextColor": "",
    "fieldBorderColor": "",
    "fieldborderTop": "",
    "fieldborderRight": "",
    "fieldborderBottom": "",
    "fieldborderLeft": "",
    "fieldborderSync": "true",
    "fieldBorderRadius": "",
    "rowSpacing": "6",
    "fieldWidth": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "marginTop_mobile": "",
    "marginBottom_mobile": "",
    "marginLeft_mobile": "",
    "marginRight_mobile": "",
    "marginSync_mobile": false,
    "marginTop_tablet": "",
    "marginBottom_tablet": "",
    "marginLeft_tablet": "",
    "marginRight_tablet": "",
    "marginSync_tablet": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "paddingTop_mobile": "",
    "paddingBottom_mobile": "",
    "paddingLeft_mobile": "",
    "paddingRight_mobile": "",
    "paddingSync_mobile": true,
    "paddingTop_tablet": "",
    "paddingBottom_tablet": "",
    "paddingLeft_tablet": "",
    "paddingRight_tablet": "",
    "paddingSync_tablet": true,
    "borderRadius": "",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "22",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadexcerpt":{
    "editortxt":"code",
    "txt": "[seedprod_edd tag=\"the_excerpt\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  },
  "edddownloadinstructions":{
    "editortxt":"code",
    "txt": "[seedprod_edd tag=\"download_instructions\"]",
    "textColor": "",
    "bgColor": "",
    "beforeIcon": "",
    "afterIcon": "",
    "marginTop": "",
    "marginBottom": "",
    "marginLeft": "",
    "marginRight": "",
    "marginSync": false,
    "customClass": "",
    "customAttributes": "",
    "paddingTop": "",
    "paddingBottom": "",
    "paddingLeft": "",
    "paddingRight": "",
    "paddingSync": true,
    "borderRadius": "",
    "borderRadiusTL":"",
    "borderRadiusTR":"",
    "borderRadiusBL":"",
    "borderRadiusBR":"",
    "borderRadiusSync":true,
    "borderTop":"0",
    "borderBottom":"0",
    "borderLeft":"0",
    "borderRight":"0",
    "borderSync":true,
    "borderStyle":"solid",
    "border": "",
    "borderStyle": "",
    "borderSize": "",
    "borderColor": "",
    "shadow": "",
    "shadowColor":"",
    "shadowHorizontal":0,
    "shadowVertical":0,
    "shadowBlur":3,
    "shadowSpread":0,
    "shadowPosition":"outline",
    "textShadow": "",
    "textshadowColor":"",
    "textshadowHorizontal":0,
    "textshadowVertical":0,
    "textshadowBlur":3,
    "textshadowSpread":0,
    "textshadowPosition":"outline",
    "font": "",
    "fontVariant": "",
    "fontSize": "",
    "fontSize_mobile": "",
    "fontSize_tablet": "",
    "lineHeight": "",
    "lineHeight_mobile": "",
    "lineHeight_tablet": "",
    "letterSpacing": "",
    "letterSpacing_mobile": "",
    "letterSpacing_tablet": "",
    "typographyBold": "",
    "typographyItalic": "",
    "typographyUnderline": "",
    "align": "left",
    "align_mobile": "left",
    "align_tablet": "left",
    "typographyLetterCase": "",
    "entranceAnimation":"",
    "hideOnDesktop": false,
    "hideOnMobile": false,
    "hideOnTablet": false
  }
}';
