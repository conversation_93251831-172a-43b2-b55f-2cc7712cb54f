"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(d){if("undefined"!=typeof window){var e,c=0,l=!1,n=!1,p="message".length,b="[iFrameSizer]",y=b.length,v=null,r=window.requestAnimationFrame,f={max:1,scroll:1,bodyScroll:1,documentElementScroll:1},k={},i=null,u={autoResize:!0,bodyBackground:null,bodyMargin:null,bodyMarginV1:8,bodyPadding:null,checkOrigin:!0,inPageLinks:!1,enablePublicMethods:!0,heightCalculationMethod:"bodyOffset",id:"iFrameResizer",interval:32,log:!1,maxHeight:1/0,maxWidth:1/0,minHeight:0,minWidth:0,resizeFrom:"parent",scrolling:!1,sizeHeight:!0,sizeWidth:!1,warningTimeout:5e3,tolerance:0,widthCalculationMethod:"scroll",closedCallback:function(){},initCallback:function(){},messageCallback:function(){R("MessageCallback function not defined")},resizedCallback:function(){},scrollCallback:function(){return!0}},F={};window.jQuery&&((e=window.jQuery).fn?e.fn.iFrameResize||(e.fn.iFrameResize=function(i){return this.filter("iframe").each(function(e,n){g(n,i)}).end()}):z("","Unable to bind to jQuery, it is not fully loaded.")),"function"==typeof define&&define.amd?define([],q):"object"===("undefined"==typeof module?"undefined":_typeof(module))&&"object"===_typeof(module.exports)?module.exports=q():window.iFrameResize=window.iFrameResize||q()}function I(e,n,i){"addEventListener"in window?e.addEventListener(n,i,!1):"attachEvent"in window&&e.attachEvent("on"+n,i)}function x(e,n,i){"removeEventListener"in window?e.removeEventListener(n,i,!1):"detachEvent"in window&&e.detachEvent("on"+n,i)}function o(e){return b+"["+(i="Host page: "+(n=e),window.top!==window.self&&(i=window.parentIFrame&&window.parentIFrame.getId?window.parentIFrame.getId()+": "+n:"Nested host page: "+n),i)+"]";var n,i}function t(e){return k[e]?k[e].log:l}function M(e,n){a("log",e,n,t(e))}function z(e,n){a("info",e,n,t(e))}function R(e,n){a("warn",e,n,!0)}function a(e,n,i,t){!0===t&&"object"===_typeof(window.console)&&console[e](o(n),i)}function s(n){function a(){e("Height"),e("Width"),P(function(){W(w),T(h),u("resizedCallback",w)},w,"init")}function e(e){var n=Number(k[h]["max"+e]),i=Number(k[h]["min"+e]),t=e.toLowerCase(),o=Number(w[t]);M(h,"Checking "+t+" is in range "+i+"-"+n),o<i&&(o=i,M(h,"Set "+t+" to min value")),n<o&&(o=n,M(h,"Set "+t+" to max value")),w[t]=""+o}function s(e){return g.substr(g.indexOf(":")+p+e)}function d(i,t){var e,n,o;e=function(){var e,n;H("Send Page Info","pageInfo:"+(e=document.body.getBoundingClientRect(),n=w.iframe.getBoundingClientRect(),JSON.stringify({iframeHeight:n.height,iframeWidth:n.width,clientHeight:Math.max(document.documentElement.clientHeight,window.innerHeight||0),clientWidth:Math.max(document.documentElement.clientWidth,window.innerWidth||0),offsetTop:parseInt(n.top-e.top,10),offsetLeft:parseInt(n.left-e.left,10),scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset})),i,t)},n=32,F[o=t]||(F[o]=setTimeout(function(){F[o]=null,e()},n))}function c(e){var n=e.getBoundingClientRect();return O(h),{x:Math.floor(Number(n.left)+Number(v.x)),y:Math.floor(Number(n.top)+Number(v.y))}}function l(e){var n=e?c(w.iframe):{x:0,y:0},i={x:Number(w.width)+n.x,y:Number(w.height)+n.y};M(h,"Reposition requested from iFrame (offset x:"+n.x+" y:"+n.y+")"),window.top!==window.self?window.parentIFrame?window.parentIFrame["scrollTo"+(e?"Offset":"")](i.x,i.y):R(h,"Unable to scroll to requested position, window.parentIFrame not found"):(v=i,f(),M(h,"--"))}function f(){!1!==u("scrollCallback",v)?T(h):S()}function u(e,n){return C(h,e,n)}var i,t,o,r,m,g=n.data,w={},h=null;"[iFrameResizerChild]Ready"===g?function(){for(var e in k)H("iFrame requested init",A(e),document.getElementById(e),e)}():b===(""+g).substr(0,y)&&g.substr(y).split(":")[0]in k?(m=g.substr(y).split(":"),w={iframe:k[m[0]]&&k[m[0]].iframe,id:m[0],height:m[1],width:m[2],type:m[3]},h=w.id,k[h]&&(k[h].loaded=!0),(r=w.type in{true:1,false:1,undefined:1})&&M(h,"Ignoring init message from meta parent page"),!r&&(o=!0,k[t=h]||(o=!1,R(w.type+" No settings for "+t+". Message was: "+g)),o)&&(M(h,"Received: "+g),i=!0,null===w.iframe&&(R(h,"IFrame ("+w.id+") not found"),i=!1),i&&function(){var e,i=n.origin,t=k[h]&&k[h].checkOrigin;if(t&&""+i!="null"&&!(t.constructor===Array?function(){var e=0,n=!1;for(M(h,"Checking connection is from allowed list of origins: "+t);e<t.length;e++)if(t[e]===i){n=!0;break}return n}():(e=k[h]&&k[h].remoteHost,M(h,"Checking connection is from: "+e),i===e)))throw new Error("Unexpected message received from: "+i+" for "+w.iframe.id+". Message was: "+n.data+". This error can be disabled by setting the checkOrigin: false option or by providing of array of trusted domains.");return!0}()&&function(){switch(k[h]&&k[h].firstRun&&k[h]&&(k[h].firstRun=!1),w.type){case"close":k[h].closeRequestCallback?C(h,"closeRequestCallback",k[h].iframe):E(w.iframe);break;case"message":r=s(6),M(h,"MessageCallback passed: {iframe: "+w.iframe.id+", message: "+r+"}"),u("messageCallback",{iframe:w.iframe,message:JSON.parse(r)}),M(h,"--");break;case"scrollTo":l(!1);break;case"scrollToOffset":l(!0);break;case"pageInfo":d(k[h]&&k[h].iframe,h),function(){function e(n,i){function t(){k[r]?d(k[r].iframe,r):o()}["scroll","resize"].forEach(function(e){M(r,n+e+" listener for sendPageInfo"),i(window,e,t)})}function o(){e("Remove ",x)}var r=h;e("Add ",I),k[r]&&(k[r].stopPageInfo=o)}();break;case"pageInfoStop":k[h]&&k[h].stopPageInfo&&(k[h].stopPageInfo(),delete k[h].stopPageInfo);break;case"inPageLink":e=s(9),i=e.split("#")[1]||"",t=decodeURIComponent(i),(o=document.getElementById(t)||document.getElementsByName(t)[0])?(n=c(o),M(h,"Moving to in page link (#"+i+") at x: "+n.x+" y: "+n.y),v={x:n.x,y:n.y},f(),M(h,"--")):window.top!==window.self?window.parentIFrame?window.parentIFrame.moveToAnchor(i):M(h,"In page link #"+i+" not found and window.parentIFrame not found"):M(h,"In page link #"+i+" not found");break;case"reset":N(w);break;case"init":a(),u("initCallback",w.iframe);break;default:a()}var e,n,i,t,o,r}())):z(h,"Ignored: "+g)}function C(e,n,i){var t=null,o=null;if(k[e]){if("function"!=typeof(t=k[e][n]))throw new TypeError(n+" on iFrame["+e+"] is not a function");o=t(i)}return o}function m(e){var n=e.id;delete k[n]}function E(e){var n=e.id;M(n,"Removing iFrame: "+n);try{e.parentNode&&e.parentNode.removeChild(e)}catch(e){}C(n,"closedCallback",n),M(n,"--"),m(e)}function O(e){null===v&&M(e,"Get page position: "+(v={x:window.pageXOffset!==d?window.pageXOffset:document.documentElement.scrollLeft,y:window.pageYOffset!==d?window.pageYOffset:document.documentElement.scrollTop}).x+","+v.y)}function T(e){null!==v&&(window.scrollTo(v.x,v.y),M(e,"Set page position: "+v.x+","+v.y),S())}function S(){v=null}function N(e){M(e.id,"Size reset requested by "+("init"===e.type?"host page":"iFrame")),O(e.id),P(function(){W(e),H("reset","reset",e.iframe,e.id)},e,"reset")}function W(i){function t(e){n||"0"!==i[e]||(n=!0,M(o,"Hidden iFrame detected, creating visibility listener"),function(){function n(){function e(n){function e(e){return"0px"===(k[n]&&k[n].iframe.style[e])}k[n]&&null!==k[n].iframe.offsetParent&&(e("height")||e("width"))&&H("Visibility change","resize",k[n].iframe,n)}for(var n in k)e(n)}function e(e){M("window","Mutation observed: "+e[0].target+" "+e[0].type),w(n,16)}var i=window.MutationObserver||window.WebKitMutationObserver;i&&(t=document.querySelector("body"),o={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0},new i(e).observe(t,o));var t,o}())}function e(e){var n;n=e,i.id?(i.iframe.style[n]=i[n]+"px",M(i.id,"IFrame ("+o+") "+n+" set to "+i[n]+"px")):M("undefined","messageData id not set"),t(e)}var o=i.iframe.id;k[o]&&(k[o].sizeHeight&&e("height"),k[o].sizeWidth&&e("width"))}function P(e,n,i){i!==n.type&&r?(M(n.id,"Requesting animation frame"),r(e)):e()}function H(e,n,i,t,o){var r,a=!1;t=t||i.id,k[t]&&(i&&"contentWindow"in i&&null!==i.contentWindow?(r=k[t]&&k[t].targetOrigin,M(t,"["+e+"] Sending msg to iframe["+t+"] ("+n+") targetOrigin: "+r),i.contentWindow.postMessage(b+n,r)):R(t,"["+e+"] IFrame("+t+") not found"),o&&k[t]&&k[t].warningTimeout&&(k[t].msgTimeout=setTimeout(function(){!k[t]||k[t].loaded||a||(a=!0,R(t,"IFrame has not responded within "+k[t].warningTimeout/1e3+" seconds. Check iFrameResizer.contentWindow.js has been loaded in iFrame. This message can be ignored if everything is working, or you can set the warningTimeout option to a higher value or zero to suppress this warning."))},k[t].warningTimeout)))}function A(e){return e+":"+k[e].bodyMarginV1+":"+k[e].sizeWidth+":"+k[e].log+":"+k[e].interval+":"+k[e].enablePublicMethods+":"+k[e].autoResize+":"+k[e].bodyMargin+":"+k[e].heightCalculationMethod+":"+k[e].bodyBackground+":"+k[e].bodyPadding+":"+k[e].tolerance+":"+k[e].inPageLinks+":"+k[e].resizeFrom+":"+k[e].widthCalculationMethod}function g(i,e){var n,t,o,r,a,s=(n=i.id,""===n&&(i.id=(t=e&&e.id||u.id+c++,null!==document.getElementById(t)&&(t+=c++),n=t),l=(e||{}).log,M(n,"Added missing iframe ID: "+n+" ("+i.src+")")),n);s in k&&"iFrameResizer"in i?R(s,"Ignored iFrame, already setup."):(r=(r=e)||{},k[s]={firstRun:!0,iframe:i,remoteHost:i.src.split("/").slice(0,3).join("/")},function(e){if("object"!==_typeof(e))throw new TypeError("Options is not an object")}(r),function(e){for(var n in u)u.hasOwnProperty(n)&&(k[s][n]=e.hasOwnProperty(n)?e[n]:u[n])}(r),k[s]&&(k[s].targetOrigin=!0===k[s].checkOrigin?""===(a=k[s].remoteHost)||"file://"===a?"*":a:"*"),function(){switch(M(s,"IFrame scrolling "+(k[s]&&k[s].scrolling?"enabled":"disabled")+" for "+s),i.style.overflow=!1===(k[s]&&k[s].scrolling)?"hidden":"auto",k[s]&&k[s].scrolling){case"omit":break;case!0:i.scrolling="yes";break;case!1:i.scrolling="no";break;default:i.scrolling=k[s]?k[s].scrolling:"no"}}(),function(){function e(e){1/0!==k[s][e]&&0!==k[s][e]&&(i.style[e]=k[s][e]+"px",M(s,"Set "+e+" = "+k[s][e]+"px"))}function n(e){if(k[s]["min"+e]>k[s]["max"+e])throw new Error("Value for min"+e+" can not be greater than max"+e)}n("Height"),n("Width"),e("maxHeight"),e("minHeight"),e("maxWidth"),e("minWidth")}(),"number"!=typeof(k[s]&&k[s].bodyMargin)&&"0"!==(k[s]&&k[s].bodyMargin)||(k[s].bodyMarginV1=k[s].bodyMargin,k[s].bodyMargin=k[s].bodyMargin+"px"),o=A(s),I(i,"load",function(){var e,n;H("iFrame.onload",o,i,d,!0),e=k[s]&&k[s].firstRun,n=k[s]&&k[s].heightCalculationMethod in f,!e&&n&&N({iframe:i,height:0,width:0,type:"init"})}),H("init",o,i,d,!0),Function.prototype.bind&&k[s]&&(k[s].iframe.iFrameResizer={close:E.bind(null,k[s].iframe),removeListeners:m.bind(null,k[s].iframe),resize:H.bind(null,"Window resize","resize",k[s].iframe),moveToAnchor:function(e){H("Move to anchor","moveToAnchor:"+e,k[s].iframe,s)},sendMessage:function(e){H("Send Message","message:"+(e=JSON.stringify(e)),k[s].iframe,s)}}))}function w(e,n){null===i&&(i=setTimeout(function(){i=null,e()},n))}function h(e){M("window","Trigger event: "+e),w(function(){j("Window "+e,"resize")},16)}function L(){"hidden"!==document.visibilityState&&(M("document","Trigger event: Visiblity change"),w(function(){j("Tab Visable","resize")},16))}function j(e,n){for(var i in k)k[t=i]&&"parent"===k[t].resizeFrom&&k[t].autoResize&&!k[t].firstRun&&H(e,n,document.getElementById(i),i);var t}function q(){function t(e,n){n&&(!function(){if(!n.tagName)throw new TypeError("Object is not a valid DOM element");if("IFRAME"!==n.tagName.toUpperCase())throw new TypeError("Expected <IFRAME> tag, found <"+n.tagName+">")}(),g(n,e),o.push(n))}var o;return function(){var e,n=["moz","webkit","o","ms"];for(e=0;e<n.length&&!r;e+=1)r=window[n[e]+"RequestAnimationFrame"];r||M("setup","RequestAnimationFrame not supported")}(),I(window,"message",s),I(window,"resize",function(){h("resize")}),I(document,"visibilitychange",L),I(document,"-webkit-visibilitychange",L),I(window,"focusin",function(){h("focus")}),I(window,"focus",function(){h("focus")}),function(e,n){var i;switch(o=[],(i=e)&&i.enablePublicMethods&&R("enablePublicMethods option has been removed, public methods are now always available in the iFrame"),_typeof(n)){case"undefined":case"string":Array.prototype.forEach.call(document.querySelectorAll(n||"iframe"),t.bind(d,e));break;case"object":t(e,n);break;default:throw new TypeError("Unexpected data type ("+_typeof(n)+")")}return o}}}();