/*! tsParticles v1.18.11 by <PERSON> */
!function(t,i){if("object"==typeof exports&&"object"==typeof module)module.exports=i();else if("function"==typeof define&&define.amd)define([],i);else{var e=i();for(var o in e)("object"==typeof exports?exports:t)[o]=e[o]}}(this,(function(){return(()=>{"use strict";var t={714:(t,i,e)=>{e.r(i),e.d(i,{AbsorberClickMode:()=>Qi,AnimationStatus:()=>y,CanvasUtils:()=>E,ClickMode:()=>l,CollisionMode:()=>h,ColorUtils:()=>A,Constants:()=>S,Container:()=>$i,DestroyType:()=>f,DivMode:()=>c,DivType:()=>w,EmitterClickMode:()=>he,HoverMode:()=>d,InlineArrangement:()=>pe,InteractivityDetect:()=>x,MoveDirection:()=>n,MoveType:()=>ye,OutMode:()=>u,ProcessBubbleType:()=>m,RotateDirection:()=>a,ShapeType:()=>g,SizeMode:()=>v,StartValueType:()=>b,ThemeMode:()=>p,Type:()=>fe,Utils:()=>P,pJSDom:()=>Ee,particlesJS:()=>Te,tsParticles:()=>Ae});class o{getSidesCount(){return 4}draw(t,i,e){t.rect(-e,-e,2*e,2*e)}}var s,n,a;!function(t){t.bottom="bottom",t.left="left",t.right="right",t.top="top"}(s||(s={})),function(t){t.bottom="bottom",t.bottomLeft="bottom-left",t.bottomRight="bottom-right",t.left="left",t.none="none",t.right="right",t.top="top",t.topLeft="top-left",t.topRight="top-right"}(n||(n={})),function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(a||(a={}));class r{static clamp(t,i,e){return Math.min(Math.max(t,i),e)}static mix(t,i,e,o){return Math.floor((t*e+i*o)/(e+o))}static randomInRange(t,i){const e=Math.max(t,i),o=Math.min(t,i);return Math.random()*(e-o)+o}static getValue(t){const i=t.random,{enable:e,minimumValue:o}="boolean"==typeof i?{enable:i,minimumValue:0}:i;return e?r.randomInRange(o,t.value):t.value}static getDistances(t,i){const e=t.x-i.x,o=t.y-i.y;return{dx:e,dy:o,distance:Math.sqrt(e*e+o*o)}}static getDistance(t,i){return r.getDistances(t,i).distance}static getParticleBaseVelocity(t){let i;switch(t.direction){case n.top:i={x:0,y:-1};break;case n.topRight:i={x:.5,y:-.5};break;case n.right:i={x:1,y:-0};break;case n.bottomRight:i={x:.5,y:.5};break;case n.bottom:i={x:0,y:1};break;case n.bottomLeft:i={x:-.5,y:1};break;case n.left:i={x:-1,y:0};break;case n.topLeft:i={x:-.5,y:-.5};break;default:i={x:0,y:0}}return i}static rotateVelocity(t,i){return{horizontal:t.horizontal*Math.cos(i)-t.vertical*Math.sin(i),vertical:t.horizontal*Math.sin(i)+t.vertical*Math.cos(i)}}static collisionVelocity(t,i,e,o){return{horizontal:t.horizontal*(e-o)/(e+o)+2*i.horizontal*o/(e+o),vertical:t.vertical}}}var l,c,d,h,u,v,p,y,f,m,g,b,w,x,k=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};function z(t,i,e,o,s,n){const a={bounced:!1};return i.min>=o.min&&i.min<=o.max&&i.max>=o.min&&i.max<=o.max&&(t.max>=e.min&&t.max<=(e.max+e.min)/2&&s>0||t.min<=e.max&&t.min>(e.max+e.min)/2&&s<0)&&(a.velocity=s*-n,a.bounced=!0),a}function M(t,i){if(i instanceof Array){for(const e of i)if(t.matches(e))return!0;return!1}return t.matches(i)}class P{static isSsr(){return"undefined"==typeof window||!window}static get animate(){return P.isSsr()?t=>setTimeout(t):t=>(window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||window.setTimeout)(t)}static get cancelAnimation(){return P.isSsr()?t=>clearTimeout(t):t=>(window.cancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelRequestAnimationFrame||window.oCancelRequestAnimationFrame||window.msCancelRequestAnimationFrame||window.clearTimeout)(t)}static isInArray(t,i){return t===i||i instanceof Array&&i.indexOf(t)>-1}static loadFont(t){return k(this,void 0,void 0,(function*(){try{yield document.fonts.load(`${t.weight} 36px '${t.font}'`)}catch(t){}}))}static arrayRandomIndex(t){return Math.floor(Math.random()*t.length)}static itemFromArray(t,i,e=!0){return t[void 0!==i&&e?i%t.length:P.arrayRandomIndex(t)]}static isPointInside(t,i,e,o){return P.areBoundsInside(P.calculateBounds(t,null!=e?e:0),i,o)}static areBoundsInside(t,i,e){let o=!0;return e&&e!==s.bottom||(o=t.top<i.height),!o||e&&e!==s.left||(o=t.right>0),!o||e&&e!==s.right||(o=t.left<i.width),!o||e&&e!==s.top||(o=t.bottom>0),o}static calculateBounds(t,i){return{bottom:t.y+i,left:t.x-i,right:t.x+i,top:t.y-i}}static loadImage(t){return new Promise(((i,e)=>{if(!t)return void e("Error tsParticles - No image.src");const o={source:t,type:t.substr(t.length-3)},s=new Image;s.addEventListener("load",(()=>{o.element=s,i(o)})),s.addEventListener("error",(()=>{e("Error tsParticles - loading image: "+t)})),s.src=t}))}static downloadSvgImage(t){return k(this,void 0,void 0,(function*(){if(!t)throw new Error("Error tsParticles - No image.src");const i={source:t,type:t.substr(t.length-3)};if("svg"!==i.type)return P.loadImage(t);const e=yield fetch(i.source);if(!e.ok)throw new Error("Error tsParticles - Image not found");return i.svgData=yield e.text(),i}))}static deepExtend(t,...i){for(const e of i){if(null==e)continue;if("object"!=typeof e){t=e;continue}const i=Array.isArray(e);!i||"object"==typeof t&&t&&Array.isArray(t)?i||"object"==typeof t&&t&&!Array.isArray(t)||(t={}):t=[];for(const i in e){if("__proto__"===i)continue;const o=e[i],s="object"==typeof o,n=t;n[i]=s&&Array.isArray(o)?o.map((t=>P.deepExtend(n[i],t))):P.deepExtend(n[i],o)}}return t}static isDivModeEnabled(t,i){return i instanceof Array?!!i.find((i=>i.enable&&P.isInArray(t,i.mode))):P.isInArray(t,i.mode)}static divModeExecute(t,i,e){if(i instanceof Array)for(const o of i){const i=o.mode;o.enable&&P.isInArray(t,i)&&P.singleDivModeExecute(o,e)}else{const o=i.mode;i.enable&&P.isInArray(t,o)&&P.singleDivModeExecute(i,e)}}static singleDivModeExecute(t,i){const e=t.selectors;if(e instanceof Array)for(const o of e)i(o,t);else i(e,t)}static divMode(t,i){if(i&&t)return t instanceof Array?t.find((t=>M(i,t.selectors))):M(i,t.selectors)?t:void 0}static circleBounceDataFromParticle(t){return{position:t.getPosition(),radius:t.getRadius(),velocity:t.velocity,factor:{horizontal:r.getValue(t.particlesOptions.bounce.horizontal),vertical:r.getValue(t.particlesOptions.bounce.vertical)}}}static circleBounce(t,i){const e=t.velocity.horizontal,o=t.velocity.vertical,s=t.position,n=i.position;if(e*(n.x-s.x)+o*(n.y-s.y)>=0){const e=-Math.atan2(n.y-s.y,n.x-s.x),o=t.radius,a=i.radius,l=r.rotateVelocity(t.velocity,e),c=r.rotateVelocity(i.velocity,e),d=r.collisionVelocity(l,c,o,a),h=r.collisionVelocity(c,l,o,a),u=r.rotateVelocity(d,-e),v=r.rotateVelocity(h,-e);t.velocity.horizontal=u.horizontal*t.factor.horizontal,t.velocity.vertical=u.vertical*t.factor.vertical,i.velocity.horizontal=v.horizontal*i.factor.horizontal,i.velocity.vertical=v.vertical*i.factor.vertical}}static rectBounce(t,i){const e=t.getPosition(),o=t.getRadius(),s=P.calculateBounds(e,o),n=z({min:s.left,max:s.right},{min:s.top,max:s.bottom},{min:i.left,max:i.right},{min:i.top,max:i.bottom},t.velocity.horizontal,r.getValue(t.particlesOptions.bounce.horizontal));n.bounced&&(void 0!==n.velocity&&(t.velocity.horizontal=n.velocity),void 0!==n.position&&(t.position.x=n.position));const a=z({min:s.top,max:s.bottom},{min:s.left,max:s.right},{min:i.top,max:i.bottom},{min:i.left,max:i.right},t.velocity.vertical,r.getValue(t.particlesOptions.bounce.vertical));a.bounced&&(void 0!==a.velocity&&(t.velocity.vertical=a.velocity),void 0!==a.position&&(t.position.y=a.position))}}class S{}function R(t,i,e){let o=e;return o<0&&(o+=1),o>1&&(o-=1),o<1/6?t+6*(i-t)*o:o<.5?i:o<2/3?t+(i-t)*(2/3-o)*6:t}function C(t){if(t.startsWith("rgb")){const i=/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([\d.]+)\s*)?\)/i.exec(t);return i?{a:i.length>4?parseFloat(i[5]):1,b:parseInt(i[3],10),g:parseInt(i[2],10),r:parseInt(i[1],10)}:void 0}if(t.startsWith("hsl")){const i=/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([\d.]+)\s*)?\)/i.exec(t);return i?A.hslaToRgba({a:i.length>4?parseFloat(i[5]):1,h:parseInt(i[1],10),l:parseInt(i[3],10),s:parseInt(i[2],10)}):void 0}if(t.startsWith("hsv")){const i=/hsva?\(\s*(\d+)°\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([\d.]+)\s*)?\)/i.exec(t);return i?A.hsvaToRgba({a:i.length>4?parseFloat(i[5]):1,h:parseInt(i[1],10),s:parseInt(i[2],10),v:parseInt(i[3],10)}):void 0}{const i=/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,e=t.replace(i,((t,i,e,o,s)=>i+i+e+e+o+o+(void 0!==s?s+s:""))),o=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(e);return o?{a:void 0!==o[4]?parseInt(o[4],16)/255:1,b:parseInt(o[3],16),g:parseInt(o[2],16),r:parseInt(o[1],16)}:void 0}}S.canvasClass="tsparticles-canvas-el",S.randomColorValue="random",S.midColorValue="mid",S.touchEndEvent="touchend",S.mouseDownEvent="mousedown",S.mouseUpEvent="mouseup",S.mouseMoveEvent="mousemove",S.touchStartEvent="touchstart",S.touchMoveEvent="touchmove",S.mouseLeaveEvent="mouseleave",S.mouseOutEvent="mouseout",S.touchCancelEvent="touchcancel",S.resizeEvent="resize",S.visibilityChangeEvent="visibilitychange",S.noPolygonDataLoaded="No polygon data loaded.",S.noPolygonFound="No polygon found, you need to specify SVG url in config.";class A{static colorToRgb(t,i,e=!0){var o,s,n;if(void 0===t)return;const a="string"==typeof t?{value:t}:t;let r;if("string"==typeof a.value)r=a.value===S.randomColorValue?A.getRandomRgbColor():A.stringToRgb(a.value);else if(a.value instanceof Array){const t=P.itemFromArray(a.value,i,e);r=A.colorToRgb({value:t})}else{const t=a.value,i=null!==(o=t.rgb)&&void 0!==o?o:a.value;if(void 0!==i.r)r=i;else{const i=null!==(s=t.hsl)&&void 0!==s?s:a.value;if(void 0!==i.h&&void 0!==i.l)r=A.hslToRgb(i);else{const i=null!==(n=t.hsv)&&void 0!==n?n:a.value;void 0!==i.h&&void 0!==i.v&&(r=A.hsvToRgb(i))}}}return r}static colorToHsl(t,i,e=!0){const o=A.colorToRgb(t,i,e);return void 0!==o?A.rgbToHsl(o):void 0}static rgbToHsl(t){const i=t.r/255,e=t.g/255,o=t.b/255,s=Math.max(i,e,o),n=Math.min(i,e,o),a={h:0,l:(s+n)/2,s:0};return s!=n&&(a.s=a.l<.5?(s-n)/(s+n):(s-n)/(2-s-n),a.h=i===s?(e-o)/(s-n):a.h=e===s?2+(o-i)/(s-n):4+(i-e)/(s-n)),a.l*=100,a.s*=100,a.h*=60,a.h<0&&(a.h+=360),a}static stringToAlpha(t){var i;return null===(i=C(t))||void 0===i?void 0:i.a}static stringToRgb(t){return C(t)}static hslToRgb(t){const i={b:0,g:0,r:0},e={h:t.h/360,l:t.l/100,s:t.s/100};if(0===e.s)i.b=e.l,i.g=e.l,i.r=e.l;else{const t=e.l<.5?e.l*(1+e.s):e.l+e.s-e.l*e.s,o=2*e.l-t;i.r=R(o,t,e.h+1/3),i.g=R(o,t,e.h),i.b=R(o,t,e.h-1/3)}return i.r=Math.floor(255*i.r),i.g=Math.floor(255*i.g),i.b=Math.floor(255*i.b),i}static hslaToRgba(t){const i=A.hslToRgb(t);return{a:t.a,b:i.b,g:i.g,r:i.r}}static hslToHsv(t){const i=t.l/100,e=i+t.s/100*Math.min(i,1-i),o=e?2*(1-i/e):0;return{h:t.h,s:100*o,v:100*e}}static hslaToHsva(t){const i=A.hslToHsv(t);return{a:t.a,h:i.h,s:i.s,v:i.v}}static hsvToHsl(t){const i=t.v/100,e=i*(1-t.s/100/2),o=0===e||1===e?0:(i-e)/Math.min(e,1-e);return{h:t.h,l:100*e,s:100*o}}static hsvaToHsla(t){const i=A.hsvToHsl(t);return{a:t.a,h:i.h,l:i.l,s:i.s}}static hsvToRgb(t){const i={b:0,g:0,r:0},e=t.h/60,o=t.s/100,s=t.v/100,n=s*o,a=n*(1-Math.abs(e%2-1));let r;if(e>=0&&e<=1?r={r:n,g:a,b:0}:e>1&&e<=2?r={r:a,g:n,b:0}:e>2&&e<=3?r={r:0,g:n,b:a}:e>3&&e<=4?r={r:0,g:a,b:n}:e>4&&e<=5?r={r:a,g:0,b:n}:e>5&&e<=6&&(r={r:n,g:0,b:a}),r){const t=s-n;i.r=Math.floor(255*(r.r+t)),i.g=Math.floor(255*(r.g+t)),i.b=Math.floor(255*(r.b+t))}return i}static hsvaToRgba(t){const i=A.hsvToRgb(t);return{a:t.a,b:i.b,g:i.g,r:i.r}}static rgbToHsv(t){const i={r:t.r/255,g:t.g/255,b:t.b/255},e=Math.max(i.r,i.g,i.b),o=e-Math.min(i.r,i.g,i.b);let s=0;e===i.r?s=(i.g-i.b)/o*60:e===i.g?s=60*(2+(i.b-i.r)/o):e===i.b&&(s=60*(4+(i.r-i.g)/o));return{h:s,s:100*(e?o/e:0),v:100*e}}static rgbaToHsva(t){const i=A.rgbToHsv(t);return{a:t.a,h:i.h,s:i.s,v:i.v}}static getRandomRgbColor(t){const i=null!=t?t:0;return{b:Math.floor(r.randomInRange(i,256)),g:Math.floor(r.randomInRange(i,256)),r:Math.floor(r.randomInRange(i,256))}}static getStyleFromRgb(t,i){return`rgba(${t.r}, ${t.g}, ${t.b}, ${null!=i?i:1})`}static getStyleFromHsl(t,i){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${null!=i?i:1})`}static getStyleFromHsv(t,i){return A.getStyleFromHsl(A.hsvToHsl(t),i)}static mix(t,i,e,o){let s=t,n=i;return void 0===s.r&&(s=A.hslToRgb(t)),void 0===n.r&&(n=A.hslToRgb(i)),{b:r.mix(s.b,n.b,e,o),g:r.mix(s.g,n.g,e,o),r:r.mix(s.r,n.r,e,o)}}static replaceColorSvg(t,i,e){if(!t.svgData)return"";return t.svgData.replace(/#([0-9A-F]{3,6})/gi,(()=>A.getStyleFromHsl(i,e)))}static getLinkColor(t,i,e){var o,s;if(e===S.randomColorValue)return A.getRandomRgbColor();if("mid"!==e)return e;{const e=null!==(o=t.getFillColor())&&void 0!==o?o:t.getStrokeColor(),n=null!==(s=null==i?void 0:i.getFillColor())&&void 0!==s?s:null==i?void 0:i.getStrokeColor();if(e&&n&&i)return A.mix(e,n,t.getRadius(),i.getRadius());{const t=null!=e?e:n;if(t)return A.hslToRgb(t)}}}static getLinkRandomColor(t,i,e){const o="string"==typeof t?t:t.value;return o===S.randomColorValue?e?A.colorToRgb({value:o}):i?S.randomColorValue:S.midColorValue:A.colorToRgb({value:o})}}function T(t,i,e){t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(e.x,e.y),t.closePath()}class E{static paintBase(t,i,e){t.save(),t.fillStyle=null!=e?e:"rgba(0,0,0,0)",t.fillRect(0,0,i.width,i.height),t.restore()}static clear(t,i){t.clearRect(0,0,i.width,i.height)}static drawLinkLine(t,i,e,o,s,n,a,l,c,d,h,u){let v=!1;if(r.getDistance(e,o)<=s)T(t,e,o),v=!0;else if(a){let i,a;const l={x:o.x-n.width,y:o.y},c=r.getDistances(e,l);if(c.distance<=s){const t=e.y-c.dy/c.dx*e.x;i={x:0,y:t},a={x:n.width,y:t}}else{const t={x:o.x,y:o.y-n.height},l=r.getDistances(e,t);if(l.distance<=s){const t=-(e.y-l.dy/l.dx*e.x)/(l.dy/l.dx);i={x:t,y:0},a={x:t,y:n.height}}else{const t={x:o.x-n.width,y:o.y-n.height},l=r.getDistances(e,t);if(l.distance<=s){const t=e.y-l.dy/l.dx*e.x;i={x:-t/(l.dy/l.dx),y:t},a={x:i.x+n.width,y:i.y+n.height}}}}i&&a&&(T(t,e,i),T(t,o,a),v=!0)}if(v){if(t.lineWidth=i,l&&(t.globalCompositeOperation=c),t.strokeStyle=A.getStyleFromRgb(d,h),u.enable){const i=A.colorToRgb(u.color);i&&(t.shadowBlur=u.blur,t.shadowColor=A.getStyleFromRgb(i))}t.stroke()}}static drawLinkTriangle(t,i,e,o,s,n,a,r){!function(t,i,e,o){t.beginPath(),t.moveTo(i.x,i.y),t.lineTo(e.x,e.y),t.lineTo(o.x,o.y),t.closePath()}(t,i,e,o),s&&(t.globalCompositeOperation=n),t.fillStyle=A.getStyleFromRgb(a,r),t.fill()}static drawConnectLine(t,i,e,o,s){t.save(),T(t,o,s),t.lineWidth=i,t.strokeStyle=e,t.stroke(),t.restore()}static gradient(t,i,e,o){const s=Math.floor(e.getRadius()/i.getRadius()),n=i.getFillColor(),a=e.getFillColor();if(!n||!a)return;const r=i.getPosition(),l=e.getPosition(),c=A.mix(n,a,i.getRadius(),e.getRadius()),d=t.createLinearGradient(r.x,r.y,l.x,l.y);return d.addColorStop(0,A.getStyleFromHsl(n,o)),d.addColorStop(s>1?1:s,A.getStyleFromRgb(c,o)),d.addColorStop(1,A.getStyleFromHsl(a,o)),d}static drawGrabLine(t,i,e,o,s,n){t.save(),T(t,e,o),t.strokeStyle=A.getStyleFromRgb(s,n),t.lineWidth=i,t.stroke(),t.restore()}static drawLight(t,i,e){const o=t.options.interactivity.modes.light.area;i.beginPath(),i.arc(e.x,e.y,o.radius,0,2*Math.PI);const s=i.createRadialGradient(e.x,e.y,0,e.x,e.y,o.radius),n=o.gradient,a={start:A.colorToRgb(n.start),stop:A.colorToRgb(n.stop)};a.start&&a.stop&&(s.addColorStop(0,A.getStyleFromRgb(a.start)),s.addColorStop(1,A.getStyleFromRgb(a.stop)),i.fillStyle=s,i.fill())}static drawParticleShadow(t,i,e,o){const s=e.getPosition(),n=t.options.interactivity.modes.light.shadow;i.save();const a=e.getRadius(),r=e.sides,l=2*Math.PI/r,c=-e.rotate.value+Math.PI/4,d=[];for(let t=0;t<r;t++)d.push({x:s.x+a*Math.sin(c+l*t)*1,y:s.y+a*Math.cos(c+l*t)*1});const h=[],u=n.length;for(const t of d){const i=Math.atan2(o.y-t.y,o.x-t.x),e=t.x+u*Math.sin(-i-Math.PI/2),s=t.y+u*Math.cos(-i-Math.PI/2);h.push({endX:e,endY:s,startX:t.x,startY:t.y})}const v=A.colorToRgb(n.color);if(!v)return;const p=A.getStyleFromRgb(v);for(let t=h.length-1;t>=0;t--){const e=t==h.length-1?0:t+1;i.beginPath(),i.moveTo(h[t].startX,h[t].startY),i.lineTo(h[e].startX,h[e].startY),i.lineTo(h[e].endX,h[e].endY),i.lineTo(h[t].endX,h[t].endY),i.fillStyle=p,i.fill()}i.restore()}static drawParticle(t,i,e,o,s,n,a,r,l,c,d){const h=e.getPosition();i.save(),i.translate(h.x,h.y),i.beginPath();const u=e.rotate.value+(e.particlesOptions.rotate.path?e.pathAngle:0);0!==u&&i.rotate(u),a&&(i.globalCompositeOperation=r);const v=e.shadowColor;d.enable&&v&&(i.shadowBlur=d.blur,i.shadowColor=A.getStyleFromRgb(v),i.shadowOffsetX=d.offset.x,i.shadowOffsetY=d.offset.y),s&&(i.fillStyle=s);const p=e.stroke;i.lineWidth=e.strokeWidth,n&&(i.strokeStyle=n),E.drawShape(t,i,e,l,c,o),p.width>0&&i.stroke(),e.close&&i.closePath(),e.fill&&i.fill(),i.restore(),i.save(),i.translate(h.x,h.y),0!==u&&i.rotate(u),a&&(i.globalCompositeOperation=r),E.drawShapeAfterEffect(t,i,e,l,c,o),i.restore()}static drawShape(t,i,e,o,s,n){if(!e.shape)return;const a=t.drawers.get(e.shape);a&&a.draw(i,e,o,s,n.value,t.retina.pixelRatio)}static drawShapeAfterEffect(t,i,e,o,s,n){if(!e.shape)return;const a=t.drawers.get(e.shape);(null==a?void 0:a.afterEffect)&&a.afterEffect(i,e,o,s,n.value,t.retina.pixelRatio)}static drawPlugin(t,i,e){void 0!==i.draw&&(t.save(),i.draw(t,e),t.restore())}}class O{constructor(t,i){this.position={x:t,y:i}}}class D extends O{constructor(t,i,e){super(t,i),this.radius=e}contains(t){return Math.pow(t.x-this.position.x,2)+Math.pow(t.y-this.position.y,2)<=this.radius*this.radius}intersects(t){const i=t,e=t,o=this.position,s=t.position,n=Math.abs(s.x-o.x),a=Math.abs(s.y-o.y),r=this.radius;if(void 0!==e.radius){return r+e.radius>Math.sqrt(n*n+a+a)}if(void 0!==i.size){const t=i.size.width,e=i.size.height,o=Math.pow(n-t,2)+Math.pow(a-e,2);return!(n>r+t||a>r+e)&&(n<=t||a<=e||o<=r*r)}return!1}}class I extends O{constructor(t,i,e,o){super(t,i),this.size={height:o,width:e}}contains(t){const i=this.size.width,e=this.size.height,o=this.position;return t.x>=o.x&&t.x<=o.x+i&&t.y>=o.y&&t.y<=o.y+e}intersects(t){const i=t,e=t,o=this.size.width,s=this.size.height,n=this.position,a=t.position;if(void 0!==e.radius)return e.intersects(this);if(void 0!==i.size){const t=i.size,e=t.width,r=t.height;return a.x<n.x+o&&a.x+e>n.x&&a.y<n.y+s&&a.y+r>n.y}return!1}}class L extends D{constructor(t,i,e,o){super(t,i,e),this.canvasSize=o,this.canvasSize={height:o.height,width:o.width}}contains(t){if(super.contains(t))return!0;const i={x:t.x-this.canvasSize.width,y:t.y};if(super.contains(i))return!0;const e={x:t.x-this.canvasSize.width,y:t.y-this.canvasSize.height};if(super.contains(e))return!0;const o={x:t.x,y:t.y-this.canvasSize.height};return super.contains(o)}intersects(t){if(super.intersects(t))return!0;const i=t,e=t,o={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==e.radius){const t=new D(o.x,o.y,2*e.radius);return super.intersects(t)}if(void 0!==i.size){const t=new I(o.x,o.y,2*i.size.width,2*i.size.height);return super.intersects(t)}return!1}}function H(t,i,e,o,s){if(o){let o={passive:!0};"boolean"==typeof s?o.capture=s:void 0!==s&&(o=s),t.addEventListener(i,e,o)}else{const o=s;t.removeEventListener(i,e,o)}}!function(t){t.attract="attract",t.bubble="bubble",t.push="push",t.remove="remove",t.repulse="repulse",t.pause="pause",t.trail="trail"}(l||(l={})),function(t){t.bounce="bounce",t.bubble="bubble",t.repulse="repulse"}(c||(c={})),function(t){t.attract="attract",t.bounce="bounce",t.bubble="bubble",t.connect="connect",t.grab="grab",t.light="light",t.repulse="repulse",t.slow="slow",t.trail="trail"}(d||(d={})),function(t){t.absorb="absorb",t.bounce="bounce",t.destroy="destroy"}(h||(h={})),function(t){t.bounce="bounce",t.bounceHorizontal="bounce-horizontal",t.bounceVertical="bounce-vertical",t.none="none",t.out="out",t.destroy="destroy"}(u||(u={})),function(t){t.precise="precise",t.percent="percent"}(v||(v={})),function(t){t.any="any",t.dark="dark",t.light="light"}(p||(p={})),function(t){t[t.increasing=0]="increasing",t[t.decreasing=1]="decreasing"}(y||(y={})),function(t){t.none="none",t.max="max",t.min="min"}(f||(f={})),function(t){t.color="color",t.opacity="opacity",t.size="size"}(m||(m={})),function(t){t.char="char",t.character="character",t.circle="circle",t.edge="edge",t.image="image",t.images="images",t.line="line",t.polygon="polygon",t.square="square",t.star="star",t.triangle="triangle"}(g||(g={})),function(t){t.max="max",t.min="min",t.random="random"}(b||(b={})),function(t){t.circle="circle",t.rectangle="rectangle"}(w||(w={})),function(t){t.canvas="canvas",t.parent="parent",t.window="window"}(x||(x={}));class F{constructor(t){this.container=t,this.canPush=!0,this.mouseMoveHandler=t=>this.mouseTouchMove(t),this.touchStartHandler=t=>this.mouseTouchMove(t),this.touchMoveHandler=t=>this.mouseTouchMove(t),this.touchEndHandler=()=>this.mouseTouchFinish(),this.mouseLeaveHandler=()=>this.mouseTouchFinish(),this.touchCancelHandler=()=>this.mouseTouchFinish(),this.touchEndClickHandler=t=>this.mouseTouchClick(t),this.mouseUpHandler=t=>this.mouseTouchClick(t),this.mouseDownHandler=()=>this.mouseDown(),this.visibilityChangeHandler=()=>this.handleVisibilityChange(),this.resizeHandler=()=>this.handleWindowResize()}addListeners(){this.manageListeners(!0)}removeListeners(){this.manageListeners(!1)}manageListeners(t){var i;const e=this.container,o=e.options,s=o.interactivity.detectsOn;let n=S.mouseLeaveEvent;if(s===x.window)e.interactivity.element=window,n=S.mouseOutEvent;else if(s===x.parent&&e.canvas.element){const t=e.canvas.element;e.interactivity.element=null!==(i=t.parentElement)&&void 0!==i?i:t.parentNode}else e.interactivity.element=e.canvas.element;const a=e.interactivity.element;if(!a)return;const r=a;(o.interactivity.events.onHover.enable||o.interactivity.events.onClick.enable)&&(H(a,S.mouseMoveEvent,this.mouseMoveHandler,t),H(a,S.touchStartEvent,this.touchStartHandler,t),H(a,S.touchMoveEvent,this.touchMoveHandler,t),o.interactivity.events.onClick.enable?(H(a,S.touchEndEvent,this.touchEndClickHandler,t),H(a,S.mouseUpEvent,this.mouseUpHandler,t),H(a,S.mouseDownEvent,this.mouseDownHandler,t)):H(a,S.touchEndEvent,this.touchEndHandler,t),H(a,n,this.mouseLeaveHandler,t),H(a,S.touchCancelEvent,this.touchCancelHandler,t)),e.canvas.element&&(e.canvas.element.style.pointerEvents=r===e.canvas.element?"initial":"none"),o.interactivity.events.resize&&H(window,S.resizeEvent,this.resizeHandler,t),document&&H(document,S.visibilityChangeEvent,this.visibilityChangeHandler,t,!1)}handleWindowResize(){var t;null===(t=this.container.canvas)||void 0===t||t.windowResize()}handleVisibilityChange(){const t=this.container,i=t.options;this.mouseTouchFinish(),i.pauseOnBlur&&((null===document||void 0===document?void 0:document.hidden)?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.getAnimationStatus()?t.play(!0):t.draw()))}mouseDown(){const t=this.container.interactivity;if(t){const i=t.mouse;i.clicking=!0,i.downPosition=i.position}}mouseTouchMove(t){var i,e,o,s,n,a,r;const l=this.container,c=l.options;if(void 0===(null===(i=l.interactivity)||void 0===i?void 0:i.element))return;let d;l.interactivity.mouse.inside=!0;const h=l.canvas.element;if(t.type.startsWith("mouse")){this.canPush=!0;const i=t;if(l.interactivity.element===window){if(h){const t=h.getBoundingClientRect();d={x:i.clientX-t.left,y:i.clientY-t.top}}}else if(c.interactivity.detectsOn===x.parent){const t=i.target,s=i.currentTarget,n=l.canvas.element;if(t&&s&&n){const e=t.getBoundingClientRect(),o=s.getBoundingClientRect(),a=n.getBoundingClientRect();d={x:i.offsetX+2*e.left-(o.left+a.left),y:i.offsetY+2*e.top-(o.top+a.top)}}else d={x:null!==(e=i.offsetX)&&void 0!==e?e:i.clientX,y:null!==(o=i.offsetY)&&void 0!==o?o:i.clientY}}else i.target===l.canvas.element&&(d={x:null!==(s=i.offsetX)&&void 0!==s?s:i.clientX,y:null!==(n=i.offsetY)&&void 0!==n?n:i.clientY})}else{this.canPush="touchmove"!==t.type;const i=t,e=i.touches[i.touches.length-1],o=null==h?void 0:h.getBoundingClientRect();d={x:e.clientX-(null!==(a=null==o?void 0:o.left)&&void 0!==a?a:0),y:e.clientY-(null!==(r=null==o?void 0:o.top)&&void 0!==r?r:0)}}const u=l.retina.pixelRatio;d&&(d.x*=u,d.y*=u),l.interactivity.mouse.position=d,l.interactivity.status=S.mouseMoveEvent}mouseTouchFinish(){const t=this.container.interactivity;if(void 0===t)return;const i=t.mouse;delete i.position,delete i.clickPosition,delete i.downPosition,t.status=S.mouseLeaveEvent,i.inside=!1,i.clicking=!1}mouseTouchClick(t){const i=this.container,e=i.options,o=i.interactivity.mouse;o.inside=!0;let s=!1;const n=o.position;if(void 0!==n&&e.interactivity.events.onClick.enable){for(const[,t]of i.plugins)if(void 0!==t.clickPositionValid&&(s=t.clickPositionValid(n),s))break;s||this.doMouseTouchClick(t),o.clicking=!1}}doMouseTouchClick(t){const i=this.container,e=i.options;if(this.canPush){const t=i.interactivity.mouse.position;if(!t)return;i.interactivity.mouse.clickPosition={x:t.x,y:t.y},i.interactivity.mouse.clickTime=(new Date).getTime();const o=e.interactivity.events.onClick;if(o.mode instanceof Array)for(const t of o.mode)this.handleClickMode(t);else this.handleClickMode(o.mode)}"touchend"===t.type&&setTimeout((()=>this.mouseTouchFinish()),500)}handleClickMode(t){const i=this.container,e=i.options,o=e.interactivity.modes.push.quantity,s=e.interactivity.modes.remove.quantity;switch(t){case l.push:o>0&&i.particles.push(o,i.interactivity.mouse);break;case l.remove:i.particles.removeQuantity(s);break;case l.bubble:i.bubble.clicking=!0;break;case l.repulse:i.repulse.clicking=!0,i.repulse.count=0;for(const t of i.repulse.particles)t.velocity.horizontal=t.initialVelocity.horizontal,t.velocity.vertical=t.initialVelocity.vertical;i.repulse.particles=[],i.repulse.finish=!1,setTimeout((()=>{i.destroyed||(i.repulse.clicking=!1)}),1e3*e.interactivity.modes.repulse.duration);break;case l.attract:i.attract.clicking=!0,i.attract.count=0;for(const t of i.attract.particles)t.velocity.horizontal=t.initialVelocity.horizontal,t.velocity.vertical=t.initialVelocity.vertical;i.attract.particles=[],i.attract.finish=!1,setTimeout((()=>{i.destroyed||(i.attract.clicking=!1)}),1e3*e.interactivity.modes.attract.duration);break;case l.pause:i.getAnimationStatus()?i.pause():i.play()}for(const[,e]of i.plugins)e.handleClickMode&&e.handleClickMode(t)}}const V=[],q=new Map,_=new Map;class B{static getPlugin(t){return V.find((i=>i.id===t))}static addPlugin(t){B.getPlugin(t.id)||V.push(t)}static getAvailablePlugins(t){const i=new Map;for(const e of V)e.needsPlugin(t.options)&&i.set(e.id,e.getPlugin(t));return i}static loadOptions(t,i){for(const e of V)e.loadOptions(t,i)}static getPreset(t){return q.get(t)}static addPreset(t,i){B.getPreset(t)||q.set(t,i)}static addShapeDrawer(t,i){B.getShapeDrawer(t)||_.set(t,i)}static getShapeDrawer(t){return _.get(t)}static getSupportedShapes(){return _.keys()}}class N{constructor(t,i){this.position=t,this.particle=i}}class W{constructor(t,i){this.rectangle=t,this.capacity=i,this.points=[],this.divided=!1}subdivide(){const t=this.rectangle.position.x,i=this.rectangle.position.y,e=this.rectangle.size.width,o=this.rectangle.size.height,s=this.capacity;this.northEast=new W(new I(t,i,e/2,o/2),s),this.northWest=new W(new I(t+e/2,i,e/2,o/2),s),this.southEast=new W(new I(t,i+o/2,e/2,o/2),s),this.southWest=new W(new I(t+e/2,i+o/2,e/2,o/2),s),this.divided=!0}insert(t){var i,e,o,s,n;return!!this.rectangle.contains(t.position)&&(this.points.length<this.capacity?(this.points.push(t),!0):(this.divided||this.subdivide(),null!==(n=(null===(i=this.northEast)||void 0===i?void 0:i.insert(t))||(null===(e=this.northWest)||void 0===e?void 0:e.insert(t))||(null===(o=this.southEast)||void 0===o?void 0:o.insert(t))||(null===(s=this.southWest)||void 0===s?void 0:s.insert(t)))&&void 0!==n&&n))}queryCircle(t,i){return this.query(new D(t.x,t.y,i))}queryCircleWarp(t,i,e){const o=e,s=e;return this.query(new L(t.x,t.y,i,void 0!==o.canvas?o.canvas.size:s))}queryRectangle(t,i){return this.query(new I(t.x,t.y,i.width,i.height))}query(t,i){var e,o,s,n;const a=null!=i?i:[];if(!t.intersects(this.rectangle))return[];for(const i of this.points)t.contains(i.position)&&a.push(i.particle);return this.divided&&(null===(e=this.northEast)||void 0===e||e.query(t,a),null===(o=this.northWest)||void 0===o||o.query(t,a),null===(s=this.southEast)||void 0===s||s.query(t,a),null===(n=this.southWest)||void 0===n||n.query(t,a)),a}}var U=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};class G{getSidesCount(){return 12}init(t){var i;return U(this,void 0,void 0,(function*(){const e=t.options;if(P.isInArray(g.char,e.particles.shape.type)||P.isInArray(g.character,e.particles.shape.type)){const t=null!==(i=e.particles.shape.options[g.character])&&void 0!==i?i:e.particles.shape.options[g.char];if(t instanceof Array)for(const i of t)yield P.loadFont(i);else void 0!==t&&(yield P.loadFont(t))}}))}draw(t,i,e){const o=i.shapeData;if(void 0===o)return;const s=o.value;if(void 0===s)return;const n=i;void 0===n.text&&(n.text=s instanceof Array?P.itemFromArray(s,i.randomIndexData):s);const a=n.text,r=o.style,l=o.weight,c=2*Math.round(e),d=o.font,h=i.fill,u=a.length*e/2;t.font=`${r} ${l} ${c}px "${d}"`;const v={x:-u,y:e/2};h?t.fillText(a,v.x,v.y):t.strokeText(a,v.x,v.y)}}var $=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};class j{constructor(){this.images=[]}getSidesCount(){return 12}getImages(t){const i=this.images.filter((i=>i.id===t.id));return i.length?i[0]:(this.images.push({id:t.id,images:[]}),this.getImages(t))}addImage(t,i){const e=this.getImages(t);null==e||e.images.push(i)}init(t){var i;return $(this,void 0,void 0,(function*(){const e=t.options.particles.shape;if(!P.isInArray(g.image,e.type)&&!P.isInArray(g.images,e.type))return;const o=null!==(i=e.options[g.images])&&void 0!==i?i:e.options[g.image];if(o instanceof Array)for(const i of o)yield this.loadImageShape(t,i);else yield this.loadImageShape(t,o)}))}destroy(){this.images=[]}loadImageShape(t,i){return $(this,void 0,void 0,(function*(){try{const e=i.replaceColor?yield P.downloadSvgImage(i.src):yield P.loadImage(i.src);this.addImage(t,e)}catch(t){console.warn(`tsParticles error - ${i.src} not found`)}}))}draw(t,i,e,o){var s,n;if(!t)return;const a=i.image,r=null===(s=null==a?void 0:a.data)||void 0===s?void 0:s.element;if(!r)return;const l=null!==(n=null==a?void 0:a.ratio)&&void 0!==n?n:1,c={x:-e,y:-e};(null==a?void 0:a.data.svgData)&&(null==a?void 0:a.replaceColor)||(t.globalAlpha=o),t.drawImage(r,c.x,c.y,2*e,2*e/l),(null==a?void 0:a.data.svgData)&&(null==a?void 0:a.replaceColor)||(t.globalAlpha=1)}}class X{getSidesCount(){return 1}draw(t,i,e){t.moveTo(0,-e/2),t.lineTo(0,e/2)}}class Y{getSidesCount(){return 12}draw(t,i,e){t.arc(0,0,e,0,2*Math.PI,!1)}}class J{getSidesCount(t){var i,e;const o=t.shapeData;return null!==(e=null!==(i=null==o?void 0:o.sides)&&void 0!==i?i:null==o?void 0:o.nb_sides)&&void 0!==e?e:5}draw(t,i,e){const o=this.getCenter(i,e),s=this.getSidesData(i,e),n=s.count.numerator*s.count.denominator,a=s.count.numerator/s.count.denominator,r=180*(a-2)/a,l=Math.PI-Math.PI*r/180;if(t){t.beginPath(),t.translate(o.x,o.y),t.moveTo(0,0);for(let i=0;i<n;i++)t.lineTo(s.length,0),t.translate(s.length,0),t.rotate(l)}}}class Q extends J{getSidesCount(){return 3}getSidesData(t,i){return{count:{denominator:2,numerator:3},length:2*i}}getCenter(t,i){return{x:-i,y:i/1.66}}}class Z{getSidesCount(t){var i,e;const o=t.shapeData;return null!==(e=null!==(i=null==o?void 0:o.sides)&&void 0!==i?i:null==o?void 0:o.nb_sides)&&void 0!==e?e:5}draw(t,i,e){var o;const s=i.shapeData,n=this.getSidesCount(i),a=null!==(o=null==s?void 0:s.inset)&&void 0!==o?o:2;t.moveTo(0,0-e);for(let i=0;i<n;i++)t.rotate(Math.PI/n),t.lineTo(0,0-e*a),t.rotate(Math.PI/n),t.lineTo(0,0-e)}}class K extends J{getSidesData(t,i){var e,o;const s=t.shapeData,n=null!==(o=null!==(e=null==s?void 0:s.sides)&&void 0!==e?e:null==s?void 0:s.nb_sides)&&void 0!==o?o:5;return{count:{denominator:1,numerator:n},length:2.66*i/(n/3)}}getCenter(t,i){return{x:-i/(this.getSidesCount(t)/3.5),y:-i/.76}}}class tt{constructor(t){this.container=t,this.size={height:0,width:0},this.context=null,this.generatedCanvas=!1}init(){var t,i,e,o,s,n,a,r,l,c,d,h;this.resize();const u=this.container.options,v=this.element;v&&(u.backgroundMode.enable?(this.originalStyle=P.deepExtend({},v.style),v.style.position="fixed",v.style.zIndex=u.backgroundMode.zIndex.toString(10),v.style.top="0",v.style.left="0",v.style.width="100%",v.style.height="100%"):(v.style.position=null!==(i=null===(t=this.originalStyle)||void 0===t?void 0:t.position)&&void 0!==i?i:"",v.style.zIndex=null!==(o=null===(e=this.originalStyle)||void 0===e?void 0:e.zIndex)&&void 0!==o?o:"",v.style.top=null!==(n=null===(s=this.originalStyle)||void 0===s?void 0:s.top)&&void 0!==n?n:"",v.style.left=null!==(r=null===(a=this.originalStyle)||void 0===a?void 0:a.left)&&void 0!==r?r:"",v.style.width=null!==(c=null===(l=this.originalStyle)||void 0===l?void 0:l.width)&&void 0!==c?c:"",v.style.height=null!==(h=null===(d=this.originalStyle)||void 0===d?void 0:d.height)&&void 0!==h?h:""));const p=u.backgroundMask.cover,y=p.color,f=u.particles.move.trail,m=A.colorToRgb(y);this.coverColor=void 0!==m?{r:m.r,g:m.g,b:m.b,a:p.opacity}:void 0,this.trailFillColor=A.colorToRgb(f.fillColor),this.initBackground(),this.paint()}loadCanvas(t,i){var e;t.className||(t.className=S.canvasClass),this.generatedCanvas&&(null===(e=this.element)||void 0===e||e.remove()),this.generatedCanvas=null!=i?i:this.generatedCanvas,this.element=t,this.originalStyle=P.deepExtend({},this.element.style),this.size.height=t.offsetHeight,this.size.width=t.offsetWidth,this.context=this.element.getContext("2d"),this.container.retina.init(),this.initBackground()}destroy(){var t;this.generatedCanvas&&(null===(t=this.element)||void 0===t||t.remove()),this.context&&E.clear(this.context,this.size)}resize(){this.element&&(this.element.width=this.size.width,this.element.height=this.size.height)}paint(){const t=this.container.options;this.context&&(t.backgroundMask.enable&&t.backgroundMask.cover&&this.coverColor?(E.clear(this.context,this.size),this.paintBase(A.getStyleFromRgb(this.coverColor,this.coverColor.a))):this.paintBase())}clear(){const t=this.container.options,i=t.particles.move.trail;t.backgroundMask.enable?this.paint():i.enable&&i.length>0&&this.trailFillColor?this.paintBase(A.getStyleFromRgb(this.trailFillColor,1/i.length)):this.context&&E.clear(this.context,this.size)}windowResize(){if(!this.element)return;const t=this.container;t.canvas.initSize(),t.particles.setDensity();for(const[,i]of t.plugins)void 0!==i.resize&&i.resize()}initSize(){if(!this.element)return;const t=this.container,i=t.retina.pixelRatio;t.canvas.size.width=this.element.offsetWidth*i,t.canvas.size.height=this.element.offsetHeight*i,this.element.width=t.canvas.size.width,this.element.height=t.canvas.size.height}drawConnectLine(t,i){var e;const o=this.lineStyle(t,i);if(!o)return;const s=this.context;if(!s)return;const n=t.getPosition(),a=i.getPosition();E.drawConnectLine(s,null!==(e=t.linksWidth)&&void 0!==e?e:this.container.retina.linksWidth,o,n,a)}drawGrabLine(t,i,e,o){var s;const n=this.container,a=n.canvas.context;if(!a)return;const r=t.getPosition();E.drawGrabLine(a,null!==(s=t.linksWidth)&&void 0!==s?s:n.retina.linksWidth,r,o,i,e)}drawParticleShadow(t,i){this.context&&E.drawParticleShadow(this.container,this.context,t,i)}drawLinkTriangle(t,i,e){var o;const s=this.container,n=s.options,a=i.destination,l=e.destination,c=t.particlesOptions.links.triangles,d=null!==(o=c.opacity)&&void 0!==o?o:(i.opacity+e.opacity)/2;if(d<=0)return;const h=t.getPosition(),u=a.getPosition(),v=l.getPosition(),p=this.context;if(!p)return;if(r.getDistance(h,u)>s.retina.linksDistance||r.getDistance(v,u)>s.retina.linksDistance||r.getDistance(v,h)>s.retina.linksDistance)return;let y=A.colorToRgb(c.color);if(!y){const i=t.particlesOptions.links,e=void 0!==i.id?s.particles.linksColors.get(i.id):s.particles.linksColor;y=A.getLinkColor(t,a,e)}y&&E.drawLinkTriangle(p,h,u,v,n.backgroundMask.enable,n.backgroundMask.composite,y,d)}drawLinkLine(t,i){var e,o;const s=this.container,n=s.options,a=i.destination;let r=i.opacity;const l=t.getPosition(),c=a.getPosition(),d=this.context;if(!d)return;let h;const u=t.particlesOptions.twinkle.lines;if(u.enable){const t=u.frequency,i=A.colorToRgb(u.color);Math.random()<t&&void 0!==i&&(h=i,r=u.opacity)}if(!h){const i=t.particlesOptions.links,e=void 0!==i.id?s.particles.linksColors.get(i.id):s.particles.linksColor;h=A.getLinkColor(t,a,e)}if(!h)return;const v=null!==(e=t.linksWidth)&&void 0!==e?e:s.retina.linksWidth,p=null!==(o=t.linksDistance)&&void 0!==o?o:s.retina.linksDistance;E.drawLinkLine(d,v,l,c,p,s.canvas.size,t.particlesOptions.links.warp,n.backgroundMask.enable,n.backgroundMask.composite,h,r,t.particlesOptions.links.shadow)}drawParticle(t,i){var e,o,s,n;if(!1===(null===(e=t.image)||void 0===e?void 0:e.loaded)||t.spawning||t.destroyed)return;const a=t.getFillColor(),r=null!==(o=t.getStrokeColor())&&void 0!==o?o:a;if(!a&&!r)return;const l=this.container.options,c=t.particlesOptions.twinkle.particles,d=c.frequency,h=A.colorToRgb(c.color),u=c.enable&&Math.random()<d,v=t.getRadius(),p=u?c.opacity:null!==(s=t.bubble.opacity)&&void 0!==s?s:t.opacity.value,y=t.infecter.infectionStage,f=l.infection.stages,m=void 0!==y?f[y].color:void 0,g=A.colorToRgb(m),b=u&&void 0!==h?h:null!=g?g:a?A.hslToRgb(a):void 0,w=u&&void 0!==h?h:null!=g?g:r?A.hslToRgb(r):void 0,x=void 0!==b?A.getStyleFromRgb(b,p):void 0;if(!this.context||!x&&!w)return;const k=void 0!==w?A.getStyleFromRgb(w,null!==(n=t.stroke.opacity)&&void 0!==n?n:p):x;this.drawParticleLinks(t),v>0&&E.drawParticle(this.container,this.context,t,i,x,k,l.backgroundMask.enable,l.backgroundMask.composite,v,p,t.particlesOptions.shadow)}drawParticleLinks(t){if(!this.context)return;const i=this.container,e=i.particles,o=t.particlesOptions;if(t.links.length>0){this.context.save();const s=t.links.filter((e=>i.particles.getLinkFrequency(t,e.destination)<=o.links.frequency));for(const n of s){const a=n.destination;if(o.links.triangles.enable){const r=s.map((t=>t.destination)),l=a.links.filter((t=>i.particles.getLinkFrequency(a,t.destination)<=a.particlesOptions.links.frequency&&r.indexOf(t.destination)>=0));if(l.length)for(const i of l){const s=i.destination;e.getTriangleFrequency(t,a,s)>o.links.triangles.frequency||this.drawLinkTriangle(t,n,i)}}n.opacity>0&&i.retina.linksWidth>0&&this.drawLinkLine(t,n)}this.context.restore()}}drawPlugin(t,i){this.context&&E.drawPlugin(this.context,t,i)}drawLight(t){this.context&&E.drawLight(this.container,this.context,t)}paintBase(t){this.context&&E.paintBase(this.context,this.size,t)}lineStyle(t,i){const e=this.container.options.interactivity.modes.connect;if(this.context)return E.gradient(this.context,t,i,e.links.opacity)}initBackground(){const t=this.container.options.background,i=this.element;if(!i)return;const e=i.style;if(t.color){const i=A.colorToRgb(t.color);i&&(e.backgroundColor=A.getStyleFromRgb(i,t.opacity))}t.image&&(e.backgroundImage=t.image),t.position&&(e.backgroundPosition=t.position),t.repeat&&(e.backgroundRepeat=t.repeat),t.size&&(e.backgroundSize=t.size)}}function it(t,i,e,o,s){switch(i){case f.max:e>=s&&t.destroy();break;case f.min:e<=o&&t.destroy()}}class et{constructor(t,i){this.container=t,this.particle=i}update(t){this.particle.destroyed||(this.updateLife(t),this.particle.destroyed||this.particle.spawning||(this.updateOpacity(t),this.updateSize(t),this.updateAngle(t),this.updateColor(t),this.updateStrokeColor(t),this.updateOutModes(t)))}updateLife(t){const i=this.particle;let e=!1;if(i.spawning&&(i.lifeDelayTime+=t.value,i.lifeDelayTime>=i.lifeDelay&&(e=!0,i.spawning=!1,i.lifeDelayTime=0,i.lifeTime=0)),-1!==i.lifeDuration&&!i.spawning&&(e?i.lifeTime=0:i.lifeTime+=t.value,i.lifeTime>=i.lifeDuration)){if(i.lifeTime=0,i.livesRemaining>0&&i.livesRemaining--,0===i.livesRemaining)return void i.destroy();const t=this.container.canvas.size;i.position.x=r.randomInRange(0,t.width),i.position.y=r.randomInRange(0,t.height),i.spawning=!0,i.lifeDelayTime=0,i.lifeTime=0;const e=i.particlesOptions.life;i.lifeDelay=1e3*r.getValue(e.delay),i.lifeDuration=1e3*r.getValue(e.duration)}}updateOpacity(t){var i,e;const o=this.particle,s=o.particlesOptions.opacity.anim,n=s.minimumValue,a=o.particlesOptions.opacity.value;if(s.enable){switch(o.opacity.status){case y.increasing:o.opacity.value>=a?o.opacity.status=y.decreasing:o.opacity.value+=(null!==(i=o.opacity.velocity)&&void 0!==i?i:0)*t.factor;break;case y.decreasing:o.opacity.value<=n?o.opacity.status=y.increasing:o.opacity.value-=(null!==(e=o.opacity.velocity)&&void 0!==e?e:0)*t.factor}it(o,s.destroy,o.opacity.value,n,a),o.destroyed||(o.opacity.value=r.clamp(o.opacity.value,n,a))}}updateSize(t){var i,e;const o=this.container,s=this.particle,n=s.particlesOptions.size.animation,a=(null!==(i=s.size.velocity)&&void 0!==i?i:0)*t.factor,l=null!==(e=s.sizeValue)&&void 0!==e?e:o.retina.sizeValue,c=n.minimumValue*o.retina.pixelRatio;if(n.enable){switch(s.size.status){case y.increasing:s.size.value>=l?s.size.status=y.decreasing:s.size.value+=a;break;case y.decreasing:s.size.value<=c?s.size.status=y.increasing:s.size.value-=a}it(s,n.destroy,s.size.value,c,l),s.destroyed||(s.size.value=r.clamp(s.size.value,c,l))}}updateAngle(t){var i;const e=this.particle,o=e.particlesOptions.rotate,s=o.animation,n=(null!==(i=e.rotate.velocity)&&void 0!==i?i:0)*t.factor,a=2*Math.PI;if(o.path)e.pathAngle=Math.atan2(e.velocity.vertical,e.velocity.horizontal);else if(s.enable)switch(e.rotate.status){case y.increasing:e.rotate.value+=n,e.rotate.value>a&&(e.rotate.value-=a);break;case y.decreasing:default:e.rotate.value-=n,e.rotate.value<0&&(e.rotate.value+=a)}}updateColor(t){var i;const e=this.particle;void 0!==e.color.value&&e.particlesOptions.color.animation.enable&&(e.color.value.h+=(null!==(i=e.color.velocity)&&void 0!==i?i:0)*t.factor,e.color.value.h>360&&(e.color.value.h-=360))}updateStrokeColor(t){var i,e;const o=this.particle,s=o.stroke.color;"string"!=typeof s&&void 0!==s&&void 0!==o.strokeColor.value&&s.animation.enable&&(o.strokeColor.value.h+=(null!==(e=null!==(i=o.strokeColor.velocity)&&void 0!==i?i:o.color.velocity)&&void 0!==e?e:0)*t.factor,o.strokeColor.value.h>360&&(o.strokeColor.value.h-=360))}updateOutModes(t){var i,e,o,n;const a=this.particle.particlesOptions.move.outModes;this.updateOutMode(t,null!==(i=a.bottom)&&void 0!==i?i:a.default,s.bottom),this.updateOutMode(t,null!==(e=a.left)&&void 0!==e?e:a.default,s.left),this.updateOutMode(t,null!==(o=a.right)&&void 0!==o?o:a.default,s.right),this.updateOutMode(t,null!==(n=a.top)&&void 0!==n?n:a.default,s.top)}updateOutMode(t,i,e){const o=this.container,s=this.particle;switch(i){case u.bounce:case u.bounceVertical:case u.bounceHorizontal:case"bounceVertical":case"bounceHorizontal":this.updateBounce(t,e,i);break;case u.destroy:P.isPointInside(s.position,o.canvas.size,s.getRadius(),e)||o.particles.remove(s);break;case u.out:P.isPointInside(s.position,o.canvas.size,s.getRadius(),e)||this.fixOutOfCanvasPosition(e);break;case u.none:this.bounceNone(e)}}fixOutOfCanvasPosition(t){const i=this.container,e=this.particle,o=e.particlesOptions.move.warp,n=i.canvas.size,a={bottom:n.height+e.getRadius()-e.offset.y,left:-e.getRadius()-e.offset.x,right:n.width+e.getRadius()+e.offset.x,top:-e.getRadius()-e.offset.y},r=e.getRadius(),l=P.calculateBounds(e.position,r);t===s.right&&l.left>n.width-e.offset.x?(e.position.x=a.left,o||(e.position.y=Math.random()*n.height)):t===s.left&&l.right<-e.offset.x&&(e.position.x=a.right,o||(e.position.y=Math.random()*n.height)),t===s.bottom&&l.top>n.height-e.offset.y?(o||(e.position.x=Math.random()*n.width),e.position.y=a.top):t===s.top&&l.bottom<-e.offset.y&&(o||(e.position.x=Math.random()*n.width),e.position.y=a.bottom)}updateBounce(t,i,e){const o=this.container,n=this.particle;let a=!1;for(const[,e]of o.plugins)if(void 0!==e.particleBounce&&(a=e.particleBounce(n,t,i)),a)break;if(a)return;const l=n.getPosition(),c=n.offset,d=n.getRadius(),h=P.calculateBounds(l,d),v=o.canvas.size;!function(t){if(t.outMode===u.bounce||t.outMode===u.bounceHorizontal||"bounceHorizontal"===t.outMode){const i=t.particle.velocity.horizontal;let e=!1;if(t.direction===s.right&&t.bounds.right>=t.canvasSize.width&&i>0||t.direction===s.left&&t.bounds.left<=0&&i<0){const i=r.getValue(t.particle.particlesOptions.bounce.horizontal);t.particle.velocity.horizontal*=-i,e=!0}if(e){const i=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width?t.particle.position.x=t.canvasSize.width-i:t.bounds.left<=0&&(t.particle.position.x=i)}}}({particle:n,outMode:e,direction:i,bounds:h,canvasSize:v,offset:c,size:d}),function(t){if(t.outMode===u.bounce||t.outMode===u.bounceVertical||"bounceVertical"===t.outMode){const i=t.particle.velocity.vertical;let e=!1;if(t.direction===s.bottom&&t.bounds.bottom>=t.canvasSize.height&&i>0||t.direction===s.top&&t.bounds.top<=0&&i<0){const i=r.getValue(t.particle.particlesOptions.bounce.vertical);t.particle.velocity.vertical*=-i,e=!0}if(e){const i=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height?t.particle.position.y=t.canvasSize.height-i:t.bounds.top<=0&&(t.particle.position.y=i)}}}({particle:n,outMode:e,direction:i,bounds:h,canvasSize:v,offset:c,size:d})}bounceNone(t){const i=this.particle;if(i.particlesOptions.move.distance)return;const e=i.particlesOptions.move.gravity,o=this.container;if(e.enable){const n=i.position;(e.acceleration>=0&&n.y>o.canvas.size.height&&t===s.bottom||e.acceleration<0&&n.y<0&&t===s.top)&&o.particles.remove(i)}else P.isPointInside(i.position,o.canvas.size,i.getRadius(),t)||o.particles.remove(i)}}class ot{constructor(){this.value="#fff"}static create(t,i){const e=null!=t?t:new ot;return void 0!==i&&e.load("string"==typeof i?{value:i}:i),e}load(t){void 0!==(null==t?void 0:t.value)&&(this.value=t.value)}}class st{constructor(){this.blur=5,this.color=new ot,this.enable=!1,this.color.value="#00ff00"}load(t){void 0!==t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=ot.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable))}}class nt{constructor(){this.enable=!1,this.frequency=1}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity))}}class at{constructor(){this.blink=!1,this.color=new ot,this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new st,this.triangles=new nt,this.width=1,this.warp=!1}load(t){void 0!==t&&(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=ot.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0!==t.warp&&(this.warp=t.warp))}}class rt{constructor(){this.enable=!1,this.rotate={x:3e3,y:3e3}}get rotateX(){return this.rotate.x}set rotateX(t){this.rotate.x=t}get rotateY(){return this.rotate.y}set rotateY(t){this.rotate.y=t}load(t){var i,e,o,s;if(void 0===t)return;void 0!==t.enable&&(this.enable=t.enable);const n=null!==(e=null===(i=t.rotate)||void 0===i?void 0:i.x)&&void 0!==e?e:t.rotateX;void 0!==n&&(this.rotate.x=n);const a=null!==(s=null===(o=t.rotate)||void 0===o?void 0:o.y)&&void 0!==s?s:t.rotateY;void 0!==a&&(this.rotate.y=a)}}class lt{constructor(){this.enable=!1,this.length=10,this.fillColor=new ot,this.fillColor.value="#000000"}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),this.fillColor=ot.create(this.fillColor,t.fillColor),void 0!==t.length&&(this.length=t.length))}}class ct{constructor(){this.enable=!1,this.minimumValue=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue))}}class dt{constructor(){this.random=new ct,this.value=0}load(t){t&&("boolean"==typeof t.random?this.random.enable=t.random:this.random.load(t.random),void 0!==t.value&&(this.value=t.value))}}class ht extends dt{constructor(){super()}}class ut{constructor(){this.delay=new ht,this.enable=!1}load(t){void 0!==t&&(this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class vt{constructor(){this.offset=45,this.value=90}load(t){void 0!==t&&(void 0!==t.offset&&(this.offset=t.offset),void 0!==t.value&&(this.value=t.value))}}class pt{constructor(){this.acceleration=9.81,this.enable=!1,this.maxSpeed=50}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=t.acceleration),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed))}}class yt{constructor(){this.default=u.out}load(t){var i,e,o,s;t&&(void 0!==t.default&&(this.default=t.default),this.bottom=null!==(i=t.bottom)&&void 0!==i?i:t.default,this.left=null!==(e=t.left)&&void 0!==e?e:t.default,this.right=null!==(o=t.right)&&void 0!==o?o:t.default,this.top=null!==(s=t.top)&&void 0!==s?s:t.default)}}class ft{constructor(){this.angle=new vt,this.attract=new rt,this.direction=n.none,this.distance=0,this.enable=!1,this.gravity=new pt,this.noise=new ut,this.outModes=new yt,this.random=!1,this.size=!1,this.speed=2,this.straight=!1,this.trail=new lt,this.vibrate=!1,this.warp=!1}get collisions(){return!1}set collisions(t){}get bounce(){return this.collisions}set bounce(t){this.collisions=t}get out_mode(){return this.outMode}set out_mode(t){this.outMode=t}get outMode(){return this.outModes.default}set outMode(t){this.outModes.default=t}load(t){var i,e;if(void 0===t)return;void 0!==t.angle&&("number"==typeof t.angle?this.angle.value=t.angle:this.angle.load(t.angle)),this.attract.load(t.attract),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity),this.noise.load(t.noise);const o=null!==(i=t.outMode)&&void 0!==i?i:t.out_mode;void 0===t.outModes&&void 0===o||("string"==typeof t.outModes||void 0===t.outModes&&void 0!==o?this.outModes.load({default:null!==(e=t.outModes)&&void 0!==e?e:o}):this.outModes.load(t.outModes)),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class mt{constructor(){this.enable=!1,this.area=800,this.factor=1e3}get value_area(){return this.area}set value_area(t){this.area=t}load(t){var i;if(void 0===t)return;void 0!==t.enable&&(this.enable=t.enable);const e=null!==(i=t.area)&&void 0!==i?i:t.value_area;void 0!==e&&(this.area=e),void 0!==t.factor&&(this.factor=t.factor)}}class gt{constructor(){this.density=new mt,this.limit=0,this.value=100}get max(){return this.limit}set max(t){this.limit=t}load(t){var i;if(void 0===t)return;this.density.load(t.density);const e=null!==(i=t.limit)&&void 0!==i?i:t.max;void 0!==e&&(this.limit=e),void 0!==t.value&&(this.value=t.value)}}class bt{constructor(){this.destroy=f.none,this.enable=!1,this.minimumValue=0,this.speed=2,this.startValue=b.random,this.sync=!1}get opacity_min(){return this.minimumValue}set opacity_min(t){this.minimumValue=t}load(t){var i;if(void 0===t)return;void 0!==t.destroy&&(this.destroy=t.destroy),void 0!==t.enable&&(this.enable=t.enable);const e=null!==(i=t.minimumValue)&&void 0!==i?i:t.opacity_min;void 0!==e&&(this.minimumValue=e),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.startValue&&(this.startValue=t.startValue),void 0!==t.sync&&(this.sync=t.sync)}}class wt extends dt{constructor(){super(),this.animation=new bt,this.random.minimumValue=.1,this.value=1}get anim(){return this.animation}set anim(t){this.animation=t}load(t){var i;t&&(super.load(t),this.animation.load(null!==(i=t.animation)&&void 0!==i?i:t.anim))}}class xt{constructor(){this.options={},this.type=g.circle}get image(){var t;return null!==(t=this.options[g.image])&&void 0!==t?t:this.options[g.images]}set image(t){this.options[g.image]=t,this.options[g.images]=t}get custom(){return this.options}set custom(t){this.options=t}get images(){return this.image instanceof Array?this.image:[this.image]}set images(t){this.image=t}get stroke(){return[]}set stroke(t){}get character(){var t;return null!==(t=this.options[g.character])&&void 0!==t?t:this.options[g.char]}set character(t){this.options[g.character]=t,this.options[g.char]=t}get polygon(){var t;return null!==(t=this.options[g.polygon])&&void 0!==t?t:this.options[g.star]}set polygon(t){this.options[g.polygon]=t,this.options[g.star]=t}load(t){var i,e,o;if(void 0===t)return;const s=null!==(i=t.options)&&void 0!==i?i:t.custom;if(void 0!==s)for(const t in s){const i=s[t];void 0!==i&&(this.options[t]=P.deepExtend(null!==(e=this.options[t])&&void 0!==e?e:{},i))}this.loadShape(t.character,g.character,g.char,!0),this.loadShape(t.polygon,g.polygon,g.star,!1),this.loadShape(null!==(o=t.image)&&void 0!==o?o:t.images,g.image,g.images,!0),void 0!==t.type&&(this.type=t.type)}loadShape(t,i,e,o){var s,n,a,r;void 0!==t&&(t instanceof Array?(this.options[i]instanceof Array||(this.options[i]=[],this.options[e]&&!o||(this.options[e]=[])),this.options[i]=P.deepExtend(null!==(s=this.options[i])&&void 0!==s?s:[],t),this.options[e]&&!o||(this.options[e]=P.deepExtend(null!==(n=this.options[e])&&void 0!==n?n:[],t))):(this.options[i]instanceof Array&&(this.options[i]={},this.options[e]&&!o||(this.options[e]={})),this.options[i]=P.deepExtend(null!==(a=this.options[i])&&void 0!==a?a:{},t),this.options[e]&&!o||(this.options[e]=P.deepExtend(null!==(r=this.options[e])&&void 0!==r?r:{},t))))}}class kt{constructor(){this.destroy=f.none,this.enable=!1,this.minimumValue=0,this.speed=5,this.startValue=b.random,this.sync=!1}get size_min(){return this.minimumValue}set size_min(t){this.minimumValue=t}load(t){var i;if(void 0===t)return;void 0!==t.destroy&&(this.destroy=t.destroy),void 0!==t.enable&&(this.enable=t.enable);const e=null!==(i=t.minimumValue)&&void 0!==i?i:t.size_min;void 0!==e&&(this.minimumValue=e),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.startValue&&(this.startValue=t.startValue),void 0!==t.sync&&(this.sync=t.sync)}}class zt extends dt{constructor(){super(),this.animation=new kt,this.random.minimumValue=1,this.value=3}get anim(){return this.animation}set anim(t){this.animation=t}load(t){var i;if(!t)return;super.load(t);const e=null!==(i=t.animation)&&void 0!==i?i:t.anim;void 0!==e&&this.animation.load(e)}}class Mt{constructor(){this.enable=!1,this.speed=0,this.sync=!1}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.sync&&(this.sync=t.sync))}}class Pt extends dt{constructor(){super(),this.animation=new Mt,this.direction=a.clockwise,this.path=!1}load(t){t&&(super.load(t),void 0!==t.direction&&(this.direction=t.direction),this.animation.load(t.animation),void 0!==t.path&&(this.path=t.path))}}class St{constructor(){this.blur=0,this.color=new ot,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000000"}load(t){void 0!==t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=ot.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}class Rt{constructor(){this.enable=!1,this.speed=1,this.sync=!0}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.sync&&(this.sync=t.sync))}}class Ct extends ot{constructor(){super(),this.animation=new Rt}static create(t,i){const e=null!=t?t:new Ct;return void 0!==i&&e.load("string"==typeof i?{value:i}:i),e}load(t){super.load(t),this.animation.load(null==t?void 0:t.animation)}}class At{constructor(){this.width=0}load(t){void 0!==t&&(void 0!==t.color&&(this.color=Ct.create(this.color,t.color)),void 0!==t.width&&(this.width=t.width),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Tt extends dt{constructor(){super(),this.random.minimumValue=.1,this.value=1}}class Et{constructor(){this.horizontal=new Tt,this.vertical=new Tt}load(t){t&&(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class Ot{constructor(){this.bounce=new Et,this.enable=!1,this.mode=h.bounce}load(t){void 0!==t&&(this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}class Dt{constructor(){this.enable=!1,this.frequency=.05,this.opacity=1}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity))}}class It{constructor(){this.lines=new Dt,this.particles=new Dt}load(t){void 0!==t&&(this.lines.load(t.lines),this.particles.load(t.particles))}}class Lt extends dt{constructor(){super(),this.sync=!1}load(t){t&&(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class Ht extends dt{constructor(){super(),this.random.minimumValue=1e-4,this.sync=!1}load(t){void 0!==t&&(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class Ft{constructor(){this.count=0,this.delay=new Lt,this.duration=new Ht}load(t){void 0!==t&&(void 0!==t.count&&(this.count=t.count),this.delay.load(t.delay),this.duration.load(t.duration))}}class Vt{constructor(){this.bounce=new Et,this.collisions=new Ot,this.color=new Ct,this.life=new Ft,this.links=new at,this.move=new ft,this.number=new gt,this.opacity=new wt,this.reduceDuplicates=!1,this.rotate=new Pt,this.shadow=new St,this.shape=new xt,this.size=new zt,this.stroke=new At,this.twinkle=new It}get line_linked(){return this.links}set line_linked(t){this.links=t}get lineLinked(){return this.links}set lineLinked(t){this.links=t}load(t){var i,e,o,s,n,a,r;if(void 0===t)return;this.bounce.load(t.bounce),this.color=Ct.create(this.color,t.color),this.life.load(t.life);const l=null!==(e=null!==(i=t.links)&&void 0!==i?i:t.lineLinked)&&void 0!==e?e:t.line_linked;void 0!==l&&this.links.load(l),this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.rotate.load(t.rotate),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.twinkle.load(t.twinkle);const c=null!==(s=null===(o=t.move)||void 0===o?void 0:o.collisions)&&void 0!==s?s:null===(n=t.move)||void 0===n?void 0:n.bounce;void 0!==c&&(this.collisions.enable=c),this.collisions.load(t.collisions);const d=null!==(a=t.stroke)&&void 0!==a?a:null===(r=t.shape)||void 0===r?void 0:r.stroke;void 0!==d&&(d instanceof Array?this.stroke=d.map((t=>{const i=new At;return i.load(t),i})):(this.stroke instanceof Array&&(this.stroke=new At),this.stroke.load(d)))}}class qt{constructor(t){this.container=t}startInfection(t){t>this.container.options.infection.stages.length||t<0||(this.infectionDelay=0,this.infectionDelayStage=t)}updateInfectionStage(t){t>this.container.options.infection.stages.length||t<0||void 0!==this.infectionStage&&this.infectionStage>t||(this.infectionStage=t,this.infectionTime=0)}updateInfection(t){const i=this.container.options,e=i.infection,o=i.infection.stages,s=o.length;if(void 0!==this.infectionDelay&&void 0!==this.infectionDelayStage){const i=this.infectionDelayStage;if(i>s||i<0)return;this.infectionDelay>1e3*e.delay?(this.infectionStage=i,this.infectionTime=0,delete this.infectionDelay,delete this.infectionDelayStage):this.infectionDelay+=t}else delete this.infectionDelay,delete this.infectionDelayStage;if(void 0!==this.infectionStage&&void 0!==this.infectionTime){const i=o[this.infectionStage];void 0!==i.duration&&i.duration>=0&&this.infectionTime>1e3*i.duration?this.nextInfectionStage():this.infectionTime+=t}else delete this.infectionStage,delete this.infectionTime}nextInfectionStage(){const t=this.container.options,i=t.infection.stages.length;if(!(i<=0||void 0===this.infectionStage)&&(this.infectionTime=0,i<=++this.infectionStage)){if(t.infection.cure)return delete this.infectionStage,void delete this.infectionTime;this.infectionStage=0,this.infectionTime=0}}}class _t{constructor(t,i){this.container=t,this.particle=i}move(t){const i=this.particle;i.bubble.inRange=!1,i.links=[];for(const[,e]of this.container.plugins){if(i.destroyed)break;e.particleUpdate&&e.particleUpdate(i,t)}i.destroyed||(this.moveParticle(t),this.moveParallax())}moveParticle(t){var i,e;const o=this.particle,s=o.particlesOptions;if(!s.move.enable)return;const n=this.container,a=this.getProximitySpeedFactor(),l=(null!==(i=o.moveSpeed)&&void 0!==i?i:n.retina.moveSpeed)*n.retina.reduceFactor,c=null!==(e=o.sizeValue)&&void 0!==e?e:n.retina.sizeValue,d=l/2*(s.move.size?o.getRadius()/c:1)*a*t.factor;this.applyNoise(t);const h=s.move.gravity;h.enable&&(o.velocity.vertical+=h.acceleration*t.factor/(60*d));const u={horizontal:o.velocity.horizontal*d,vertical:o.velocity.vertical*d};h.enable&&u.vertical>=h.maxSpeed&&h.maxSpeed>0&&(u.vertical=h.maxSpeed,o.velocity.vertical=u.vertical/d),o.position.x+=u.horizontal,o.position.y+=u.vertical,s.move.vibrate&&(o.position.x+=Math.sin(o.position.x*Math.cos(o.position.y)),o.position.y+=Math.cos(o.position.y*Math.sin(o.position.x)));const v=o.initialPosition,p=r.getDistance(v,o.position);o.maxDistance&&(p>=o.maxDistance&&!o.misplaced?(o.misplaced=p>o.maxDistance,o.velocity.horizontal=o.velocity.vertical/2-o.velocity.horizontal,o.velocity.vertical=o.velocity.horizontal/2-o.velocity.vertical):p<o.maxDistance&&o.misplaced?o.misplaced=!1:o.misplaced&&((o.position.x<v.x&&o.velocity.horizontal<0||o.position.x>v.x&&o.velocity.horizontal>0)&&(o.velocity.horizontal*=-Math.random()),(o.position.y<v.y&&o.velocity.vertical<0||o.position.y>v.y&&o.velocity.vertical>0)&&(o.velocity.vertical*=-Math.random())))}applyNoise(t){const i=this.particle;if(!i.particlesOptions.move.noise.enable)return;const e=this.container;if(i.lastNoiseTime<=i.noiseDelay)return void(i.lastNoiseTime+=t.value);const o=e.noise.generate(i);i.velocity.horizontal+=Math.cos(o.angle)*o.length,i.velocity.horizontal=r.clamp(i.velocity.horizontal,-1,1),i.velocity.vertical+=Math.sin(o.angle)*o.length,i.velocity.vertical=r.clamp(i.velocity.vertical,-1,1),i.lastNoiseTime-=i.noiseDelay}moveParallax(){const t=this.container,i=t.options;if(P.isSsr()||!i.interactivity.events.onHover.parallax.enable)return;const e=this.particle,o=i.interactivity.events.onHover.parallax.force,s=t.interactivity.mouse.position;if(!s)return;const n=t.canvas.size.width/2,a=t.canvas.size.height/2,r=i.interactivity.events.onHover.parallax.smooth,l=e.getRadius()/o,c=(s.x-n)*l,d=(s.y-a)*l;e.offset.x+=(c-e.offset.x)/r,e.offset.y+=(d-e.offset.y)/r}getProximitySpeedFactor(){const t=this.container,i=t.options;if(!P.isInArray(d.slow,i.interactivity.events.onHover.mode))return 1;const e=this.container.interactivity.mouse.position;if(!e)return 1;const o=this.particle.getPosition(),s=r.getDistance(e,o),n=t.retina.slowModeRadius;if(s>n)return 1;return(s/n||0)/i.interactivity.modes.slow.factor}}class Bt{constructor(t,i,e,o){var s,n,l,c,d,h,u,v,p;this.id=t,this.container=i,this.links=[],this.fill=!0,this.close=!0,this.lastNoiseTime=0,this.destroyed=!1,this.misplaced=!1;const f=i.retina.pixelRatio,m=i.options,g=new Vt;g.load(m.particles);const w=g.shape.type,x=g.reduceDuplicates;if(this.shape=w instanceof Array?P.itemFromArray(w,this.id,x):w,null==o?void 0:o.shape){if(o.shape.type){const t=o.shape.type;this.shape=t instanceof Array?P.itemFromArray(t,this.id,x):t}const t=new xt;if(t.load(o.shape),this.shape){const i=t.options[this.shape];i&&(this.shapeData=P.deepExtend({},i instanceof Array?P.itemFromArray(i,this.id,x):i))}}else{const t=g.shape.options[this.shape];t&&(this.shapeData=P.deepExtend({},t instanceof Array?P.itemFromArray(t,this.id,x):t))}void 0!==o&&g.load(o),void 0!==(null===(s=this.shapeData)||void 0===s?void 0:s.particles)&&g.load(null===(n=this.shapeData)||void 0===n?void 0:n.particles),this.fill=null!==(c=null===(l=this.shapeData)||void 0===l?void 0:l.fill)&&void 0!==c?c:this.fill,this.close=null!==(h=null===(d=this.shapeData)||void 0===d?void 0:d.close)&&void 0!==h?h:this.close,this.particlesOptions=g,this.noiseDelay=1e3*r.getValue(this.particlesOptions.move.noise.delay),i.retina.initParticle(this);const k=this.particlesOptions.color,z=this.particlesOptions.size,M=r.getValue(z)*i.retina.pixelRatio,S="boolean"==typeof z.random?z.random:z.random.enable;this.size={value:M},this.direction=this.particlesOptions.move.direction,this.bubble={inRange:!1},this.initialVelocity=this.calculateVelocity(),this.velocity={horizontal:this.initialVelocity.horizontal,vertical:this.initialVelocity.vertical},this.pathAngle=Math.atan2(this.initialVelocity.vertical,this.initialVelocity.horizontal);const R=this.particlesOptions.rotate;this.rotate={value:(R.random.enable?360*Math.random():R.value)*Math.PI/180};let C=R.direction;if(C===a.random){C=Math.floor(2*Math.random())>0?a.counterClockwise:a.clockwise}switch(C){case a.counterClockwise:case"counterClockwise":this.rotate.status=y.decreasing;break;case a.clockwise:this.rotate.status=y.increasing}const T=this.particlesOptions.rotate.animation;T.enable&&(this.rotate.velocity=T.speed/360*i.retina.reduceFactor,T.sync||(this.rotate.velocity*=Math.random()));const E=this.particlesOptions.size.animation;if(E.enable){if(this.size.status=y.increasing,!S)switch(E.startValue){case b.min:this.size.value=E.minimumValue*f;break;case b.random:this.size.value=r.randomInRange(E.minimumValue*f,this.size.value);break;case b.max:default:this.size.status=y.decreasing}this.size.velocity=(null!==(u=this.sizeAnimationSpeed)&&void 0!==u?u:i.retina.sizeAnimationSpeed)/100*i.retina.reduceFactor,E.sync||(this.size.velocity*=Math.random())}this.color={value:A.colorToHsl(k,this.id,x)};const O=this.particlesOptions.color.animation;O.enable&&(this.color.velocity=O.speed/100*i.retina.reduceFactor,O.sync||(this.color.velocity*=Math.random())),this.position=this.calcPosition(this.container,e),this.initialPosition={x:this.position.x,y:this.position.y},this.offset={x:0,y:0};const D=this.particlesOptions.opacity,I="boolean"==typeof D.random?D.random:D.random.enable;this.opacity={value:r.getValue(D)};const L=D.animation;if(L.enable){if(this.opacity.status=y.increasing,!I)switch(L.startValue){case b.min:this.opacity.value=L.minimumValue;break;case b.random:this.opacity.value=r.randomInRange(L.minimumValue,this.opacity.value);break;case b.max:default:this.opacity.status=y.decreasing}this.opacity.velocity=L.speed/100*i.retina.reduceFactor,L.sync||(this.opacity.velocity*=Math.random())}this.sides=24;let H=i.drawers.get(this.shape);H||(H=B.getShapeDrawer(this.shape),H&&i.drawers.set(this.shape,H));const F=null==H?void 0:H.getSidesCount;F&&(this.sides=F(this));const V=this.loadImageShape(i,H);if(V&&(this.image=V.image,this.fill=V.fill,this.close=V.close),this.stroke=this.particlesOptions.stroke instanceof Array?P.itemFromArray(this.particlesOptions.stroke,this.id,x):this.particlesOptions.stroke,this.strokeWidth=this.stroke.width*i.retina.pixelRatio,this.strokeColor={value:null!==(v=A.colorToHsl(this.stroke.color))&&void 0!==v?v:this.color.value},"string"!=typeof this.stroke.color){const t=null===(p=this.stroke.color)||void 0===p?void 0:p.animation;t&&this.strokeColor&&(t.enable?(this.strokeColor.velocity=t.speed/100*i.retina.reduceFactor,t.sync||(this.strokeColor.velocity=this.strokeColor.velocity*Math.random())):this.strokeColor.velocity=0,t.enable&&!t.sync&&this.strokeColor.value&&(this.strokeColor.value.h=360*Math.random()))}const q=g.life;this.lifeDelay=i.retina.reduceFactor?r.getValue(q.delay)*(q.delay.sync?1:Math.random())/i.retina.reduceFactor*1e3:0,this.lifeDelayTime=0,this.lifeDuration=i.retina.reduceFactor?r.getValue(q.duration)*(q.duration.sync?1:Math.random())/i.retina.reduceFactor*1e3:0,this.lifeTime=0,this.livesRemaining=g.life.count,this.spawning=this.lifeDelay>0,this.lifeDuration<=0&&(this.lifeDuration=-1),this.livesRemaining<=0&&(this.livesRemaining=-1),this.shadowColor=A.colorToRgb(this.particlesOptions.shadow.color),this.updater=new et(i,this),this.infecter=new qt(i),this.mover=new _t(i,this)}move(t){this.mover.move(t)}update(t){this.updater.update(t)}draw(t){this.container.canvas.drawParticle(this,t)}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y}}getRadius(){return this.bubble.radius||this.size.value}getFillColor(){var t;return null!==(t=this.bubble.color)&&void 0!==t?t:this.color.value}getStrokeColor(){var t,i;return null!==(i=null!==(t=this.bubble.color)&&void 0!==t?t:this.strokeColor.value)&&void 0!==i?i:this.color.value}destroy(){this.destroyed=!0,this.bubble.inRange=!1,this.links=[]}calcPosition(t,i){var e,o;for(const[,e]of t.plugins){const t=void 0!==e.particlePosition?e.particlePosition(i,this):void 0;if(void 0!==t)return P.deepExtend({},t)}const s={x:null!==(e=null==i?void 0:i.x)&&void 0!==e?e:Math.random()*t.canvas.size.width,y:null!==(o=null==i?void 0:i.y)&&void 0!==o?o:Math.random()*t.canvas.size.height},n=this.particlesOptions.move.outMode;return(P.isInArray(n,u.bounce)||P.isInArray(n,u.bounceHorizontal))&&(s.x>t.canvas.size.width-2*this.size.value?s.x-=this.size.value:s.x<2*this.size.value&&(s.x+=this.size.value)),(P.isInArray(n,u.bounce)||P.isInArray(n,u.bounceVertical))&&(s.y>t.canvas.size.height-2*this.size.value?s.y-=this.size.value:s.y<2*this.size.value&&(s.y+=this.size.value)),s}calculateVelocity(){const t=r.getParticleBaseVelocity(this),i={horizontal:0,vertical:0},e=this.particlesOptions.move;let o,s=Math.PI/4;"number"==typeof e.angle?o=Math.PI/180*e.angle:(o=Math.PI/180*e.angle.value,s=Math.PI/180*e.angle.offset);const n={left:Math.sin(s+o/2)-Math.sin(s-o/2),right:Math.cos(s+o/2)-Math.cos(s-o/2)};return e.straight?(i.horizontal=t.x,i.vertical=t.y,e.random&&(i.horizontal+=r.randomInRange(n.left,n.right)/2,i.vertical+=r.randomInRange(n.left,n.right)/2)):(i.horizontal=t.x+r.randomInRange(n.left,n.right)/2,i.vertical=t.y+r.randomInRange(n.left,n.right)/2),i}loadImageShape(t,i){var e,o,s,n,a;if(this.shape!==g.image&&this.shape!==g.images)return;const r=i.getImages(t).images,l=this.shapeData,c=null!==(e=r.find((t=>t.source===l.src)))&&void 0!==e?e:r[0],d=this.getFillColor();let h;if(!c)return;if(void 0!==c.svgData&&l.replaceColor&&d){const t=A.replaceColorSvg(c,d,this.opacity.value),i=new Blob([t],{type:"image/svg+xml"}),e=URL||window.URL||window.webkitURL||window,s=e.createObjectURL(i),n=new Image;h={data:c,loaded:!1,ratio:l.width/l.height,replaceColor:null!==(o=l.replaceColor)&&void 0!==o?o:l.replace_color,source:l.src},n.addEventListener("load",(()=>{this.image&&(this.image.loaded=!0,c.element=n),e.revokeObjectURL(s)})),n.addEventListener("error",(()=>{e.revokeObjectURL(s),P.loadImage(l.src).then((t=>{this.image&&(c.element=t.element,this.image.loaded=!0)}))})),n.src=s}else h={data:c,loaded:!0,ratio:l.width/l.height,replaceColor:null!==(s=l.replaceColor)&&void 0!==s?s:l.replace_color,source:l.src};h.ratio||(h.ratio=1);return{image:h,fill:null!==(n=l.fill)&&void 0!==n?n:this.fill,close:null!==(a=l.close)&&void 0!==a?a:this.close}}}class Nt{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.interactivity.mouse,e=t.options.interactivity.events;if(!e.onHover.enable||!i.position)return!1;const o=e.onHover.mode;return P.isInArray(d.grab,o)}reset(){}interact(){var t;const i=this.container,e=i.options.interactivity;if(e.events.onHover.enable&&i.interactivity.status===S.mouseMoveEvent){const o=i.interactivity.mouse.position;if(void 0===o)return;const s=i.retina.grabModeDistance,n=i.particles.quadTree.queryCircle(o,s);for(const a of n){const n=a.getPosition(),l=r.getDistance(n,o);if(l<=s){const n=e.modes.grab.links,r=n.opacity,c=r-l*r/s;if(c>0){const e=null!==(t=n.color)&&void 0!==t?t:a.particlesOptions.links.color;if(!i.particles.grabLineColor){const t=i.options.interactivity.modes.grab.links;i.particles.grabLineColor=A.getLinkRandomColor(e,t.blink,t.consent)}const s=A.getLinkColor(a,void 0,i.particles.grabLineColor);if(void 0===s)return;i.canvas.drawGrabLine(a,s,c,o)}}}}}}class Wt{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.options,e=t.interactivity.mouse,o=i.interactivity.events,s=o.onDiv,n=P.isDivModeEnabled(c.repulse,s);if(!(n||o.onHover.enable&&e.position||o.onClick.enable&&e.clickPosition))return!1;const a=o.onHover.mode,r=o.onClick.mode;return P.isInArray(d.repulse,a)||P.isInArray(l.repulse,r)||n}reset(){}interact(){const t=this.container,i=t.options,e=t.interactivity.status===S.mouseMoveEvent,o=i.interactivity.events,s=o.onHover.enable,n=o.onHover.mode,a=o.onClick.enable,r=o.onClick.mode,h=o.onDiv;e&&s&&P.isInArray(d.repulse,n)?this.hoverRepulse():a&&P.isInArray(l.repulse,r)?this.clickRepulse():P.divModeExecute(c.repulse,h,((t,i)=>this.singleSelectorRepulse(t,i)))}singleSelectorRepulse(t,i){const e=this.container,o=document.querySelectorAll(t);o.length&&o.forEach((t=>{const o=t,s=e.retina.pixelRatio,n={x:(o.offsetLeft+o.offsetWidth/2)*s,y:(o.offsetTop+o.offsetHeight/2)*s},a=o.offsetWidth/2*s,r=i.type===w.circle?new D(n.x,n.y,a):new I(o.offsetLeft*s,o.offsetTop*s,o.offsetWidth*s,o.offsetHeight*s),l=e.options.interactivity.modes.repulse.divs,c=P.divMode(l,o);this.processRepulse(n,a,r,c)}))}hoverRepulse(){const t=this.container,i=t.interactivity.mouse.position;if(!i)return;const e=t.retina.repulseModeDistance;this.processRepulse(i,e,new D(i.x,i.y,e))}processRepulse(t,i,e,o){var s;const n=this.container,a=n.particles.quadTree.query(e);for(const e of a){const{dx:a,dy:l,distance:c}=r.getDistances(e.position,t),d={x:a/c,y:l/c},h=100*(null!==(s=null==o?void 0:o.speed)&&void 0!==s?s:n.options.interactivity.modes.repulse.speed),u=r.clamp((1-Math.pow(c/i,2))*h,0,50);e.position.x=e.position.x+d.x*u,e.position.y=e.position.y+d.y*u}}clickRepulse(){const t=this.container;if(t.repulse.finish||(t.repulse.count||(t.repulse.count=0),t.repulse.count++,t.repulse.count===t.particles.count&&(t.repulse.finish=!0)),t.repulse.clicking){const i=t.retina.repulseModeDistance,e=Math.pow(i/6,3),o=t.interactivity.mouse.clickPosition;if(void 0===o)return;const s=new D(o.x,o.y,e),n=t.particles.quadTree.query(s);for(const i of n){const{dx:s,dy:n,distance:a}=r.getDistances(o,i.position),l=a*a,c=t.options.interactivity.modes.repulse.speed,d=-e*c/l;if(l<=e){t.repulse.particles.push(i);const e=Math.atan2(n,s);i.velocity.horizontal=d*Math.cos(e),i.velocity.vertical=d*Math.sin(e)}}}else if(!1===t.repulse.clicking){for(const i of t.repulse.particles)i.velocity.horizontal=i.initialVelocity.horizontal,i.velocity.vertical=i.initialVelocity.vertical;t.repulse.particles=[]}}}function Ut(t,i,e,o){if(i>e){const s=t+(i-e)*o;return r.clamp(s,t,i)}if(i<e){const s=t-(e-i)*o;return r.clamp(s,i,t)}}class Gt{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.options,e=t.interactivity.mouse,o=i.interactivity.events,s=o.onDiv,n=P.isDivModeEnabled(c.bubble,s);if(!(n||o.onHover.enable&&e.position||o.onClick.enable&&e.clickPosition))return!1;const a=o.onHover.mode,r=o.onClick.mode;return P.isInArray(d.bubble,a)||P.isInArray(l.bubble,r)||n}reset(t,i){t.bubble.inRange&&!i||(delete t.bubble.div,delete t.bubble.opacity,delete t.bubble.radius,delete t.bubble.color)}interact(){const t=this.container.options.interactivity.events,i=t.onHover,e=t.onClick,o=i.enable,s=i.mode,n=e.enable,a=e.mode,r=t.onDiv;o&&P.isInArray(d.bubble,s)?this.hoverBubble():n&&P.isInArray(l.bubble,a)?this.clickBubble():P.divModeExecute(c.bubble,r,((t,i)=>this.singleSelectorHover(t,i)))}singleSelectorHover(t,i){const e=this.container,o=document.querySelectorAll(t);o.length&&o.forEach((t=>{const o=t,s=e.retina.pixelRatio,n={x:(o.offsetLeft+o.offsetWidth/2)*s,y:(o.offsetTop+o.offsetHeight/2)*s},a=o.offsetWidth/2*s,r=i.type===w.circle?new D(n.x,n.y,a):new I(o.offsetLeft*s,o.offsetTop*s,o.offsetWidth*s,o.offsetHeight*s),l=e.particles.quadTree.query(r);for(const t of l){if(!r.contains(t.getPosition()))continue;t.bubble.inRange=!0;const i=e.options.interactivity.modes.bubble.divs,s=P.divMode(i,o);t.bubble.div&&t.bubble.div===o||(this.reset(t,!0),t.bubble.div=o),this.hoverBubbleSize(t,1,s),this.hoverBubbleOpacity(t,1,s),this.hoverBubbleColor(t,s)}}))}process(t,i,e,o){const s=this.container,n=o.bubbleObj.optValue;if(void 0===n)return;const a=s.options.interactivity.modes.bubble.duration,r=s.retina.bubbleModeDistance,l=o.particlesObj.optValue,c=o.bubbleObj.value,d=o.particlesObj.value||0,h=o.type;if(n!==l)if(s.bubble.durationEnd)c&&(h===m.size&&delete t.bubble.radius,h===m.opacity&&delete t.bubble.opacity);else if(i<=r){if((null!=c?c:d)!==n){const i=d-e*(d-n)/a;h===m.size&&(t.bubble.radius=i),h===m.opacity&&(t.bubble.opacity=i)}}else h===m.size&&delete t.bubble.radius,h===m.opacity&&delete t.bubble.opacity}clickBubble(){var t;const i=this.container,e=i.options,o=i.interactivity.mouse.clickPosition;if(void 0===o)return;const s=i.retina.bubbleModeDistance,n=i.particles.quadTree.queryCircle(o,s);for(const s of n){if(!i.bubble.clicking)continue;s.bubble.inRange=!i.bubble.durationEnd;const n=s.getPosition(),a=r.getDistance(n,o),l=((new Date).getTime()-(i.interactivity.mouse.clickTime||0))/1e3;l>e.interactivity.modes.bubble.duration&&(i.bubble.durationEnd=!0),l>2*e.interactivity.modes.bubble.duration&&(i.bubble.clicking=!1,i.bubble.durationEnd=!1);const c={bubbleObj:{optValue:i.retina.bubbleModeSize,value:s.bubble.radius},particlesObj:{optValue:null!==(t=s.sizeValue)&&void 0!==t?t:i.retina.sizeValue,value:s.size.value},type:m.size};this.process(s,a,l,c);const d={bubbleObj:{optValue:e.interactivity.modes.bubble.opacity,value:s.bubble.opacity},particlesObj:{optValue:s.particlesOptions.opacity.value,value:s.opacity.value},type:m.opacity};this.process(s,a,l,d),i.bubble.durationEnd?delete s.bubble.color:a<=i.retina.bubbleModeDistance?this.hoverBubbleColor(s):delete s.bubble.color}}hoverBubble(){const t=this.container,i=t.interactivity.mouse.position;if(void 0===i)return;const e=t.retina.bubbleModeDistance,o=t.particles.quadTree.queryCircle(i,e);for(const s of o){s.bubble.inRange=!0;const o=s.getPosition(),n=r.getDistance(o,i),a=1-n/e;n<=e?a>=0&&t.interactivity.status===S.mouseMoveEvent&&(this.hoverBubbleSize(s,a),this.hoverBubbleOpacity(s,a),this.hoverBubbleColor(s)):this.reset(s),t.interactivity.status===S.mouseLeaveEvent&&this.reset(s)}}hoverBubbleSize(t,i,e){var o;const s=this.container,n=(null==e?void 0:e.size)?e.size*s.retina.pixelRatio:s.retina.bubbleModeSize;if(void 0===n)return;const a=null!==(o=t.sizeValue)&&void 0!==o?o:s.retina.sizeValue,r=Ut(t.size.value,n,a,i);void 0!==r&&(t.bubble.radius=r)}hoverBubbleOpacity(t,i,e){var o;const s=this.container.options,n=null!==(o=null==e?void 0:e.opacity)&&void 0!==o?o:s.interactivity.modes.bubble.opacity;if(void 0===n)return;const a=t.particlesOptions.opacity.value,r=Ut(t.opacity.value,n,a,i);void 0!==r&&(t.bubble.opacity=r)}hoverBubbleColor(t,i){var e;const o=this.container.options;if(void 0===t.bubble.color){const s=null!==(e=null==i?void 0:i.color)&&void 0!==e?e:o.interactivity.modes.bubble.color;if(void 0===s)return;const n=s instanceof Array?P.itemFromArray(s):s;t.bubble.color=A.colorToHsl(n)}}}class $t{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.interactivity.mouse,e=t.options.interactivity.events;if(!e.onHover.enable||!i.position)return!1;const o=e.onHover.mode;return P.isInArray(d.connect,o)}reset(){}interact(){const t=this.container;if(t.options.interactivity.events.onHover.enable&&"mousemove"===t.interactivity.status){const i=t.interactivity.mouse.position;if(!i)return;const e=Math.abs(t.retina.connectModeRadius),o=t.particles.quadTree.queryCircle(i,e);let s=0;for(const i of o){const e=i.getPosition();for(const n of o.slice(s+1)){const o=n.getPosition(),s=Math.abs(t.retina.connectModeDistance),a=Math.abs(e.x-o.x),r=Math.abs(e.y-o.y);a<s&&r<s&&t.canvas.drawConnectLine(i,n)}++s}}}}class jt{constructor(t){this.container=t}isEnabled(t){return t.particlesOptions.links.enable}reset(){}interact(t){var i;const e=this.container,o=t.particlesOptions.links,s=o.opacity,n=null!==(i=t.linksDistance)&&void 0!==i?i:e.retina.linksDistance,a=e.canvas.size,l=o.warp,c=t.getPosition(),d=l?new L(c.x,c.y,n,a):new D(c.x,c.y,n),h=e.particles.quadTree.query(d);for(const i of h){const d=i.particlesOptions.links;if(t===i||!d.enable||o.id!==d.id||i.spawning||i.destroyed)continue;const h=i.getPosition();let u=r.getDistance(c,h);if(l&&u>n){const t={x:h.x-a.width,y:h.y};if(u=r.getDistance(c,t),u>n){const t={x:h.x-a.width,y:h.y-a.height};if(u=r.getDistance(c,t),u>n){const t={x:h.x,y:h.y-a.height};u=r.getDistance(c,t)}}}if(u>n)return;const v=(1-u/n)*s,p=t.particlesOptions.links;let y=void 0!==p.id?e.particles.linksColors.get(p.id):e.particles.linksColor;if(!y){const t=p.color;y=A.getLinkRandomColor(t,p.blink,p.consent),void 0!==p.id?e.particles.linksColors.set(p.id,y):e.particles.linksColor=y}-1===i.links.map((t=>t.destination)).indexOf(t)&&-1===t.links.map((t=>t.destination)).indexOf(i)&&t.links.push({destination:i,opacity:v})}}}class Xt{constructor(t){this.container=t}interact(t){var i;const e=this.container,o=null!==(i=t.linksDistance)&&void 0!==i?i:e.retina.linksDistance,s=t.getPosition(),n=e.particles.quadTree.queryCircle(s,o);for(const i of n){if(t===i||!i.particlesOptions.move.attract.enable||i.destroyed||i.spawning)continue;const e=i.getPosition(),{dx:o,dy:n}=r.getDistances(s,e),a=t.particlesOptions.move.attract.rotate,l=o/(1e3*a.x),c=n/(1e3*a.y);t.velocity.horizontal-=l,t.velocity.vertical-=c,i.velocity.horizontal+=l,i.velocity.vertical+=c}}isEnabled(t){return t.particlesOptions.move.attract.enable}reset(){}}class Yt{constructor(t){this.container=t}isEnabled(t){return t.particlesOptions.collisions.enable}reset(){}interact(t){const i=this.container,e=t.getPosition(),o=i.particles.quadTree.queryCircle(e,2*t.getRadius());for(const i of o){if(t===i||!i.particlesOptions.collisions.enable||t.particlesOptions.collisions.mode!==i.particlesOptions.collisions.mode||i.destroyed||i.spawning)continue;const o=i.getPosition();r.getDistance(e,o)<=t.getRadius()+i.getRadius()&&this.resolveCollision(t,i)}}resolveCollision(t,i){switch(t.particlesOptions.collisions.mode){case h.absorb:this.absorb(t,i);break;case h.bounce:!function(t,i){P.circleBounce(P.circleBounceDataFromParticle(t),P.circleBounceDataFromParticle(i))}(t,i);break;case h.destroy:!function(t,i){void 0===t.getRadius()&&void 0!==i.getRadius()?t.destroy():void 0!==t.getRadius()&&void 0===i.getRadius()?i.destroy():void 0!==t.getRadius()&&void 0!==i.getRadius()&&(t.getRadius()>=i.getRadius()?i.destroy():t.destroy())}(t,i)}}absorb(t,i){const e=this.container,o=e.options.fpsLimit/1e3;if(void 0===t.getRadius()&&void 0!==i.getRadius())t.destroy();else if(void 0!==t.getRadius()&&void 0===i.getRadius())i.destroy();else if(void 0!==t.getRadius()&&void 0!==i.getRadius())if(t.getRadius()>=i.getRadius()){const s=r.clamp(t.getRadius()/i.getRadius(),0,i.getRadius())*o;t.size.value+=s,i.size.value-=s,i.getRadius()<=e.retina.pixelRatio&&(i.size.value=0,i.destroy())}else{const s=r.clamp(i.getRadius()/t.getRadius(),0,t.getRadius())*o;t.size.value-=s,i.size.value+=s,t.getRadius()<=e.retina.pixelRatio&&(t.size.value=0,t.destroy())}}}class Jt{constructor(t){this.container=t}isEnabled(){return this.container.options.infection.enable}reset(){}interact(t,i){var e,o;const s=t.infecter;if(s.updateInfection(i.value),void 0===s.infectionStage)return;const n=this.container,a=n.options.infection;if(!a.enable||a.stages.length<1)return;const r=a.stages[s.infectionStage],l=n.retina.pixelRatio,c=2*t.getRadius()+r.radius*l,d=t.getPosition(),h=null!==(e=r.infectedStage)&&void 0!==e?e:s.infectionStage,u=n.particles.quadTree.queryCircle(d,c),v=r.rate,p=u.length;for(const i of u){if(i===t||i.destroyed||i.spawning||void 0!==i.infecter.infectionStage&&i.infecter.infectionStage===s.infectionStage)continue;const e=i.infecter;if(Math.random()<v/p)if(void 0===e.infectionStage)e.startInfection(h);else if(e.infectionStage<s.infectionStage)e.updateInfectionStage(h);else if(e.infectionStage>s.infectionStage){const t=a.stages[e.infectionStage],i=null!==(o=null==t?void 0:t.infectedStage)&&void 0!==o?o:e.infectionStage;s.updateInfectionStage(i)}}}}class Qt{constructor(t){this.container=t,this.delay=0}interact(t){if(!this.container.retina.reduceFactor)return;const i=this.container,e=i.options.interactivity.modes.trail,o=1e3*e.delay/this.container.retina.reduceFactor;this.delay<o&&(this.delay+=t.value),this.delay>=o&&(i.particles.push(e.quantity,i.interactivity.mouse,e.particles),this.delay-=o)}isEnabled(){const t=this.container,i=t.options,e=t.interactivity.mouse,o=i.interactivity.events;return e.clicking&&e.inside&&!!e.position&&P.isInArray(l.trail,o.onClick.mode)||e.inside&&!!e.position&&P.isInArray(d.trail,o.onHover.mode)}reset(){}}class Zt{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.options,e=t.interactivity.mouse,o=i.interactivity.events;if(!(o.onHover.enable&&e.position||o.onClick.enable&&e.clickPosition))return!1;const s=o.onHover.mode,n=o.onClick.mode;return P.isInArray(d.attract,s)||P.isInArray(l.attract,n)}reset(){}interact(){const t=this.container,i=t.options,e=t.interactivity.status===S.mouseMoveEvent,o=i.interactivity.events,s=o.onHover.enable,n=o.onHover.mode,a=o.onClick.enable,r=o.onClick.mode;e&&s&&P.isInArray(d.attract,n)?this.hoverAttract():a&&P.isInArray(l.attract,r)&&this.clickAttract()}hoverAttract(){const t=this.container,i=t.interactivity.mouse.position;if(!i)return;const e=t.retina.attractModeDistance;this.processAttract(i,e,new D(i.x,i.y,e))}processAttract(t,i,e){const o=this.container,s=o.particles.quadTree.query(e);for(const e of s){const{dx:s,dy:n,distance:a}=r.getDistances(e.position,t),l={x:s/a,y:n/a},c=o.options.interactivity.modes.attract.speed,d=r.clamp((1-Math.pow(a/i,2))*c,0,50);e.position.x=e.position.x-l.x*d,e.position.y=e.position.y-l.y*d}}clickAttract(){const t=this.container;if(t.attract.finish||(t.attract.count||(t.attract.count=0),t.attract.count++,t.attract.count===t.particles.count&&(t.attract.finish=!0)),t.attract.clicking){const i=t.interactivity.mouse.clickPosition;if(!i)return;const e=t.retina.attractModeDistance;this.processAttract(i,e,new D(i.x,i.y,e))}else!1===t.attract.clicking&&(t.attract.particles=[])}}class Kt{constructor(t){this.container=t}interact(t){const i=this.container;if(i.options.interactivity.events.onHover.enable&&"mousemove"===i.interactivity.status){const e=this.container.interactivity.mouse.position;e&&i.canvas.drawParticleShadow(t,e)}}isEnabled(){const t=this.container,i=t.interactivity.mouse,e=t.options.interactivity.events;if(!e.onHover.enable||!i.position)return!1;const o=e.onHover.mode;return P.isInArray(d.light,o)}reset(){}}class ti{constructor(t){this.container=t}interact(){const t=this.container;if(t.options.interactivity.events.onHover.enable&&"mousemove"===t.interactivity.status){const i=t.interactivity.mouse.position;if(!i)return;t.canvas.drawLight(i)}}isEnabled(){const t=this.container,i=t.interactivity.mouse,e=t.options.interactivity.events;if(!e.onHover.enable||!i.position)return!1;const o=e.onHover.mode;return P.isInArray(d.light,o)}reset(){}}class ii{constructor(t){this.container=t}isEnabled(){const t=this.container,i=t.options,e=t.interactivity.mouse,o=i.interactivity.events,s=o.onDiv;return e.position&&o.onHover.enable&&P.isInArray(d.bounce,o.onHover.mode)||P.isDivModeEnabled(c.bounce,s)}interact(){const t=this.container,i=t.options.interactivity.events,e=t.interactivity.status===S.mouseMoveEvent,o=i.onHover.enable,s=i.onHover.mode,n=i.onDiv;e&&o&&P.isInArray(d.bounce,s)?this.processMouseBounce():P.divModeExecute(c.bounce,n,((t,i)=>this.singleSelectorBounce(t,i)))}reset(){}processMouseBounce(){const t=this.container,i=10*t.retina.pixelRatio,e=t.interactivity.mouse.position,o=t.retina.bounceModeDistance;e&&this.processBounce(e,o,new D(e.x,e.y,o+i))}singleSelectorBounce(t,i){const e=this.container,o=document.querySelectorAll(t);o.length&&o.forEach((t=>{const o=t,s=e.retina.pixelRatio,n={x:(o.offsetLeft+o.offsetWidth/2)*s,y:(o.offsetTop+o.offsetHeight/2)*s},a=o.offsetWidth/2*s,r=10*s,l=i.type===w.circle?new D(n.x,n.y,a+r):new I(o.offsetLeft*s-r,o.offsetTop*s-r,o.offsetWidth*s+2*r,o.offsetHeight*s+2*r);this.processBounce(n,a,l)}))}processBounce(t,i,e){const o=this.container.particles.quadTree.query(e);for(const s of o)e instanceof D?P.circleBounce(P.circleBounceDataFromParticle(s),{position:t,radius:i,velocity:{horizontal:0,vertical:0},factor:{horizontal:0,vertical:0}}):e instanceof I&&P.rectBounce(s,P.calculateBounds(t,i))}}class ei{constructor(t){this.container=t,this.externalInteractors=[new ii(t),new Gt(t),new $t(t),new Nt(t),new ti(t),new Zt(t),new Wt(t),new Qt(t)],this.particleInteractors=[new Xt(t),new Kt(t),new Yt(t),new Jt(t),new jt(t)]}init(){}externalInteract(t){for(const i of this.externalInteractors)i.isEnabled()&&i.interact(t)}particlesInteract(t,i){for(const i of this.externalInteractors)i.reset(t);for(const e of this.particleInteractors)e.isEnabled(t)&&e.interact(t,i)}}class oi{constructor(t){this.container=t,this.nextId=0,this.array=[],this.limit=0,this.linksFreq=new Map,this.trianglesFreq=new Map,this.interactionManager=new ei(t);const i=this.container.canvas.size;this.linksColors=new Map,this.quadTree=new W(new I(-i.width/4,-i.height/4,3*i.width/2,3*i.height/2),4)}get count(){return this.array.length}init(){const t=this.container,i=t.options;this.linksFreq=new Map,this.trianglesFreq=new Map;let e=!1;for(const e of i.manualParticles){const i=e.position?{x:e.position.x*t.canvas.size.width/100,y:e.position.y*t.canvas.size.height/100}:void 0;this.addParticle(i,e.options)}for(const[,i]of t.plugins)if(void 0!==i.particlesInitialization&&(e=i.particlesInitialization()),e)break;if(!e)for(let t=this.count;t<i.particles.number.value;t++)this.addParticle();if(i.infection.enable)for(let t=0;t<i.infection.infections;t++){const t=this.array.filter((t=>void 0===t.infecter.infectionStage));P.itemFromArray(t).infecter.startInfection(0)}this.interactionManager.init(),t.noise.init()}redraw(){this.clear(),this.init(),this.draw({value:0,factor:0})}removeAt(t,i){if(t>=0&&t<=this.count)for(const e of this.array.splice(t,null!=i?i:1))e.destroy()}remove(t){this.removeAt(this.array.indexOf(t))}update(t){const i=[];this.container.noise.update();for(const e of this.array)e.move(t),e.destroyed?i.push(e):this.quadTree.insert(new N(e.getPosition(),e));for(const t of i)this.remove(t);this.interactionManager.externalInteract(t);for(const i of this.container.particles.array)i.update(t),i.destroyed||i.spawning||this.interactionManager.particlesInteract(i,t)}draw(t){const i=this.container;i.canvas.clear();const e=this.container.canvas.size;this.quadTree=new W(new I(-e.width/4,-e.height/4,3*e.width/2,3*e.height/2),4),this.update(t);for(const[,e]of i.plugins)i.canvas.drawPlugin(e,t);for(const i of this.array)i.draw(t)}clear(){this.array=[]}push(t,i,e){const o=this.container,s=o.options.particles.number.limit*o.density;if(this.pushing=!0,s>0){const i=this.count+t-s;i>0&&this.removeQuantity(i)}for(let o=0;o<t;o++)this.addParticle(null==i?void 0:i.position,e);this.pushing=!1}addParticle(t,i){try{const e=new Bt(this.nextId,this.container,t,i);return this.array.push(e),this.nextId++,e}catch(t){return void console.warn("error adding particle")}}removeQuantity(t){this.removeAt(0,t)}getLinkFrequency(t,i){const e=`${Math.min(t.id,i.id)}_${Math.max(t.id,i.id)}`;let o=this.linksFreq.get(e);return void 0===o&&(o=Math.random(),this.linksFreq.set(e,o)),o}getTriangleFrequency(t,i,e){let[o,s,n]=[t.id,i.id,e.id];o>s&&([s,o]=[o,s]),s>n&&([n,s]=[s,n]),o>n&&([n,o]=[o,n]);const a=`${o}_${s}_${n}`;let r=this.trianglesFreq.get(a);return void 0===r&&(r=Math.random(),this.trianglesFreq.set(a,r)),r}setDensity(){const t=this.container.options;this.applyDensity(t.particles)}applyDensity(t){var i;if(!(null===(i=t.number.density)||void 0===i?void 0:i.enable))return;const e=t.number,o=this.initDensityFactor(e.density),s=e.value,n=e.limit>0?e.limit:s,a=Math.min(s,n)*o,r=this.count;this.limit=e.limit*o,r<a?this.push(Math.abs(a-r),void 0,t):r>a&&this.removeQuantity(r-a)}initDensityFactor(t){const i=this.container;if(!i.canvas.element||!t.enable)return 1;const e=i.canvas.element,o=i.retina.pixelRatio;return e.width*e.height/(t.factor*o*o*t.area)}}class si{constructor(t){this.container=t}init(){const t=this.container,i=t.options;i.detectRetina?this.pixelRatio=P.isSsr()?1:window.devicePixelRatio:this.pixelRatio=1;const e=this.container.options.motion;if(e&&(e.disable||e.reduce.value))if(P.isSsr()||"undefined"==typeof matchMedia||!matchMedia)this.reduceFactor=1;else{const i=matchMedia("(prefers-reduced-motion: reduce)");if(i){this.handleMotionChange(i);const e=()=>{this.handleMotionChange(i),t.refresh().catch((()=>{}))};void 0!==i.addEventListener?i.addEventListener("change",e):void 0!==i.addListener&&i.addListener(e)}}else this.reduceFactor=1;const o=this.pixelRatio;if(t.canvas.element){const i=t.canvas.element;t.canvas.size.width=i.offsetWidth*o,t.canvas.size.height=i.offsetHeight*o}const s=i.particles;this.linksDistance=s.links.distance*o,this.linksWidth=s.links.width*o,this.moveSpeed=s.move.speed*o,this.sizeValue=s.size.value*o,this.sizeAnimationSpeed=s.size.animation.speed*o;const n=i.interactivity.modes;this.connectModeDistance=n.connect.distance*o,this.connectModeRadius=n.connect.radius*o,this.grabModeDistance=n.grab.distance*o,this.repulseModeDistance=n.repulse.distance*o,this.bounceModeDistance=n.bounce.distance*o,this.attractModeDistance=n.attract.distance*o,this.slowModeRadius=n.slow.radius*o,this.bubbleModeDistance=n.bubble.distance*o,n.bubble.size&&(this.bubbleModeSize=n.bubble.size*o)}initParticle(t){const i=t.particlesOptions,e=this.pixelRatio;t.linksDistance=i.links.distance*e,t.linksWidth=i.links.width*e,t.moveSpeed=i.move.speed*e,t.sizeValue=i.size.value*e,t.sizeAnimationSpeed=i.size.animation.speed*e,t.maxDistance=i.move.distance*e}handleMotionChange(t){const i=this.container.options;if(t.matches){const t=i.motion;this.reduceFactor=t.disable?0:t.reduce.value?1/t.reduce.factor:1}else this.reduceFactor=1}}class ni{constructor(t){this.container=t}nextFrame(t){try{const i=this.container;if(void 0!==i.lastFrameTime&&t<i.lastFrameTime+1e3/i.fpsLimit)return void i.draw();const e=t-i.lastFrameTime,o={value:e,factor:60*e/1e3};i.lastFrameTime=t,i.particles.draw(o),i.getAnimationStatus()&&i.draw()}catch(t){console.error("tsParticles error in animation loop",t)}}}class ai{constructor(){this.enable=!1,this.mode=[]}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}class ri{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type=w.circle}get elementId(){return this.ids}set elementId(t){this.ids=t}get el(){return this.elementId}set el(t){this.elementId=t}get ids(){return this.selectors instanceof Array?this.selectors.map((t=>t.replace("#",""))):this.selectors.replace("#","")}set ids(t){this.selectors=t instanceof Array?t.map((t=>"#"+t)):"#"+t}load(t){var i,e;if(void 0===t)return;const o=null!==(e=null!==(i=t.ids)&&void 0!==i?i:t.elementId)&&void 0!==e?e:t.el;void 0!==o&&(this.ids=o),void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type)}}class li{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class ci{constructor(){this.enable=!1,this.mode=[],this.parallax=new li}load(t){void 0!==t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class di{constructor(){this.onClick=new ai,this.onDiv=new ri,this.onHover=new ci,this.resize=!0}get onclick(){return this.onClick}set onclick(t){this.onClick=t}get ondiv(){return this.onDiv}set ondiv(t){this.onDiv=t}get onhover(){return this.onHover}set onhover(t){this.onHover=t}load(t){var i,e,o;if(void 0===t)return;this.onClick.load(null!==(i=t.onClick)&&void 0!==i?i:t.onclick);const s=null!==(e=t.onDiv)&&void 0!==e?e:t.ondiv;void 0!==s&&(s instanceof Array?this.onDiv=s.map((t=>{const i=new ri;return i.load(t),i})):(this.onDiv=new ri,this.onDiv.load(s))),this.onHover.load(null!==(o=t.onHover)&&void 0!==o?o:t.onhover),void 0!==t.resize&&(this.resize=t.resize)}}class hi{constructor(){this.distance=200,this.duration=.4}load(t){void 0!==t&&(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.color&&(t.color instanceof Array?this.color=t.color.map((t=>ot.create(void 0,t))):(this.color instanceof Array&&(this.color=new ot),this.color=ot.create(this.color,t.color))),void 0!==t.size&&(this.size=t.size))}}class ui extends hi{constructor(){super(),this.selectors=[]}get ids(){return this.selectors instanceof Array?this.selectors.map((t=>t.replace("#",""))):this.selectors.replace("#","")}set ids(t){this.selectors=t instanceof Array?t.map((t=>"#"+t)):"#"+t}load(t){super.load(t),void 0!==t&&(void 0!==t.ids&&(this.ids=t.ids),void 0!==t.selectors&&(this.selectors=t.selectors))}}class vi extends hi{load(t){super.load(t),void 0!==t&&void 0!==t.divs&&(t.divs instanceof Array?this.divs=t.divs.map((t=>{const i=new ui;return i.load(t),i})):((this.divs instanceof Array||!this.divs)&&(this.divs=new ui),this.divs.load(t.divs)))}}class pi{constructor(){this.opacity=.5}load(t){void 0!==t&&void 0!==t.opacity&&(this.opacity=t.opacity)}}class yi{constructor(){this.distance=80,this.links=new pi,this.radius=60}get line_linked(){return this.links}set line_linked(t){this.links=t}get lineLinked(){return this.links}set lineLinked(t){this.links=t}load(t){var i,e;void 0!==t&&(void 0!==t.distance&&(this.distance=t.distance),this.links.load(null!==(e=null!==(i=t.links)&&void 0!==i?i:t.lineLinked)&&void 0!==e?e:t.line_linked),void 0!==t.radius&&(this.radius=t.radius))}}class fi{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){void 0!==t&&(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.opacity&&(this.opacity=t.opacity))}}class mi{constructor(){this.distance=100,this.links=new fi}get line_linked(){return this.links}set line_linked(t){this.links=t}get lineLinked(){return this.links}set lineLinked(t){this.links=t}load(t){var i,e;void 0!==t&&(void 0!==t.distance&&(this.distance=t.distance),this.links.load(null!==(e=null!==(i=t.links)&&void 0!==i?i:t.lineLinked)&&void 0!==e?e:t.line_linked))}}class gi{constructor(){this.quantity=2}get particles_nb(){return this.quantity}set particles_nb(t){this.quantity=t}load(t){var i;if(void 0===t)return;const e=null!==(i=t.quantity)&&void 0!==i?i:t.particles_nb;void 0!==e&&(this.quantity=e)}}class bi{constructor(){this.quantity=4}get particles_nb(){return this.quantity}set particles_nb(t){this.quantity=t}load(t){var i;if(void 0===t)return;const e=null!==(i=t.quantity)&&void 0!==i?i:t.particles_nb;void 0!==e&&(this.quantity=e)}}class wi{constructor(){this.distance=200,this.duration=.4,this.speed=1}load(t){void 0!==t&&(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.speed&&(this.speed=t.speed))}}class xi extends wi{constructor(){super(),this.selectors=[]}get ids(){return this.selectors instanceof Array?this.selectors.map((t=>t.replace("#",""))):this.selectors.replace("#","")}set ids(t){this.selectors=t instanceof Array?t.map((()=>"#"+t)):"#"+t}load(t){super.load(t),void 0!==t&&(void 0!==t.ids&&(this.ids=t.ids),void 0!==t.selectors&&(this.selectors=t.selectors))}}class ki extends wi{load(t){super.load(t),void 0!==(null==t?void 0:t.divs)&&(t.divs instanceof Array?this.divs=t.divs.map((t=>{const i=new xi;return i.load(t),i})):((this.divs instanceof Array||!this.divs)&&(this.divs=new xi),this.divs.load(t.divs)))}}class zi{constructor(){this.factor=3,this.radius=200}get active(){return!1}set active(t){}load(t){void 0!==t&&(void 0!==t.factor&&(this.factor=t.factor),void 0!==t.radius&&(this.radius=t.radius))}}class Mi{constructor(){this.delay=1,this.quantity=1}load(t){void 0!==t&&(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.quantity&&(this.quantity=t.quantity),void 0!==t.particles&&(this.particles=P.deepExtend({},t.particles)))}}class Pi{constructor(){this.distance=200,this.duration=.4,this.speed=1}load(t){void 0!==t&&(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.speed&&(this.speed=t.speed))}}class Si{constructor(){this.start=new ot,this.stop=new ot,this.start.value="#ffffff",this.stop.value="#000000"}load(t){void 0!==t&&(this.start=ot.create(this.start,t.start),this.stop=ot.create(this.stop,t.stop))}}class Ri{constructor(){this.gradient=new Si,this.radius=1e3}load(t){void 0!==t&&(this.gradient.load(t.gradient),void 0!==t.radius&&(this.radius=t.radius))}}class Ci{constructor(){this.color=new ot,this.color.value="#000000",this.length=2e3}load(t){void 0!==t&&(this.color=ot.create(this.color,t.color),void 0!==t.length&&(this.length=t.length))}}class Ai{constructor(){this.area=new Ri,this.shadow=new Ci}load(t){void 0!==t&&(this.area.load(t.area),this.shadow.load(t.shadow))}}class Ti{constructor(){this.distance=200}load(t){t&&void 0!==t.distance&&(this.distance=t.distance)}}class Ei{constructor(){this.attract=new Pi,this.bounce=new Ti,this.bubble=new vi,this.connect=new yi,this.grab=new mi,this.light=new Ai,this.push=new bi,this.remove=new gi,this.repulse=new ki,this.slow=new zi,this.trail=new Mi}load(t){void 0!==t&&(this.attract.load(t.attract),this.bubble.load(t.bubble),this.connect.load(t.connect),this.grab.load(t.grab),this.light.load(t.light),this.push.load(t.push),this.remove.load(t.remove),this.repulse.load(t.repulse),this.slow.load(t.slow),this.trail.load(t.trail))}}class Oi{constructor(){this.detectsOn=x.canvas,this.events=new di,this.modes=new Ei}get detect_on(){return this.detectsOn}set detect_on(t){this.detectsOn=t}load(t){var i,e,o;if(void 0===t)return;const s=null!==(i=t.detectsOn)&&void 0!==i?i:t.detect_on;void 0!==s&&(this.detectsOn=s),this.events.load(t.events),this.modes.load(t.modes),!0===(null===(o=null===(e=t.modes)||void 0===e?void 0:e.slow)||void 0===o?void 0:o.active)&&(this.events.onHover.mode instanceof Array?this.events.onHover.mode.indexOf(d.slow)<0&&this.events.onHover.mode.push(d.slow):this.events.onHover.mode!==d.slow&&(this.events.onHover.mode=[this.events.onHover.mode,d.slow]))}}class Di{constructor(){this.color=new ot,this.opacity=1}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Ii{constructor(){this.composite="destination-out",this.cover=new Di,this.enable=!1}load(t){if(void 0!==t){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const i=t.cover,e="string"==typeof t.cover?{color:t.cover}:t.cover;this.cover.load(void 0!==i.color?i:{color:e})}void 0!==t.enable&&(this.enable=t.enable)}}}class Li{constructor(){this.color=new ot,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Hi{constructor(){this.color=new ot,this.color.value="#ff0000",this.radius=0,this.rate=1}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),this.duration=t.duration,this.infectedStage=t.infectedStage,void 0!==t.radius&&(this.radius=t.radius),void 0!==t.rate&&(this.rate=t.rate))}}class Fi{constructor(){this.cure=!1,this.delay=0,this.enable=!1,this.infections=0,this.stages=[]}load(t){void 0!==t&&(void 0!==t.cure&&(this.cure=t.cure),void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.infections&&(this.infections=t.infections),void 0!==t.stages&&(this.stages=t.stages.map((t=>{const i=new Hi;return i.load(t),i}))))}}class Vi{constructor(){this.mode=p.any,this.value=!1}load(t){void 0!==t&&(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class qi{constructor(){this.name="",this.default=new Vi}load(t){void 0!==t&&(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=P.deepExtend({},t.options)))}}class _i{constructor(){this.enable=!1,this.zIndex=-1}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class Bi{constructor(){this.factor=4,this.value=!1}load(t){t&&(void 0!==t.factor&&(this.factor=t.factor),void 0!==t.value&&(this.value=t.value))}}class Ni{constructor(){this.disable=!1,this.reduce=new Bi}load(t){t&&(void 0!==t.disable&&(this.disable=t.disable),this.reduce.load(t.reduce))}}class Wi{load(t){var i,e;t&&(void 0!==t.position&&(this.position={x:null!==(i=t.position.x)&&void 0!==i?i:50,y:null!==(e=t.position.y)&&void 0!==e?e:50}),void 0!==t.options&&(this.options=P.deepExtend({},t.options)))}}class Ui{constructor(){this.autoPlay=!0,this.background=new Li,this.backgroundMask=new Ii,this.backgroundMode=new _i,this.detectRetina=!0,this.fpsLimit=30,this.infection=new Fi,this.interactivity=new Oi,this.manualParticles=[],this.motion=new Ni,this.particles=new Vt,this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!1,this.themes=[]}get fps_limit(){return this.fpsLimit}set fps_limit(t){this.fpsLimit=t}get retina_detect(){return this.detectRetina}set retina_detect(t){this.detectRetina=t}load(t){var i,e;if(void 0===t)return;if(void 0!==t.preset)if(t.preset instanceof Array)for(const i of t.preset)this.importPreset(i);else this.importPreset(t.preset);void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay);const o=null!==(i=t.detectRetina)&&void 0!==i?i:t.retina_detect;void 0!==o&&(this.detectRetina=o);const s=null!==(e=t.fpsLimit)&&void 0!==e?e:t.fps_limit;if(void 0!==s&&(this.fpsLimit=s),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),this.background.load(t.background),this.backgroundMode.load(t.backgroundMode),this.backgroundMask.load(t.backgroundMask),this.infection.load(t.infection),this.interactivity.load(t.interactivity),void 0!==t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const i=new Wi;return i.load(t),i}))),this.motion.load(t.motion),this.particles.load(t.particles),B.loadOptions(this,t),void 0!==t.themes)for(const i of t.themes){const t=new qi;t.load(i),this.themes.push(t)}}setTheme(t){if(t){const i=this.themes.find((i=>i.name===t));i&&this.load(i.options)}else{const t="undefined"!=typeof matchMedia&&matchMedia("(prefers-color-scheme: dark)").matches;let i=this.themes.find((i=>i.default.value&&(i.default.mode===p.dark&&t||i.default.mode===p.light&&!t)));i||(i=this.themes.find((t=>t.default.value&&t.default.mode===p.any))),i&&this.load(i.options)}}importPreset(t){this.load(B.getPreset(t))}}var Gi=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};class $i{constructor(t,i,...e){this.id=t,this.sourceOptions=i,this.firstStart=!0,this.started=!1,this.destroyed=!1,this.paused=!0,this.lastFrameTime=0,this.pageHidden=!1,this.retina=new si(this),this.canvas=new tt(this),this.particles=new oi(this),this.drawer=new ni(this),this.noise={generate:()=>({angle:Math.random()*Math.PI*2,length:Math.random()}),init:()=>{},update:()=>{}},this.interactivity={mouse:{clicking:!1,inside:!1}},this.bubble={},this.repulse={particles:[]},this.attract={particles:[]},this.plugins=new Map,this.drawers=new Map,this.density=1,this.options=new Ui;for(const t of e)this.options.load(B.getPreset(t));const o=B.getSupportedShapes();for(const t of o){const i=B.getShapeDrawer(t);i&&this.drawers.set(t,i)}this.sourceOptions&&this.options.load(this.sourceOptions),this.fpsLimit=this.options.fpsLimit>0?this.options.fpsLimit:60,this.options.setTheme(void 0),this.eventListeners=new F(this),"undefined"!=typeof IntersectionObserver&&IntersectionObserver&&(this.intersectionObserver=new IntersectionObserver((t=>this.intersectionManager(t))))}play(t){const i=this.paused||t;if(!this.firstStart||this.options.autoPlay){if(this.paused&&(this.paused=!1),i){for(const[,t]of this.plugins)t.play&&t.play();this.lastFrameTime=performance.now()}this.draw()}else this.firstStart=!1}pause(){if(void 0!==this.drawAnimationFrame&&(P.cancelAnimation(this.drawAnimationFrame),delete this.drawAnimationFrame),!this.paused){for(const[,t]of this.plugins)t.pause&&t.pause();this.pageHidden||(this.paused=!0)}}draw(){this.drawAnimationFrame=P.animate((t=>this.drawer.nextFrame(t)))}getAnimationStatus(){return!this.paused}setNoise(t,i,e){t&&("function"==typeof t?(this.noise.generate=t,i&&(this.noise.init=i),e&&(this.noise.update=e)):(t.generate&&(this.noise.generate=t.generate),t.init&&(this.noise.init=t.init),t.update&&(this.noise.update=t.update)))}destroy(){this.stop(),this.canvas.destroy();for(const[,t]of this.drawers)t.destroy&&t.destroy(this);for(const t of this.drawers.keys())this.drawers.delete(t);this.destroyed=!0}exportImg(t){this.exportImage(t)}exportImage(t,i,e){var o;return null===(o=this.canvas.element)||void 0===o?void 0:o.toBlob(t,null!=i?i:"image/png",e)}exportConfiguration(){return JSON.stringify(this.options,void 0,2)}refresh(){return Gi(this,void 0,void 0,(function*(){this.stop(),yield this.start()}))}stop(){if(this.started){this.firstStart=!0,this.started=!1,this.eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.clear(),this.interactivity.element instanceof HTMLElement&&this.intersectionObserver&&this.intersectionObserver.observe(this.interactivity.element);for(const[,t]of this.plugins)t.stop&&t.stop();for(const t of this.plugins.keys())this.plugins.delete(t);this.particles.linksColors=new Map,delete this.particles.grabLineColor,delete this.particles.linksColor}}loadTheme(t){return Gi(this,void 0,void 0,(function*(){this.options.setTheme(t),yield this.refresh()}))}start(){return Gi(this,void 0,void 0,(function*(){if(!this.started){yield this.init(),this.started=!0,this.eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this.intersectionObserver&&this.intersectionObserver.observe(this.interactivity.element);for(const[,t]of this.plugins)void 0!==t.startAsync?yield t.startAsync():void 0!==t.start&&t.start();this.play()}}))}init(){return Gi(this,void 0,void 0,(function*(){this.retina.init(),this.canvas.init(),this.fpsLimit=this.options.fpsLimit>0?this.options.fpsLimit:60;const t=B.getAvailablePlugins(this);for(const[i,e]of t)this.plugins.set(i,e);for(const[,t]of this.drawers)t.init&&(yield t.init(this));for(const[,t]of this.plugins)t.init?t.init(this.options):void 0!==t.initAsync&&(yield t.initAsync(this.options));this.canvas.initSize(),this.particles.init(),this.particles.setDensity()}))}intersectionManager(t){if(this.options.pauseOnOutsideViewport)for(const i of t)i.target===this.interactivity.element&&(i.isIntersecting?this.play():this.pause())}}var ji=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};const Xi=[];function Yi(t){console.error("Error tsParticles - fetch status: "+t),console.error("Error tsParticles - File config not found")}class Ji{static dom(){return Xi}static domItem(t){const i=Ji.dom(),e=i[t];if(e&&!e.destroyed)return e;i.splice(t,1)}static load(t,i,e){return ji(this,void 0,void 0,(function*(){const o=document.getElementById(t);if(o)return Ji.set(t,o,i,e)}))}static set(t,i,e,o){return ji(this,void 0,void 0,(function*(){const s=e instanceof Array?P.itemFromArray(e,o):e,n=Ji.dom(),a=n.findIndex((i=>i.id===t));if(a>=0){const t=Ji.domItem(a);t&&!t.destroyed&&(t.destroy(),n.splice(a,1))}let r,l;if("canvas"===i.tagName.toLowerCase())r=i,l=!1;else{const t=i.getElementsByTagName("canvas");t.length?(r=t[0],r.className||(r.className=S.canvasClass),l=!1):(l=!0,r=document.createElement("canvas"),r.className=S.canvasClass,r.style.width="100%",r.style.height="100%",i.appendChild(r))}const c=new $i(t,s);return a>=0?n.splice(a,0,c):n.push(c),c.canvas.loadCanvas(r,l),yield c.start(),c}))}static loadJSON(t,i,e){return ji(this,void 0,void 0,(function*(){const o=i instanceof Array?P.itemFromArray(i,e):i,s=yield fetch(o);if(s.ok)return Ji.load(t,yield s.json());Yi(s.status)}))}static setJSON(t,i,e){return ji(this,void 0,void 0,(function*(){const o=yield fetch(e);if(o.ok){const e=yield o.json();return Ji.set(t,i,e)}Yi(o.status)}))}static setOnClickHandler(t){const i=Ji.dom();if(0===i.length)throw new Error("Can only set click handlers after calling tsParticles.load() or tsParticles.loadJSON()");for(const e of i){const i=e.interactivity.element;if(!i)continue;const o=(i,o)=>{if(e.destroyed)return;const s=e.retina.pixelRatio,n={x:o.x*s,y:o.y*s},a=e.particles.quadTree.queryCircle(n,e.retina.sizeValue);t(i,a)},s=t=>{if(e.destroyed)return;const i=t,s={x:i.offsetX||i.clientX,y:i.offsetY||i.clientY};o(t,s)},n=()=>{e.destroyed||(c=!0,d=!1)},a=()=>{e.destroyed||(d=!0)},r=t=>{var i,s,n;if(!e.destroyed){if(c&&!d){const a=t,r=a.touches[a.touches.length-1],l=null===(i=e.canvas.element)||void 0===i?void 0:i.getBoundingClientRect(),c={x:r.clientX-(null!==(s=null==l?void 0:l.left)&&void 0!==s?s:0),y:r.clientY-(null!==(n=null==l?void 0:l.top)&&void 0!==n?n:0)};o(t,c)}c=!1,d=!1}},l=()=>{e.destroyed||(c=!1,d=!1)};let c=!1,d=!1;i.addEventListener("click",s),i.addEventListener("touchstart",n),i.addEventListener("touchmove",a),i.addEventListener("touchend",r),i.addEventListener("touchcancel",l)}}}var Qi,Zi=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};class Ki{constructor(t,i,e,o){var s,n;this.absorbers=t,this.container=i,this.initialPosition=o,this.options=e,this.dragging=!1,this.opacity=this.options.opacity,this.size=r.getValue(e.size)*i.retina.pixelRatio,this.mass=this.size*e.size.density*i.retina.reduceFactor;const a=e.size.limit;this.limit=void 0!==a?a*i.retina.pixelRatio*i.retina.reduceFactor:a;const l="string"==typeof e.color?{value:e.color}:e.color;this.color=null!==(s=A.colorToRgb(l))&&void 0!==s?s:{b:0,g:0,r:0},this.position=null!==(n=this.initialPosition)&&void 0!==n?n:this.calcPosition()}attract(t){const i=this.options;if(i.draggable){const t=this.container.interactivity.mouse;if(t.clicking&&t.downPosition){r.getDistance(this.position,t.downPosition)<=this.size&&(this.dragging=!0)}else this.dragging=!1;this.dragging&&t.position&&(this.position.x=t.position.x,this.position.y=t.position.y)}const e=t.getPosition(),{dx:o,dy:s,distance:n}=r.getDistances(this.position,e),a=Math.atan2(o,s),l=this.mass/Math.pow(n,2)*this.container.retina.reduceFactor;if(n<this.size+t.getRadius()){const e=.033*t.getRadius()*this.container.retina.pixelRatio;this.size>t.getRadius()&&n<this.size-t.getRadius()?i.destroy?t.destroy():(t.needsNewPosition=!0,this.updateParticlePosition(t,a,l)):(i.destroy&&(t.size.value-=e),this.updateParticlePosition(t,a,l)),(void 0===this.limit||this.size<this.limit)&&(this.size+=e),this.mass+=e*this.options.size.density*this.container.retina.reduceFactor}else this.updateParticlePosition(t,a,l)}resize(){const t=this.initialPosition;this.position=t&&P.isPointInside(t,this.container.canvas.size)?t:this.calcPosition()}draw(t){t.translate(this.position.x,this.position.y),t.beginPath(),t.arc(0,0,this.size,0,2*Math.PI,!1),t.closePath(),t.fillStyle=A.getStyleFromRgb(this.color,this.opacity),t.fill()}calcPosition(){var t,i;const e=this.container,o=this.options.position;return{x:(null!==(t=null==o?void 0:o.x)&&void 0!==t?t:100*Math.random())/100*e.canvas.size.width,y:(null!==(i=null==o?void 0:o.y)&&void 0!==i?i:100*Math.random())/100*e.canvas.size.height}}updateParticlePosition(t,i,e){var o;if(t.destroyed)return;const s=this.container.canvas.size;if(t.needsNewPosition){const i=t.getRadius();t.position.x=Math.random()*(s.width-2*i)+i,t.position.y=Math.random()*(s.height-2*i)+i,t.needsNewPosition=!1}if(this.options.orbits){void 0===t.orbitRadius&&(t.orbitRadius=r.getDistance(t.getPosition(),this.position)),t.orbitRadius<=this.size&&!this.options.destroy&&(t.orbitRadius=Math.random()*Math.max(s.width,s.height)),void 0===t.orbitAngle&&(t.orbitAngle=Math.random()*Math.PI*2);const i=t.orbitRadius,n=t.orbitAngle;t.velocity.horizontal=0,t.velocity.vertical=0,t.position.x=this.position.x+i*Math.cos(n),t.position.y=this.position.y+i*Math.sin(n),t.orbitRadius-=e,t.orbitAngle+=(null!==(o=t.moveSpeed)&&void 0!==o?o:this.container.retina.moveSpeed)/100*this.container.retina.reduceFactor}else t.velocity.horizontal+=Math.sin(i)*e,t.velocity.vertical+=Math.cos(i)*e}}class te extends dt{constructor(){super(),this.density=5,this.random.minimumValue=1,this.value=50}load(t){t&&(super.load(t),void 0!==t.density&&(this.density=t.density),void 0!==t.limit&&(this.limit=t.limit),void 0!==t.limit&&(this.limit=t.limit))}}class ie{constructor(){this.color=new ot,this.color.value="#000000",this.draggable=!1,this.opacity=1,this.destroy=!0,this.orbits=!1,this.size=new te}load(t){void 0!==t&&(void 0!==t.color&&(this.color=ot.create(this.color,t.color)),void 0!==t.draggable&&(this.draggable=t.draggable),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.position&&(this.position={x:t.position.x,y:t.position.y}),void 0!==t.size&&this.size.load(t.size),void 0!==t.destroy&&(this.destroy=t.destroy),void 0!==t.orbits&&(this.orbits=t.orbits))}}!function(t){t.absorber="absorber"}(Qi||(Qi={}));class ee{constructor(t){this.container=t,this.array=[],this.absorbers=[],this.interactivityAbsorbers=[];t.addAbsorber=(t,i)=>this.addAbsorber(t,i)}init(t){var i,e;if(!t)return;t.absorbers&&(t.absorbers instanceof Array?this.absorbers=t.absorbers.map((t=>{const i=new ie;return i.load(t),i})):(this.absorbers instanceof Array&&(this.absorbers=new ie),this.absorbers.load(t.absorbers)));const o=null===(e=null===(i=t.interactivity)||void 0===i?void 0:i.modes)||void 0===e?void 0:e.absorbers;if(o&&(o instanceof Array?this.interactivityAbsorbers=o.map((t=>{const i=new ie;return i.load(t),i})):(this.interactivityAbsorbers instanceof Array&&(this.interactivityAbsorbers=new ie),this.interactivityAbsorbers.load(o))),this.absorbers instanceof Array)for(const t of this.absorbers)this.addAbsorber(t);else this.addAbsorber(this.absorbers)}particleUpdate(t){for(const i of this.array)if(i.attract(t),t.destroyed)break}draw(t){for(const i of this.array)t.save(),i.draw(t),t.restore()}stop(){this.array=[]}resize(){for(const t of this.array)t.resize()}handleClickMode(t){const i=this.container,e=this.absorbers,o=this.interactivityAbsorbers;if(t===Qi.absorber){let t;o instanceof Array?o.length>0&&(t=P.itemFromArray(o)):t=o;const s=null!=t?t:e instanceof Array?P.itemFromArray(e):e,n=i.interactivity.mouse.clickPosition;this.addAbsorber(s,n)}}addAbsorber(t,i){const e=new Ki(this,this.container,t,i);return this.array.push(e),e}removeAbsorber(t){const i=this.array.indexOf(t);i>=0&&this.array.splice(i,1)}}const oe=new class{constructor(){this.id="absorbers"}getPlugin(t){return new ee(t)}needsPlugin(t){var i,e,o;if(void 0===t)return!1;const s=t.absorbers;let n=!1;return s instanceof Array?s.length&&(n=!0):(void 0!==s||(null===(o=null===(e=null===(i=t.interactivity)||void 0===i?void 0:i.events)||void 0===e?void 0:e.onClick)||void 0===o?void 0:o.mode)&&P.isInArray(Qi.absorber,t.interactivity.events.onClick.mode))&&(n=!0),n}loadOptions(t,i){var e,o;if(!this.needsPlugin(t)&&!this.needsPlugin(i))return;const s=t;if(null==i?void 0:i.absorbers)if((null==i?void 0:i.absorbers)instanceof Array)s.absorbers=null==i?void 0:i.absorbers.map((t=>{const i=new ie;return i.load(t),i}));else{let t=s.absorbers;void 0===(null==t?void 0:t.load)&&(s.absorbers=t=new ie),t.load(null==i?void 0:i.absorbers)}const n=null===(o=null===(e=null==i?void 0:i.interactivity)||void 0===e?void 0:e.modes)||void 0===o?void 0:o.absorbers;if(n)if(n instanceof Array)s.interactivity.modes.absorbers=n.map((t=>{const i=new ie;return i.load(t),i}));else{let t=s.interactivity.modes.absorbers;void 0===(null==t?void 0:t.load)&&(s.interactivity.modes.absorbers=t=new ie),t.load(n)}}};class se{constructor(){this.mode=v.percent,this.height=0,this.width=0}load(t){void 0!==t&&(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.height&&(this.height=t.height),void 0!==t.width&&(this.width=t.width))}}function ne(t,i){return t+i*(Math.random()-.5)}function ae(t,i){return{x:ne(t.x,i.x),y:ne(t.y,i.y)}}class re{constructor(t,i,e,o){var s,n,a;this.emitters=t,this.container=i,this.initialPosition=o,this.emitterOptions=P.deepExtend({},e),this.position=null!==(s=this.initialPosition)&&void 0!==s?s:this.calcPosition();let r=P.deepExtend({},this.emitterOptions.particles);void 0===r&&(r={}),void 0===r.move&&(r.move={}),void 0===r.move.direction&&(r.move.direction=this.emitterOptions.direction),this.particlesOptions=r,this.size=null!==(n=this.emitterOptions.size)&&void 0!==n?n:(()=>{const t=new se;return t.load({height:0,mode:v.percent,width:0}),t})(),this.lifeCount=null!==(a=this.emitterOptions.life.count)&&void 0!==a?a:-1,this.immortal=this.lifeCount<=0,this.play()}play(){if(this.container.retina.reduceFactor&&(this.lifeCount>0||this.immortal||!this.emitterOptions.life.count)){if(void 0===this.startInterval){const t=1e3*this.emitterOptions.rate.delay/this.container.retina.reduceFactor;this.startInterval=window.setInterval((()=>{this.emit()}),t)}(this.lifeCount>0||this.immortal)&&this.prepareToDie()}}pause(){const t=this.startInterval;void 0!==t&&(clearInterval(t),delete this.startInterval)}resize(){const t=this.initialPosition;this.position=t&&P.isPointInside(t,this.container.canvas.size)?t:this.calcPosition()}prepareToDie(){var t;const i=null===(t=this.emitterOptions.life)||void 0===t?void 0:t.duration;this.container.retina.reduceFactor&&(this.lifeCount>0||this.immortal)&&void 0!==i&&i>0&&setTimeout((()=>{var t;this.pause(),this.immortal||this.lifeCount--,this.lifeCount>0||this.immortal?(this.position=this.calcPosition(),setTimeout((()=>{this.play()}),1e3*(null!==(t=this.emitterOptions.life.delay)&&void 0!==t?t:0)/this.container.retina.reduceFactor)):this.destroy()}),1e3*i)}destroy(){this.emitters.removeEmitter(this)}calcPosition(){var t,i;const e=this.container,o=this.emitterOptions.position;return{x:(null!==(t=null==o?void 0:o.x)&&void 0!==t?t:100*Math.random())/100*e.canvas.size.width,y:(null!==(i=null==o?void 0:o.y)&&void 0!==i?i:100*Math.random())/100*e.canvas.size.height}}emit(){const t=this.container,i=this.position,e={x:this.size.mode===v.percent?t.canvas.size.width*this.size.width/100:this.size.width,y:this.size.mode===v.percent?t.canvas.size.height*this.size.height/100:this.size.height};for(let o=0;o<this.emitterOptions.rate.quantity;o++)t.particles.addParticle(ae(i,e),this.particlesOptions)}}class le{constructor(){this.quantity=1,this.delay=.1}load(t){void 0!==t&&(void 0!==t.quantity&&(this.quantity=t.quantity),void 0!==t.delay&&(this.delay=t.delay))}}class ce{load(t){void 0!==t&&(void 0!==t.count&&(this.count=t.count),void 0!==t.delay&&(this.delay=t.delay),void 0!==t.duration&&(this.duration=t.duration))}}class de{constructor(){this.direction=n.none,this.life=new ce,this.rate=new le}load(t){void 0!==t&&(void 0!==t.size&&(void 0===this.size&&(this.size=new se),this.size.load(t.size)),void 0!==t.direction&&(this.direction=t.direction),this.life.load(t.life),void 0!==t.particles&&(this.particles=P.deepExtend({},t.particles)),this.rate.load(t.rate),void 0!==t.position&&(this.position={x:t.position.x,y:t.position.y}))}}var he;!function(t){t.emitter="emitter"}(he||(he={}));class ue{constructor(t){this.container=t,this.array=[],this.emitters=[],this.interactivityEmitters=[];t.addEmitter=(t,i)=>this.addEmitter(t,i)}init(t){var i,e;if(!t)return;t.emitters&&(t.emitters instanceof Array?this.emitters=t.emitters.map((t=>{const i=new de;return i.load(t),i})):(this.emitters instanceof Array&&(this.emitters=new de),this.emitters.load(t.emitters)));const o=null===(e=null===(i=t.interactivity)||void 0===i?void 0:i.modes)||void 0===e?void 0:e.emitters;if(o&&(o instanceof Array?this.interactivityEmitters=o.map((t=>{const i=new de;return i.load(t),i})):(this.interactivityEmitters instanceof Array&&(this.interactivityEmitters=new de),this.interactivityEmitters.load(o))),this.emitters instanceof Array)for(const t of this.emitters)this.addEmitter(t);else this.addEmitter(this.emitters)}play(){for(const t of this.array)t.play()}pause(){for(const t of this.array)t.pause()}stop(){this.array=[]}handleClickMode(t){const i=this.container,e=this.emitters,o=this.interactivityEmitters;if(t===he.emitter){let t;o instanceof Array?o.length>0&&(t=P.itemFromArray(o)):t=o;const s=null!=t?t:e instanceof Array?P.itemFromArray(e):e,n=i.interactivity.mouse.clickPosition;this.addEmitter(P.deepExtend({},s),n)}}resize(){for(const t of this.array)t.resize()}addEmitter(t,i){const e=new re(this,this.container,t,i);return this.array.push(e),e}removeEmitter(t){const i=this.array.indexOf(t);i>=0&&this.array.splice(i,1)}}const ve=new class{constructor(){this.id="emitters"}getPlugin(t){return new ue(t)}needsPlugin(t){var i,e,o;if(void 0===t)return!1;const s=t.emitters;let n=!1;return s instanceof Array?s.length&&(n=!0):(void 0!==s||(null===(o=null===(e=null===(i=t.interactivity)||void 0===i?void 0:i.events)||void 0===e?void 0:e.onClick)||void 0===o?void 0:o.mode)&&P.isInArray(he.emitter,t.interactivity.events.onClick.mode))&&(n=!0),n}loadOptions(t,i){var e,o;if(!this.needsPlugin(t)&&!this.needsPlugin(i))return;const s=t;if(null==i?void 0:i.emitters)if((null==i?void 0:i.emitters)instanceof Array)s.emitters=null==i?void 0:i.emitters.map((t=>{const i=new de;return i.load(t),i}));else{let t=s.emitters;void 0===(null==t?void 0:t.load)&&(s.emitters=t=new de),t.load(null==i?void 0:i.emitters)}const n=null===(o=null===(e=null==i?void 0:i.interactivity)||void 0===e?void 0:e.modes)||void 0===o?void 0:o.emitters;if(n)if(n instanceof Array)s.interactivity.modes.emitters=n.map((t=>{const i=new de;return i.load(t),i}));else{let t=s.interactivity.modes.emitters;void 0===(null==t?void 0:t.load)&&(s.interactivity.modes.emitters=t=new de),t.load(n)}}};var pe,ye,fe;!function(t){t.equidistant="equidistant",t.onePerPoint="one-per-point",t.perPoint="per-point",t.randomLength="random-length",t.randomPoint="random-point"}(pe||(pe={})),function(t){t.path="path",t.radius="radius"}(ye||(ye={})),function(t){t.inline="inline",t.inside="inside",t.outside="outside",t.none="none"}(fe||(fe={}));class me{constructor(){this.color=new ot,this.width=.5,this.opacity=1}load(t){var i;void 0!==t&&(this.color=ot.create(this.color,t.color),"string"==typeof this.color.value&&(this.opacity=null!==(i=A.stringToAlpha(this.color.value))&&void 0!==i?i:this.opacity),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.width&&(this.width=t.width))}}class ge{constructor(){this.enable=!1,this.stroke=new me}get lineWidth(){return this.stroke.width}set lineWidth(t){this.stroke.width=t}get lineColor(){return this.stroke.color}set lineColor(t){this.stroke.color=ot.create(this.stroke.color,t)}load(t){var i;if(void 0!==t){void 0!==t.enable&&(this.enable=t.enable);const e=null!==(i=t.stroke)&&void 0!==i?i:{color:t.lineColor,width:t.lineWidth};this.stroke.load(e)}}}class be{constructor(){this.radius=10,this.type=ye.path}load(t){void 0!==t&&(void 0!==t.radius&&(this.radius=t.radius),void 0!==t.type&&(this.type=t.type))}}class we{constructor(){this.arrangement=pe.onePerPoint}load(t){void 0!==t&&void 0!==t.arrangement&&(this.arrangement=t.arrangement)}}class xe{constructor(){this.path=[],this.size={height:0,width:0}}load(t){void 0!==t&&(void 0!==t.path&&(this.path=t.path),void 0!==t.size&&(void 0!==t.size.width&&(this.size.width=t.size.width),void 0!==t.size.height&&(this.size.height=t.size.height)))}}class ke{constructor(){this.draw=new ge,this.enable=!1,this.inline=new we,this.move=new be,this.scale=1,this.type=fe.none}get inlineArrangement(){return this.inline.arrangement}set inlineArrangement(t){this.inline.arrangement=t}load(t){var i;if(void 0!==t){this.draw.load(t.draw);const e=null!==(i=t.inline)&&void 0!==i?i:{arrangement:t.inlineArrangement};void 0!==e&&this.inline.load(e),this.move.load(t.move),void 0!==t.scale&&(this.scale=t.scale),void 0!==t.type&&(this.type=t.type),void 0!==t.enable?this.enable=t.enable:this.enable=this.type!==fe.none,void 0!==t.url&&(this.url=t.url),void 0!==t.data&&("string"==typeof t.data?this.data=t.data:(this.data=new xe,this.data.load(t.data))),void 0!==t.position&&(this.position={x:t.position.x,y:t.position.y})}}}var ze=function(t,i,e,o){return new(e||(e=Promise))((function(s,n){function a(t){try{l(o.next(t))}catch(t){n(t)}}function r(t){try{l(o.throw(t))}catch(t){n(t)}}function l(t){var i;t.done?s(t.value):(i=t.value,i instanceof e?i:new e((function(t){t(i)}))).then(a,r)}l((o=o.apply(t,i||[])).next())}))};function Me(t){t.velocity.horizontal=t.velocity.vertical/2-t.velocity.horizontal,t.velocity.vertical=t.velocity.horizontal/2-t.velocity.vertical}function Pe(t,i,e){const o=A.colorToRgb(e.color);if(o){t.beginPath(),t.moveTo(i[0].x,i[0].y);for(const e of i)t.lineTo(e.x,e.y);t.closePath(),t.strokeStyle=A.getStyleFromRgb(o),t.lineWidth=e.width,t.stroke()}}function Se(t,i,e,o){t.translate(o.x,o.y);const s=A.colorToRgb(e.color);s&&(t.strokeStyle=A.getStyleFromRgb(s,e.opacity),t.lineWidth=e.width,t.stroke(i))}class Re{constructor(t){this.container=t,this.dimension={height:0,width:0},this.path2DSupported=!!window.Path2D,this.options=new ke,this.polygonMaskMoveRadius=this.options.move.radius*t.retina.pixelRatio}initAsync(t){return ze(this,void 0,void 0,(function*(){this.options.load(null==t?void 0:t.polygon);const i=this.options;this.polygonMaskMoveRadius=i.move.radius*this.container.retina.pixelRatio,i.enable&&(yield this.initRawData())}))}resize(){const t=this.container,i=this.options;i.enable&&i.type!==fe.none&&(this.redrawTimeout&&clearTimeout(this.redrawTimeout),this.redrawTimeout=window.setTimeout((()=>ze(this,void 0,void 0,(function*(){yield this.initRawData(!0),t.particles.redraw()}))),250))}stop(){delete this.raw,delete this.paths}particlesInitialization(){const t=this.options;return!(!t.enable||t.type!==fe.inline||t.inline.arrangement!==pe.onePerPoint&&t.inline.arrangement!==pe.perPoint)&&(this.drawPoints(),!0)}particlePosition(t){var i,e;if(this.options.enable&&(null!==(e=null===(i=this.raw)||void 0===i?void 0:i.length)&&void 0!==e?e:0)>0)return P.deepExtend({},t||this.randomPoint())}particleBounce(t){const i=this.options;if(i.enable&&i.type!==fe.none&&i.type!==fe.inline){if(!this.checkInsidePolygon(t.getPosition()))return Me(t),!0}else if(i.enable&&i.type===fe.inline&&t.initialPosition){if(r.getDistance(t.initialPosition,t.getPosition())>this.polygonMaskMoveRadius)return Me(t),!0}return!1}clickPositionValid(t){const i=this.options;return i.enable&&i.type!==fe.none&&i.type!==fe.inline&&this.checkInsidePolygon(t)}draw(t){var i;if(!(null===(i=this.paths)||void 0===i?void 0:i.length))return;const e=this.options,o=e.draw;if(!e.enable||!o.enable)return;const s=this.raw;for(const i of this.paths){const e=i.path2d,n=this.path2DSupported;t&&(n&&e&&this.offset?Se(t,e,o.stroke,this.offset):s&&Pe(t,s,o.stroke))}}checkInsidePolygon(t){var i,e;const o=this.container,s=this.options;if(!s.enable||s.type===fe.none||s.type===fe.inline)return!0;if(!this.raw)throw new Error(S.noPolygonFound);const n=o.canvas.size,a=null!==(i=null==t?void 0:t.x)&&void 0!==i?i:Math.random()*n.width,r=null!==(e=null==t?void 0:t.y)&&void 0!==e?e:Math.random()*n.height;let l=!1;for(let t=0,i=this.raw.length-1;t<this.raw.length;i=t++){const e=this.raw[t],o=this.raw[i];e.y>r!=o.y>r&&a<(o.x-e.x)*(r-e.y)/(o.y-e.y)+e.x&&(l=!l)}return s.type===fe.inside?l:s.type===fe.outside&&!l}parseSvgPath(t,i){var e,o,s;const n=null!=i&&i;if(void 0!==this.paths&&!n)return this.raw;const a=this.container,r=this.options,l=(new DOMParser).parseFromString(t,"image/svg+xml"),c=l.getElementsByTagName("svg")[0];let d=c.getElementsByTagName("path");d.length||(d=l.getElementsByTagName("path")),this.paths=[];for(let t=0;t<d.length;t++){const i=d.item(t);i&&this.paths.push({element:i,length:i.getTotalLength()})}const h=a.retina.pixelRatio,u=r.scale/h;this.dimension.width=parseFloat(null!==(e=c.getAttribute("width"))&&void 0!==e?e:"0")*u,this.dimension.height=parseFloat(null!==(o=c.getAttribute("height"))&&void 0!==o?o:"0")*u;const v=null!==(s=r.position)&&void 0!==s?s:{x:50,y:50};return this.offset={x:a.canvas.size.width*v.x/(100*h)-this.dimension.width/2,y:a.canvas.size.height*v.y/(100*h)-this.dimension.height/2},function(t,i,e){const o=[];for(const s of t){const t=s.element.pathSegList,n=t.numberOfItems,a={x:0,y:0};for(let s=0;s<n;s++){const n=t.getItem(s),r=window.SVGPathSeg;switch(n.pathSegType){case r.PATHSEG_MOVETO_ABS:case r.PATHSEG_LINETO_ABS:case r.PATHSEG_CURVETO_CUBIC_ABS:case r.PATHSEG_CURVETO_QUADRATIC_ABS:case r.PATHSEG_ARC_ABS:case r.PATHSEG_CURVETO_CUBIC_SMOOTH_ABS:case r.PATHSEG_CURVETO_QUADRATIC_SMOOTH_ABS:{const t=n;a.x=t.x,a.y=t.y;break}case r.PATHSEG_LINETO_HORIZONTAL_ABS:a.x=n.x;break;case r.PATHSEG_LINETO_VERTICAL_ABS:a.y=n.y;break;case r.PATHSEG_LINETO_REL:case r.PATHSEG_MOVETO_REL:case r.PATHSEG_CURVETO_CUBIC_REL:case r.PATHSEG_CURVETO_QUADRATIC_REL:case r.PATHSEG_ARC_REL:case r.PATHSEG_CURVETO_CUBIC_SMOOTH_REL:case r.PATHSEG_CURVETO_QUADRATIC_SMOOTH_REL:{const t=n;a.x+=t.x,a.y+=t.y;break}case r.PATHSEG_LINETO_HORIZONTAL_REL:a.x+=n.x;break;case r.PATHSEG_LINETO_VERTICAL_REL:a.y+=n.y;break;case r.PATHSEG_UNKNOWN:case r.PATHSEG_CLOSEPATH:continue}o.push({x:a.x*i+e.x,y:a.y*i+e.y})}}return o}(this.paths,u,this.offset)}downloadSvgPath(t,i){return ze(this,void 0,void 0,(function*(){const e=this.options,o=t||e.url,s=null!=i&&i;if(!o||void 0!==this.paths&&!s)return this.raw;const n=yield fetch(o);if(!n.ok)throw new Error("tsParticles Error - Error occurred during polygon mask download");return this.parseSvgPath(yield n.text(),i)}))}drawPoints(){if(this.raw)for(const t of this.raw)this.container.particles.addParticle({x:t.x,y:t.y})}randomPoint(){const t=this.container,i=this.options;let e;if(i.type===fe.inline)switch(i.inline.arrangement){case pe.randomPoint:e=this.getRandomPoint();break;case pe.randomLength:e=this.getRandomPointByLength();break;case pe.equidistant:e=this.getEquidistantPointByIndex(t.particles.count);break;case pe.onePerPoint:case pe.perPoint:default:e=this.getPointByIndex(t.particles.count)}else e={x:Math.random()*t.canvas.size.width,y:Math.random()*t.canvas.size.height};return this.checkInsidePolygon(e)?e:this.randomPoint()}getRandomPoint(){if(!this.raw||!this.raw.length)throw new Error(S.noPolygonDataLoaded);const t=P.itemFromArray(this.raw);return{x:t.x,y:t.y}}getRandomPointByLength(){var t,i,e;const o=this.options;if(!this.raw||!this.raw.length||!(null===(t=this.paths)||void 0===t?void 0:t.length))throw new Error(S.noPolygonDataLoaded);const s=P.itemFromArray(this.paths),n=Math.floor(Math.random()*s.length)+1,a=s.element.getPointAtLength(n);return{x:a.x*o.scale+((null===(i=this.offset)||void 0===i?void 0:i.x)||0),y:a.y*o.scale+((null===(e=this.offset)||void 0===e?void 0:e.y)||0)}}getEquidistantPointByIndex(t){var i,e,o,s,n,a,r;const l=this.container.options,c=this.options;if(!this.raw||!this.raw.length||!(null===(i=this.paths)||void 0===i?void 0:i.length))throw new Error(S.noPolygonDataLoaded);let d,h=0;const u=this.paths.reduce(((t,i)=>t+i.length),0)/l.particles.number.value;for(const i of this.paths){const e=u*t-h;if(e<=i.length){d=i.element.getPointAtLength(e);break}h+=i.length}return{x:(null!==(e=null==d?void 0:d.x)&&void 0!==e?e:0)*c.scale+(null!==(s=null===(o=this.offset)||void 0===o?void 0:o.x)&&void 0!==s?s:0),y:(null!==(n=null==d?void 0:d.y)&&void 0!==n?n:0)*c.scale+(null!==(r=null===(a=this.offset)||void 0===a?void 0:a.y)&&void 0!==r?r:0)}}getPointByIndex(t){if(!this.raw||!this.raw.length)throw new Error(S.noPolygonDataLoaded);const i=this.raw[t%this.raw.length];return{x:i.x,y:i.y}}createPath2D(){var t,i;const e=this.options;if(this.path2DSupported&&(null===(t=this.paths)||void 0===t?void 0:t.length))for(const t of this.paths){const o=null===(i=t.element)||void 0===i?void 0:i.getAttribute("d");if(o){const i=new Path2D(o),s=document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGMatrix(),n=new Path2D,a=s.scale(e.scale);n.addPath?(n.addPath(i,a),t.path2d=n):delete t.path2d}else delete t.path2d;!t.path2d&&this.raw&&(t.path2d=new Path2D,t.path2d.moveTo(this.raw[0].x,this.raw[0].y),this.raw.forEach(((i,e)=>{var o;e>0&&(null===(o=t.path2d)||void 0===o||o.lineTo(i.x,i.y))})),t.path2d.closePath())}}initRawData(t){return ze(this,void 0,void 0,(function*(){const i=this.options;if(i.url)this.raw=yield this.downloadSvgPath(i.url,t);else if(i.data){const e=i.data;let o;if("string"!=typeof e){const t=e.path instanceof Array?e.path.map((t=>`<path d="${t}" />`)).join(""):`<path d="${e.path}" />`;o=`<svg ${'xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"'} width="${e.size.width}" height="${e.size.height}">${t}</svg>`}else o=e;this.raw=this.parseSvgPath(o,t)}this.createPath2D()}))}}const Ce=new class{constructor(){this.id="polygonMask"}getPlugin(t){return new Re(t)}needsPlugin(t){var i,e,o;return null!==(e=null===(i=null==t?void 0:t.polygon)||void 0===i?void 0:i.enable)&&void 0!==e?e:void 0!==(null===(o=null==t?void 0:t.polygon)||void 0===o?void 0:o.type)&&t.polygon.type!==fe.none}loadOptions(t,i){if(!this.needsPlugin(i))return;const e=t;let o=e.polygon;void 0===(null==o?void 0:o.load)&&(e.polygon=o=new ke),o.load(null==i?void 0:i.polygon)}};const Ae=new class extends class{constructor(){this.initialized=!1;const t=new o,i=new G,e=new j;B.addShapeDrawer(g.line,new X),B.addShapeDrawer(g.circle,new Y),B.addShapeDrawer(g.edge,t),B.addShapeDrawer(g.square,t),B.addShapeDrawer(g.triangle,new Q),B.addShapeDrawer(g.star,new Z),B.addShapeDrawer(g.polygon,new K),B.addShapeDrawer(g.char,i),B.addShapeDrawer(g.character,i),B.addShapeDrawer(g.image,e),B.addShapeDrawer(g.images,e)}init(){this.initialized||(this.initialized=!0)}loadFromArray(t,i,e){return Zi(this,void 0,void 0,(function*(){return Ji.load(t,i,e)}))}load(t,i){return Zi(this,void 0,void 0,(function*(){return Ji.load(t,i)}))}set(t,i,e){return Zi(this,void 0,void 0,(function*(){return Ji.set(t,i,e)}))}loadJSON(t,i,e){return Ji.loadJSON(t,i,e)}setOnClickHandler(t){Ji.setOnClickHandler(t)}dom(){return Ji.dom()}domItem(t){return Ji.domItem(t)}addShape(t,i,e,o,s){let n;n="function"==typeof i?{afterEffect:o,destroy:s,draw:i,init:e}:i,B.addShapeDrawer(t,n)}addPreset(t,i){B.addPreset(t,i)}addPlugin(t){B.addPlugin(t)}}{constructor(){super(),this.addPlugin(oe),this.addPlugin(ve),this.addPlugin(Ce)}};Ae.init();const{particlesJS:Te,pJSDom:Ee}=(t=>{const i=(i,e)=>t.load(i,e);i.load=(i,e,o)=>{t.loadJSON(i,e).then((t=>{t&&o(t)}))},i.setOnClickHandler=i=>{t.setOnClickHandler(i)};return{particlesJS:i,pJSDom:t.dom()}})(Ae)}},i={};function e(o){if(i[o])return i[o].exports;var s=i[o]={exports:{}};return t[o](s,s.exports,e),s.exports}return e.d=(t,i)=>{for(var o in i)e.o(i,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:i[o]})},e.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i),e.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e(714)})()}));