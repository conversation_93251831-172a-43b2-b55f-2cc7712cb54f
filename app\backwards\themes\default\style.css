/* Styles */
html, 
body {
    height: 100%;
}

#seed-csp4-page{
    padding:60px 20px 60px;
    min-height: 100%; 
}

body{
    height: 100%;
	text-align:center;
}

#seed-csp4-content{
	max-width:600px;
	margin:0 auto;
}

#seed-csp4-content img{
    max-width: 100%;
    height: auto;
}

#seed-csp4-image{
	margin-bottom:20px;
}

#seed-csp4-headline{
	margin-top: 0;
}

#seed-csp4-description{
}

#seed-csp4-footer{

}

#seed-csp4-credit{
	position: fixed;
	bottom:0;
	right:20px;
	background-color: #000;
	background-color: rgba(0, 0, 0, 0.70);
	padding:1px 5px 2px;
	border-radius: 4px 4px 0px 0px;
}

.placeholder { color: #aaa; }


/* =WordPress Core
-------------------------------------------------------------- */
.alignnone {
    margin: 5px 20px 20px 0;
}

.aligncenter, div.aligncenter {
    display:block;
    margin: 5px auto 5px auto;
}

.alignright {
    float:right;
    margin: 5px 0 20px 20px;
}

.alignleft {
    float:left;
    margin: 5px 20px 20px 0;
}

.aligncenter {
    display: block;
    margin: 5px auto 5px auto;
}

a img.alignright {
    float:right;
    margin: 5px 0 20px 20px;
}

a img.alignnone {
    margin: 5px 20px 20px 0;
}

a img.alignleft {
    float:left;
    margin: 5px 20px 20px 0;
}

a img.aligncenter {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.wp-caption {
    background: #fff;
    border: 1px solid #f0f0f0;
    max-width: 96%; /* Image does not overflow the content area */
    padding: 5px 3px 10px;
    text-align: center;
}

.wp-caption.alignnone {
    margin: 5px 20px 20px 0;
}

.wp-caption.alignleft {
    margin: 5px 20px 20px 0;
}

.wp-caption.alignright {
    margin: 5px 0 20px 20px;
}

.wp-caption img {
    border: 0 none;
    height: auto;
    margin:0;
    max-width: 98.5%;
    padding:0;
    width: auto;
}

.wp-caption p.wp-caption-text {
    font-size:11px;
    line-height:17px;
    margin:0;
    padding:0 4px 5px;
}


/* =Supersized
-------------------------------------------------------------- */
#supersized-loader { position:absolute; top:50%; left:50%; z-index:-1; width:60px; height:60px; margin:-30px 0 0 -30px; text-indent:-999em; }
#supersized img{max-width:none;}
#supersized {  display:none; position:fixed; left:0; top:0; overflow:hidden; z-index:-999; height:100%; width:100%;margin:0; }
#supersized img { width:auto; height:auto; position:relative; display:none; outline:none; border:none; }
#supersized.speed img { -ms-interpolation-mode:nearest-neighbor; image-rendering: -moz-crisp-edges; }	/*Speed*/
#supersized.quality img { -ms-interpolation-mode:bicubic; image-rendering: optimizeQuality; }			/*Quality*/
#supersized li { display:block; list-style:none; z-index:-30; position:fixed; overflow:hidden; top:0; left:0; width:100%; height:100%; background:#111; }
#supersized a { width:100%; height:100%; display:block; }
#supersized li.prevslide { z-index:-20; }
#supersized li.activeslide { z-index:-10; }
#supersized li.image-loading img{ visibility:hidden; }
#supersized li.prevslide img, #supersized li.activeslide img{ display:inline; }
