"use strict";function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),t}if("undefined"==typeof ImgPreviewer){var _ImgPreviewer=function(){function i(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};_classCallCheck(this,i),this.selector=t,this.options=e,this.initialize()}return _createClass(i,[{key:"initialize",value:function(){var t=this;this.index=0,this.imageElements=[],this.config=Object.assign({ratio:.9,zoom:{min:.1,max:5,step:.1},opacity:.6,scrollbar:!0},this.options),this.uniqueId=0,this.previewContainerId=null,this.generateUniqueIds(),this.previewContainer=null,this.historyInfo=null,this.imageEl=null,this.body=document.body||document.getElementsByTagName("body")[0],this.windowWidth=window.innerWidth,this.windowHeight=window.innerHeight,window.addEventListener("resize",function(){t.handleResize()}),this.render(),this.update(),this.bindEvents()}},{key:"handleResize",value:function(){var h=this;if(null!=this.historyInfo&&this.historyInfo&&this.historyInfo.width&&this.historyInfo.height){var d=this.index,u=this.config.ratio,m=this.imageElements,g=this.config.scrollbar,f=new Image;f.src=m[d].href,f.onload=function(){var t=f.width,e=f.height,i=window.innerWidth,n=window.innerHeight;if(i<t||n<e){var o=i/t,s=n/e,a=Math.min(o,s);t*=a,e*=a}else if(i<=768&&300<t){var r=300/t;t*=r,e*=r}var c=(window.innerWidth-t)/2,l=(window.innerHeight-e)/2;h.currentImageScale=h.calcScaleNums(t,e,u),h.historyInfo={startX:c,startY:l,width:t,height:e,endX:0,endY:0,scale:h.currentImageScale,rotate:0},h.imageEl.src=m[d].href,h.setImageBaseStyle(h.imageEl,t,e,c,l),setTimeout(function(){h.setImageAnimationParams(h.historyInfo)}),h.previewContainer.classList.add("show"),!g&&h.toggleScrollBar(!1)}}}},{key:"generateUniqueIds",value:function(){this.uniqueId=this.selector.replace(/[^a-zA-Z0-9]/g,""),this.previewContainerId="image-preview-container"+this.uniqueId}},{key:"render",value:function(){this.previewContainer=document.createElement("div"),this.previewContainer.classList.add("image-preview-container"),this.previewContainer.id="image-preview-container-"+this.uniqueId;var t="preview-image-"+this.uniqueId,e="prev-button-"+this.uniqueId,i="next-button-"+this.uniqueId,n="close-button-"+this.uniqueId,o="reset-button-"+this.uniqueId,s="rotate-left-button-"+this.uniqueId,a="rotate-right-button-"+this.uniqueId;this.previewContainer.innerHTML='\n            <div class="preview-header">\n                <div class="nums">\n                    <p>\n                        <span id="current-index-'.concat(this.uniqueId,'"></span>\n                        &nbsp;/&nbsp;\n                        <span id="total-nums-').concat(this.uniqueId,'"></span>\n                    </p>\n                </div>\n                <div class="tool-btn">\n                    <button id="').concat(s,'" data-tooltip="Rotate Right"><i class="iconfont icon-xuanzhuan"></i></button>\n                    <button id="').concat(a,'" data-tooltip="Rotate Left"><i class="iconfont icon-xuanzhuan1"></i></button>\n                    <button id="').concat(o,'" data-tooltip="Reset"><i class="iconfont icon-zhongzhi"></i></button>\n                    <button id="').concat(n,'" data-tooltip="Close"><i class="iconfont icon-account-practice-lesson-close"></i></button>\n                </div>\n            </div>\n            <div class="image-container">\n                <button id="prev" class="').concat(e,'" data-tooltip="Prev"><i class="iconfont icon-shangyige"></i></button>\n                <div class="img-content" id="image-content-').concat(this.uniqueId,'"><img id="').concat(t,'" src="" alt="" /></div>\n                <button id="next" class="').concat(i,'" data-tooltip="Next"><i class="iconfont icon-xiayige"></i></button>\n            </div>'),this.body.appendChild(this.previewContainer),this.imageEl=document.getElementById(t)}},{key:"update",value:function(){var i=this;this.imageElements=document.querySelectorAll("".concat(this.selector," a:not(.sp-hidden-items)")),this.imageElements.forEach(function(t,e){t.onclick=function(t){i.handleOpen(t,e),i.taggleModel(!0),i.updateIndex(e)}})}},{key:"getElementRect",value:function(t){return t.getBoundingClientRect()}},{key:"calcScaleNums",value:function(t,e,i){var n=this.windowWidth*i/t,o=this.windowHeight*i/e,s=o<n?o:n;return 1<s&&(s=1),s}},{key:"setImageBaseStyle",value:function(t,e,i,n,o){t.style.cssText="width:".concat(e,"px;height:").concat(i,"px;position:fixed; top:").concat(o,"px; left:").concat(n,"px;")}},{key:"taggleModel",value:function(t){this.previewContainer.style.display=t?"block":"none"}},{key:"toggleModel",value:function(t){this.previewContainer.style.display=t?"block":"none"}},{key:"setImageAnimationParams",value:function(t){this.imageEl.style.setProperty("--offsetX","".concat(t.endX,"px")),this.imageEl.style.setProperty("--offsetY","".concat(t.endY+30,"px")),this.imageEl.style.setProperty("--scale","".concat(t.scale)),this.imageEl.style.setProperty("--rotate","".concat(t.rotate,"deg"))}},{key:"useIndexUpdateImage",value:function(t){var c=this,l=this.config.ratio,h=100,d=100,u=this.imageElements[t].href,m=new Image;m.src=u,m.onload=function(){d=m.width,h=m.height;var t=window.innerWidth,e=window.innerHeight;if(t<d||e<h){var i=t/d,n=e/h,o=Math.min(i,n);d*=o,h*=o}else if(t<=768&&300<d){var s=300/d;d*=s,h*=s}var a=(window.innerWidth-d)/2,r=(window.innerHeight-h)/2;c.imageEl.classList.add("moving"),c.setImageBaseStyle(c.imageEl,d,h,a,r),c.historyInfo={startX:a,startY:r,width:d,height:h,endX:0,endY:0,scale:c.calcScaleNums(d,h,l),rotate:0},c.imageEl.src=u,c.setImageAnimationParams(c.historyInfo),setTimeout(function(){c.imageEl.classList.remove("moving")})}}},{key:"bindEvents",value:function(){var e=this,t=this.previewContainer;if(t){var i="prev-button-"+this.uniqueId,n="next-button-"+this.uniqueId,o="close-button-"+this.uniqueId,s="reset-button-"+this.uniqueId,a="rotate-left-button-"+this.uniqueId,r="rotate-right-button-"+this.uniqueId;t.querySelector("#".concat(o)).addEventListener("click",function(){e.handleClose()}),t.querySelector(".".concat(i)).addEventListener("click",function(){e.prev()}),t.querySelector(".".concat(n)).addEventListener("click",function(){e.next()}),t.querySelector("#".concat(s)).addEventListener("click",function(){e.handleReset()}),t.querySelector("#".concat(a)).addEventListener("click",function(){e.handelRotateLeft()}),t.querySelector("#".concat(r)).addEventListener("click",function(){e.handelRotateRight()}),t.addEventListener("click",function(t){t.target.classList.contains("image-container")&&e.handleClose()}),document.addEventListener("keydown",function(t){"Escape"===t.key&&e.handleClose()})}}},{key:"handleReset",value:function(){this.imageEl.style.top="".concat(this.historyInfo.startY,"px"),this.imageEl.style.left="".concat(this.historyInfo.startX,"px"),this.imageEl.style.setProperty("--rotate","0deg"),this.imageEl.style.setProperty("--scale","".concat(this.historyInfo.scale)),this.historyInfo.rotate=0}},{key:"handelRotateLeft",value:function(){this.historyInfo.rotate-=90,this.imageEl.style.setProperty("--rotate","".concat(this.historyInfo.rotate,"deg"))}},{key:"handelRotateRight",value:function(){this.historyInfo.rotate+=90,this.imageEl.style.setProperty("--rotate","".concat(this.historyInfo.rotate,"deg"))}},{key:"runAnimation",value:function(e,i,n){var o=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,s=window.cancelAnimationFrame||window.mozCancelAnimationFrame,a=i.start||0,r=i.end||0,c=i.step,l=null;!function t(){0<c&&a<r||c<0&&r<a?(a+=c,e.style[i.style]=i.template.replace("$",a),l=o(t)):(n&&n(),s(l))}()}},{key:"handleOpen",value:function(t,h){var d=this;t.preventDefault();var u=this.config.ratio,m=this.imageElements,g=this.config.scrollbar,f=new Image;f.src=m[h].href,f.onload=function(){var t=f.width,e=f.height,i=window.innerWidth,n=window.innerHeight;if(i<t||n<e){var o=i/t,s=n/e,a=Math.min(o,s);t*=a,e*=a}else if(i<=768&&300<t){var r=300/t;t*=r,e*=r}var c=(window.innerWidth-t)/2,l=(window.innerHeight-e)/2;d.currentImageScale=d.calcScaleNums(t,e,u),d.historyInfo={startX:c,startY:l,width:t,height:e,endX:0,endY:0,scale:d.currentImageScale,rotate:0},d.imageEl.src=m[h].href,d.setImageBaseStyle(d.imageEl,t,e,c,l),setTimeout(function(){d.setImageAnimationParams(d.historyInfo)}),d.previewContainer.classList.add("show"),!g&&d.toggleScrollBar(!1)}}},{key:"handleClose",value:function(){var t=this,e=this.config.opacity;this.imageElements[this.index];this.imageEl.style.display="none",this.runAnimation(this.previewContainer,{start:e,end:0,step:-.05,style:"background",template:"rgba(0, 0, 0, $)"},function(){t.imageEl.src="",t.imageEl.style="",t.previewContainer.style="",t.previewContainer.classList.remove("hiding"),t.toggleModel(!1)}),this.previewContainer.classList.remove("show"),this.previewContainer.classList.add("hiding"),!this.config.scrollbar&&this.toggleScrollBar(!0)}},{key:"prev",value:function(){0!==this.index&&(this.index-=1,this.updateIndex(this.index),this.useIndexUpdateImage(this.index))}},{key:"next",value:function(){this.index<this.imageElements.length-1&&(this.index+=1,this.updateIndex(this.index),this.useIndexUpdateImage(this.index))}},{key:"updateIndex",value:function(t){this.index=t;var e=document.getElementById("total-nums-".concat(this.uniqueId)),i=document.getElementById("current-index-".concat(this.uniqueId));e&&i?(e.innerText=this.imageElements.length,i.innerText=t+1):console.error("Total numbers element or current index element not found.")}},{key:"setImageStyles",value:function(t,e,i){this.historyInfo.endX=t/2-this.historyInfo.width/2-this.historyInfo.startX,this.historyInfo.endY=e/2-this.historyInfo.height/2-this.historyInfo.startY,this.historyInfo.scale=this.historyInfo._scale=this.calcScaleNums(t,this.historyInfo.width,e,this.historyInfo.height,this.mergeOptions.fillRatio||this.defaultOptions.fillRatio),i&&(this.historyInfo.rotate=0,this.imageEl.style.setProperty("--rotate","0")),this.setImageBaseStyle(this.imageEl,t,e,this.historyInfo.startX-1,this.historyInfo.startY),this.imageEl.style.setProperty("--offsetX","".concat(this.historyInfo.endX,"px")),this.imageEl.style.setProperty("--offsetY","".concat(this.historyInfo.endY,"px")),this.imageEl.style.setProperty("--scale","".concat(this.historyInfo.scale))}}]),i}();window.ImgPreviewer=_ImgPreviewer}