"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(d){if("undefined"!=typeof window){var n=!0,i=10,o="",r=0,a="",t=null,u="",c=!1,l={resize:1,click:1},s=128,f=!0,m=1,g="bodyOffset",h=g,p=!0,y="",v={},b=32,e=null,w=!1,T="[iFrameSizer]",E=T.length,S="",O={max:1,min:1,bodyScroll:1,documentElementScroll:1},M="child",I=!0,N=window.parent,C="*",k=0,A=!1,x=null,z=16,L=1,R="scroll",F=R,P=window,D=function(){ue("MessageCallback function not defined")},q=function(){},H=function(){},W={height:function(){return ue("Custom height calculation function not defined"),document.documentElement.offsetHeight},width:function(){return ue("Custom width calculation function not defined"),document.body.scrollWidth}},j={},B=!1;try{var V=Object.create({},{passive:{get:function(){B=!0}},once:{get:function(){!0}}});window.addEventListener("test",te,V),window.removeEventListener("test",te,V)}catch(e){}var J,U,_,K,Q,X,Y,$=Date.now||function(){return(new Date).getTime()},G={bodyOffset:function(){return document.body.offsetHeight+ve("marginTop")+ve("marginBottom")},offset:function(){return G.bodyOffset()},bodyScroll:function(){return document.body.scrollHeight},custom:function(){return W.height()},documentElementOffset:function(){return document.documentElement.offsetHeight},documentElementScroll:function(){return document.documentElement.scrollHeight},max:function(){return Math.max.apply(null,we(G))},min:function(){return Math.min.apply(null,we(G))},grow:function(){return G.max()},lowestElement:function(){return Math.max(G.bodyOffset()||G.documentElementOffset(),be("bottom",Ee()))},taggedElement:function(){return Te("bottom","data-iframe-height")}},Z={bodyScroll:function(){return document.body.scrollWidth},bodyOffset:function(){return document.body.offsetWidth},custom:function(){return W.width()},documentElementScroll:function(){return document.documentElement.scrollWidth},documentElementOffset:function(){return document.documentElement.offsetWidth},scroll:function(){return Math.max(Z.bodyScroll(),Z.documentElementScroll())},max:function(){return Math.max.apply(null,we(Z))},min:function(){return Math.min.apply(null,we(Z))},rightMostElement:function(){return be("right",Ee())},taggedElement:function(){return Te("right","data-iframe-width")}},ee=(J=Se,Q=null,X=0,Y=function(){X=$(),Q=null,K=J.apply(U,_),Q||(U=_=null)},function(){var e=$();X||(X=e);var t=z-(e-X);return U=this,_=arguments,t<=0||z<t?(Q&&(clearTimeout(Q),Q=null),X=e,K=J.apply(U,_),Q||(U=_=null)):Q||(Q=setTimeout(Y,t)),K});ne(window,"message",ke),ne(window,"readystatechange",Ae),Ae()}function te(){}function ne(e,t,n,o){"addEventListener"in window?e.addEventListener(t,n,!!B&&(o||{})):"attachEvent"in window&&e.attachEvent("on"+t,n)}function oe(e,t,n){"removeEventListener"in window?e.removeEventListener(t,n,!1):"detachEvent"in window&&e.detachEvent("on"+t,n)}function ie(e){return e.charAt(0).toUpperCase()+e.slice(1)}function re(e){return T+"["+S+"] "+e}function ae(e){w&&"object"===_typeof(window.console)&&console.log(re(e))}function ue(e){"object"===_typeof(window.console)&&console.warn(re(e))}function ce(){var e;!function(){function e(e){return"true"===e}var t=y.substr(E).split(":");S=t[0],r=d!==t[1]?Number(t[1]):r,c=d!==t[2]?e(t[2]):c,w=d!==t[3]?e(t[3]):w,b=d!==t[4]?Number(t[4]):b,n=d!==t[6]?e(t[6]):n,a=t[7],h=d!==t[8]?t[8]:h,o=t[9],u=t[10],k=d!==t[11]?Number(t[11]):k,v.enable=d!==t[12]&&e(t[12]),M=d!==t[13]?t[13]:M,F=d!==t[14]?t[14]:F}(),ae("Initialising iFrame ("+location.href+")"),function(){function e(e,t){return"function"==typeof e&&(ae("Setup custom "+t+"CalcMethod"),W[t]=e,e="custom"),e}"iFrameResizer"in window&&Object===window.iFrameResizer.constructor&&(t=window.iFrameResizer,ae("Reading data from page: "+JSON.stringify(t)),D="messageCallback"in t?t.messageCallback:D,q="readyCallback"in t?t.readyCallback:q,C="targetOrigin"in t?t.targetOrigin:C,h="heightCalculationMethod"in t?t.heightCalculationMethod:h,F="widthCalculationMethod"in t?t.widthCalculationMethod:F,h=e(h,"height"),F=e(F,"width"));var t;ae("TargetOrigin for parent set to: "+C)}(),function(){d===a&&(a=r+"px");le("margin",function(e,t){-1!==t.indexOf("-")&&(ue("Negative CSS value ignored for "+e),t="");return t}("margin",a))}(),le("background",o),le("padding",u),(e=document.createElement("div")).style.clear="both",e.style.display="block",document.body.appendChild(e),me(),ge(),document.documentElement.style.height="",document.body.style.height="",ae('HTML & body height set to "auto"'),ae("Enable public methods"),P.parentIFrame={autoResize:function(e){return!0===e&&!1===n?(n=!0,he()):!1===e&&!0===n&&(n=!1,pe()),n},close:function(){Ce(0,0,"close"),ae("Disable outgoing messages"),I=!1,ae("Remove event listener: Message"),oe(window,"message",ke),!0===n&&pe()},getId:function(){return S},getPageInfo:function(e){"function"==typeof e?(H=e,Ce(0,0,"pageInfo")):(H=function(){},Ce(0,0,"pageInfoStop"))},moveToAnchor:function(e){v.findTarget(e)},reset:function(){Ne("parentIFrame.reset")},scrollTo:function(e,t){Ce(t,e,"scrollTo")},scrollToOffset:function(e,t){Ce(t,e,"scrollToOffset")},sendMessage:function(e,t){Ce(0,0,"message",JSON.stringify(e),t)},setHeightCalculationMethod:function(e){h=e,me()},setWidthCalculationMethod:function(e){F=e,ge()},setTargetOrigin:function(e){ae("Set targetOrigin: "+e),C=e},size:function(e,t){var n=(e||"")+(t?","+t:"");Oe("size","parentIFrame.size("+n+")",e,t)}},he(),v=function(){function r(e){var t=e.getBoundingClientRect(),n={x:window.pageXOffset!==d?window.pageXOffset:document.documentElement.scrollLeft,y:window.pageYOffset!==d?window.pageYOffset:document.documentElement.scrollTop};return{x:parseInt(t.left,10)+parseInt(n.x,10),y:parseInt(t.top,10)+parseInt(n.y,10)}}function n(e){var t,n=e.split("#")[1]||e,o=decodeURIComponent(n),i=document.getElementById(o)||document.getElementsByName(o)[0];d!==i?(t=r(i),ae("Moving to in page link (#"+n+") at x: "+t.x+" y: "+t.y),Ce(t.y,t.x,"scrollToOffset")):(ae("In page link (#"+n+") not found in iFrame, so sending to parent"),Ce(0,0,"inPageLink","#"+n))}function e(){""!==location.hash&&"#"!==location.hash&&n(location.href)}function t(){Array.prototype.forEach.call(document.querySelectorAll('a[href^="#"]'),function(e){function t(e){e.preventDefault(),n(this.getAttribute("href"))}"#"!==e.getAttribute("href")&&ne(e,"click",t)})}v.enable?Array.prototype.forEach&&document.querySelectorAll?(ae("Setting up location.hash handlers"),t(),ne(window,"hashchange",e),setTimeout(e,s)):ue("In page linking not fully supported in this browser! (See README.md for IE8 workaround)"):ae("In page linking not enabled");return{findTarget:n}}(),Oe("init","Init message from host page"),q()}function le(e,t){d!==t&&""!==t&&"null"!==t&&ae("Body "+e+' set to "'+(document.body.style[e]=t)+'"')}function se(n){var e={add:function(e){function t(){Oe(n.eventName,n.eventType)}j[e]=t,ne(window,e,t,{passive:!0})},remove:function(e){var t=j[e];delete j[e],oe(window,e,t)}};n.eventNames&&Array.prototype.map?(n.eventName=n.eventNames[0],n.eventNames.map(e[n.method])):e[n.method](n.eventName),ae(ie(n.method)+" event listener: "+n.eventType)}function de(e){se({method:e,eventType:"Animation Start",eventNames:["animationstart","webkitAnimationStart"]}),se({method:e,eventType:"Animation Iteration",eventNames:["animationiteration","webkitAnimationIteration"]}),se({method:e,eventType:"Animation End",eventNames:["animationend","webkitAnimationEnd"]}),se({method:e,eventType:"Input",eventName:"input"}),se({method:e,eventType:"Mouse Up",eventName:"mouseup"}),se({method:e,eventType:"Mouse Down",eventName:"mousedown"}),se({method:e,eventType:"Orientation Change",eventName:"orientationchange"}),se({method:e,eventType:"Print",eventName:["afterprint","beforeprint"]}),se({method:e,eventType:"Ready State Change",eventName:"readystatechange"}),se({method:e,eventType:"Touch Start",eventName:"touchstart"}),se({method:e,eventType:"Touch End",eventName:"touchend"}),se({method:e,eventType:"Touch Cancel",eventName:"touchcancel"}),se({method:e,eventType:"Transition Start",eventNames:["transitionstart","webkitTransitionStart","MSTransitionStart","oTransitionStart","otransitionstart"]}),se({method:e,eventType:"Transition Iteration",eventNames:["transitioniteration","webkitTransitionIteration","MSTransitionIteration","oTransitionIteration","otransitioniteration"]}),se({method:e,eventType:"Transition End",eventNames:["transitionend","webkitTransitionEnd","MSTransitionEnd","oTransitionEnd","otransitionend"]}),"child"===M&&se({method:e,eventType:"IFrame Resized",eventName:"resize"})}function fe(e,t,n,o){return t!==e&&(e in n||(ue(e+" is not a valid option for "+o+"CalculationMethod."),e=t),ae(o+' calculation method set to "'+e+'"')),e}function me(){h=fe(h,g,G,"height")}function ge(){F=fe(F,R,Z,"width")}function he(){var e;!0===n?(de("add"),e=b<0,window.MutationObserver||window.WebKitMutationObserver?e?ye():t=function(){function t(e){function t(e){!1===e.complete&&(ae("Attach listeners to "+e.src),e.addEventListener("load",i,!1),e.addEventListener("error",r,!1),c.push(e))}"attributes"===e.type&&"src"===e.attributeName?t(e.target):"childList"===e.type&&Array.prototype.forEach.call(e.target.querySelectorAll("img"),t)}function o(e){var t;ae("Remove listeners from "+e.src),e.removeEventListener("load",i,!1),e.removeEventListener("error",r,!1),t=e,c.splice(c.indexOf(t),1)}function n(e,t,n){o(e.target),Oe(t,n+": "+e.target.src,d,d)}function i(e){n(e,"imageLoad","Image loaded")}function r(e){n(e,"imageLoadFailed","Image load failed")}function e(e){Oe("mutationObserver","mutationObserver: "+e[0].target+" "+e[0].type),e.forEach(t)}var a,u,c=[],l=window.MutationObserver||window.WebKitMutationObserver,s=(a=document.querySelector("body"),u={attributes:!0,attributeOldValue:!1,characterData:!0,characterDataOldValue:!1,childList:!0,subtree:!0},s=new l(e),ae("Create body MutationObserver"),s.observe(a,u),s);return{disconnect:function(){"disconnect"in s&&(ae("Disconnect body MutationObserver"),s.disconnect(),c.forEach(o))}}}():(ae("MutationObserver not supported in this browser!"),ye())):ae("Auto Resize disabled")}function pe(){de("remove"),null!==t&&t.disconnect(),clearInterval(e)}function ye(){0!==b&&(ae("setInterval: "+b+"ms"),e=setInterval(function(){Oe("interval","setInterval: "+b)},Math.abs(b)))}function ve(e,o){var t=0;return o=o||document.body,t="defaultView"in document&&"getComputedStyle"in document.defaultView?null!==(t=document.defaultView.getComputedStyle(o,null))?t[e]:0:function(e){if(/^\d+(px)?$/i.test(e))return parseInt(e,i);var t=o.style.left,n=o.runtimeStyle.left;return o.runtimeStyle.left=o.currentStyle.left,o.style.left=e||0,e=o.style.pixelLeft,o.style.left=t,o.runtimeStyle.left=n,e}(o.currentStyle[e]),parseInt(t,i)}function be(e,t){for(var n,o=t.length,i=0,r=0,a=ie(e),u=$(),c=0;c<o;c++)r<(i=t[c].getBoundingClientRect()[e]+ve("margin"+a,t[c]))&&(r=i);return u=$()-u,ae("Parsed "+o+" HTML elements"),ae("Element position calculated in "+u+"ms"),z/2<(n=u)&&ae("Event throttle increased to "+(z=2*n)+"ms"),r}function we(e){return[e.bodyOffset(),e.bodyScroll(),e.documentElementOffset(),e.documentElementScroll()]}function Te(e,t){var n=document.querySelectorAll("["+t+"]");return 0===n.length&&(ue("No tagged elements ("+t+") found on page"),document.querySelectorAll("body *")),be(e,n)}function Ee(){return document.querySelectorAll("body *")}function Se(e,t,n,o){var i,r;!function(){function e(e,t){return!(Math.abs(e-t)<=k)}return i=d!==n?n:G[h](),r=d!==o?o:Z[F](),e(m,i)||c&&e(L,r)}()&&"init"!==e?e in{init:1,interval:1,size:1}||!(h in O||c&&F in O)?e in{interval:1}||ae("No change in size detected"):Ne(t):(Me(),Ce(m=i,L=r,e))}function Oe(e,t,n,o){A&&e in l?ae("Trigger event cancelled: "+e):(e in{reset:1,resetPage:1,init:1}||ae("Trigger event: "+t),"init"===e?Se(e,t,n,o):ee(e,t,n,o))}function Me(){A||(A=!0,ae("Trigger event lock on")),clearTimeout(x),x=setTimeout(function(){A=!1,ae("Trigger event lock off"),ae("--")},s)}function Ie(e){m=G[h](),L=Z[F](),Ce(m,L,e)}function Ne(e){var t=h;h=g,ae("Reset trigger event: "+e),Me(),Ie("reset"),h=t}function Ce(e,t,n,o,i){var r;!0===I&&(d===i?i=C:ae("Message targetOrigin: "+i),ae("Sending message to host page ("+(r=S+":"+e+":"+t+":"+n+(d!==o?":"+o:""))+")"),N.postMessage(T+r,i))}function ke(t){var n={init:function(){y=t.data,N=t.source,ce(),f=!1,setTimeout(function(){p=!1},s)},reset:function(){p?ae("Page reset ignored by init"):(ae("Page size reset by host page"),Ie("resetPage"))},resize:function(){Oe("resizeParent","Parent window requested size check")},moveToAnchor:function(){v.findTarget(i())},inPageLink:function(){this.moveToAnchor()},pageInfo:function(){var e=i();ae("PageInfoFromParent called from parent: "+e),H(JSON.parse(e)),ae(" --")},message:function(){var e=i();ae("MessageCallback called from parent: "+e),D(JSON.parse(e)),ae(" --")}};function o(){return t.data.split("]")[1].split(":")[0]}function i(){return t.data.substr(t.data.indexOf(":")+1)}function r(){return t.data.split(":")[2]in{true:1,false:1}}function e(){var e=o();e in n?n[e]():("undefined"==typeof module||!module.exports)&&"iFrameResize"in window||"jQuery"in window&&"iFrameResize"in window.jQuery.prototype||r()||ue("Unexpected message ("+t.data+")")}T===(""+t.data).substr(0,E)&&(!1===f?e():r()?n.init():ae('Ignored message of type "'+o()+'". Received before initialization.'))}function Ae(){"loading"!==document.readyState&&window.parent.postMessage("[iFrameResizerChild]Ready","*")}}();