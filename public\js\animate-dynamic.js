/*
* animate.js - animate-dynamic.ga
* Version - v2.18.8
* Licensed under the MIT license - https://opensource.org/licenses/MIT

* Copyright (c) 2021 <PERSON> (Ko<PERSON><PERSON><PERSON><PERSON>)
*/

function aniUtil_dramatic() { jQuery(".aniUtil_dramatic").each((function () { jQuery(this).css("opacity", 100), jQuery(this).hasClass("aniUtil_disabled") || jQuery(this).hasClass("animate__animated") || jQuery(this).css("opacity", 0) })) } function view_Animations() { jQuery("*[class*='ani_']:not([class*='aniUtil_onClick']):not([class*='aniUtil_onMouse']):not([class*='aniUtil_onKey']):not([class*='aniUtil_disabled'])").each((function () { var a = get_aniClasses(this); "full" === isScrolledIntoView(this) ? jQuery(this).hasClass("aniUtil_animating") || jQuery(this).hasClass("animate__animated") || (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 100), jQuery(this).addClass(a), jQuery(this).addClass("aniUtil_animating"), this.addEventListener("animationend", (() => { jQuery(this).removeClass("aniUtil_animating") }))) : "no" === isScrolledIntoView(this) && jQuery(this).hasClass("aniUtil_active") && !jQuery(this).hasClass("aniUtil_animating") && (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0), jQuery(this).removeClass(a)) })), jQuery("*[class*='aniCus_tubeLight']:not([class*='aniUtil_onClick']):not([class*='aniUtil_onMouse']):not([class*='aniUtil_onKey']):not([class*='aniUtil_disabled'])").each((function () { "full" === isScrolledIntoView(this) ? jQuery(this).hasClass("aniUtil_animating") || jQuery(this).hasClass("animate__animated") || aniCus_tubeLight(this, 1) : "no" === isScrolledIntoView(this) && jQuery(this).hasClass("aniUtil_active") && !jQuery(this).hasClass("aniUtil_animating") && (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0), jQuery(this).removeClass("animate__animated animate__fadeIn animate__slower")) })), jQuery("*[class*='aniCus_OutIn']:not([class*='aniUtil_onClick']):not([class*='aniUtil_onMouse']):not([class*='aniUtil_onKey']):not([class*='aniUtil_disabled'])").each((function () { var a = get_aniOutInClasses(this), i = a[0], s = a[1]; "full" === isScrolledIntoView(this) ? jQuery(this).hasClass("aniUtil_animating") || jQuery(this).hasClass("animate__animated") || aniCus_OutIn(this, 1, i, s) : "no" === isScrolledIntoView(this) && jQuery(this).hasClass("aniUtil_active") && !jQuery(this).hasClass("aniUtil_animating") && (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0), jQuery(this).removeClass(s)) })) } function click_Animations() { jQuery("*[class*='ani_'][class*='aniUtil_onClick']:not([class*='aniUtil_disabled'])").each((function () { var a = get_aniClasses(this); jQuery(this).hasClass("aniUtil_onClick") && jQuery(this).click((function () { jQuery(this).hasClass("aniUtil_disabled") || (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 100), jQuery(this).addClass(a), jQuery(this).hasClass("aniUtil_active") ? this.addEventListener("animationend", (() => { jQuery(this).removeClass(a), jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0) })) : jQuery(this).removeClass("aniUtil_onClick")) })) })), jQuery("*[class*='aniCus_clickDisabled']:not([class*='aniUtil_disabled'])").each((function () { jQuery(this).click((function () { jQuery(this).hasClass("aniUtil_disabled") || (jQuery(this).attr("style", "border: 2px solid red !important"), jQuery(this).addClass("animate__animated animate__shakeX animate__faster"), this.addEventListener("animationend", (() => { jQuery(this).css({ border: "revert" }), jQuery(this).removeClass("animate__animated animate__shakeX animate__faster") }))) })) })), jQuery("*[class*='aniCus_tubeLight'][class*='aniUtil_onClick']:not([class*='aniUtil_disabled'])").each((function () { jQuery(this).hasClass("aniUtil_onClick") && (jQuery(this).hasClass("aniUtil_animating") || jQuery(this).click((function () { jQuery(this).hasClass("aniUtil_disabled") || aniCus_tubeLight(this, 2) }))) })), jQuery("*[class*='aniCus_OutIn'][class*='aniUtil_onClick']:not([class*='aniUtil_disabled'])").each((function () { var a = get_aniOutInClasses(this), i = a[0], s = a[1]; jQuery(this).hasClass("aniUtil_onClick") && (jQuery(this).hasClass("aniUtil_animating") || jQuery(this).click((function () { jQuery(this).hasClass("aniUtil_disabled") || aniCus_OutIn(this, 2, i, s) }))) })) } function hover_Animations() { jQuery("*[class*='ani_'][class*='aniUtil_onMouse']:not([class*='aniUtil_disabled'])").each((function () { var a = get_aniClasses(this); jQuery(this).hasClass("aniUtil_onMouse") && (jQuery(this).mouseover((function () { jQuery(this).hasClass("aniUtil_disabled") || (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 100), jQuery(this).addClass(a), jQuery(this).hasClass("aniUtil_onMouseRepeat") ? jQuery(this).addClass("animate__infinite") : jQuery(this).hasClass("aniUtil_active") ? this.addEventListener("animationend", (() => { jQuery(this).removeClass(a), jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0) })) : jQuery(this).removeClass("aniUtil_onMouse")) })), jQuery(this).mouseout((function () { jQuery(this).hasClass("aniUtil_onMouseRepeat") && (jQuery(this).removeClass("animate__infinite"), jQuery(this).hasClass("aniUtil_active") ? this.addEventListener("animationend", (() => { jQuery(this).removeClass(a), jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0) })) : (jQuery(this).removeClass("aniUtil_onMouse"), jQuery(this).removeClass("aniUtil_onMouseRepeat"))) }))) })), jQuery("*[class*='aniCus_tubeLight'][class*='aniUtil_onMouse']:not([class*='aniUtil_disabled'])").each((function () { jQuery(this).hasClass("aniUtil_onMouse") && (jQuery(this).hasClass("aniUtil_animating") || jQuery(this).mouseover((function () { jQuery(this).hasClass("aniUtil_disabled") || aniCus_tubeLight(this, 3) }))) })), jQuery("*[class*='aniCus_OutIn'][class*='aniUtil_onMouse']:not([class*='aniUtil_disabled'])").each((function () { var a = get_aniOutInClasses(this), i = a[0], s = a[1]; jQuery(this).hasClass("aniUtil_onMouse") && (jQuery(this).hasClass("aniUtil_animating") || jQuery(this).mouseover((function () { jQuery(this).hasClass("aniUtil_disabled") || aniCus_OutIn(this, 3, i, s) }))) })) } function inner_Animations() { jQuery(".aniUtil_scrollDiv").each((function () { jQuery(this).scroll((function () { var a = this; jQuery("*[class*='aniIn_']:not([class*='aniUtil_onClick']):not([class*='aniUtil_onMouse']):not([class*='aniUtil_onKey']):not([class*='aniUtil_disabled'])").each((function () { var i = "", s = this.classList; jQuery(s).each((function () { if (this.match(/^aniIn_/)) { var a = this.split("_")[1]; i = "animate__animated animate__" + a } })), !0 === isScrolledIntoDivView(this, a) ? (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 100), jQuery(this).addClass(i)) : jQuery(this).hasClass("aniUtil_active") && (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0), jQuery(this).removeClass(i)) })) })) })) } function key_Animations(a) { var i = "*[class*='aniUtil_onKey-" + a.code + "']:not([class*='aniUtil_disabled'])"; jQuery(i).each((function () { var a = get_aniClasses(this); if (jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 100), jQuery(this).addClass(a), jQuery(this).hasClass("aniUtil_active") && this.addEventListener("animationend", (() => { jQuery(this).removeClass(a), jQuery(this).hasClass("aniUtil_dramatic") && jQuery(this).css("opacity", 0) })), jQuery(this).hasClass("aniCus_tubeLight") && aniCus_tubeLight(this, 4), jQuery(this).is('[class*="aniCus_OutIn"]')) { var i = get_aniOutInClasses(this); aniCus_OutIn(this, 4, i[0], i[1]) } })) } function get_aniClasses(a) { a = jQuery(a)[0]; var i = "", s = a.classList; return jQuery(s).each((function () { if (this.match(/^ani_/)) { var a = this.split("_")[1]; i = "animate__animated animate__" + a } })), i } function get_aniOutInClasses(a) { var i = "", s = "", t = a.classList; return jQuery(t).each((function () { this.match(/^aniCus_OutIn/) && (i = "animate__animated animate__" + this.split("-")[1], s = "animate__animated animate__" + this.split("-")[2]) })), [i, s] }

//Check if element is scrolled into view
function isScrolledIntoView(elem) {
       //This function tells whether the element is visible on view
       var rect = elem.getBoundingClientRect();
       var elemTop = rect.top;
       var elemBottom = rect.bottom;

       var viewportHeight = window.innerHeight * 0.8;
       var elemHeight = elem.offsetHeight;
       var minVisibleHeight = elemHeight * 0.8;

       //Completely visible
       if ((elemTop >= 0 && elemBottom <= window.innerHeight)) return "full";
       //Consider completely visible if half of element is within the viewport
       else if ((rect.y <= (window.innerHeight * .5) && rect.height >= viewportHeight)) return "full";
       //Partially visible
       else if ((elemTop < window.innerHeight && elemBottom >= 0)) return "partial";
       //Not visible
       else return "no";
}

function isScrolledIntoDivView(elem, parent) {
       const elemrect = elem.getBoundingClientRect();
       const parentrect = parent.getBoundingClientRect();
       var parentTop = parentrect.top;
       var parentBottom = parentrect.bottom;

       var elemTop = elemrect.top;
       var elemBottom = elemrect.bottom;

       // Calculate 80% of the parent height
       var parentHeight = parent.clientHeight * 0.8;

       // Consider the element completely visible if it fits within 80% of the parent's height
       return ((elemBottom <= parentBottom) && (elemTop >= parentTop + parentHeight));
}

function aniCus_tubeLight(a, i) { 2 == i || 3 == i || 4 == i ? jQuery(a).hasClass("animate__animated") || jQuery(a).hasClass("aniUtil_animating") || (jQuery(a).hasClass("aniUtil_dramatic") && jQuery(a).css("opacity", 100), jQuery(a).addClass("animate__animated animate__flash animate__repeat-2 animate__faster"), jQuery(a).addClass("aniUtil_animating"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__flash animate__repeat-2 animate__faster"), jQuery(a).addClass("animate__animated animate__fadeOut animate__slow"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__fadeOut animate__slow"), jQuery(a).addClass("animate__animated animate__flash animate__faster"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__flash animate__faster"), jQuery(a).addClass("animate__animated animate__fadeIn animate__slower"), a.addEventListener("animationend", (() => { jQuery(a).hasClass("aniUtil_active") ? jQuery(a).removeClass("animate__animated animate__fadeIn animate__slower") : 2 == i ? jQuery(a).removeClass("aniUtil_onClick") : 3 == i && jQuery(a).removeClass("aniUtil_onMouse") })) })) })) })), jQuery(a).removeClass("aniUtil_animating")) : 1 == i && (jQuery(a).hasClass("animate__animated") || jQuery(a).hasClass("aniUtil_animating") || (jQuery(a).hasClass("aniUtil_dramatic") && jQuery(a).css("opacity", 100), jQuery(a).addClass("animate__animated animate__flash animate__repeat-2 animate__faster"), jQuery(a).addClass("aniUtil_animating"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__flash animate__repeat-2 animate__faster"), jQuery(a).addClass("animate__animated animate__fadeOut animate__slow"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__fadeOut animate__slow"), jQuery(a).addClass("animate__animated animate__flash animate__faster"), a.addEventListener("animationend", (() => { jQuery(a).removeClass("animate__animated animate__flash animate__faster"), jQuery(a).addClass("animate__animated animate__fadeIn animate__slower") })) })) })), jQuery(a).removeClass("aniUtil_animating"))) } function aniCus_OutIn(a, i, s, t) { 2 == i || 3 == i || 4 == i ? jQuery(a).hasClass("animate__animated") || jQuery(a).hasClass("aniUtil_animating") || (jQuery(a).hasClass("aniUtil_dramatic") && jQuery(a).css("opacity", 100), jQuery(a).addClass(s), jQuery(a).addClass("aniUtil_animating"), a.addEventListener("animationend", (() => { jQuery(a).removeClass(s), jQuery(a).addClass(t), a.addEventListener("animationend", (() => { jQuery(a).removeClass("aniUtil_animating"), jQuery(a).hasClass("aniUtil_active") ? jQuery(a).removeClass(t) : 2 == i ? jQuery(a).removeClass("aniUtil_onClick") : 3 == i && jQuery(a).removeClass("aniUtil_onMouse") })) }))) : 1 == i && (jQuery(a).hasClass("animate__animated") || jQuery(a).hasClass("aniUtil_animating") || (jQuery(a).hasClass("aniUtil_dramatic") && jQuery(a).css("opacity", 100), jQuery(a).addClass(s), jQuery(a).addClass("aniUtil_animating"), a.addEventListener("animationend", (() => { jQuery(a).removeClass(s), jQuery(a).addClass(t), a.addEventListener("animationend", (() => { jQuery(a).removeClass("aniUtil_animating") })) })))) } function aniCus_text() { jQuery("*[class*='aniCus_text']").each((function () { var a = this, i = "", s = a.classList; jQuery(s).each((function () { if (this.match(/^aniCus_text/)) { var a = this.split("-")[1]; i = "ani_" + a } })), jQuery(this).hasClass("aniUtil_dramatic") && (i += " aniUtil_dramatic", jQuery(this).removeClass("aniUtil_dramatic")), jQuery(this).hasClass("aniUtil_active") && (i += " aniUtil_active"), jQuery(this).hasClass("aniUtil_onClick") && (i += " aniUtil_onClick"), jQuery(this).hasClass("aniUtil_onMouse") && (i += " aniUtil_onMouse"), a.innerHTML = a.textContent.replace(/\S/g, ((a, s) => "<span class='" + i + ` aniUtil_letter-jQuery{s}'>jQuery{a}</span>`)) })), jQuery("*[class*='aniUtil_letter']").each((function () { var a = 0, i = this.classList; jQuery(i).each((function () { if (this.match(/^aniUtil_letter/)) { var i = this.split("-")[1]; a = parseInt(i) / 10, a += "s" } })), (jQuery(this).hasClass("aniUtil_onClick") || jQuery(this).hasClass("aniUtil_onMouse")) && (a = ""), this.style.display = "inline-block", this.style.animationDelay = a })) } function aniUtil_disable(a) { if ("all" == a) jQuery("*[class*='ani_']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='aniIn_']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='aniCus_']").each((function () { jQuery(this).addClass("aniUtil_disabled") })); else if ("custom" == a) jQuery("*[class*='aniCus_']").each((function () { jQuery(this).addClass("aniUtil_disabled") })); else if ("seekers" == a) jQuery("*[class*='ani_bounce']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_flash']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_pulse']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_rubberBand']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_shakeX']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_shakeY']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_headShake']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_swing']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_tada']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_wobble']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_jello']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_heartBeat']").each((function () { jQuery(this).addClass("aniUtil_disabled") })); else if ("specials" == a) jQuery("*[class*='ani_hinge']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_jackInTheBox']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_rollIn']").each((function () { jQuery(this).addClass("aniUtil_disabled") })), jQuery("*[class*='ani_rollOut']").each((function () { jQuery(this).addClass("aniUtil_disabled") })); else { jQuery("*[class*='" + ("ani_" + a) + "']").each((function () { jQuery(this).addClass("aniUtil_disabled") })) } aniUtil_dramatic() } function aniUtil_enable(a) { if ("all" == a) jQuery("*[class*='ani_']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='aniIn_']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='aniCus_']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })); else if ("custom" == a) jQuery("*[class*='aniCus_']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })); else if ("seekers" == a) jQuery("*[class*='ani_bounce']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_flash']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_pulse']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_rubberBand']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_shakeX']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_shakeY']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_headShake']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_swing']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_tada']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_wobble']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_jello']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_heartBeat']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })); else if ("specials" == a) jQuery("*[class*='ani_hinge']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_jackInTheBox']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_rollIn']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })), jQuery("*[class*='ani_rollOut']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })); else { jQuery("*[class*='" + ("ani_" + a) + "']").each((function () { jQuery(this).removeClass("aniUtil_disabled") })) } aniUtil_dramatic() } function aniUtil_animate(a, i) { jQuery(a).hasClass("aniUtil_disabled") && jQuery(a).removeClass("aniUtil_disabled"), jQuery(a).addClass(i), jQuery(a).hasClass("aniUtil_onClick") || jQuery(a).hasClass("aniUtil_onMouse") || view_Animations(), jQuery(a).hasClass("aniUtil_onClick") && click_Animations(), jQuery(a).hasClass("aniUtil_onMouse") && hover_Animations() } function aniUtil_inanimate(a) { jQuery(a).addClass("aniUtil_disabled") } function aniUtil_reset(a) { jQuery(a).removeClass(get_aniClasses(a)), jQuery(a).hasClass("aniUtil_onMouse") || jQuery(a).hasClass("aniUtil_onClick") || jQuery(a).is('[class*="aniCus_onKey"]') || view_Animations() } function aniUtil_flush(a) { jQuery(a).removeClass(get_aniClasses(a)), jQuery(a).removeClass("ani_" + get_aniClasses(a).split("__")[2]) } jQuery(window).on("load", (function () { aniCus_text(), aniUtil_dramatic(), view_Animations(), click_Animations(), hover_Animations(), inner_Animations() })), jQuery(window).scroll((function () { view_Animations() })), jQuery(document).keyup((function (a) { key_Animations(a) }));