{"version": 3, "sources": ["admin-style.css", "admin-style.less"], "names": [], "mappings": "AAAA,YAAY;ACeM;EAAA,gBAAA;CDZjB;AAQD;ECQA,yBAAA;CDbC;ACiBD;;;EAAY,YAAA;CDZX;ACgBD;EACI,uBAAA;CDdH;ACiBiD;;EAC9C,8BAAA;CDdH;ACiBD;EAAA,iBAAA;CDdC;AAUD;;ECOI,oBAAA;CDbH;ACiBD;;;;;EAAA,yBAAA;EAAA,oCAAA;UAAA,4BAAA;CDTC;ACgBD;;;;;EAAA,yBAAA;EAAA,oCAAA;UAAA,4BAAA;CDRC;ACQD;;;;;;;;;;;;;;;;EAAA,sCAAA;UAAA,8BAAA;EAAA,mBAAA;EAAA,0BAAA;EAAA,uBAAA;EAAA,eAAA;CDcC;AAJD;;;;;;;;;;;;;;;;ECLI,sCAAA;UAAA,8BAAA;EACA,mBAAA;EDQI,0BAAA;ECJR,uBAAA;EAAA,eAAA;CD0BC;AC1BD;EAAA,eAAA;CD6BC;AC7BD;EAAA,oBAAA;EAAA,wBAAA;EAEQ,iBAAA;EACA,aAAA;EACA,sBAAA;CDgCP;AAVD;ECZA,YAAA;EACI,oBAAA;EDcA,aAAA;ECXJ,iCAAA;EACI,iBAAA;EACA,eAAA;EACA,gBAAA;CDwBH;AAnBD;ECHI,aAAA;EDaI,mBAAA;ECVR,aAAA;CDwBC;AAxBD;ECGI,mBAAA;EACA,gBAAA;EACA,mBAAA;CDwBH;AA7BD;EAmBQ,oBAAA;ECnBR,uBAAA;CDiCC;AAVD;ECvBA,cAAA;EAcQ,oBAAA;EACA,gBAAA;EACA,eAAA;EDaA,iBAAA;CAWP;AAjBD;ECHQ,YAAA;EDaA,gBAAA;ECVR,oBAAA;CDsBC;AAtBD;ECIQ,0BAAA;EACA,2BAAA;CDqBP;AA1BD;EAiBY,sBAAA;CAYX;AAPD;;ECtBA,yBAAA;CDiCC;AAPD;EC1BA,+BAAA;CDoCC;AAVD;EAGQ,gBAAA;ECPR,mBAAA;EAA6B,oBAAA;CDmB5B;AAND;EACI,8BAAA;ECVJ,+BAAA;CDmBC;AALA;EACG,iCAAA;CAOH;AAJD;EACI,0BAAA;CAMH;AAHD;ECJA,0BAAA;EACI,sBAAA;EDMA,iBAAA;CAKH;AAFD;ECHI,uBAAA;CDQH;AADD;EACI,YAAA;ECDJ,0BAAA;EACI,mBAAA;EACA,kBAAA;EACA,mBAAA;CDKH;AAAD;;ECAA,8BAAA;CDIC;AAAD;;;;;;;;;;;;;;;ECAA,8BAAA;EAAa,wHAAA;UAAA,gHAAA;EAAmB,iBAAA;CDmB/B;AAHD;;;ECdI,0BAAA;EACA,WAAA;CDsBH;ACTkC;EAAmC,uBAAA;EAClE,sBAAA;CDYH;ACRD;;;EACI,aAAA;CDYH;ACRD;EAAiB,aAAA;CDWhB;AAHD;ECJA,aAAA;EACI,oBAAA;CDUH;AAFD;ECHI,aAAA;EDKA,oBAAA;CAIH;AADD;EACI,aAAA;ECDJ,oBAAA;CDKC;AAAD;ECAA,+BAAA;CDGC;ACCD;EACI,+BAAA;CDCH;AAcD;;ECiBI,eAAA;CD3BH;AAcD;EACI,eAAA;ECmBJ,gBAAA;CD9BC;AAgBD;ECcA,6BAAA;EACA,gCAAA;CD3BC;AAiBD;ECsBA,6BAAA;EACI,gCAAA;CDpCH;ACwCD;EAAuC,qBAAA;CDrCtC;AAmBD;;ECwBI,YAAA;EACA,kCAAA;CDvCH;AAcD;;EC2BI,4DAAA;UAAA,oDAAA;EDpBI,qBAAA;CAhBP;AC2CD;EACI,YAAA;EACA,kCAAA;CDzCH;AC6CD;;;EACI,uBAAA;CDzCH;AAsBD;ECsBA,sBAAA;EACI,mBAAA;EACA,qBAAA;EACA,qBAAA;EDpBA,oCAAA;CApBH;AAuBD;ECqB0D,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDvCzD;AAsBD;ECsBI,0BAAA;EACA,iCAAA;EDpBA,YAAA;CApBH;AAuBD;ECqB0D,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDvCzD;AAsBD;ECsBI,0BAAA;EACA,iCAAA;EDpBA,YAAA;CApBH;AAuBD;ECuBI,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDzCH;AAsBD;ECuBwC,0BAAA;EAAkB,iCAAA;EDpBtD,YAAA;CApBH;AAuBD;ECsB0D,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDxCzD;AAsBD;ECuBI,0BAAA;EACA,iCAAA;EDrBA,YAAA;CApBH;AAuBD;ECsB0C,0BAAA;EAAkB,iCAAA;EDnBxD,YAAA;CArBH;AAwBD;ECqBI,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDxCH;AAwBD;ECqB0C,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDxCzC;AC2CD;EACI,0BAAA;EACA,iCAAA;EACA,YAAA;CDzCH;AAwBD;;ECqB0C,qCAAA;EAAA,iBAAA;EAAA,iBAAA;CDvCzC;AC0CD;;EACI,0BAAA;EACA,iCAAA;EACA,YAAA;CDvCH;AC0C+B;EAC5B,qCAAA;EAAA,iBAAA;EAAA,iBAAA;CDtCH;AAqBD;ECoBA,0BAAA;EAAwB,iCAAA;EACpB,YAAA;CDrCH;AAsBD;ECoBA,qCAAA;EAAsB,iBAAA;EAAA,iBAAA;CDrCrB;AAqBD;EACI,0BAAA;ECmBJ,iCAAA;EACI,YAAA;CDrCH;AAsBD;;ECoBA,qCAAA;EAAoB,iBAAA;EAAA,iBAAA;CDpCnB;AAoBD;;EACI,0BAAA;ECmBJ,iCAAA;EACI,YAAA;CDnCH;AAoBD;ECoBA,qCAAA;EAAmB,iBAAA;EAAA,iBAAA;CDnClB;AAmBD;EACI,0BAAA;ECmBJ,iCAAA;EACI,YAAA;CDnCH;AAoBD;ECoBA,qCAAA;EAAc,iBAAA;EAAA,iBAAA;CDnCb;AAmBD;EACI,0BAAA;ECmBJ,iCAAA;EACI,YAAA;CDnCH;AAoBD;ECqBA,qCAAA;EAAqB,iBAAA;EAAA,iBAAA;CDpCpB;AAmBD;EACI,0BAAA;ECoBJ,iCAAA;EACI,YAAA;CDpCH;AAoBD;ECsBA,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDrCC;AAmBD;EACI,0BAAA;ECuBJ,iCAAA;EACI,YAAA;CDvCH;AAqBD;ECyBA,mCAAA;EAAA,iBAAA;EAAA,iBAAA;CDzCC;AC6CD;EAEQ,0BAAA;EDzBJ,iCAAA;ECuBJ,YAAA;CDxCC;ACkDD;EACC,qBAAA;EAAA,qBAAA;EAAA,cAAA;ED3BG,mBAAA;EC8BJ,oBAAA;CDjDC;ACqDD;EACC,uBAAA;ED7BG,cAAA;ECgCJ,kBAAA;EACC,mBAAA;CDpDA;AAyBD;ECkCA,oBAAA;CDxDC;AC4DD;EAEI,kBAAA;CD3DH;ACyDD;EAKI,4BAAA;EDjCI,6DAAA;UAAA,qDAAA;CAzBP;AA6BD;EACC,YAAA;CA3BA;ACkED;EDnCC,aAAA;CA5BA;ACoED;EDpCC,aAAA;CA7BA;AAgCD;EACC,aAAA;CA9BA;AAiCD;ECwCI,mBAAA;CDtEH;AAkCD;ECyCA,kBAAA;CDxEC;AC4ED;EACC,YAAA;EACA,eAAA;EDxCG,gBAAA;EC2CJ,0BAAA;EACI,kBAAA;CD3EH;AC+ED;EACI,YAAA;ED1CA,gBAAA;EC6CF,iBAAA;EACE,UAAA;EACA,mBAAA;CD9EH;AAqCD;EC8CI,sBAAA;EACA,mBAAA;CDhFH;ACoFD;EACI,iBAAA;CDlFH;ACsFD;EACI,0BAAA;EACA,cAAA;CDpFH;ACwFD;EACI,mBAAA;EACA,YAAA;EACA,gBAAA;EACA,yBAAA;UAAA,iBAAA;CDtFH;AAyCD;ECkDI,uBAAA;CDxFH;AA0CD;EACC,iBAAA;EACA,kBAAA;CAxCA;AA2CD;ECoDI,mBAAA;EACA,sBAAA;CD5FH;AA4CD;ECoDI,kBAAA;CD7FH;ACgGG;EDlDA,sBAAA;ECuCJ,mBAAA;CDjFC;ACkGD;EACI,kBAAA;EACA,mBAAA;EACA,WAAA;EACA,UAAA;CDhGH;AA+CD;ECsDI,mBAAA;EACA,eAAA;CDlGH;AAgDD;ECuDI,iBAAA;EACA,UAAA;EACA,gBAAA;CDpGH;AAiDD;ECuDI,eAAA;EACA,sBAAA;EDrDA,kBAAA;ECwDJ,YAAA;CDtGC;AAkDD;ECwDI,0BAAA;EACA,YAAA;CDvGH;AAmDD;ECwDI,sBAAA;EDtDA,mBAAA;CAjDH;AAoDD;EC0DI,iBAAA;EACA,mBAAA;EDxDA,mDAAA;UAAA,2CAAA;EC2DJ,mBAAA;EACI,UAAA;EDzDA,YAAA;EC4DJ,aAAA;EACI,cAAA;EACA,gBAAA;EACA,oBAAA;ED1DA,cAAA;CAlDH;AAuCD;ECyEI,gBAAA;CD7GH;ACkHD;EACI,aAAA;EACA,mBAAA;EACA,WAAA;EACA,YAAA;EACA,SAAA;EACA,UAAA;EACA,oBAAA;EAPJ,6BAAA;EASQ,uDAAA;CDhHP;AAuDD;EC+DI,iBAAA;EACA,mBAAA;ED7DA,mDAAA;UAAA,2CAAA;ECgEJ,mBAAA;EACI,UAAA;EACA,aAAA;EACA,aAAA;ED9DA,gBAAA;ECkEJ,cAAA;CDtHC;AC0HD;EACI,aAAA;EDjEA,mBAAA;ECoEJ,WAAA;EACI,YAAA;EDlEA,SAAA;ECqEJ,UAAA;EACI,oBAAA;EDnEA,6BAAA;ECuEJ,uDAAA;CD5HC;ACgIa;EACV,mBAAA;EDrEA,iBAAA;ECwEJ,mBAAA;EACI,8BAAA;CD/HH;AA2DD;EC0EA,4BAAA;CDlIC;AA4DD;EC0EI,gBAAA;EACA,0BAAA;EACA,6BAAA;CDnIH;AA6DD;EACI,sBAAA;ECyFJ,iBAAA;EAA6B,oBAAA;CDlJ5B;AA8DD;EC0FA,uBAAA;EACI,sBAAA;EDxFA,mBAAA;ECkGJ,gBAAA;EAGQ,iBAAA;EACA,kBAAA;EACA,iCAAA;CD/JP;AAqDD;ECqGA,kBAAA;CDvJC;ACuJD;EAYQ,qCAAA;EDlGJ,iCAAA;ECsFJ,uBAAA;CDlJC;AAgED;ECoGQ,qCAAA;EACA,iCAAA;EACA,uBAAA;CDjKP;AAkED;EC0GI,gBAAA;CDzKH;AAmED;EC0GI,WAAA;CD1KH;AAoED;EC0GI,sBAAA;CD3KH;AAqED;ECyGQ,kBAAA;CD3KP;AAuED;EC4GI,gBAAA;CDhLH;AAwED;;EC4GI,cAAA;CDhLH;ACyKD;EAUQ,yBAAA;EDzGJ,oCAAA;UAAA,4BAAA;CAtEH;AACD,aAAa;AA0Eb;EC6GE,mBAAA;EACA,mBAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,gBAAA;CDpLD;AAwFD;EC+GI,aAAA;CDpMH;ACwMD;;EACI,gBAAA;ED9GA,aAAA;CAtFH;AA0FD;EACI,2BAAA;CAxFH;AAkGD;ECyHI,mBAAA;EACA,iBAAA;EACA,mBAAA;CDxNH;AA6FD;EC8HI,iBAAA;EDrHI,oBAAA;CAlGP;AAyFD;EAYQ,cAAA;CAlGP;AAsFD;EAeQ,YAAA;ECyGR,gBAAA;EAcQ,mBAAA;EACA,UAAA;EDpHA,gBAAA;ECmKR,YAAA;CDpQC;AA0GD;ECoKA,0BAAA;EACI,eAAA;EACA,aAAA;EACA,UAAA;EACA,mBAAA;EACA,UAAA;EACA,UAAA;EACA,YAAA;EDlKA,YAAA;ECqKJ,iBAAA;EACI,mBAAA;CD5QH;AA6FD;ECiLI,uBAAA;EDnKI,YAAA;CAvGP;AA4GD;ECqKI,4CAAA;EACA,gCAAA;EAEA,6BAAA;EDpKA,aAAA;ECuKJ,aAAA;EACI,iBAAA;EDrKA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EC0KJ,yBAAA;MAAA,sBAAA;UAAA,wBAAA;CDnRC;AAkGD;ECmLQ,sBAAA;CDlRP;AA+FD;EAaQ,iBAAA;CAzGP;ACwRD;ED1KA,gBAAA;EACE,aAAA;ECyKF,YAAA;EAAA,iDAAA;EAGQ,6BAAA;EACA,sBAAA;EDxKN,4BAAA;ECoKF,SAAA;CD9QC;AC8QD;EDjKI,gBAAA;ECiKJ,kBAAA;CD1QC;AA8GD;EC4JA,+BAAA;CDvQC;AA+GD;ECwJA,0BAAA;EAoBQ,eAAA;EDzKJ,aAAA;ECqJJ,iBAAA;EAAA,YAAA;EAuBQ,gBAAA;EDxKJ,qBAAA;ECiJJ,sDAAA;UAAA,8CAAA;CD7PC;AAgHD;EC6IA,qCAAA;CD1PC;AAmHD;EC2KQ,oBAAA;EDzKJ,0BAAA;ECqIJ,iBAAA;CDrPC;AA8GD;EAMQ,kBAAA;EC6KR,mBAAA;EACI,uBAAA;CD7RH;AAqHD;EC6KQ,oBAAA;EACA,0BAAA;EACA,wDAAA;UAAA,gDAAA;EACA,iBAAA;CD/RP;AAuHD;EC6KQ,aAAA;ED3KJ,iBAAA;EC6JJ,qBAAA;EAkBQ,eAAA;ED5KJ,iBAAA;EC0JJ,6DAAA;UAAA,qDAAA;CD9QC;AA+GD;EAQQ,mBAAA;CApHP;AA4GD;EAWQ,kBAAA;CApHP;AAyGD;ECiMC,yBAAA;EACA,eAAA;CDvSA;AAmKD;ECyMI,uBAAA;EACA,aAAA;EACA,WAAA;EACA,mBAAA;EACA,mBAAA;EACA,SAAA;EDvMA,OAAA;CAjKH;AAoKD;EC6MA,oBAAA;EACC,gBAAA;EACA,YAAA;EACA,UAAA;EACA,gBAAA;ED3MG,gBAAA;EC8MJ,YAAA;CD/WC;AAqKD;EC8MA,WAAA;EACA,aAAA;ED5MI,iBAAA;CAnKH;AAsKD;;EC+MA,mBAAA;ED7MI,aAAA;ECgNJ,aAAA;EACI,YAAA;EACA,mBAAA;EAEA,oBAAA;CDnXH;AAuKD;ECwMA,oBAAA;CD5WC;AC4WD;EAaQ,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,wBAAA;ED7MA,oBAAA;CAxKP;AA2KD;ECkNA,gBAAA;CD1XC;AA4KD;ECmNA,eAAA;CD5XC;AAyKD;;EAGQ,2BAAA;ECoNR,iBAAA;CD3XC;AAoKD;EC0NI,oBAAA;CD3XH;AAiKD;ECuNA,kBAAA;EAOQ,gBAAA;EACA,oBAAA;CD3XP;AA4JD;EAgBQ,iBAAA;ECuMR,oBAAA;CD/WC;AAwJD;EAoBQ,eAAA;CAzKP;AAqJD;;ECyOQ,aAAA;CD1XP;AAiJD;ECuNA,oBAAA;EAuBQ,sBAAA;CD3XP;AA6ID;ECgPQ,eAAA;CD1XP;AA0ID;ECuNA,uBAAA;EAgCQ,2BAAA;EACA,gBAAA;CD7XP;AAqID;ECuNA,0BAAA;EAAA,2BAAA;CDxVC;AA6KD;EC2KA,kBAAA;EA0CQ,iBAAA;CD9XP;AAyKD;EC6NA,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACC,0BAAA;MAAA,uBAAA;UAAA,+BAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACG,mBAAA;EDvNI,eAAA;EC0NR,YAAA;EACI,iBAAA;EDxNI,iBAAA;EC2NR,gBAAA;EACI,iCAAA;CDrYH;AA6JD;EC6OA,0BAAA;CDvYC;AA0JD;EAuBQ,eAAA;CA9KP;ACkZe;EACf,WAAA;CDhZA;AAoLD;EACC,OAAA;ECkOD,QAAA;EDhOC,aAAA;EACA,YAAA;ECuOD,oBAAA;EACC,YAAA;CDxZA;AAsLD;ECyOA,kBAAA;EACI,aAAA;CD5ZH;AAuLD;EC0OA,mBAAA;EACI,aAAA;CD9ZH;AAwLD;EACI,eAAA;EC4OJ,mBAAA;EACI,gBAAA;ED1OA,kBAAA;CAtLH;AAkLD;ECiPU,kBAAA;EACN,eAAA;CDhaH;AAwLD;ECgPA,YAAA;CDraC;AC8aD;EAAoB,iBAAA;CD3anB;ACkbD;EACI,YAAA;ED/OA,cAAA;CAhMH;AAqMD;EACC,4BAAA;EACA,iBAAA;EACA,aAAA;CAnMA;AAsMD;EACC,iBAAA;EACA,iBAAA;ECqPD,aAAA;CDxbC;AC6bD;EACI,mBAAA;EAAoC,kBAAA;EAAkB,gBAAA;EACtD,YAAA;EACA,aAAA;EACA,eAAA;EACA,qBAAA;EACA,qBAAA;EAAA,qBAAA;EAAA,cAAA;EDjPA,yBAAA;MAAA,sBAAA;UAAA,wBAAA;ECyPJ,0BAAA;MAAA,uBAAA;UAAA,oBAAA;CD/bC;AAyMD;EC6PA,YAAA;CDncC;AA6MD;ECiQA,YAAA;ED/PC,aAAA;EACA,kBAAA;EACA,mBAAA;CA3MA;AA8MD;EACA,gBAAA;EACA,iBAAA;ECmQA,iBAAA;EDjQA,oBAAA;EACA,eAAA;CA5MC;AA+MD;EC0QA,eAAA;EDxQA,gBAAA;EACA,oBAAA;CA7MC;AAgND;EACI,cAAA;EC4QJ,iBAAA;EACC,0BAAA;ED1QG,mBAAA;EC6QJ,gBAAA;EACC,oBAAA;CD1dA;AAwMD;ECqRA,cAAA;EACC,oBAAA;ED5QO,gBAAA;CA7MP;AAmMD;EAaQ,UAAA;ECgRR,gBAAA;EACI,eAAA;CD5dH;AAiND;EC2RI,mBAAA;CDzeH;AAkND;EC2RI,mBAAA;EACA,kBAAA;CD1eH;AC8eD;EACI,kBAAA;CD5eH;ACkfD;EACI,mBAAA;ED7RA,iBAAA;ECgSJ,0BAAA;EACI,mBAAA;EACA,cAAA;CDjfH;AC2eD;EAQI,gBAAA;EACA,eAAA;EACA,mBAAA;ED9RI,iBAAA;CAjNP;ACqeD;EDjRQ,UAAA;ECoSR,kBAAA;CDpfC;ACieD;EAuBA,YAAA;EACI,gBAAA;EDnSI,oBAAA;CAjNP;AC4dD;EA8BI,sBAAA;EDpSI,YAAA;ECuSR,gBAAA;CDxfC;ACudD;EAqCI,YAAA;CDzfH;ACodD;EAwCI,oBAAA;EDrSI,gBAAA;CAnNP;ACgdD;;EA8CI,aAAA;CD1fH;AC4cD;EDvPQ,aAAA;CAlNP;ACycD;EAsDI,aAAA;EACA,iBAAA;CD5fH;AAyND;EACC,gBAAA;EC6SD,mBAAA;EACI,iCAAA;CDngBH;ACugBD;EACI,YAAA;CDrgBH;AA2ND;EC4SI,eAAA;ED1SA,iBAAA;EC8SJ,gBAAA;CDtgBC;AA4ND;EC4SI,gBAAA;CDrgBH;ACygBwE;EDzSxE,iCAAA;EAAA,yBAAA;CA7NA;AAgOD;EACC,WAAA;CA9NA;AAiOD;;EC8SA,WAAA;CD3gBC;AC+gBD;EAAuB,+BAAA;EAAA,uBAAA;CD5gBtB;ACghBD;EAAsB,WAAA;CD7gBrB;ACqhBD;ED7SC,gBAAA;CArOA;AAwOD;EACI,oBAAA;CAtOH;AAyOD;EACI,oBAAA;ECgTJ,iBAAA;EAEQ,wBAAA;CDvhBP;AA0OD;ECiTQ,sBAAA;CDxhBP;AA2OD;ECiTQ,gBAAA;CDzhBP;AC+gBD;EAaQ,aAAA;CDzhBP;AA6OD;EACI,oBAAA;CA3OH;AA8OD;ECoTQ,wBAAA;CD/hBP;AAgPD;EACI,mBAAA;CA9OH;AAiPD;ECiUI,uBAAA;EACA,wBAAA;CD/iBH;AAkPD;;;ECiUI,uBAAA;CD9iBH;AAgPD;EACI,aAAA;CA9OH;AAiPD;ECmUI,uBAAA;CDjjBH;AAkPD;ECoUE,kBAAA;CDnjBD;AAoPD;ECoUI,uBAAA;EACA,wDAAA;UAAA,gDAAA;EACA,aAAA;EACA,mBAAA;EACA,kBAAA;EACA,gBAAA;EDlUA,YAAA;ECwTF,eAAA;CDziBD;ACyiBC;;EAeM,oBAAA;EACA,YAAA;CDpjBP;AAqPD;ECmUQ,mCAAA;EAAA,iBAAA;EAAA,iBAAA;EDjUJ,eAAA;ECqUF,mBAAA;EACE,YAAA;EDnUA,UAAA;ECsUF,gBAAA;CDtjBD;AC+jBC;EACE,yBAAA;KAAA,sBAAA;UAAA,iBAAA;CD7jBH;AA6PD;ECwUA,0BAAA;CDlkBC;AA8PD;ECwUI,YAAA;EACA,qCAAA;CDnkBH;AAiQD;ECwUI,mBAAA;EACA,sBAAA;CDtkBH;AAkQD;ECyUA,mBAAA;EACI,SAAA;EACA,YAAA;CDxkBH;AAmQD;EACI,gBAAA;EC2UJ,kBAAA;EACI,gBAAA;CD3kBH;AACD,2BAA2B;AA0Q3B;EACC,aAAA;CAxQA;AA2QD;EACC,iBAAA;EACA,kBAAA;CAzQA;AA4QD;EACC,cAAA;CA1QA;AA6QD;EACC,mBAAA;CA3QA;AC0lBD;ED3UC,oBAAA;CA5QA;AA+QD;EACI,aAAA;CA7QH;AAgRD;EC+UA,cAAA;CD5lBC;AA2RD;ECgVI,gBAAA;EACA,cAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,aAAA;EACA,qCAAA;EACA,eAAA;EACA,sCAAA;EAAA,8BAAA;CDxmBH;AA4RD;EACI,oBAAA;ECqVJ,uBAAA;CD9mBC;AA+RD;ECwVA,cAAA;CDpnBC;ACwnBD;EACI,gBAAA;EACA,cAAA;EDvVA,UAAA;ECqVJ,WAAA;EAIG,iBAAA;EACA,eAAA;CDrnBF;AC4nBD;EACI,eAAA;CD1nBH;AAoSD;EC0VI,WAAA;CD3nBH;ACgoBD;EACA,WAAA;CD9nBC;AAsSD;;ECkWA,8BAAA;EACI,sBAAA;CDpoBH;AAuSD;EACI,iBAAA;ECmWJ,kBAAA;EACI,mBAAA;EDjWA,iBAAA;ECoWJ,aAAA;EACI,gBAAA;EACA,YAAA;CDxoBH;AC6oBD;EACI,iBAAA;EACA,aAAA;EACA,gBAAA;EDnWA,YAAA;CAvSH;AA2SD;;EAEI,4BAAA;ECuWJ,uBAAA;EACI,iCAAA;EACA,kBAAA;EACA,mBAAA;CD/oBH;AA6SD;;EACI,gBAAA;CA1SH;AA6SD;EACI,mBAAA;ECyWJ,oBAAA;CDnpBC;ACupBD;EAEI,mCAAA;EAAA,iBAAA;EAAA,iBAAA;EDzWA,aAAA;ECuWJ,eAAA;CDhpBC;ACgpBD;EAWI,mCAAA;EAAA,iBAAA;EAAA,iBAAA;ED3WA,aAAA;EC+WJ,eAAA;CDxpBC;AC6pBD;;;EACI,uBAAA;CDzpBH;AA4SD;ECoXI,oBAAA;EACA,iBAAA;EACA,wBAAA;EDjXA,iBAAA;CA3SH;AA8SD;EACI,YAAA;CA5SH;AA+SD;;EACI,gBAAA;CA5SH;AA+SD;;ECsXI,qBAAA;EAAA,aAAA;CDjqBH;AA+SD;;EACI,yBAAA;EAAA,iBAAA;CA5SH;AA+SD;ECuXA,mBAAA;EACI,mBAAA;EACA,mBAAA;EACA,aAAA;CDnqBH;ACwqBD;;EAGI,kCAAA;CDvqBH;AAgTD;EC4XE,mBAAA;EACA,QAAA;EDzXM,aAAA;EC2XR,iBAAA;EACI,mBAAA;EACA,oBAAA;EACA,YAAA;EACA,4BAAA;EDzXI,+BAAA;CA/SP;AAqSD;;ECwYI,mBAAA;ED1XI,YAAA;ECwXR,eAAA;EAIQ,UAAA;CDxqBP;AAkTD;EC8XQ,eAAA;ED1XA,iBAAA;ECsXR,gBAAA;EAOQ,iBAAA;ED1XA,gBAAA;EC8XR,gBAAA;CD/qBC;AACD,kBAAkB;AAiUlB;EACI,2BAAA;EC4WJ,0BAAA;EAkBQ,oBAAA;ED3XJ,sBAAA;ECyWJ,uBAAA;EAqBQ,qBAAA;EACA,4BAAA;ED3XJ,mDAAA;UAAA,2CAAA;EC+XJ,gCAAA;EAAA,gCAAA;EAAA,yBAAA;EACI,sCAAA;EAAA,8BAAA;CD7rBH;AAkUC;EC+XF,gCAAA;EAAA,gCAAA;EAAA,yBAAA;EACI,wBAAA;EAIJ,uBAAA;EACI,mBAAA;EACA,oBAAA;CDjsBH;ACosBG;ED/XA,aAAA;ECkYJ,0BAAA;EACI,+BAAA;MAAA,uBAAA;UAAA,mBAAA;EACA,uBAAA;EACA,mBAAA;EDhYA,YAAA;ECoYJ,uBAAA;EACI,oBAAA;EACA,mDAAA;UAAA,2CAAA;EDlYA,kCAAA;EAAA,0BAAA;CAlUH;ACwrBG;EAgBA,UAAA;CDrsBH;ACqrBG;EAmBA,6BAAA;EACA,gCAAA;CDrsBH;ACirBG;ED7WI,8BAAA;EC4XR,iCAAA;CD5rBC;ACwsBO;EACR,cAAA;CDtsBC;AC2sBG;EACA,eAAA;CDzsBH;AC6sBG;;EDrYA,WAAA;CApUH;AAuUC;;EC0YE,8BAAA;EDvYA,sBAAA;CArUH;ACitBG;;EACA,gBAAA;CD9sBH;AAwUD;ECyYQ,mBAAA;EACA,aAAA;EACA,iBAAA;EACA,eAAA;EACA,YAAA;EACA,aAAA;EDvYJ,YAAA;EC4XJ,0BAAA;EAeQ,iCAAA;EAGA,yBAAA;EACA,qCAAA;EDxYJ,4CAAA;CAtUH;AAyUD;EC6YQ,eAAA;EACA,qCAAA;CDntBP;AA0UD;EACI,kBAAA;CAxUH;AA2UD;ECgYA,gBAAA;EAeQ,WAAA;EACA,SAAA;ED5YJ,UAAA;ECiZJ,iBAAA;EACI,cAAA;EACA,aAAA;CDztBH;AC6tBD;EACI,mBAAA;EACA,WAAA;EACA,aAAA;EACA,cAAA;EACA,aAAA;EACA,aAAA;EACA,gBAAA;EACA,iBAAA;EDhZA,YAAA;ECwYJ,0BAAA;EAWQ,kBAAA;EACA,kBAAA;EDhZJ,kCAAA;UAAA,0BAAA;ECoYJ,gBAAA;EAeQ,eAAA;EACA,oBAAA;EACA,+CAAA;UAAA,uCAAA;CD3tBP;AA6UD;ECkZQ,iCAAA;UAAA,yBAAA;EACA,mBAAA;EACA,aAAA;EDhZJ,YAAA;CA3UH;AA8UD;ECmZQ,cAAA;CD9tBP;AA+UD;ECwZI,mBAAA;EACA,WAAA;EACA,SAAA;EACA,WAAA;EACA,iBAAA;EACA,YAAA;EACA,aAAA;EACA,iBAAA;CDpuBH;AAgVD;EC2ZA,eAAA;EACI,iBAAA;EACA,YAAA;EDzZA,0BAAA;EC4ZJ,mBAAA;EACI,kBAAA;EACA,iCAAA;UAAA,yBAAA;ED1ZA,aAAA;EC6ZJ,eAAA;EACI,oBAAA;ED3ZA,wFAAA;EAAA,sDAAA;EC+ZJ,yCAAA;UAAA,iCAAA;EACI,mBAAA;EACA,UAAA;EACA,aAAA;CD5uBH;AAqVD;EC+ZI,gBAAA;CDjvBH;AAsVD;ECgaI,gBAAA;CDnvBH;AAwVD;ECgaI,iCAAA;CDrvBH;ACyvBD;EACI,oBAAA;ED/ZA,mBAAA;CAvVH;ACqvBD;EAKI,oBAAA;EACA,UAAA;EACA,WAAA;EACA,eAAA;EACA,gBAAA;CDvvBH;AA0VD;ECkaI,qDAAA;EACA,iDAAA;EACA,6BAAA;EDhaA,uBAAA;ECmaH,gCAAA;EACG,kBAAA;CD1vBH;AA4VD;EACA,aAAA;CA1VC;AAgWD;EACI,sBAAA;CA9VH;AAiWD;ECqaA,cAAA;CDnwBC;ACwwBD;EACI,cAAA;CDtwBH;AAmWD;ECuaI,YAAA;CDvwBH;AAoWD;ECuaI,+7BAAA;EACA,wCAAA;EDraA,8CAAA;ECwaJ,aAAA;CDzwBC;AC8wBD;EAEI,o7BAAA;EDzaA,6BAAA;EC4aJ,mCAAA;CD9wBC;ACmxBD;EACI,+7BAAA;EACA,wCAAA;EACA,+CAAA;ED5aA,aAAA;CApWH;AAuWD;EC8aI,+7BAAA;ED5aA,wCAAA;ECgbJ,8CAAA;EACI,aAAA;CDpxBH;ACwxBD;EAA8B,6BAAA;CDrxB7B;AAyWD;;ECkbA,gBAAA;CDvxBC;AAyWD;ECgbI,uBAAA;CDtxBH;AA0WD;ECkbI,yBAAA;CDzxBH;AAuWD;;EAMI,uBAAA;CAzWH;AAmWD;EC8bI,uBAAA;CD9xBH;AA+WD;ECqbI,cAAA;CDjyBH;AAiXD;ECsbI,+BAAA;EDpbA,gBAAA;EAGA,OAAA;ECybJ,QAAA;EACI,eAAA;EDvbA,eAAA;EC0bJ,YAAA;EACI,aAAA;CD1yBH;AAoXD;EC4bI,mBAAA;EACA,YAAA;CD7yBH;AAqXD;EACI,oBAAA;EC6bJ,uBAAA;CD/yBC;AAsXD;EC6bI,uBAAA;EACA,mBAAA;EACA,iBAAA;ED3bA,aAAA;EC8bJ,cAAA;EACI,mBAAA;CDjzBH;AA8WD;ECqcI,0BAAA;CDhzBH;AAuXD;EACI,iCAAA;ECkcJ,iBAAA;EACI,sBAAA;CDtzBH;AACD,qBAAqB;AAyXrB;EAGI,iBAAA;ECwcJ,aAAA;EACI,mBAAA;EDtcF,mBAAA;ECqcF,eAAA;EAGQ,0BAAA;EDrcN,wHAAA;UAAA,gHAAA;CAzXD;AA2XD;EACI,oBAAA;EC0cJ,uBAAA;EACI,eAAA;EACA,aAAA;CDl0BH;ACu0BD;EACI,mBAAA;EACA,gBAAA;CDr0BH;ACm0BD;EDvcQ,0BAAA;CAzXP;AA6XD;EC+cA,sBAAA;EACI,eAAA;CDz0BH;AAyXD;ECmdA,gBAAA;CDz0BC;AAsXD;ECmdoG,oBAAA;CDt0BnG;ACs0BgP;EAAqC,kBAAA;EAAyC,gBAAA;EAC3T,YAAA;CDl0BH;ACi0BgP;EDnczO,YAAA;EC2cR,0BAAA;EACI,sBAAA;CDr0BH;AC4zBgP;EAc/O,aAAA;EACE,kBAAA;EACA,aAAA;CDv0BH;ACuzBgP;EAmB7O,kBAAA;CDv0BH;ACozBgP;EAsB7O,YAAA;CDv0BH;ACizBgP;EDnbzO,iBAAA;EC8cN,gBAAA;CDx0BD;AA+XD;EC8cI,iBAAA;EACA,iCAAA;ED5cA,aAAA;CA7XH;AA+XD;EACI,gCAAA;CA7XH;AAiYD;ECgeI,gBAAA;EACA,iBAAA;EACA,kBAAA;EACA,mBAAA;EACA,sBAAA;CD91BH;AAkYD;EACI,gBAAA;ECkeF,eAAA;EACE,eAAA;CDj2BH;AAoYD;ECkeI,oBAAA;EACA,mBAAA;CDn2BH;ACu2BC;EACE,oBAAA;EDjeA,YAAA;ECoeF,aAAA;EACE,eAAA;EACA,iBAAA;EDleJ,gBAAA;EACA,2BAAA;CAnYC;AC81BC;EAUO,eAAA;EACL,aAAA;EACA,0BAAA;EDleI,iBAAA;ECqeN,gBAAA;CDt2BD;AAsYD;ECseI,+BAAA;EACA,0BAAA;CDz2BH;AAuYD;ECseI,+BAAA;CD12BH;AC82BC;EACE,+BAAA;CD52BH;AAyYD;ECweE,yBAAA;CD92BD;AA0YD;EACI,oBAAA;ECyeF,mBAAA;EACE,oBAAA;CDh3BH;AAqYD;EC8eQ,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACF,kBAAA;EDxeE,qBAAA;EC6eN,yBAAA;MAAA,sBAAA;UAAA,wBAAA;EACE,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,iBAAA;ED3eI,gBAAA;CAvYP;AA4XD;ECofE,kBAAA;CD72BD;AAyXD;EC6fQ,oBAAA;ED1eA,eAAA;CAxYP;AC23BC;EAEE,qBAAA;EAAA,qBAAA;EAAA,cAAA;ED9eI,kBAAA;ECifN,qBAAA;EACE,yBAAA;MAAA,sBAAA;UAAA,wBAAA;ED/eI,0BAAA;MAAA,uBAAA;UAAA,oBAAA;ECkfN,iBAAA;EAEM,gBAAA;CD73BP;ACk3BC;EDneM,kBAAA;CA5YP;AC+2BC;EDheM,oBAAA;ECyfN,eAAA;CDp4BD;ACo4BC;EAQM,oBAAA;EDzfJ,mBAAA;ECifF,YAAA;CD/3BD;AC84BC;EAEM,oBAAA;ED5fJ,YAAA;EC0fF,aAAA;EAMM,mBAAA;EACA,kBAAA;ED7fJ,qBAAA;EAAA,qBAAA;EAAA,cAAA;ECsfF,yBAAA;MAAA,sBAAA;UAAA,wBAAA;EAWM,mBAAA;CD/4BP;ACo4BC;EAcM,iBAAA;ED9fA,2BAAA;CAhZP;ACg4BC;EAkBM,qBAAA;EAAA,qBAAA;EAAA,cAAA;ED9fA,6BAAA;EAAA,8BAAA;MAAA,2BAAA;UAAA,uBAAA;EC4eN,gBAAA;EAqBQ,yBAAA;MAAA,sBAAA;UAAA,wBAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,YAAA;EACA,aAAA;ED9fF,aAAA;ECseN,aAAA;CDr3BD;ACq3BC;EDjeM,oBAAA;MAAA,YAAA;UAAA,QAAA;ECieN,qBAAA;EAAA,qBAAA;EAAA,cAAA;EAiCQ,0BAAA;MAAA,uBAAA;UAAA,oBAAA;CDj5BT;AAwZD;ECigBU,iBAAA;ED/fN,8BAAA;ECsdF,eAAA;EA4CM,mBAAA;EACA,gBAAA;EACA,oBAAA;EACA,aAAA;EACA,iBAAA;EACA,mBAAA;EACA,gBAAA;ED/fJ,gBAAA;CAtZH;AC85BC;EACI,aAAA;EACA,mBAAA;CD55BL;AA4ZD;EACI,YAAA;ECygBF,aAAA;CDl6BD;AA6ZD;EC0gBE,YAAA;CDp6BD;AA+ZD;EC2gBI,oBAAA;EACA,0BAAA;EACA,eAAA;EACA,iBAAA;EACA,YAAA;EDzgBA,oBAAA;EC4gBF,mBAAA;EACE,4BAAA;EACA,+BAAA;EACA,mBAAA;EACA,mBAAA;CDx6BH;AAgaD;EC6gBI,oBAAA;EACI,0BAAA;ED3gBJ,eAAA;EC8gBF,mBAAA;EACE,gBAAA;ED5gBA,YAAA;EC0hBJ,oBAAA;EACA,mBAAA;CDv7BC;AAiaD;EC0hBI,YAAA;CDx7BH;AAkaD;EACI,0BAAA;EACA,eAAA;EC2hBJ,aAAA;EAAA,iBAAA;EAAA,YAAA;EAEQ,aAAA;EDxhBJ,YAAA;EC8hBF,mBAAA;EAA6B,SAAA;EAC3B,oCAAA;UAAA,4BAAA;EACA,gBAAA;EACA,mBAAA;CD57BH;ACi8BD;EACI,0BAAA;ED7hBA,aAAA;ECgiBJ,kBAAA;EACI,iBAAA;EACA,YAAA;EACA,aAAA;EACA,WAAA;EACA,mBAAA;EACA,SAAA;EACA,oCAAA;UAAA,4BAAA;EACA,gBAAA;EACA,mBAAA;CDh8BH;AAoaD;ECiiBA,oCAAA;CDl8BC;ACs8BD;EACI,YAAA;CDp8BH;AAuaD;ECmiBI,oBAAA;EACA,0BAAA;EACA,eAAA;EACA,gBAAA;EACA,YAAA;EACA,oBAAA;EACA,mBAAA;EACA,6BAAA;EDjiBA,gCAAA;ECoiBJ,mBAAA;CDx8BC;AAwaD;ECoiBI,+BAAA;CDz8BH;AA0aD;ECuiBI,iCAAA;CD98BH;ACs9BD;EACI,gBAAA;CDp9BH;AA8aD;EACI,qBAAA;EAAA,qBAAA;EAAA,cAAA;EC6iBJ,sBAAA;EACI,oBAAA;MAAA,gBAAA;CDx9BH;AA+aD;;EC8iBI,eAAA;EACA,gBAAA;CDz9BH;AAgbD;EC+iBI,WAAA;ED7iBA,iBAAA;CA9aH;AAibD;;ECgjBI,gBAAA;EACA,iBAAA;CD79BH;AAkbD;;;ECijBY,mBAAA;ED/iBR,eAAA;EACA,YAAA;ECkjBF,iBAAA;CD/9BD;AAibD;ECkjBI,gBAAA;CDh+BH;ACo+BC;EACE,mCAAA;CDl+BH;AAmbD;ECmjBI,mCAAA;CDn+BH;AAobD;ECqjBY,oDAAA;UAAA,4CAAA;CDt+BX;AAqbD;ECsjBI,sDAAA;UAAA,8CAAA;EDpjBA,WAAA;ECujBF,YAAA;CDz+BD;AAsbD;ECwjBE,gDAAA;UAAA,wCAAA;EACE,WAAA;EAAO,YAAA;CD1+BV;AAwbD;EACI,gDAAA;CAtbH;AAybD;EACI,sDAAA;CAvbH;AA0bD;EACI,8BAAA;CAxbH;AC6+BC;EAAmC,UAAA;EACjC,iBAAA;EACA,oBAAA;EACA,gBAAA;EACA,eAAA;EACA,iBAAA;CD1+BH;AA6bD;EACI,UAAA;ECmjBJ,gBAAA;EACI,oBAAA;EACA,gBAAA;EDjjBA,iBAAA;ECojBJ,YAAA;CD9+BC;ACo/BD;EAAO,YAAA;EACH,uBAAA;EDpjBA,mBAAA;CA5bH;ACu/BkB;EAAyB,4BAAA;EAAyB,6BAAA;CDn/BpE;AAkcD;EACI,8BAAA;CAhcH;AAmcD;ECujBA,gBAAA;EAGQ,iEAAA;UAAA,yDAAA;CDz/BP;AAscD;ECwjBI,kBAAA;CD3/BH;AAwcD;EACI,gCAAA;EAAA,gCAAA;EAAA,yBAAA;CAtcH;AAqcD;ECyjBQ,iCAAA;CD3/BP;AAycD;EACI,kBAAA;CAvcH;AA0cD;ECsiBA,qCAAA;EAkBQ,uBAAA;EACA,oCAAA;UAAA,4BAAA;CD9/BP;AA4cD;EC+hBA,yBAAA;EAwBY,2BAAA;EDpjBR,+BAAA;CA1cH;AA6cD;EC2jBI,iBAAA;EDzjBA,oBAAA;CA3cH;ACigCD;EAQQ,kDAAA;UAAA,0CAAA;CDtgCP;AAgdD;;;;;;;;;;EACI,eAAA;CArcH;AACD,eAAe;ACi/Bf;EAoBQ,0BAAA;EACA,eAAA;CDlgCP;AA6cC;ECgiBF,gBAAA;EA2BQ,oBAAA;EACA,YAAA;EACA,mBAAA;EDxjBJ,sBAAA;EC2hBJ,0BAAA;EAgCQ,+CAAA;UAAA,uCAAA;EDxjBJ,iBAAA;ECwhBJ,YAAA;EAmCQ,sBAAA;CDpgCP;AA8cC;EC4jBF,SAAA;EAAA,UAAA;EAEI,oBAAA;ED1jBA,mBAAA;EC8jBJ,YAAA;EAAA,sBAAA;CDxgCC;ACgiCO;EACA,mBAAA;CD9hCP;ACmiCD;EACI,4BAAA;EACA,0CAAA;EACA,2CAAA;EACA,4CAAA;EACA,aAAA;EDlkBA,sBAAA;EC6jBJ,cAAA;EAOQ,iBAAA;CDhiCP;ACmiCO;EACA,gBAAA;CDjiCP;ACoiCO;EACA,4BAAA;EACA,0CAAA;EDjkBJ,2CAAA;ECijBJ,yCAAA;EAmBQ,UAAA;EACJ,sBAAA;EACA,cAAA;EDjkBA,iBAAA;CAheH;AAmeC;ECyiBF,iBAAA;CDzgCC;ACsiCO;EDjkBJ,4BAAA;ECoiBJ,0CAAA;EAgCQ,yCAAA;EDjkBJ,4CAAA;ECiiBJ,WAAA;EAmCQ,qBAAA;EACA,eAAA;EACA,gBAAA;CDpiCP;ACuiCO;EACA,kBAAA;CDriCP;AAseC;ECshBF,4BAAA;EA8CQ,yCAAA;EACA,2CAAA;EACA,4CAAA;EACA,YAAA;EACA,qBAAA;EDjkBJ,eAAA;EC+gBJ,gBAAA;CDl/BC;ACyiCO;EACA,mBAAA;EDjkBJ,WAAA;ECukBJ,oDAAA;EAAA,4CAAA;CD3iCC;AC8iCG;EACA,oBAAA;EACA,WAAA;EACA,kCAAA;EAAA,0BAAA;CD5iCH;AC+iCG;EACA,0BAAA;CD7iCH;ACgjCG;EDrkBE,yBAAA;UAAA,iBAAA;CAxeL;AA6eC;ECykBF,8BAAA;EAEQ,wBAAA;CDpjCP;AAyeC;EAIM,yBAAA;CA1eP;AAseC;EAOM,mBAAA;EC6kBR,gBAAA;EACI,YAAA;CDtjCH;AA+eC;ECskBF,qBAAA;CDljCC;AC4jCO;EACJ,iEAAA;UAAA,yDAAA;ED3kBA,8BAAA;CA9eH;AAifC;EC6jBF,qBAAA;CD3iCC;AAkfC;EC6kBU,qBAAA;CD5jCX;AC+jCe;EAvBhB,qCAAA;CDriCC;AAwfC;EC6iBF,oCAAA;UAAA,4BAAA;CDliCC;ACokCW;EAEA,YAAA;ED1kBF,aAAA;ECsiBV,6BAAA;CD7hCC;AC+jCW;EAlCZ,aAAA;CD1hCC;AC4jCW;EAlCZ,2BAAA;CDvhCC;ACuhCD;EDxhBQ,aAAA;CA5fP;ACohCD;EDphBQ,YAAA;ECohBR,aAAA;CDhhCC;ACghCD;ED/gBQ,eAAA;EC+gBR,0BAAA;EAgEQ,2BAAA;ED5kBA,mCAAA;CA9fP;AC0gCD;EAoEQ,qCAAA;ED5kBA,oCAAA;UAAA,4BAAA;CA9fP;ACsgCD;EAwEQ,uBAAA;EACA,2BAAA;EACA,4BAAA;EACA,sCAAA;CD3kCP;ACggCD;EA+EQ,8BAAA;ED5kBA,2BAAA;EC6fR,+BAAA;CD3/BC;AC2/BD;EAAA,mBAAA;EAoFY,4BAAA;EACA,0BAAA;EACA,2BAAA;ED3kBF,4BAAA;ECqfV,+BAAA;EA0FY,gCAAA;EACA,+CAAA;EACA,gDAAA;CD5kCX;ACg/BD;EAiGQ,mBAAA;EACA,WAAA;EACA,gBAAA;ED9kBA,eAAA;EC2eR,iBAAA;EAsGQ,oBAAA;EACA,aAAA;CD9kCP;ACklCO;EACA,qCAAA;EACA,0BAAA;CDhlCP;AAogBC;EC+dF,2BAAA;EAkHQ,0BAAA;CDjlCP;AAogBC;EC2dF,4BAAA;CD59BC;ACwlCO;EACA,YAAA;ED9kBF,cAAA;CAvgBL;ACylCO;EACA,0BAAA;CDvlCP;AC0lCO;ED9kBJ,8CAAA;UAAA,sCAAA;ECycJ,mBAAA;EAyIQ,YAAA;EACA,aAAA;EACA,SAAA;EACA,QAAA;CDzlCP;AC6lCO;EACA,8CAAA;UAAA,sCAAA;EACA,mBAAA;EDhlBJ,YAAA;EC8bJ,aAAA;EAoJY,SAAA;ED/kBR,QAAA;CA1gBH;AC6lCW;EACA,8CAAA;UAAA,sCAAA;CD3lCX;ACk8BD;EA6JgB,8CAAA;UAAA,sCAAA;CD5lCf;AC+7BD;EDpaA;ICklBY,0CAAA;YAAA,kCAAA;GDzmCT;EC27BH;IAiLY,2CAAA;YAAA,mCAAA;GDzmCT;ECw7BH;IAqLQ,8CAAA;YAAA,sCAAA;GD1mCL;CACF;ACo7BD;EDpaA;ICklBY,0CAAA;YAAA,kCAAA;GDzmCT;EC27BH;IAiLY,2CAAA;YAAA,mCAAA;GDzmCT;ECw7BH;IAqLQ,8CAAA;YAAA,sCAAA;GD1mCL;CACF;AA2hBD;;;ECqlBQ,iCAAA;CD3mCP;ACknCO;;EDnlBJ,8CAAA;UAAA,sCAAA;ECgZJ,mBAAA;EAqMY,gBAAA;EDllBR,cAAA;CA3hBH;ACw6BD;EA6MQ,aAAA;CDlnCP;ACq6BD;EAkNQ,mBAAA;EDrlBJ,YAAA;ECmYJ,WAAA;EAsNQ,oBAAA;EDtlBJ,iBAAA;ECgYJ,0BAAA;EA0NQ,mCAAA;EACA,iBAAA;EACA,iBAAA;EACA,gBAAA;EACA,WAAA;CDtnCP;ACw5BD;EAmOQ,eAAA;CDxnCP;ACq5BD;EAsOY,eAAA;CDxnCX;AAmiBD;EC0lBQ,mBAAA;EACA,cAAA;EDxlBJ,oBAAA;EC4WJ,iBAAA;EAgPQ,0BAAA;EACA,mCAAA;EDzlBJ,iBAAA;EACA,iBAAA;EC+lBJ,gBAAA;EAEQ,WAAA;CDhoCP;AC8nCD;EAMQ,gBAAA;EACA,gBAAA;EACA,iBAAA;EACA,eAAA;CDjoCP;AAqiBD;ECgmBQ,sBAAA;CDloCP;AAsiBD;EACI,cAAA;EC8kBJ,iBAAA;CDjnCC;AA2iBD;EACI,gBAAA;ECqkBJ,eAAA;EA4BQ,gBAAA;EACA,uBAAA;CDxoCP;AACD,8BAA8B;AC8oC9B;EACI,qBAAA;CD5oCH;AC+oCG;EDhmBA,oBAAA;EComBJ,UAAA;EACI,gBAAA;EDlmBA,gBAAA;ECimBJ,OAAA;EAGQ,QAAA;EAHR,YAAA;EAMQ,YAAA;CD/oCP;AACD,uBAAuB;ACkpCf;EDjmBJ,eAAA;ECqmBJ,mBAAA;EACI,WAAA;EACA,aAAA;EACA,aAAA;EACA,sDAAA;UAAA,8CAAA;EDnmBA,WAAA;ECqmBI,qDAAA;UAAA,6CAAA;CDlpCP;AACD,4CAA4C;ACqpCpC;EACA,eAAA;EACA,gBAAA;EDlmBJ,cAAA;ECslBJ,UAAA;EAeQ,YAAA;CDnpCP;ACooCD;EAmBQ,YAAA;EDlmBJ,aAAA;EC+kBJ,+BAAA;UAAA,uBAAA;EAqBY,cAAA;EACA,8BAAA;EACA,uBAAA;EDjmBR,wBAAA;EC0kBJ,mBAAA;EAAA,2DAAA;UAAA,mDAAA;CDznCC;ACwpCW;EACA,iBAAA;EDjmBR,mBAAA;CApjBH;AAujBC;;ECqmBM,mBAAA;CDxpCP;AC2pCO;EACA;IAAA,gCAAA;GDxpCL;ECypCK;IAAA,kCAAA;GDtpCL;CACF;AAojBC;ECwmBF;IAAA,gCAAA;YAAA,wBAAA;GDxpCG;ECypCC;IAAA,kCAAA;YAAA,0BAAA;GDtpCD;CACF;ACwpCG;EDtmBE,oBAAA;CA/iBL;AAkjBC;;EC+lBF,sBAAA;EASQ,YAAA;EACA,oBAAA;EDrmBJ,yBAAA;UAAA,iBAAA;EC2lBJ,cAAA;CDzoCC;AC0pCD;EACI,6BAAA;CDxpCH;AAmjBD;EACI,iBAAA;ECmmBJ,sBAAA;CDnpCC;ACmpCD;ED7lBI,6BAAA;CAnjBH;ACgpCD;;EAgBQ,iBAAA;CD5pCP;AAujBD;ECqlBA,2BAAA;CDzoCC;AAwjBD;;;;EACI,6BAAA;ECglBJ,sBAAA;EA0BQ,oCAAA;CD3pCP;ACioCD;EA6BY,0BAAA;CD3pCX;AC8nCD;EAmCQ,8BAAA;EACA,qBAAA;EACA,iBAAA;CD9pCP;AAwjBD;EC0mBQ,aAAA;EACA,mBAAA;EACA,iBAAA;EDxmBJ,iBAAA;EC6mBJ,oBAAA;EACI,iBAAA;CDlqCH;AAgjBD;ECsnBA,aAAA;EACI,kBAAA;EACA,mBAAA;EACA,oBAAA;EACA,gBAAA;CDnqCH;AAyiBD;ECsnBA,qBAAA;CD5pCC;AAsiBD;EC+nBQ,oCAAA;UAAA,4BAAA;EACA,0BAAA;CDlqCP;AAkiBD;ECmoBQ,uBAAA;CDlqCP;AA+hBD;EAwBY,wDAAA;UAAA,gDAAA;CApjBX;ACwqCD;EAGQ,cAAA;ED9mBJ,yCAAA;EC2mBJ,YAAA;CDnqCC;ACmqCD;EAMY,iCAAA;CDtqCX;ACgqCD;EASY,gBAAA;CDtqCX;AC6pCD;EDlmBQ,2BAAA;CAxjBP;AC0pCD;EAcgB,iBAAA;ED5mBR,mBAAA;EC8lBR,YAAA;EAoBQ,iBAAA;CDxqCP;ACopCD;EAuBQ,cAAA;EACA,iBAAA;EACA,gBAAA;EACA,iBAAA;ED/mBJ,YAAA;CAxjBH;AC6oCD;EA8BQ,iBAAA;EACA,YAAA;EACA,sBAAA;CDxqCP;ACwoCD;EAAA,4BAAA;CDroCC;ACqoCD;EAAA,4BAAA;EAsCY,mBAAA;EACA,wBAAA;CDvqCX;AA4jBD;;ECokBA,gBAAA;CD5nCC;AA8jBD;;EAEI,gBAAA;CA5jBH;AAgkBD;ECmnBQ,+BAAA;CDhrCP;AA6jBD;EAGQ,gBAAA;CA7jBP;AAikBD;ECqnBQ,aAAA;CDnrCP;AA8jBD;EC2nBI,sBAAA;EDrnBI,kBAAA;CAhkBP;AA0jBD;ECgoByB,iBAAA;EDtnBjB,uBAAA;ECwnBR,gBAAA;EACI,gBAAA;EACA,mBAAA;CDvrCH;AAokBD;EC0nBA,uBAAA;EACI,0BAAA;EACA,mBAAA;EDxnBA,mBAAA;ECsnBJ,mBAAA;CDvrCC;AA6jBD;EAOQ,mBAAA;ECynBR,UAAA;EACI,YAAA;EDvnBI,YAAA;EC0nBR,gBAAA;CD1rCC;AAsjBD;ECooBA,iBAAA;EAGQ,gBAAA;EACA,iBAAA;CDzrCP;AAijBD;ECooBA,YAAA;EAQQ,gBAAA;EDvnBJ,iBAAA;CAjkBH;AA4iBD;ECgpBQ,eAAA;CDzrCP;AAyiBD;EA2BQ,iBAAA;ECymBR,gBAAA;EAiBQ,iBAAA;CDzrCP;AAoiBD;ECupBY,UAAA;CDxrCX;AAiiBD;ECooBA,mBAAA;EAwBQ,eAAA;EACA,iBAAA;CDzrCP;AA4hBD;ECgqBQ,UAAA;EDvnBA,WAAA;EC2lBR,WAAA;EA+BQ,YAAA;CDzrCP;AAshBD;EA8CQ,UAAA;ECslBR,sBAAA;EAoCQ,YAAA;EACA,gBAAA;EACA,mBAAA;CDzrCP;AA+gBD;EC6qBQ,aAAA;EACA,mBAAA;EACA,UAAA;EACA,QAAA;CDzrCP;AAukBD;ECwnBY,oBAAA;EACA,YAAA;EDtnBR,mBAAA;ECmkBJ,gBAAA;EAsDY,mBAAA;EACA,0BAAA;EDtnBR,UAAA;EC+jBJ,WAAA;EA2DQ,gBAAA;EDvnBJ,uBAAA;CArkBH;AAukBD;EC8nBI,cAAA;CDlsCH;AAwkBD;EC+nBA,eAAA;EACI,iBAAA;CDpsCH;AACD,aAAa;AAykBb;ECgoBI,wBAAA;EACA,2BAAA;CDtsCH;AA6kBD;EACI,qCAAA;CA3kBH;AA6kBD;ECkoBI,YAAA;EDhoBA,mBAAA;CA3kBH;AAykBD;ECsoBI,gBAAA;CD5sCH;AAskBD;EC2oBI,aAAA;EACA,YAAA;EACA,iBAAA;EACA,oBAAA;CD9sCH;AAgkBD;EAcQ,mBAAA;CA3kBP;AA6jBD;ECopBI,YAAA;EACA,gBAAA;CD9sCH;AAyjBD;EAoBY,gBAAA;CA1kBX;AAsjBD;EC2pBI,eAAA;EACA,2BAAA;CD9sCH;AAkjBD;ECgqBI,4BAAA;EDpoBQ,wBAAA;ECuoBZ,kBAAA;CDhtCC;AA6iBD;ECuqBA,kBAAA;EACI,YAAA;EACA,gBAAA;EACA,gBAAA;EACA,uBAAA;CDjtCH;AAsiBD;EC8qBI,gBAAA;EACA,mBAAA;CDjtCH;AAkiBD;ECuqBA,oBAAA;CDtsCC;AA+hBD;ECwrBA,qBAAA;CDptCC;AA4hBD;;EAmDY,gBAAA;CA3kBX;AAwhBD;EC8rBI,kBAAA;EACA,mBAAA;CDntCH;AAohBD;ECmsBI,oBAAA;EACA,0BAAA;EACA,YAAA;CDptCH;AA+gBD;ECusBQ,iBAAA;CDntCP;AA4gBD;EC4rBA,iBAAA;EAeQ,0BAAA;CDntCP;AAwgBD;EC4rBA,uBAAA;EAmBQ,uBAAA;EACA,2BAAA;EDtoBA,8BAAA;EC4oBR,mBAAA;EACI,oBAAA;EACA,mBAAA;ED1oBI,kBAAA;EC6oBR,mBAAA;CDxtCC;AA6fD;EC+tBA,iBAAA;CDztCC;AA0fD;ECiuBI,YAAA;EACA,WAAA;EACA,mBAAA;CDxtCH;AAqfD;EA0FY,aAAA;EC4oBZ,WAAA;EAII,kBAAA;CD1tCH;AAgfD;ECgvBI,eAAA;EACA,iBAAA;EACA,WAAA;CD7tCH;AA2eD;ECqvBI,uBAAA;ED9oBI,0BAAA;EC+nBR,mBAAA;EAkBI,cAAA;EACA,aAAA;EACA,0BAAA;ED9oBI,mBAAA;EC0nBR,WAAA;EAuBQ,UAAA;ED9oBA,mBAAA;CA9kBP;AA+dD;ECqwBI,gBAAA;EACA,aAAA;CDjuCH;AA2dD;EC0wBA,cAAA;CDluCC;AAwdD;EC6wBI,eAAA;EACA,gBAAA;EACA,mBAAA;EACA,mBAAA;EDnpBI,iBAAA;CA9kBP;AAidD;ECqxBI,gBAAA;EDppBI,iBAAA;ECupBR,iBAAA;EACI,mBAAA;EDrpBI,UAAA;ECopBR,eAAA;CDjuCC;AAycD;EC+xBQ,uBAAA;EDrpBA,uBAAA;EC0pBR,2BAAA;EACI,iBAAA;EDvpBI,2BAAA;EC2pBR,mBAAA;EACI,kBAAA;EACA,oBAAA;EACA,mBAAA;CD1uCH;AA8bD;EC8yBI,iBAAA;CDzuCH;AA2bD;ECizBI,YAAA;EDzpBQ,WAAA;EC4pBZ,qBAAA;EAEQ,eAAA;CD3uCP;AAqbD;ECyzBQ,YAAA;EACA,gBAAA;CD3uCP;AAibD;EC6zBQ,kBAAA;ED3pBQ,cAAA;EC+pBhB,eAAA;CD7uCC;AA4aD;ECo0BgB,UAAA;EACZ,kBAAA;EACA,gBAAA;EACA,YAAA;CD7uCH;AAsaD;EC40BA,oBAAA;EAEQ,YAAA;CDhvCP;AAkaD;ECi1BQ,mBAAA;CDhvCP;AA+ZD;ECq1BQ,aAAA;ED/pBA,eAAA;CAjlBP;AA2ZD;EC41BI,YAAA;EACA,aAAA;EACA,eAAA;CDpvCH;AAsZD;EAgMQ,0BAAA;ECqqBR,uBAAA;EAEI,8BAAA;EACA,2BAAA;CDxvCH;AAgZD;EC02BI,mBAAA;CDvvCH;AA6YD;EC62BI,gBAAA;CDvvCH;AA0YD;ECq2BA,uBAAA;EAaQ,eAAA;CDxvCP;AAsYD;EAkNQ,mBAAA;CArlBP;AAmYD;EC63BA,aAAA;CD7vCC;AAgYD;EC63BA,aAAA;EAGQ,kCAAA;EACA,mBAAA;EDpqBA,aAAA;ECgqBR,QAAA;EAOQ,YAAA;CD5vCP;AAwXD;EAmOQ,mBAAA;EC0pBR,YAAA;CDjvCC;AAoXD;EC44BY,iBAAA;CD7vCX;AAiXD;ECm5BY,mBAAA;EACA,YAAA;EDxqBJ,iBAAA;CAxlBP;AA4WD;ECy5BY,gBAAA;EACA,uBAAA;CDlwCX;AACD,cAAc;AA+lBd;ECkrBQ,0BAAA;EACA,4BAAA;CD9wCP;AA2lBD;ECqoBA,yBAAA;EAkDQ,oBAAA;EACA,eAAA;EACA,gBAAA;CD9wCP;AAqlBD;EC4rBQ,yBAAA;ED/qBA,gBAAA;ECmrBR,oBAAA;EACI,eAAA;EACA,iBAAA;CDhxCH;AA8kBD;ECqsBI,gBAAA;EACA,kBAAA;EDjrBI,YAAA;ECsrBC,cAAA;CDnxCR;AAwkBD;EC8sBI,iBAAA;CDnxCH;AAqkBD;EC4tBE,gBAAA;EAEM,eAAA;EACA,gBAAA;EACA,mBAAA;EACA,qBAAA;CD/xCP;AC0xCC;EAUM,iBAAA;EDhsBJ,iBAAA;ECsrBF,mBAAA;EAaM,0BAAA;CDjyCP;AAomBD;EACI,iBAAA;CAlmBH;AAimBD;ECosBQ,gBAAA;CDlyCP;AA8lBD;ECysBE,kBAAA;EACI,mBAAA;CDpyCL;AA0lBD;EC8sBI,iBAAA;CDryCH;AC0yCD;EACI,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,+BAAA;EAAA,8BAAA;MAAA,wBAAA;UAAA,oBAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,+BAAA;EDpsBA,gCAAA;CAnmBH;ACoyCD;EAMuC,uBAAA;EACnC,uBAAA;EDpsBI,oBAAA;MAAA,YAAA;UAAA,QAAA;ECwsBN,oBAAA;EAAqD,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACnD,6BAAA;EAAA,8BAAA;MAAA,uBAAA;UAAA,mBAAA;EDrsBI,6BAAA;MAAA,gBAAA;CAlmBP;AC2xCD;EAkBU,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,cAAA;CD1yCT;ACuxCD;EAuBU,mBAAA;CD3yCT;ACoxCD;EAeE,uBAAA;EAWM,gBAAA;EDxsBI,cAAA;CAjmBX;AC+wCD;EAiCA,oBAAA;MAAA,YAAA;UAAA,QAAA;CD7yCC;AC4wCD;EAmCI,cAAA;ED1sBQ,oBAAA;EC6sBV,eAAA;EACI,gBAAA;CD7yCL;ACswCD;EA0CA,2BAAA;CD7yCC;ACmwCD;ED9pBQ,qBAAA;EAAA,qBAAA;EAAA,cAAA;EC8sBN,iBAAA;EACE,0BAAA;EACA,2BAAA;EACA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,+BAAA;ED5sBI,0BAAA;MAAA,uBAAA;UAAA,oBAAA;CAlmBP;AAwmBD;ECitBE,iBAAA;EAEE,iBAAA;EACA,mBAAA;EDhtBA,0BAAA;CAtmBH;AAkmBD;ECwtBQ,0BAAA;CDvzCP;AA+lBD;EC6tBI,iBAAA;EACA,iBAAA;CDzzCH;AA2lBD;ECguBQ,2BAAA;CDxzCP;AAymBD;EC2sBE,iBAAA;EAUM,oBAAA;EAEA,0BAAA;EACA,gBAAA;CD3zCP;AAmmBD;EC2sBE,mBAAA;CD3yCD;AAgmBD;EC+tBQ,mBAAA;CD5zCP;AA6lBD;EC2sBE,iBAAA;CDryCD;AA0lBD;EC2sBE,eAAA;EA4BM,UAAA;EACA,2BAAA;CD7zCP;AAqlBD;EAqBQ,gBAAA;ECsrBN,iBAAA;EAiCU,oBAAA;CD5zCX;AAglBD;ECivBQ,iBAAA;EDttBA,2BAAA;CAvmBP;AA4kBD;ECsvBI,2BAAA;EDxtBQ,iBAAA;CAtmBX;AAwkBD;ECkwBI,eAAA;EACA,kBAAA;ED9tBI,gBAAA;CAxmBP;AAmkBD;EAwCQ,eAAA;ECmuBN,gBAAA;EACE,YAAA;EACA,iBAAA;CD10CH;AA6mBD;ECmuBI,iBAAA;CD70CH;AA+mBD;ECouBI,iBAAA;EDluBA,0BAAA;ECquBJ,cAAA;EAAoB,eAAA;CDh1CnB;AAymBD;EAMQ,mBAAA;CA5mBP;AAsmBD;EC4uBI,gBAAA;EDluBI,oBAAA;CA5mBP;AAkmBD;EAaQ,gBAAA;ECsuBR,iBAAA;CDj1CC;AA8lBD;ECqvBI,oBAAA;CDh1CH;AAinBD;ECsuBQ,iBAAA;CDp1CP;AA8mBD;ECwuBQ,0BAAA;CDn1CP;AA2mBD;EC0uBY,iBAAA;EACA,mBAAA;EDpuBA,0BAAA;ECyuBJ,iBAAA;EACK,cAAA;EACT,gBAAA;EDvuBQ,YAAA;CA7mBX;AAkmBD;ECqvBI,eAAA;EAAI,gBAAA;CDn1CP;AA8lBD;EC4vBM,0BAAA;EDvuBE,iBAAA;EC0uBJ,cAAA;EACE,oBAAA;EDxuBE,gBAAA;EC2uBJ,YAAA;EAAK,iBAAA;CDx1CR;AAqlBD;EA6BQ,UAAA;EACA,oBAAA;EACA,cAAA;EACA,gBAAA;EACA,eAAA;CA/mBP;AA8kBD;EAoCQ,mBAAA;CA/mBP;AA2kBD;EAsCY,gBAAA;EACA,2BAAA;EACA,kBAAA;CA9mBX;AAskBD;EA2CY,iBAAA;CA9mBX;AAmkBD;EAgDQ,mBAAA;EACA,6BAAA;EACA,2BAAA;EACA,6BAAA;EACA,4CAAA;CAhnBP;AA4jBD;EAwDQ,mBAAA;EACA,4BAAA;EACA,2BAAA;EACA,6BAAA;EACA,+CAAA;CAjnBP;AAqjBD;EAgEQ,mBAAA;EACA,2BAAA;EACA,2BAAA;EACA,6BAAA;EACA,4CAAA;CAlnBP;AAunBD;EACI,sBAAA;CArnBH;AACD,mBAAmB;AAynBnB;EAAyB,qBAAA;CAtnBxB;AAwnBD;EACI,iBAAA;EACA,0BAAA;EACA,kBAAA;EACA,kBAAA;CAtnBH;AACD,iBAAiB;AA0nBjB;EACI,mBAAA;EACA,mBAAA;CAxnBH;AAsnBD;EAGU,mBAAA;CAtnBT;AAynBD;EACI,wBAAA;CAvnBH;AA0nBD;EACI,cAAA;CAxnBH;AAunBD;EAGQ,iBAAA;EACA,gBAAA;EACA,wBAAA;CAvnBP;AAknBD;EAQQ,0BAAA;CAvnBP;AA+mBD;EAWQ,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,oBAAA;EACA,kBAAA;CAvnBP;AAymBD;EAiBQ,mBAAA;CAvnBP;AAsmBD;EAmBY,0BAAA;EACA,iBAAA;CAtnBX;AAkmBD;EAwBQ,0BAAA;EACA,YAAA;EACA,aAAA;EACA,sBAAA;EACA,sBAAA;CAvnBP;AA2lBD;EA+BQ,iCAAA;EACA,WAAA;EACA,sBAAA;CAvnBP;AAslBD;EAoCQ,mBAAA;EACA,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,oBAAA;EACA,aAAA;EACA,YAAA;EACA,mBAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;CAvnBP;AA0kBD;EA+CY,aAAA;EACA,sBAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;CAtnBX;AAmkBD;EAsDY,eAAA;EACA,gBAAA;CAtnBX;AA+jBD;EA2DQ,iBAAA;CAvnBP;AA8nBD;EACI,iBAAA;EACA,kBAAA;EACA,+BAAA;EACA,mBAAA;CA5nBH;AA+nBD;EACI,gBAAA;EACA,aAAA;EACA,eAAA;EACA,UAAA;EACA,oBAAA;EACA,eAAA;EACA,YAAA;EACA,gBAAA;CA7nBH;AAioBD;EACI,YAAA;EACA,aAAA;EACA,mBAAA;CA/nBH;AAkoBD;EACI,4BAAA;EACA,0BAAA;CAhoBH;AAmoBD;EACI,YAAA;EACA,oBAAA;CAjoBH;AAooBD;EACI,kBAAA;EACA,gBAAA;EACA,iBAAA;EACA,0BAAA;EACA,eAAA;EACA,aAAA;CAloBH;AAqoBD;EACI,0BAAA;EACA,oBAAA;EACA,YAAA;EACA,WAAA;CAnoBH;AAsoBD;EACI,gBAAA;EACA,iBAAA;EACA,2BAAA;EACA,8BAAA;EACA,oBAAA;EACA,aAAA;CApoBH;AAuoBD;EACI,oBAAA;CAroBH;AAwoBD;EACI,oBAAA;EACA,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,aAAA;EACA,gBAAA;EACA,0CAAA;EACA,0BAAA;EACA,6BAAA;EACA,oBAAA;EACA,mBAAA;CAtoBH;AA6nBD;EAUU,kBAAA;CApoBT;AAuoBD;EACI,oBAAA;CAroBH;AAwoBD;EACI,oBAAA;CAtoBH;AAyoBD;EACI,iBAAA;EACA,gBAAA;EACA,iBAAA;EACA,aAAA;EACA,gBAAA;EACA,eAAA;EACA,eAAA;EACA,mBAAA;EACA,iEAAA;UAAA,yDAAA;CAvoBH;AA8nBD;EAWQ,YAAA;EACA,iBAAA;CAtoBP;AA0nBD;EAeQ,eAAA;EACA,oCAAA;CAtoBP;AAsnBD;EAmBQ,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;CAtoBP;AA4oBD;EACI,oBAAA;EACA,YAAA;CA1oBH;AA6oBD;EACI,aAAA;CA3oBH;AA8oBD;EACI,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,sBAAA;MAAA,mBAAA;UAAA,0BAAA;EACA,aAAA;CA5oBH;AA+oBD;EACI,aAAA;CA7oBH;AA4oBD;EAII,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,aAAA;EACA,2BAAA;MAAA,wBAAA;UAAA,qBAAA;CA7oBH;AAuoBD;EAUI,qBAAA;EAAA,qBAAA;EAAA,cAAA;EACA,0BAAA;MAAA,uBAAA;UAAA,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,eAAA;EACA,iBAAA;CA9oBH;AA+nBD;EAkBI,gBAAA;EACA,YAAA;EACA,qDAAA;UAAA,6CAAA;CA9oBH;AA0nBD;EAuBQ,YAAA;CA9oBP;AAopBD;EACI,WAAA;EACA,QAAA;EACA,eAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,wBAAA;CAlpBH;AAqpBD;EACI,YAAA;EACA,YAAA;EACA,QAAA;EACA,eAAA;EACA,yBAAA;MAAA,sBAAA;UAAA,wBAAA;EACA,aAAA;CAnpBH;AAspBD;EACI,mBAAA;EACA,WAAA;CAppBH;AAupBD;EACI,SAAA;CArpBH;AAopBD;EAIQ,uCAAA;EACA,wCAAA;EACA,+BAAA;EACA,gCAAA;CArpBP;AA0pBD;EACI,4BAAA;EACA,6BAAA;CAxpBH;AA2pBD;EACI,YAAA;EACA,gBAAA;EACA,sHAAA;EACA,2BAAA;EACA,6BAAA;EACA,2BAAA;EACA,mBAAA;EACA,YAAA;CAzpBH;AA4pBD;EAEQ,YAAA;EACA,gBAAA;EACA,qHAAA;EACA,2BAAA;EACA,6BAAA;EACA,2BAAA;EACA,mBAAA;EACA,YAAA;CA3pBP;AA+pBD;EACI,YAAA;EACA,gBAAA;EACA,qHAAA;EACA,2BAAA;EACA,6BAAA;EACA,4BAAA;EACA,mBAAA;EACA,WAAA;CA7pBH;AAgqBD;EAEQ,YAAA;EACA,gBAAA;EACA,sHAAA;EACA,2BAAA;EACA,6BAAA;EACA,4BAAA;EACA,mBAAA;EACA,WAAA;CA/pBP;AAmqBD;EACI,mBAAA;EACA,YAAA;EACA,WAAA;EACA,gBAAA;EACA,iBAAA;EACA,oBAAA;EACA,cAAA;EACA,yDAAA;UAAA,iDAAA;CAjqBH;AAqqBD;EAEI,YAAA;EACA,WAAA;EACA,kBAAA;EACA,gBAAA;EACA,iBAAA;EACA,oBAAA;EACA,cAAA;EACA,yDAAA;UAAA,iDAAA;EACA,OAAA;EACA,aAAA;CApqBH;AAypBD;EAaQ,aAAA;CAnqBP;AAspBD;EAgBQ,wCAAA;UAAA,gCAAA;CAnqBP;AAuqBD;EACI,oCAAA;UAAA,4BAAA;CArqBH;AAwqBD;EACI,oBAAA;CAtqBH;AAqqBD;;EAGQ,YAAA;EACA,eAAA;CApqBP;AAgqBD;EAOQ,yBAAA;CApqBP;AA6pBD;EAUQ,UAAA;CApqBP;AA0pBD;EAcY,iBAAA;EACA,gBAAA;CArqBX;AAspBD;EAqBY,aAAA;EACA,aAAA;EACA,oBAAA;CAxqBX;AAipBD;EA0BY,aAAA;EACA,aAAA;EACA,oBAAA;EACA,0BAAA;CAxqBX;AA2oBD;EAkCQ,iBAAA;CA1qBP;AAwoBD;EAqCQ,mBAAA;CA1qBP;AAqoBD;EA6CQ,sBAAA;EACA,WAAA;EACA,eAAA;CA/qBP;AAgoBD;EAkDQ,0BAAA;EACA,YAAA;EACA,mBAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;CA/qBP;AAmrBD;EACI,+NAAA;EACA,2BAAA;EACA,oDAAA;EACA,eAAA;EACA,gBAAA;EACA,sBAAA;CAjrBH;AAsrBC;EACE,kBAAA;EACA,6BAAA;EACA,8BAAA;EACA,iBAAA;CAprBH;AAisBC;EAEM,aAAA;EACA,iBAAA;EACA,YAAA;EACA,sBAAA;EACA,mBAAA;EACA,gBAAA;CAhsBP;AAyrBC;EAUM,iBAAA;CAhsBP;AAsrBC;EAaM,iBAAA;CAhsBP;AAmrBC;EAgBM,4BAAA;EACA,+BAAA;CAhsBP;AA+qBC;EAoBM,6BAAA;EACA,gCAAA;CAhsBP;AAosBC;EACI,yBAAA;CAlsBL;AAqsBC;EACE,8BAAA;EACA,iBAAA;EACA,kBAAA;CAnsBH;AAssBD;EACI,8BAAA;EACA,iBAAA;EACA,kBAAA;CApsBH;AAusBD;;EACI,0BAAA;CApsBH;AAwsBC;;EACE,iCAAA;CArsBH;AAwsBC;EAEQ,gBAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,iBAAA;EAEA,iEAAA;UAAA,yDAAA;CAxsBT;AAgsBC;EAWM,YAAA;CAxsBP;AA6rBC;EAcM,aAAA;CAxsBP;AA4sBD;EACI,+BAAA;EACA,4CAAA;CA1sBH;AA6sBC;EACI,mCAAA;EACA,oCAAA;UAAA,4BAAA;CA3sBL;AA6sBD;EACI,+BAAA;EACA,4CAAA;EACA,oBAAA;CA3sBH;AA8sBC;EACE,uBAAA;EACA,+BAAA;EACA,iCAAA;EACA,oCAAA;UAAA,4BAAA;CA5sBH;AAwsBC;EAMM,uBAAA;CA3sBP;AAgtBC;EACF,0BAAA;CA9sBC;AAitBC;EAEE,+BAAA;EACA,iCAAA;CAhtBH;AA6sBC;EAOM,uBAAA;CAjtBP;AAqtBC;EACE,+BAAA;EACA,4CAAA;CAntBH;AAitBC;EAIM,0BAAA;CAltBP;AA8sBC;EAOU,uBAAA;CAltBX;AA2sBC;EAUM,uBAAA;EACA,+BAAA;EACA,iCAAA;EACA,oCAAA;UAAA,4BAAA;CAltBP;AAqsBC;EAkBM,uBAAA;EACA,+BAAA;EACA,iCAAA;EACA,oCAAA;UAAA,4BAAA;CAptBP;AA+rBC;EAuBU,uBAAA;CAntBX;AA4rBC;EA4BM,uBAAA;EACA,+BAAA;EACA,iCAAA;EACA,oCAAA;UAAA,4BAAA;CArtBP;AAsrBC;EAiCU,uBAAA;CAptBX;AAmrBC;EAsCM,uBAAA;CAttBP;AA0tBC;EACE,wCAAA;CAxtBH;AA+tBD;EACI,qCAAA;CA7tBH;AAguBD;EACI,iCAAA;EACA,qCAAA;CA9tBH;AAiuBD;EACI,cAAA;CA/tBH;AAmuBC;EACE,gBAAA;EACA,OAAA;EACA,QAAA;EACA,aAAA;EACA,cAAA;EACA,cAAA;EACA,eAAA;EACA,gCAAA;CAjuBH;AAouBD;EACI,YAAA;EACA,aAAA;EACA,0BAAA;CAluBH;AAquBD;;EACI,2CAAA;EAAA,mCAAA;CAluBH;AAquBD;;EACI,WAAA;CAluBH;AAquBD;EACI,yBAAA;CAnuBH;AAsuBD;EACI,sBAAA;EACA,sBAAA;EACA,iBAAA;EACA,mBAAA;CApuBH;AAsuBG;EACI,0BAAA;EACA,0BAAA;EACA,YAAA;CApuBP;AAsuBO;EACI,0BAAA;EACA,sBAAA;EACA,YAAA;CApuBX;AAyuBD;EACI,cAAA;EACA,kDAAA;UAAA,0CAAA;CAvuBH;AAyuBC;EACE;;IACE,wBAAA;IACA,qEAAA;GAtuBH;EAyuBC;IACE,aAAA;IACA,qEAAA;GAvuBH;EA0uBC;IACE,0DAAA;GAxuBH;EA2uBC;;IACE,+CAAA;GAxuBH;CACF;AAutBC;EACE;;IACE,wBAAA;IACA,qEAAA;GAtuBH;EAyuBC;IACE,aAAA;IACA,qEAAA;GAvuBH;EA0uBC;IACE,0DAAA;GAxuBH;EA2uBC;;IACE,+CAAA;GAxuBH;CACF;;AAED,0k4CAA0k4C", "file": "admin-style.css", "sourcesContent": [null, "/* Global */\n\n.seedprod {\n}\n\nbody{\n    //font-family: 'Open Sans', sans-serif;\n}\n\nimg{border: inherit}\n\n[v-cloak]{\n\tdisplay: none !important;\n}\n\nbody, .subsubsub, .tablenav .tablenav-pages{\ncolor: #222;\n}\n\n.subsubsub .current{\n    color: #222 !important;\n}\n\n.search-box,.subsubsub{\n    margin-bottom:0px !important;\n}\n\nbody.seedprod-body{\n    background: #fff;\n}\n\nbody.seedprod_page_seedprod_pro_template #wpwrap, body.seedprod_page_seedprod_lite_template #wpwrap{\n    background:  #f7f6f7;\n}\n\n.seedprod_page_seedprod_pro_template{\nbutton, input, optgroup, select, textarea{\n    outline:none !important;\n    box-shadow: none !important;\n}\n}\n\n.seedprod_page_seedprod_lite_template{\n    button, input, optgroup, select, textarea{\n        outline:none !important;\n        box-shadow: none !important;\n    }\n    }\n\n.seedprod_page_seedprod_pro_template{\ninput[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], select, textarea {\n    box-shadow: 0 0 0 transparent;\n    border-radius: 4px;\n    border: 1px solid #d3ced2;\n    background-color: #fff;\n    color: #32373c;\n}\n}\n\n.seedprod_page_seedprod_lite_template{\n    input[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], select, textarea {\n        box-shadow: 0 0 0 transparent;\n        border-radius: 4px;\n        border: 1px solid #d3ced2;\n        background-color: #fff;\n        color: #32373c;\n    }\n    }\n\n// body.seedprod-body #wpwrap{\n//     background: #f1f1f1;\n// }\n\n.seedprod-builder{\n    display: none ;\n}\n\n.seedprod-action-txt{\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    overflow: hidden;\n    width: 340px;\n    display: inline-block;\n}\n\n#seedprod-page-name{\n    width:100%;\n    background:#fbfcfb;\n    height: 75px;\n    border-bottom:1px solid #f0f0f0;\n    margin-top: 67px;\n    display: table;\n    padding:0 23px;\n    input{\n        height:40px;\n        border-radius:2px;\n        width: 315px;\n    }\n    label{\n        margin-bottom: 0px;\n        font-size:16px;\n        margin-right:15px;\n    }\n    .form-group{\n        display: table-cell;\n        vertical-align: middle\n    }\n}\n.template-chooser{\n    h2{\n        margin-top:0;\n        margin-bottom: 15px;\n        font-size:24px;\n        color: #2d2d2d;\n        font-weight: 400;\n    }\n    p{\n        color: #666;\n        font-size: 14px;\n        margin-bottom:30px;\n    }\n    .highlight-template{\n        color: #f1470d !important;\n        text-decoration: underline;\n        :hover{\n            text-decoration: none;\n        }\n    }\n}\n\n.seedprod-builder #adminmenu,.seedprod-builder #wpadminbar{\n    display:none !important;\n}\n\n#seedprod-steps-nav{\n    border-left: 1px solid #f9f9f9;\n    a{\n        font-size:14px;\n        padding-left: 15px;\n        padding-right: 15px;\n    }\n}\n\n.seedprod-builder-step .container-fluid {\n    padding-left: 23px !important;\n    padding-right: 23px !important;\n}\n\n .seedprod-builder-step h1{\n    margin:20px 0 10px 0 !important;\n}\n\n.seedprod table{\n    background-color: #f8f8f8;\n}\n\n.seedprod .navbar-default{\n    background-color: #fafbfc;\n    border-color: #e8e8e8;\n    overflow: hidden;\n}\n\n.seedprod-builder .navbar-default{\n    background-color: #fff;\n}\n\n.seedprod .panel-default>.panel-heading {\n    color: #222;\n    background-color: #f8f8f8;\n    border-color: #ddd;\n    font-weight: bold;\n    position: relative;\n}\n\n.widefat thead th, .widefat thead td {\n    border-bottom: 1px solid #ddd;\n}\n\n.widget-top, .menu-item-handle, .widget-inside, #menu-settings-column .accordion-container, #menu-management .menu-edit, .manage-menus, table.widefat, .stuffbox, p.popular-tags, .widgets-holder-wrap, .wp-editor-container, .popular-tags, .feature-filter, .imgedit-group, .comment-ays {\n    border: 1px solid transparent;\n    box-shadow: 0 2px 2px 0 rgba(0,0,0,.14), 0 3px 1px -2px rgba(0,0,0,.2), 0 1px 5px 0 rgba(0,0,0,.12);\n    margin-top:20px;\n}\n\n\n\n\n// .seedprod-builder{ .form-control, input[type=\"text\"], input[type=\"password\"], input[type=\"checkbox\"], input[type=\"color\"], input[type=\"date\"], input[type=\"datetime\"], input[type=\"datetime-local\"], input[type=\"email\"], input[type=\"month\"], input[type=\"number\"], input[type=\"search\"], input[type=\"radio\"], input[type=\"tel\"], input[type=\"text\"], input[type=\"time\"], input[type=\"url\"], input[type=\"week\"], select, textarea{\n//     background-color: #fff;\n//     border-color: #DEDADE;\n//     box-shadow: none;\n// }\n// }\n\n.seedprod .form-control[disabled], .seedprod .form-control[readonly], fieldset[disabled] .seedprod .form-control {\n    background-color: #fbfcfb;\n    opacity: 1;\n}\n\n.seedprod-builder .input-group-addon{\n    background-color: #fff;\n    border-color: #DEDADE;\n}\n\n.column.entries, .column.subscribers, .column.active {\n    width:120px;\n}\n\n.column.type{\n    width:160px;\n}\n\n.column.status{\n    width:210px;\n    white-space: nowrap;\n}\n\n.column.starts{\n    width:130px;\n    white-space: nowrap;\n\n}\n.column.ends{\n    width:130px;\n    white-space: nowrap;\n}\n\n.vue-js-switch .v-switch-core{\n    background: #d6d6d6 !important;\n}\n\n.vue-js-switch.toggled .v-switch-core{\n    background: #4CAF50 !important;\n}\n\n\n\n\n\n// .wp-core-ui #post-body-content .button{\n//     color: #222;\n//     border-color: #ddd;\n//     background: #f8f8f8;\n//     box-shadow:none;\n// }\n\n\n.seedprod .navbar-default .navbar-nav>li>a , .seedprod .input-group-addon{\n    color: #b3b3b3;\n}\n\n.seedprod-builder #seedprod-sidebar-wrapper .input-group-addon{\n    color: #c7c7c7;\n    font-size:13px;\n\n}\n\n.seedprod .navbar-nav>li>a{\n    padding-top: 19px !important;\n    padding-bottom: 23px !important;\n}\n\n.seedprod-builder .navbar-nav>li>a{\n    padding-top: 23px !important;\n    padding-bottom: 23px !important;\n}\n\n#seedprod-steps-nav a span{\n    padding: 0 10px 24px;\n}\n\n.seedprod .navbar-default .navbar-nav>.active>a,\n.seedprod .navbar-default .navbar-nav>.active>a:focus\n{\n    color: #222;\n    background-color: #fff !important;\n    span{\n        box-shadow: inset 0 -3px 0px 0px #f1470d !important;\n        padding: 0 10px 24px;\n    }\n\n}\n\n\n.seedprod .navbar-default .navbar-nav>li>a:hover{\n    color: #222;\n    background-color: #fff !important;\n}\n\n.striped > tbody > :nth-child(even), ul.striped > :nth-child(even), .alternate {\n    background-color: #fff;\n}\n\n.seedprod-icon::before {\n    display: inline-block;\n    font-style: normal;\n    font-variant: normal;\n    text-rendering: auto;\n    -webkit-font-smoothing: antialiased;\n}\n\n.icon-refer-a-friend::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f004\";\n}\n\n.icon-refer-a-friend{\n    background-color: #e42e2f;\n    border-color: #e42e2f !important;\n    color: #fff;\n}\n\n.icon-automatic-entry::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f3ff\";\n}\n\n.icon-automatic-entry{\n    background-color: #FF9800;\n    border-color: #FF9800 !important;\n    color: #fff;\n}\n\n.icon-submit-image::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f03e\";\n}\n\n.icon-submit-image{\n    background-color: #009688;\n    border-color: #009688 !important;\n    color: #fff;\n}\n\n.icon-polls-surveys::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f681\";\n}\n\n.icon-polls-surveys{\n    background-color: #E91E63;\n    border-color: #E91E63 !important;\n    color: #fff;\n}\n\n.icon-join-newsletter{\n    background-color: #4CAF50;\n    border-color: #4CAF50 !important;\n    color: #fff;\n}\n\n.icon-join-newsletter::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f0e0\";\n}\n\n\n.icon-visit-a-page::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f35d\";\n}\n\n.icon-visit-a-page{\n    background-color: #0000ba;\n    border-color: #0000ba !important;\n    color: #fff;\n}\n\n.icon-visit-fb::before, .icon-fb-page-post::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f09a\";\n}\n\n.icon-visit-fb, .icon-fb-page-post{\n    background-color: #4267b2;\n    border-color: #4267b2 !important;\n    color: #fff;\n}\n\n.icon-tweet::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f099\";\n}\n\n.icon-tweet{\n    background-color: #38A1F3;\n    border-color: #38A1F3 !important;\n    color: #fff;\n}\n\n.icon-twitter-follow::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f099\";\n}\n\n.icon-twitter-follow{\n    background-color: #38A1F3;\n    border-color: #38A1F3 !important;\n    color: #fff;\n}\n\n.icon-instagram-follow::before, .icon-instagram-page-post::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f16d\";\n}\n\n.icon-instagram-follow, .icon-instagram-page-post{\n    background-color: #f55f3f;\n    border-color: #f55f3f !important;\n    color: #fff;\n}\n\n.icon-pinterest-follow::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f0d2\";\n}\n\n.icon-pinterest-follow{\n    background-color: #e60023;\n    border-color: #e60023 !important;\n    color: #fff;\n}\n\n.icon-youtube-follow::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f167\";\n}\n\n.icon-youtube-follow{\n    background-color: #ff0000;\n    border-color: #ff0000 !important;\n    color: #fff;\n}\n\n.icon-watch-a-video::before {\n    font-family: \"Font Awesome 5 Brands\"; font-weight: 400; content: \"\\f167\";\n}\n\n.icon-watch-a-video{\n    background-color: #ff0000;\n    border-color: #ff0000 !important;\n    color: #fff;\n}\n\n.icon-question::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f059\";\n}\n\n.icon-question{\n    background-color: #9900bb;\n    border-color: #9900bb !important;\n    color: #fff;\n}\n\n\n.icon-invent-your-own::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f0eb\";\n}\n\n.icon-invent-your-own{\n    background-color: #0f0f0f;\n    border-color: #0f0f0f !important;\n    color: #fff;\n}\n\n\n.seedprod-page-types{\n    display: flex;\n    margin-left:-20px;\n    margin-right:-20px;\n}\n\n.seedprod-page-type{\n    border:1px solid #ccc;\n    padding:20px;\n    margin-left:20px;\n    margin-right:20px;\n}\n\n\n#seedprod-dashboard{\npadding-right:20px;\n}\n\n.seedprod-app{\n    #poststuff{\n        padding-top:30px\n    }\n    .widefat{\n        margin-top:10px !important;\n        box-shadow: 0 1px 1px rgba(0,0,0,0.04) !important;\n    }\n}\n\n#seedprod-dashboard .column-active{\n\twidth:90px;\n}\n\n#seedprod-dashboard .column-subscribers{\n\twidth:120px;\n}\n\n#seedprod-dashboard .column-entries{\n\twidth:120px;\n}\n\n#seedprod-entries .column-entries{\n\twidth:120px;\n}\n\n#seedprod-entries .fa-trophy{\n    padding-right:5px;\n}\n\n#seedprod-entries #poststuff{\n    padding-top:10px;\n}\n\n#lpage-details-sub{\n    color: #999;\n    display: block;\n    font-size: 10px;\n    text-transform: uppercase;\n    margin: 0 0 8px 0;\n}\n\n#lpage-details-title{\n    float: left;\n    font-size: 18px;\n    font-weight: 400;\n    margin: 0;\n    padding-right:8px\n}\n\n#lpage-selector {\n    display: inline-block;\n    position: relative;\n}\n\n#seedprod-entries{\n    margin-top:20px;\n}\n\n#seedprod-entries .toggle.active {\n    background-color: #e3e3e3;\n    outline: none;\n}\n\n#seedprod-entries .toggle {\n    border-radius: 50%;\n    color: #444;\n    font-size: 16px;\n    box-shadow: none;\n}\n\n#seedprod-entries .toggle:before {\n    vertical-align: middle;\n}\n\n#lpage-details-actions{\n margin-top: 26px;\n text-align:right;\n}\n\n.lpage-details-actions{\n    margin-right:12px;\n    text-decoration:none;\n}\n\n.lpage-details-actions:last-child {\n    margin-right:0px;\n  }\n\n  .lpage-selector {\n    display: inline-block;\n    position: relative;\n}\n\n.lpage-selector ul {\n    max-height: 196px;\n    overflow-x: hidden;\n    padding: 0;\n    margin: 0;\n}\n\n#raffleprsss-lpage-list-loading{\n    text-align: center;\n    display: block;\n}\n\n.lpage-selector ul li {\n    list-style: none;\n    margin: 0;\n    font-size: 13px;\n}\n\n.lpage-selector ul li a {\n    display: block;\n    text-decoration: none;\n    padding: 5px 15px;\n    color: #444;\n}\n\n.lpage-selector ul li a:hover {\n    background-color: #f7f7f7;\n    color: #444;\n}\n\n#winner-selector{\n    display: inline-block;\n    position: relative;\n}\n\n#seedprod-winner-options{\n    background: #fff;\n    border-radius: 3px;\n    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);\n    position: absolute;\n    top: 69px;\n    left: -15px;\n    width: 300px;\n    z-index: 9992;\n    font-size: 13px;\n    font-weight: normal;\n    padding: 20px;\n    input[type=\"radio\"]{\n        margin-top: 3px;\n    }\n}\n\n#seedprod-winner-options:before{\n    content: \" \";\n    position: absolute;\n    top: -10px;\n    left: 145px;\n    width: 0;\n    height: 0;\n    border-style: solid;\n\n    border-width: 0 8px 10px 8px;\n    border-color: transparent transparent #fff transparent;\n}\n\n#lpage-list{\n    background: #fff;\n    border-radius: 3px;\n    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.1);\n    position: absolute;\n    top: 27px;\n    left: -108px;\n    width: 230px;\n    padding: 10px 0;\n    z-index: 9991;\n}\n\n#lpage-list:before{\n    content: \" \";\n    position: absolute;\n    top: -10px;\n    left: 110px;\n    width: 0;\n    height: 0;\n    border-style: solid;\n    border-width: 0 8px 10px 8px;\n    border-color: transparent transparent #fff transparent;\n}\n\n\n#seedprod-header{\n    position: relative;\n    background: #fff;\n    margin-left: -20px;\n    border-bottom:1px solid #ccc;\n}\n\n.seedprod-header-margin{\n    margin-top:20px !important;\n}\n\n.seedprod .navbar-right{\n    margin-right: 0;\n    border-color:transparent;\n    border-bottom-color: #e7e7e7;\n}\n\n.seedprod-breadcrumbs{\n    display: inline-block;\n    margin-top:10px;\n    margin-bottom:20px;\n}\n\n#seedprod-page-title{\n    background-color: #fff;\n    margin: 0 0 0px -20px;\n    padding: 15px 20px;\n    font-size: 23px;\n    font-weight: 400;\n    line-height: 29px;\n    border-bottom:1px solid #e7e8e7;\n    .add-new-h2 {\n        margin-left: 28px;\n    }\n}\n\n.seedprod-btn-orange {\n    background-color: #f1460d !important;\n    border-color: #f0460d !important;\n    color: #fff !important;\n}\n\n.seedprod-btn-orange:hover {\n    background-color: #c1390a !important;\n    border-color: #c1390a !important;\n    color: #fff !important;\n}\n\n\n.seedprod-breadcrumbs small{\n    font-size:14px;\n}\n\n.seedprod .container-fluid{\n    padding:0;\n}\n\n.d-ib{\n    display:inline-block;\n}\n\n.seedprod .tablenav{\n    margin: 0px 0 4px;\n}\n\n\n.seedprod-default-cusor{\n    cursor: default;\n}\n\n.seedprod td, .seedprod th{\n    padding:10px\n}\n\n.seedprod :focus{\n    outline: none !important;\n    box-shadow: none !important;\n}\n\n\n/* Builder */\n#seedprod-search-close{\n    margin-left: -24px;\n    margin-right: 11px;\n    color: #aaa;\n    z-index:999;\n    position: relative;\n    cursor: pointer;\n}\n// #seedprod-customizer {\n//     background: #fff;\n//     position: fixed;\n//     top: 0;\n//     bottom: 0;\n//     left: 0;\n//     right: 0;\n//     z-index: 99999;\n//     height: 100%;\n\n// }\n\n\n\n#seedprod-customizer-wrapper {\n    height: 100%;\n}\n\n.seedprod-builder .mx-input, #seedprod-sidebar-wrapper .form-control{\n    font-size: 16px;\n    height:40px;\n\n}\n\n.seedprod-builder .btn i{\n    color: rgba(0, 0, 0, 0.34);\n}\n\n// .seedprod-builder .seedprod-sidebar-form-body .btn i {\n//     margin-right: 4px;\n// }\n// .seedprod-builder .seedprod-settings-page-wrapper .btn i {\n//     margin-right: 4px;\n// }\n\n.rules-modal{\n\n    .seedprod-modal-container{\n        border-radius: 6px;\n        max-width:620px;\n        position: relative;\n    }\n    hr{\n        margin-top:15px;\n        margin-bottom:25px;\n    }\n    .seedprod-modal-body{\n        margin-top:0;\n    }\n    i{\n        color: #ccc;\n        cursor: pointer;\n        position:absolute;\n        top:20px;\n        font-size:16px;\n        right:20px\n    }\n}\n\n\n\n\n\n#seedprod-sidebar-nav-wrapper {\n    background-color: #2d2d2d;\n    color: #a3a3a3;\n    z-index: 200;\n    left: 0px;\n    position: absolute;\n    bottom: 0;\n    top: 67px;\n    right: auto;\n    width: 95px;\n    overflow-y: auto;\n    overflow-x: hidden;\n    .active{\n        background-color: #000;\n        color:#fff;\n    }\n}\n\n\n.sp-section-toolbar{\n    background: url(../svg/section-toolbar.svg);\n    background-position: top center;\n    background-repeat: no-repeat;\n    width: 218px;\n    height: 28px;\n    margin-top: -4px;\n    align-items: center;\n    justify-content: center;\n    span{\n        margin: 2px 2px 0 2px;\n    }\n    span:first-child{\n        margin-left:6px\n    }\n}\n\n#sp-sidebar-drawer{\ncursor: pointer;\n  height:76px;\n  width:13px;\n  background-image: url(../svg/sidebar-drawer.svg);\n  background-repeat: no-repeat;\n  background-size: auto;\n  background-position: -7px 0;\n  top:50%;\n  svg{\n    min-width: 23px;\n    margin-left: -6px;\n  }\n}\n\n.sp-sidebar-drawer-closed{\n    margin-left: -320px !important;\n}\n\n#seedprod-sidebar-wrapper {\n    background-color: #F7F6F7;\n    color: #4F394D;\n    width: 320px;\n    min-width: 320px;\n    z-index:10;\n    margin-top: 0px;\n    margin-bottom: 125px;\n    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15);\n}\n\n#seedprod-preview-wrapper.sp-setup_settings_connect{\n    background-color: #EDEBED !important;\n}\n\n\n\n#seedprod-preview-wrapper{\n    margin-bottom: 72px;\n    background-color: #7B6B7A;\n    overflow-y: auto;\n\n    #seedprod-builder-view{\n        margin-left:auto;\n        margin-right:auto;\n        background-color: #fff;\n    }\n}\n\n.sp-mobile-view{\n    border-radius: 10px;\n    border: 5px solid #4F394D;\n    box-shadow: 0px 15px 50px rgba(35, 8, 32, 0.25);\n    margin-top:15px;\n}\n\n\n.seedprod-sidebar-form-body {\n    padding: 0px;\n    padding-top:0px;\n    padding-bottom: 30px;\n    margin: 0 20px;\n    text-align: left;\n    box-shadow: inset 0 -1px 0 rgba(167,156,166,0.25);\n    .col-md-6:first-child{\n        padding-right:7px;\n    }\n    .col-md-6:last-child{\n        padding-left:7px;\n    }\n    .help_tip i{\n        vertical-align: text-top;\n        color: #c7c7c7;\n    }\n}\n\n// .seedprod_collaspe, .seedprod-sidebar-form-body{\n//     border-bottom: 1px solid #e3dfe3;\n// }\n\n\n// .seedprod-sidebar-form h3{\n//     cursor: pointer;\n//     color: #4f394d;\n//     font-weight: 600;\n//     font-size: 16px;\n//     padding: 15px 0;\n// }\n\n\n\n// .seedprod-sidebar-form h3::after {\n//     text-align: right;\n//     float:right;\n//     content: \"\";\n//     // font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f078\";\n//     // color: #4F394D ;\n//     background-image: url(../img/down-arrow.png);\n//     background-repeat: no-repeat;\n//     background-size: auto;\n//     background-position: 0;\n//     width: 13px;\n//     height: 13px;\n// }\n\n\n// .seedprod-sidebar-form h3.seedprod_collaspe::after {\n//     text-align: right;\n//     float:right;\n//     content: \"\";\n//     background-image: url(../img/right-arrow.png);\n//     background-repeat: no-repeat;\n//     background-size: auto;\n//     background-position: 0;\n//     width: 13px;\n//     height: 13px;\n// }\n\n\n#seedprod-dragbar {\n    background-color: #ccc;\n    height: 100%;\n    width: 3px;\n    cursor: col-resize;\n    position: absolute;\n    right: 0;\n    top: 0;\n}\n\n#seedprod-ajax-status{\n    background: #230820;\n    position: fixed;\n    right: 29px;\n    top: 26px;\n    font-size:16px;\n    z-index: 999999;\n    color: #fff;\n}\n\n#seedprod-builder-view{\n    padding:0;\n    height:100%;\n    overflow-y: auto;\n}\n\n#seedprod-preview, .seedprod-settings-page {\n    overflow-y: scroll;\n    float: right;\n    height: 100%;\n    width: 100%;\n    padding:40px 35px;\n    //background:#EDEBED;\n    background:#F7F6F7;\n}\n\n.sp-sectiontemplates  .seedprod-settings-page {\n    background:#EDEBED;\n}\n\n\n\n#seedprod-sidebar-wrapper .sp-setting-control{\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-bottom:10px;\n   }\n\n#seedprod-sidebar-wrapper .sp-setting-control label{\n       font-size:13px;\n   }\n\n.seedprod-settings-page-wrapper{\n    color:#4F394D;\n    p, label{\n        font-size:16px !important;\n        font-weight:600;\n    }\n    .form-group{\n        margin-bottom:25px;\n    }\n\n    h1{\n        font-weight: bold;\n        font-size: 20px;\n        margin-bottom: 40px;\n    }\n    hr{\n        margin-top: 15px;\n        margin-bottom: 25px;\n    }\n    .checkbox label{\n        color: #4F394D;\n    }\n    .form-control, .btn {\n        height: 40px;\n    }\n    .help_tip {\n        vertical-align: top;\n        display: inline-block;\n    i{\n        color: #c7c7c7;\n    }\n\n    }\n    .seedprod-affiliate-details{\n        color: #999 !important;\n        font-size:13px !important;\n        margin-top:8px;\n        a{\n            color: #f1470d !important;\n            text-decoration: underline;\n        }\n    }\n}\n\n#seedprod-settings{\n    border-right:0px;\n    border-left:0px;\n\n    .list-group-item{\n        display: flex;\n        justify-content: space-between ;\n        align-items: center;\n        padding: 19px 24px;\n        color: #7B6B7A;\n        width: 100%;\n        text-align: left;\n        font-weight: 600;\n        font-size: 16px;\n        border-bottom: 1px solid #EDEBED;\n    }\n\n    .list-group-item:first-child{\n        border-top:0  !important;\n\n    }\n\n    .active{\n        color: #f1470d;\n    }\n\n}\n\n#seedprod-preview{\n    padding:0;\n}\n\n#seedprod-customizer-header-wrapper{\n\ttop:0;\n\tleft:0;\n\theight: 67px;\n\twidth:100%;\n    background:#fafafa;\n    z-index:99;\n\n}\n\n.seedprod .navbar-brand{\n    padding: 6px 15px;\n    height: 67px;\n}\n\n.seedprod-builder .navbar-brand{\n    padding: 11px 15px;\n    height: 67px;\n}\n\n#seedprod-builder-help{\n    color: #A79CA6;\n    margin-right: 18px;\n    font-size:14px;\n    padding: 8px 12px;\n    i{    margin-right: 4px;\n        color:#A79CA6;\n    }\n}\n\n#seedprod-builder-help:hover{\n    color: #fff;\n}\n\n\n\n\n\n.seedprod-overflow-hidden{\n    overflow: hidden;\n}\n\n// .seedprod-overflow-scroll{\n//     overflow-y: scroll;\n// }\n\n#seedprod_preview{\n    width:100%;\n    height:700px;\n}\n\n\n\n.seedprod .navbar{\n\tborder-radius: 0 !important;\n\tmargin-bottom: 0;\n\theight: 90px;\n}\n\n.seedprod-builder .navbar{\n\tborder-radius: 0;\n\tmargin-bottom: 0;\n\theight: 67px;\n}\n\n\n\n#seedprod-builder-close{\n    margin-right: 16px;\n    margin-left: 14px;\n    font-size:15px;\n    width: 40px;\n    height: 40px;\n    color: #a79ca6;\n    border-radius: 100px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n#seedprod-builder-close:hover{\n    color: #fff;\n}\n\n\n\n\n.seedprod-install-option{\n\tfloat: left;\n\twidth:250px;\n\tmin-height: 195px;\n\tmargin-right:20px;\n}\n\n#seedprod-publish-title{\nfont-size:24px;\nfont-weight: 400;\nmargin-top:50px;\nmargin-bottom:15px;\ncolor:#2d2d2d;\n}\n\n#seedprod-publish-desc{\ncolor:#676767;\nfont-size:14px;\nmargin-bottom:30px;\n}\n\n.seedprod-publish-option{\n    padding:20px;\n    background: #fff;\n    border: 1px solid #f1f1f1;\n    border-radius: 8px;\n    cursor: pointer;\n    margin-bottom:20px;\n    h3{\n        margin-top:0;\n        margin-bottom: 17px;\n        font-size: 16px;\n    }\n    p{\n        margin:0;\n        font-size:14px;\n        color: #a7a7a7;\n    }\n}\n\n.seedprod-publish-options .col-sm-4:first-child{\n    padding-right:7px;\n}\n\n.seedprod-publish-options .col-sm-4:nth-child(2){\n    padding-right:7px;\n    padding-left:7px;\n}\n\n.seedprod-publish-options .col-sm-4:last-child{\n    padding-left:7px;\n}\n\n#seedprod-publish-options-details{\n    padding:30px 20px;\n    background: #fff;\n    border: 1px solid #f1f1f1;\n    border-radius: 8px;\n    margin:0 0px;\n    h4{\n        font-size: 18px;\n        color: #2d2d2d;\n        margin:0 0 40px 0;\n        font-weight: 400;\n    }\n    ol{\n        margin:0;\n        margin-left: 15px;\n    }\n    li{\n        color: #666;\n        font-size:14px;\n        margin-bottom:10px;\n\n    }\n    p{\n        margin:40px 0 15px 0;\n        color: #666;\n        font-size: 14px;\n    }\n    .fa-copy{\n\n        color:#999;\n    }\n    .input-group-addon{\n        padding: 2px 10px 0;\n        cursor: pointer;\n    }\n    .input-group-addon, .form-control{\n        height: 40px;\n    }\n    .form-control{\n        width: 215px;\n    }\n    .btn{\n        height: 40px;\n        margin-left:7px;\n    }\n\n\n\n}\n\n.seedprod-sidebar-nav{\n\tpadding:17px 0;\n\ttext-align: center;\n    border-bottom: 1px solid #3d3d3d;\n}\n\n.seedprod-sidebar-nav:hover{\n    color: #fff;\n}\n\n.seedprod-sidebar-nav span{\n    display: block;\n    padding-top:5px;\n    font-size:14px;\n}\n\n.seedprod-sidebar-nav i{\n    font-size: 25px;\n}\n\n\n\n.seedprod-enter-active{\n\ttransition: opacity .3s\n}\n\n.seedprod-leave-active {\n\topacity: 0\n}\n\n.seedprod-enter,.seedprod-leave-to /* .fade-leave-active below version 2.1.8 */ {\n\topacity: 0\n}\n\n.fade-enter-active{\n\ttransition: opacity 1s\n}\n\n.fade-leave-active {\n\topacity: 0\n}\n\n// .fade-enter,.fade-leave-to /* .fade-leave-active below version 2.1.8 */ {\n// \topacity: 0\n// }\n\n#seedprod-sidebar-nav-wrapper > div{\n\tcursor: pointer;\n}\n\n.seedprod-prize{\n    margin-bottom:13px;\n}\n\n.seedprod-prize .btn{\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.toast {\n    opacity: 1 !important;\n}\n\n.seedprod-install-option{\n    cursor:pointer;\n}\n\n.seedprod .vue-swatches__row{\n    height:24px;\n}\n\n.seedprod .vue-swatches__container{\n    white-space: normal;\n}\n\n.seedprod .vue-swatches__wrapper{\n    width: 230px !important;\n}\n\n\n.seedprod .input-group-addon{\n    padding:2px 6px 0;\n}\n\n.seedprod .vue-swatches .vue-swatches__trigger{\n    width: 20px !important;\n    height: 20px !important;\n}\n\n.seedprod .mx-input,.seedprod .mx-input-append ,.seedprod .mx-calendar{\n    color:#555 !important ;\n}\n.seedprod .mx-input-append{\n    padding: 7px;\n}\n\n.seedprod .mx-calendar .actived{\n    color:#fff !important;\n}\n\n.seedprod .mx-datepicker-popup{\n    margin-top: -10px;\n}\n\n\n.seedprod .time-picker input.display-time{\n    border:1px solid #ddd;\n    box-shadow: inset 0 1px 2px rgba( 0, 0, 0, 0.07 );\n    height: 34px;\n    border-radius: 4px;\n    padding: 6px 10px;\n    font-size:14px;\n    width: 100%;\n    color: #6a6a6a;\n}\n\n.time-picker .dropdown ul li.active, .time-picker .dropdown ul li.active:hover {\n    background: #f3510a;\n    color: #fff;\n}\n\n.seedprod-builder .time-select:after{\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f017\";\n    color: #a6a6a6;\n    position: absolute;\n    right: 23px;\n    top:11px;\n    font-size:13px;\n}\n\n.seedprod-builder select.form-control {\n    // appearance:none;\n    // background: url(../img/down-arrow.png) 250px no-repeat;\n}\n\n.time-select select.form-control {\n     appearance:none;\n}\n\n\n\n\n.mx-panel-date td.today {\n    color: #f3510a !important;\n}\n\n.mx-calendar-content .cell.actived {\n    color: #fff;\n    background-color: #f3510a !important;\n}\n\n\n\n.seedprod-img-preview{\n    position:relative;\n    display: inline-block;\n}\n\n.seedprod-img-preview i{\n    position: absolute;\n    top: 4px;\n    right: -6px;\n}\n\n.seedprod-img-preview img{\n    max-width:100%;\n    max-height:280px;\n    min-width: 50px;\n    // border:1px solid #DEDADE;\n    // padding:5px;\n\n}\n\n/* Slideshow BG Settings */\n.sp-slideshow-bg-settings {\n}\n\n.sp-slideshow-bg-settings .seedprod-img-preview {\n\tcursor: move;\n}\n\n.sp-slideshow-bg-settings .seedprod-img-preview img {\n\tmax-width: 150px;\n\tmax-height: 150px;\n}\n\n.sp-slideshow-bg-settings .seedprod-img-preview button:nth-of-type(2) {\n\tdisplay: none;\n}\n\n.sp-slideshow-bg-settings .sp-slide .sp-remove-slide-btn {\n\tvisibility: hidden;\n}\n\n.sp-slideshow-bg-settings .sp-slide:hover .sp-remove-slide-btn {\n\tvisibility: visible;\n}\n\n.seedprod textarea.form-control{\n    height:auto;\n}\n\n.seedprod :focus {\n    outline: none;\n}\n\n// .seedprod label{\n//     cursor: default;\n// }\n\n// .seedprod-builder label{\n//     font-size:13px;\n//     font-weight: normal;\n// }\n\n\n.seedprod-modal-mask {\n    position: fixed;\n    z-index: 9998;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, .5);\n    display: table;\n    transition: opacity .3s ease;\n}\n\n.seedprod-modal-wrapper {\n    display: table-cell;\n    vertical-align: middle;\n}\n\n\n\n.seedprod-modal-header h3 {\n    margin-top: 0;\n}\n\n.seedprod-modal-header h3{\n    font-size: 20px;\n    margin-top: 0;\n    margin: 0;\n    padding: 0;\n    background: #fff;\n    display: block;\n}\n\n\n\n.seedprod-modal-body {\n    margin: 20px 0;\n}\n\n.seedprod-modal-enter {\n    opacity: 0;\n}\n\n.seedprod-modal-leave-active {\n    opacity: 0;\n}\n\n.seedprod-modal-enter .seedprod-modal-container,\n.seedprod-modal-leave-active .seedprod-modal-container {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n}\n\n.seedprod .btn-prize{\n    text-align:left;\n    border-left:none;\n    border-right:none;\n    border-radius:0;\n    width: 213px;\n    font-size: 13px;\n    color: #666;\n\n}\n\n.seedprod .btn-prize-sngle{\n    text-align:left;\n    width: 213px;\n    font-size: 13px;\n    color: #666;\n\n}\n\n.seedprod .btn-prize,\n.seedprod .btn-prize-single{\n    background: #fff !important;\n    color: #333 !important;\n    border-color: #efefef !important;\n    padding-left:2px;\n    padding-right:2px;\n\n}\n\n.seedprod .btn-add-prize, .seedprod .btn-prize-single{\n    font-size: 14px;\n}\n\n.seedprod .btn-prize-single{\n    padding-left:10px;\n    padding-right:10px;\n}\n\n.seedprod .btn-prize::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f303\";\n    float:right;\n    color: #c7c7c7;\n}\n\n\n.seedprod .btn-prize-single::before {\n    font-family: \"Font Awesome 5 Free\"; font-weight: 900; content: \"\\f303\";\n    float:right;\n    color: #c7c7c7;\n}\n\n.seedprod .btn-prize:active::before, .seedprod .btn-prize:hover::before, .seedprod .btn-prize-single:hover::before {\n    color: #333 !important;\n}\n\n.seedprod .btn-my-entry-option{\n\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    text-align:left;\n}\n\n.seedprod-prize .input-group-addon:hover{\n    color: #333;\n}\n\n.seedprod-prize-close, .seedprod-block-option-remove{\n    cursor:pointer;\n}\n\n.seedprod-prize-move, .seedprod-block-option-move{\n    cursor:grab;\n}\n\n.seedprod-prize-move:active, .seedprod-block-option-move:active{\n    cursor:grabbing;\n}\n\n.seedprod .btn-entry-option{\n    margin-bottom:8px;\n    position: relative;\n    padding-left: 45px;\n    height: 40px;\n}\n\n.seedprod .btn-entry-option:active,.seedprod .btn-entry-option:focus {\n    background-color:#fff !important;\n}\n\n.btn-entry-option{\n    .seedprod-block-option-icon{\n        position: absolute;\n        left: 0;\n        height: 40px;\n        padding-top: 9px;\n        padding-left: 10px;\n        padding-right: 10px;\n        width: 35px;\n        border-top-left-radius: 3px;\n        border-bottom-left-radius: 3px;\n    }\n    .fa-plus,.fa-sync-alt{\n        position: absolute;\n        right: 10px;\n        color: #c7c7c7;\n        top: 12px;\n    }\n}\n\n#seedprod-add-actions{\n\n    h3{\n        color:#4F394D;\n        font-weight: 600;\n        font-size: 16px;\n        margin: 0px 20px;\n        padding:15px 0;\n        cursor:pointer\n\n    }\n\n    // h3:last-of-type{\n    //     border-bottom: 1px solid #e3dfe3 !important;\n    // }\n\n\n}\n\n\n\n\n/* scroll modal */\n\n.seedprod-modal-mask {\n    position: fixed !important;\n    z-index: 99999 !important;\n    top: 0px !important;\n    right: 0px !important;\n    bottom: 0px !important;\n    left: 0px !important;\n    overflow-y: auto !important;\n    transform: translate3d(0,0,0) !important;\n    display: flex !important;\n    transition: opacity .3s ease;\n  }\n\n  .seedprod-modal-wrapper {\n    display: flex !important;\n    margin: auto !important;\n    //max-width: 760px !important;\n    width: 100% !important;\n    padding-left:80px;\n    padding-right: 80px;\n  }\n\n  .seedprod-modal-container {\n    width: 600px;\n    background-color: #ffffff;\n    flex: 1 !important;\n    width: 100% !important;\n    margin: 100px auto;\n    padding:0 ;\n    background-color: #fff;\n    border-radius: 10px;\n    box-shadow: 0 2px 8px rgba(35,8,32, 0.6);\n    transition: all .3s ease;\n    .seedprod-modal-body{\n        margin:0;\n    }\n    .sp-modal-left{\n        border-top-left-radius: 10px;\n        border-bottom-left-radius: 10px;\n    }\n    .sp-modal-right{\n        border-top-right-radius: 10px;\n        border-bottom-right-radius: 10px;\n    }\n  }\n\n  .seedprod-modal-header h3 {\n    margin-top: 0;\n  }\n\n  .seedprod-modal-body {\n    margin: 20px 0;\n  }\n\n  .seedprod-modal-enter, .seedprod-modal-leave {\n    opacity: 0;\n  }\n\n  .seedprod-modal-enter .seedprod-modal-container,\n  .seedprod-modal-leave .seedprod-modal-container {\n    -webkit-transform: scale(1.1);\n    transform: scale(1.1);\n  }\n\n  .seedprod input[type=checkbox], .seedprod input[type=radio] {\n    margin: 0px 0 0;\n}\n\n.seedprod-sidebar-nav.active:before {\n    position: absolute;\n    right: -10px;\n    margin-top: 28px;\n    display: block;\n    width: 20px;\n    height: 20px;\n    content: '';\n    background-color: #ffffff;\n    -webkit-transform: rotate(45deg);\n    -ms-transform: rotate(45deg);\n    -o-transform: rotate(45deg);\n    transform: rotate(45deg);\n    border-bottom: 1px solid transparent;\n    border-right: 1px solid rgba(0,0,0,.05);\n}\n\n.seedprod .modal{\n    z-index: 99999;\n    background-color:rgba(0, 0, 0, 0.5);\n}\n\n.seedprod .modal-dialog{\n    margin: 80px auto;\n}\n\n#seedprod-upgrade-ribbon-wrapper{\n    position: fixed;\n    z-index: 1;\n    right: 0;\n    bottom: 0;\n    overflow: hidden;\n    height: 110px;\n    width: 150px;\n}\n\n#seedprod-upgrade-ribbon{\n    position: absolute;\n    z-index: 1;\n    right: -48px;\n    bottom: -15px;\n    height: 75px;\n    width: 150px;\n    font-size: 14px;\n    font-weight: 700;\n    color: #fff;\n    text-transform: uppercase;\n    text-align: right;\n    line-height: 20px;\n    transform: rotate(-45deg);\n    cursor:pointer;\n    display: block;\n    background: #4CAF50;\n    box-shadow: inset 0 3px 15px -5px #000;\n}\n\n#seedprod-upgrade-ribbon span{\n    transform: rotate(45deg);\n    position: absolute;\n    bottom: 25px;\n    right: 43px;\n}\n\n.seedprod-pro #seedprod-upgrade-ribbon-wrapper{\n    display: none;\n  }\n\n.seedprod .ribbon {\n    position: absolute;\n    right: 0px;\n    top: 0px;\n    z-index: 1;\n    overflow: hidden;\n    width: 75px;\n    height: 75px;\n    text-align: left;\n}\n\n.ribbon span {\n    font-size: 8px;\n    font-weight: 700;\n    color: #fff;\n    text-transform: uppercase;\n    text-align: center;\n    line-height: 20px;\n    transform: rotate(45deg);\n    width: 100px;\n    display: block;\n    background: #79a70a;\n    background: linear-gradient(#ff9c41 0%,#ff9c41 100%);\n    box-shadow: 0 3px 20px -5px #000;\n    position: absolute;\n    top: 18px;\n    right: -21px;\n}\n\n\n\n\n\n.seedprod-img-preview i{\n    cursor: pointer;\n}\n\n.seedprod .help-block{\n    font-size: 12px;\n\n}\n\n.seedprod .has-error .form-control {\n    border-color: #ff0000 !important;\n}\n\n.seedprod-builder .input-group-poll-choices{\n    margin-bottom:10px;\n    position: relative;\n    i{\n   position: absolute ;\n   top: 13px;\n    right: 7px;\n    color: #c7c7c7;\n    cursor: pointer;\n    }\n}\n\n.seedprod-winner{\n    background-color: rgba(241, 71, 13, 0.08) !important;\n    background-image: url(../img/confetti-light.png);\n    background-repeat: no-repeat;\n    background-size: 200px;\n    background-position: 23px -48px;\n    font-weight: bold;\n\n}\n\n.seedprod-invalid-entry{\nopacity: 0.4;\n}\n\n.notice, div.updated, div.error{\n}\n\n.error{\n    color:red !important;\n}\n\n.seedprod-pro #wpfooter{\n    display:none;\n}\n\n#footer-upgrade{\n    display:none;\n}\n\n#email_integrations{\n    width:100%\n}\n\nhtml{\n    background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==') !important;\n    background-repeat: no-repeat !important;\n    background-position: center center !important;\n    height: 100%;\n}\n\n#email_integrations_wrapper{\n    background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==');\n    background-repeat: no-repeat;\n    background-position: center center;\n}\n\n.iframe_loading{\n    background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==') !important;\n    background-repeat: no-repeat !important;\n    background-position: center 100px   !important;\n    height: 100%;\n}\n\n.results_loading{\n    background-image: url('data:image/gif;base64,R0lGODlhEAAQAPIAAP///wAAAMLCwkJCQgAAAGJiYoKCgpKSkiH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAEAAQAAADMwi63P4wyklrE2MIOggZnAdOmGYJRbExwroUmcG2LmDEwnHQLVsYOd2mBzkYDAdKa+dIAAAh+QQJCgAAACwAAAAAEAAQAAADNAi63P5OjCEgG4QMu7DmikRxQlFUYDEZIGBMRVsaqHwctXXf7WEYB4Ag1xjihkMZsiUkKhIAIfkECQoAAAAsAAAAABAAEAAAAzYIujIjK8pByJDMlFYvBoVjHA70GU7xSUJhmKtwHPAKzLO9HMaoKwJZ7Rf8AYPDDzKpZBqfvwQAIfkECQoAAAAsAAAAABAAEAAAAzMIumIlK8oyhpHsnFZfhYumCYUhDAQxRIdhHBGqRoKw0R8DYlJd8z0fMDgsGo/IpHI5TAAAIfkECQoAAAAsAAAAABAAEAAAAzIIunInK0rnZBTwGPNMgQwmdsNgXGJUlIWEuR5oWUIpz8pAEAMe6TwfwyYsGo/IpFKSAAAh+QQJCgAAACwAAAAAEAAQAAADMwi6IMKQORfjdOe82p4wGccc4CEuQradylesojEMBgsUc2G7sDX3lQGBMLAJibufbSlKAAAh+QQJCgAAACwAAAAAEAAQAAADMgi63P7wCRHZnFVdmgHu2nFwlWCI3WGc3TSWhUFGxTAUkGCbtgENBMJAEJsxgMLWzpEAACH5BAkKAAAALAAAAAAQABAAAAMyCLrc/jDKSatlQtScKdceCAjDII7HcQ4EMTCpyrCuUBjCYRgHVtqlAiB1YhiCnlsRkAAAOwAAAAAAAAAAAA==') !important;\n    background-repeat: no-repeat !important;\n    background-position: center 50px   !important;\n    height: 100%;\n}\n\n.seedprod .navbar-right {\n    margin-right: 0px !important;\n}\n\n.seedprod_page_seedprod_account .seedprod .radio label, .seedprod-pro_page_seedprod_account .seedprod .radio label{\n    padding-left:0;\n}\n\n.seedprod .navbar-header {\n    float: left !important;\n}\n\n.seedprod{\n.navbar-collapse.collapse {\n    display: block!important;\n  }\n\n  .navbar-nav>li, .navbar-nav {\n    float: left !important;\n  }\n\n\n  .navbar-right {\n    float: right!important;\n  }\n}\n\n.seedprod-pro-feature{\n    opacity: 0.65;\n}\n\n\n.seedprod-pro-upgrade{\n    background: rgba(4, 4, 4, 0.6);\n    position: fixed;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n    z-index: 99999;\n    display: table;\n    width: 100%;\n    height: 100%;\n}\n\n.seedprod-pro-upgrade-row{\n    display: table-row;\n    width: 100%;\n}\n\n.seedprod-pro-upgrade-cell{\n    display: table-cell;\n    vertical-align: middle;\n}\n\n.seedprod-pro-upgrade-content{\n    background-color: #fff;\n    border-radius: 4px;\n    max-width: 500px;\n    margin: auto;\n    padding: 20px;\n    text-align:center;\n    .btn{\n        text-transform: uppercase;\n    }\n}\n\n.handdrawn-underline {\n    border-bottom: 2px solid #ff9948;\n    line-height: .9;\n    display: inline-block;\n}\n\n\n/* Builder Preview */\n#seedprod-wrapper{\n\n\n    max-width: 480px;\n    margin:auto;\n    text-align:center;\n  border-radius: 6px;\n  color: #696969;\n  background-color: #fbfbfb;\n  box-shadow: 0 2px 2px 0 rgba(0,0,0,.14), 0 3px 1px -2px rgba(0,0,0,.2), 0 1px 5px 0 rgba(0,0,0,.12);\n}\n#seedprod-wrapper .seedprod-td{\n    display: table-cell;\n    vertical-align: middle;\n    padding:0 3px;\n    height: 68px;\n}\n\n#seedprod-page-footer {\n    padding: 15px 20px;\n    font-size: 11px;\n    a{\n        color: #9c9c9c !important;\n    }\n}\n\n#seedprod-page-entries {\n    padding: 11px 0px 0 0;\n    color: #2d2d2d;\n    p > strong{\n        font-size: 16px;\n    }\n    p{\n        margin-bottom:11px;\n    }\n}\n\n#seedprod-page-login {\n    padding: 15px 0px;\n    font-size: 14px;\n    color:#666;\n    .btn-primary{\n        color: #fff;\n        background-color: #36ad39;\n        border-color: #36ad39;\n    }\n    .form-control{\n        height: 40px;\n        margin-right:8px;\n        width:165px;\n    }\n    .btn{\n        padding: 9px 20px;\n    }\n    i{\n        color: #fff;\n    }\n    #login-ft-txt{\n        margin-top:15px;\n        font-size:12px;\n    }\n}\n\n#seedprod-page-form {\n    background: #fff;\n    border-bottom: 1px solid #f2f2f2;\n    padding:5px;\n}\n#seedprod-wrapper .seedprod-top-sep{\n    border-right: 1px solid #f2f2f2;\n\n}\n\n#seedprod-wrapper .seedprod-over-txt{\n    font-size:20px;\n    line-height: 0.9;\n    font-weight: bold;\n    margin-bottom: 5px;\n    display: inline-block;\n}\n\n#seedprod-wrapper .seedprod-under-txt{\n    font-size:12px;\n    line-height: 1;\n    display: block;\n\n}\n\n.seedprod-drag-source{\n    margin-bottom: -8px;\n    margin-right: -8px;\n}\n\n#sp-blocks-editing{\n    background: #DD4A1F;\n    color: #fff;\n    height:56px;\n    line-height: 1;\n    font-weight: 600;\nfont-size: 16px;\ntext-transform: capitalize;\n    span{\n        display: block;\n        opacity: 0.5;\n        text-transform: uppercase;\n        font-weight: 600;\nfont-size: 10px;\n    }\n}\n\n.sp-block-type-global{\n    background: #E4E1E4!important;\n    color: #4F394D !important;\n}\n\n.sp-block-type-row{\n    background: #3F5FD1 !important;\n}\n\n.sp-block-type-section{\n    background: #7036BB !important;\n}\n\n.sp-sidebar-headers .sp-w-5 {\n    width: 1.5rem !important;\n}\n\n#sp-blocks-edit-sections {\n    margin-bottom:16px;\n    margin-left: -20px;\n    margin-right: -20px;\n    div{\n        display:flex;\n        padding-top:18px;\n        padding-bottom:18px;\n        justify-content: center;\n        align-items: center;\n        font-weight: 600;\n        font-size: 13px;\n\n    }\n    i{\n        margin-right:6px;\n    }\n    div.sp-secondary{\n        background: #DEDADE;\n        color: #7B6B7A;\n    }\n}\n\n#sp-blocks-sections {\n    div{\n        display:flex;\n        padding-top:18px;\n        padding-bottom:18px;\n        justify-content: center;\n        align-items: center;\n        font-weight: 600;\n        font-size: 14px;\n\n    }\n    i{\n        margin-right:6px;\n    }\n    div.sp-secondary{\n        background: #DEDADE;\n        color: #7B6B7A;\n    }\n}\n\n\n.seedprod-block-option:hover{\n    background: #DD4A1F;\n    border-radius:5px;\n    color: #fff;\n}\n\n.seedprod-block-option{\n    background: #EDEBED;\n    width:88px;\n    height:85px;\n    margin-bottom:8px;\n    margin-right:8px;\n    display:flex;\n    justify-content: center;\n    border-radius:5px;\n\n    i{\n        margin-top:24px;\n        font-size:20px !important;\n    }\n    button{\n        display: flex;\n        flex-direction: column;\n        font-size:11px;\n        justify-content: center;\n        align-items:center;\n        width:100%;\n        height:100%;\n        cursor: move;\n        padding:1px;\n\n    }\n    span{\n        flex:1;\n        display: flex;\n        align-items: center;\n    }\n\n}\n\n\n\n#seedprod-wrapper .seedprod-block-option-details{\n    background: #fff;\n    border-top:1px solid #f2f2f2;\n    color: #707070;\n    position: relative;\n    padding:20px 0;\n    display: table-cell;\n    width: 480px;\n    text-align: left;\n    padding-left: 74px;\n    font-size:16px;\n    cursor: pointer;\n}\n\n\n\n.seedprod-entry-drag-area{\n    width: 582px;\n    margin-left: -34px;\n}\n\n.seedprod-entry-drag-area > div{\n    width:100%;\n    margin:auto;\n}\n\n#seedprod-wrapper    .seedprod-block-option-move:hover{\n    color: #333;\n}\n\n\n#seedprod-wrapper .seedprod-block-option-move{\n    background: #fcfcfc;\n    border: 1px solid #f2f2f2;\n    color: #b3b3b3;\n    padding: 18px 0 ;\n    width: 34px;\n    display:table-cell;\n    visibility: hidden;\n    border-top-left-radius: 3px;\n    border-bottom-left-radius: 3px;\n    text-align: center;\n    position: relative;\n}\n\n#seedprod-wrapper    .seedprod-block-option-edit{\n    background: #fcfcfc;\n    border: 1px solid #f2f2f2;\n    color: #b3b3b3;\n    border-right: none;\n    padding: 18px 0;\n    width: 34px;\n    display:table-cell;\n    visibility: hidden;\n}\n\n#seedprod-wrapper    .seedprod-block-option-edit:hover{\n    color: #333;\n}\n\n#seedprod-wrapper .seedprod-block-option-value{\n    border: 1px solid #f2f2f2;\n    color: #9b9b9b;\n    padding:2px;\n    padding-top: 5px;\n    width: 30px;\n    height: 30px;\n    right: 24px;\n    position: absolute;\n    top: 50%;\n    transform: translateY(-50%);\n    font-size:13px;\n    text-align: center;\n}\n\n .seedprod-block-option-icon{\n    border: 1px solid #f2f2f2;\n    padding:2px;\n    padding-left:3px;\n    padding-top: 4px;\n    width: 29px;\n    height: 29px;\n    left: 24px;\n    position: absolute;\n    top: 50%;\n    transform: translateY(-50%);\n    font-size:14px;\n    text-align: center;\n}\n\n.seedprod-block-option-icon::before{\n    -webkit-font-smoothing: antialiased;\n}\n\n#seedprod-wrapper    .seedprod-block-option-remove:hover{\n    color: #333;\n}\n\n\n#seedprod-wrapper    .seedprod-block-option-remove{\n    background: #fcfcfc;\n    border: 1px solid #f2f2f2;\n    color: #b3b3b3;\n    padding: 18px 0;\n    width: 34px;\n    display:table-cell;\n    visibility: hidden;\n    border-top-right-radius: 3px;\n    border-bottom-right-radius: 3px;\n    text-align: center;\n}\n\n.show_hidden{\n    visibility:visible !important;\n}\n\n\n.seedprod-entry-drag-area > div:last-child .seedprod-block-option-details\n{\n    border-bottom:1px solid #f2f2f2;\n}\n\n#seedprod-wrapper .seedprod-block-option-edit{\n    cursor:pointer;\n}\n\n\n#seedprod-wrapper .seedprod-page .equal {\n    display: flex;\n    display: -webkit-flex;\n    flex-wrap: wrap;\n  }\n\n#seedprod-wrapper  .row , #seedprod-wrapper .seedprod .row{\n    margin-left: 0;\n    margin-right: 0;\n}\n\n\n#seedprod-prize-info .col-md-12{\n    padding:0;\n    background: #fff;\n}\n\n#seedprod-wrapper  .col-xs-4, #seedprod-wrapper .seedprod .col-xs-4 {\n    padding-left: 0;\n    padding-right: 0;\n}\n\n\n#seedprod-countdown,#seedprod-total-entries,#seedprod-my-entires {\n    text-align:center;\n    display: table;\n    width: 100%;\n    min-height: 40px;\n}\n\n#seedprod-countdown{\n    font-size: 16px;\n}\n\n#seedprod-wrapper .slider-btn-left{\n    background: transparent !important;\n}\n\n#seedprod-wrapper .slider-btn-right{\n    background: transparent !important;\n}\n\n#seedprod-wrapper .slider-icon{\n    box-shadow: rgba(0, 0, 0, 0.3) -1px 1px 0px;\n}\n\n#seedprod-wrapper .slider-indicator-icon{\n    box-shadow: rgba(255, 255, 255, 0.76) 0 0 2px;\n    width: 8px;\n    height: 8px;\n}\n\n#seedprod-wrapper  .slider-indicator-active{\n    box-shadow: rgba(0, 0, 0, 0.76) 0 0 2px;\n    width: 7px;\n    height: 7px;\n}\n\n\n#seedprod-wrapper .slider-indicator-icon{\n    background-color: rgba(0,0,0,.3) !important;\n}\n\n#seedprod-wrapper .slider-indicator-active {\n    background-color: rgba(255,255,255,.9) !important;\n}\n\n#seedprod-wrapper #seedprod-prize-info {\n    border-top: 1px solid #f2f2f2;\n    //border-bottom: 1px solid #f2f2f2;\n}\n\n#seedprod-wrapper h1{\n    margin:0;\n    margin-top: 30px;\n    margin-bottom: 10px;\n    font-size: 24px;\n    color: #2d2d2d;\n    font-weight: 700;\n}\n\n#seedprod-wrapper #seedprod-header{\n    margin:0;\n    padding:0 40px;\n    margin-bottom:12px;\n    font-size:16px;\n    line-height: 1.4;\n    color: #666;\n}\n\n.seedprod .btn-default:hover {\n    color: #222;\n    background-color: #fff;\n    border-color: #bbb;\n}\n\n\n\n.seedprod-action-single h3{\n    margin-left: 0px !important;\n    margin-right: 0px !important;\n}\n\n.seedprod-action-single .seedprod-action-header:first-child {\n    margin-bottom: 0px !important;\n}\n\n.seedprod-sidebar-action-body{\n    padding:10px 0;\n   // border-bottom-width: 1px;\n   // border-color: #d3ced2;\n    box-shadow: inset 0px -1px 0px rgba(167, 156, 166, 0.25);\n}\n\n.seedprod-action-single .btn-group button{\n    padding: 9px 16px;\n\n}\n\n.seedprod .btn-group{\n    display:flex !important;\n    .btn-default{\n        border-color: #DEDADE !important;\n    }\n}\n\n.seedprod-action-single .btn{\n    padding: 8px 12px;\n}\n\n.seedprod-action-single .btn-group .active{\n    background-color: #f1470d !important;\n    color: #fff !important;\n    box-shadow: none !important;\n\n}\n\n.seedprod-action-header:first-child{\n    border-top:0 !important;\n    margin-top:1px !important;\n    margin-bottom:20px !important;\n}\n\n.seedprod-action-single{\n    padding-top: 0px;\n    padding-bottom: 0px;\n}\n\n\n.seedprod-block-option-details.active{\n    box-shadow:inset 0px 0px 1px 1px #f3510b;\n}\n\n.seedprod .has-error .help-block, .seedprod .has-error .control-label, .seedprod .has-error .radio, .seedprod .has-error .checkbox, .seedprod .has-error .radio-inline, .seedprod .has-error .checkbox-inline, .seedprod .has-error.radio label, .seedprod .has-error.checkbox label, .seedprod .has-error.radio-inline label, .seedprod .has-error.checkbox-inline label {\n    color: #ff0000;\n}\n\n\n\n\n/* Tool tips */\n.seedprod-tooltip  {\n    display: block !important;\n    z-index: 99999;\n\n  }\n\n  .seedprod-tooltip  .tooltip-inner {\n    font-size:13px;\n    background: #4F394D;\n    color: #fff;\n    border-radius: 6px;\n    padding: 5px 10px 4px;\n    border:1px solid #4F394D;\n    box-shadow: 0 0 3px #4F394D !important;\n    max-width:250px;\n    width:auto;\n    word-wrap: break-word;\n  }\n\n  .seedprod-tooltip .tooltip-arrow {\n    width: 0;\n    height: 0;\n    border-style: solid;\n    position: absolute;\n    margin: 5px;\n    border-color:#4F394D;\n  }\n\n  .seedprod-tooltip .tooltip-arrow:before {\n    // position: absolute;\n    // bottom: -1px;\n    // left: 33px;\n    // display: block;\n    // width: 10px;\n    // height: 10px;\n    // content: '';\n    // background-color: white;\n    // -webkit-transform: rotate(45deg);\n    // transform: rotate(45deg);\n    // border-bottom: 1px solid #b9b9b9;\n    // border-right: 1px solid rgba(193, 193, 193, 0.87);\n    // z-index: 1;\n}\n\n  .seedprod-tooltip[x-placement^=\"top\"] {\n    margin-bottom: 5px;\n  }\n\n  .seedprod-tooltip[x-placement^=\"top\"] .tooltip-arrow {\n    border-width: 5px 5px 0 5px;\n    border-left-color: transparent !important;\n    border-right-color: transparent !important;\n    border-bottom-color: transparent !important;\n    bottom: -5px;\n    left: calc(50% - 5px);\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n\n  .seedprod-tooltip[x-placement^=\"bottom\"] {\n    margin-top: 5px;\n  }\n\n  .seedprod-tooltip[x-placement^=\"bottom\"] .tooltip-arrow {\n    border-width: 0 5px 5px 5px;\n    border-left-color: transparent !important;\n    border-right-color: transparent !important;\n    border-top-color: transparent !important;\n    top: -5px;\n    left: calc(50% - 5px);\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n\n  .seedprod-tooltip[x-placement^=\"right\"] {\n    margin-left: 5px;\n  }\n\n  .seedprod-tooltip[x-placement^=\"right\"] .tooltip-arrow {\n    border-width: 5px 5px 5px 0;\n    border-left-color: transparent !important;\n    border-top-color: transparent !important;\n    border-bottom-color: transparent !important;\n    left: -5px;\n    top: calc(50% - 5px);\n    margin-left: 0;\n    margin-right: 0;\n  }\n\n  .seedprod-tooltip[x-placement^=\"left\"] {\n    margin-right: 5px;\n  }\n\n  .seedprod-tooltip[x-placement^=\"left\"] .tooltip-arrow {\n    border-width: 5px 0 5px 5px;\n    border-top-color: transparent !important;\n    border-right-color: transparent !important;\n    border-bottom-color: transparent !important;\n    right: -5px;\n    top: calc(50% - 5px);\n    margin-left: 0;\n    margin-right: 0;\n  }\n\n  .seedprod-tooltip[aria-hidden='true'] {\n    visibility: hidden;\n    opacity: 0;\n    transition: opacity .15s, visibility .15s;\n  }\n\n  .seedprod-tooltip[aria-hidden='false'] {\n    visibility: visible;\n    opacity: 1;\n    transition: opacity .15s;\n  }\n\n  .swal2-container{\n    z-index: 99999 !important;\n  }\n\n  button.swal-styled{\n      box-shadow:none;\n  }\n\n\n\n  .swal2-toast{\n    border-radius: 2px !important;\n    border:none !important;\n    .swal2-title{\n        display:none !important;\n    }\n    #swal2-content{\n        padding-left:10px;\n        font-size: 13px;\n        color: #fff;\n    }\n\n  }\n\n  .swal2-toast .swal2-image{\n    margin:0 !important;\n  }\n\n  .swal2-popup.swal2-toast{\n    box-shadow: 0 0 3px rgba(239, 239, 239, 0.45)!important;\n    border-radius:3px !important;\n  }\n\n  .swal2-top-end{\n    top:30px !important;\n  }\n\n  .seedprod-builder{\n    .swal2-top-end{\n        top:70px !important;\n      }\n  }\n\n  .seedprod-upgrade-popup{\n    .swal2-modal{\n        padding: 20px 0px 0px 0px !important;\n    }\n  }\n\n  .swal2-confirm{\n      box-shadow: none !important;\n  }\n\n  .seedprod-moreinfo-popup{\n      .swal2-image{\n          width:40px;\n          height:40px;\n          margin: 10px auto !important;\n\n      }\n      .swal2-modal{\n        width:600px;\n      }\n      .swal2-content{\n        font-size:16px !important;\n      }\n  }\n\n  .seedprod-upgrade-popup {\n      .swal2-modal{\n        width:600px;\n      }\n\n    .swal2-image{\n        width:40px;\n        height:40px;\n\n    }\n    .swal2-title{\n        line-height: 1;\n        color: #230820 !important;\n        font-size:18px !important;\n        margin: 0 40px 20px 40px!important;\n      }\n      .swal2-styled.swal2-confirm{\n        background-color: #DD4A1F !important;\n        box-shadow: none !important;\n      }\n      .swal2-content{\n          color: #fff !important;\n          font-size:18px !important;\n          line-height: 1.5 !important;\n          padding: 0px 40px 0px 40px !important;\n\n      }\n      .swal2-confirm{\n        padding: 15px 28px !important;\n        font-size: 18px !important;\n        margin-bottom: 27px !important;\n      }\n      .swal2-footer{\n          position: relative;\n          border-top: none !important;\n          color: #2d2d2d !important;\n          font-size:16px !important;\n          line-height: 1.5 !important;\n          background: #f9ffab !important;\n          padding: 37px 0 37px !important;\n          border-bottom-left-radius: .3125em !important;\n          border-bottom-right-radius: .3125em !important;\n      }\n      .fa-check-circle{\n        position: absolute;\n        top: -16px;\n        font-size: 23px;\n        color: #36ad39;\n        background: #fff;\n        border-radius: 50px;\n        padding: 6px;\n      }\n  }\n\n  .swal2-popup .swal2-styled.swal2-cancel{\n      background-color: #DEDADE !important;\n      color: #4F394D !important;\n  }\n\n  .swal2-popup .swal2-title{\n      font-size:18px !important;\n      color: #230820 !important;\n  }\n  .swal2-modal .swal2-close{\n    font-size: 1.5em !important;\n  }\n\n//   .swal2-modal .swal2-image{\n//     display:none !important;\n//   }\n\n  .vue-popover-theme{\n      z-index:10;\n      outline:none;\n  }\n\n  .v-popover .trigger{\n      display: block !important;\n  }\n\n  .sp-highlight-theme-template{\n    box-shadow: inset 0 0 1px 1px #0F8A5E;\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0px;\n    left: 0;\n    }\n\n  .sp-highlight-section{\n    box-shadow: inset 0 0 1px 1px #7036BB;\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0px;\n    left: 0;\n    }\n\n    .sp-highlight-row {\n        box-shadow: inset 0 0 1px 1px #3F5FD1;\n        }\n\n  .sp-highlight-block{\n    box-shadow: inset 0 0 1px 1px #dd4a1f;\n    // position: absolute;\n    // width: 100%;\n    // height: 100%;\n    // top: 0px;\n    // left: 0;\n    // animation-name: hightlight_pulse_color;\n    // animation-duration: 1.5s;\n    // animation-iteration-count: infinite;\n    // animation-timing-function: ease;\n    }\n\n\n\n@keyframes hightlight_pulse_color {\n0% {\n    box-shadow: inset 0 0 1px 1px red;\n}\n50% {\n    box-shadow: inset 0 0 1px 1px #fff;\n}\n100% {\n    box-shadow: inset 0 0 1px 1px #f3510b;\n}\n}\n\n.seedprod-highlight-prize {\n   .input-group-addon,.btn-prize,.btn-prize-single {\n        border-color: #f3510a !important;\n    }\n\n\n}\n\n  .seedprod-highlight-option , .seedprod-highlight-option-slider-img{\n    box-shadow: inset 0 0 1px 1px #f3510b;\n    position: relative;\n    cursor: pointer;\n    z-index:9999;\n\n}\n\n.seedprod-highlight-padding {\n    padding:2px;\n}\n\n.seedprod-highlight-option-slider-img::before {\n    position: absolute;\n    bottom:0px;\n    left:-1px;\n    background: #f3510a;\n    padding: 0px 4px;\n    color:rgba(0,0,0,0.5);\n    font-family: \"Font Awesome 5 Free\";\n    font-weight: 900;\n    content: \"\\f303\";\n    font-size:13px;\n    z-index: 9;\n}\n\n#seedprod-welcome-video{\n    margin:30px 0;\n}\n\n#seedprod-welcome-upgrade{\n    margin:30px 0;\n}\n\n.seedprod-highlight-option::before {\n    position: absolute;\n    bottom: -17px;\n    background: #f3510a;\n    padding: 0px 4px;\n    color:rgba(0,0,0,0.5);\n    font-family: \"Font Awesome 5 Free\";\n    font-weight: 900;\n    content: \"\\f303\";\n    font-size:13px;\n    z-index: 9;\n}\n\n#seedprod-install-options button{\n    font-size:13px;\n    margin-top:5px;\n    min-width: 250px;\n    display: block;\n}\n\n.seedprod-50{\n    width:49% !important;\n}\n\nh1.seedprod-highlight-option::before {\n    bottom: -15px;\n    padding: 1px 4px;\n}\n\n// input[type=\"text\"]:focus, input[type=\"password\"]:focus, input[type=\"color\"]:focus, input[type=\"date\"]:focus, input[type=\"datetime\"]:focus, input[type=\"datetime-local\"]:focus, input[type=\"email\"]:focus, input[type=\"month\"]:focus, input[type=\"number\"]:focus, input[type=\"search\"]:focus, input[type=\"tel\"]:focus, input[type=\"text\"]:focus, input[type=\"time\"]:focus, input[type=\"url\"]:focus, input[type=\"week\"]:focus, input[type=\"checkbox\"]:focus, input[type=\"radio\"]:focus, select:focus, textarea:focus {\n//     border-color: #5b9dd9;//#f3510a;\n// }\n\n.help_tip{\n    cursor: pointer;\n    padding: 0 5px;\n    font-size:13px;\n    vertical-align: middle;\n}\n\n\n  /* Make clicks pass-through */\n#nprogress {\n    pointer-events: none;\n  }\n\n  #nprogress .bar {\n    background: #f3510a;\n    margin:0;\n    position: fixed;\n    z-index: 999999;\n    top: 0;\n    left: 0;\n\n    width: 100%;\n    height: 2px;\n  }\n\n  /* Fancy blur effect */\n  #nprogress .peg {\n    display: block;\n    position: absolute;\n    right: 0px;\n    width: 100px;\n    height: 100%;\n    box-shadow: 0 0 10px #f3510a, 0 0 5px #f3510a;\n    opacity: 1.0;\n\n            transform: rotate(3deg) translate(0px, -4px);\n  }\n\n  /* Remove these to get rid of the spinner */\n  #nprogress .spinner {\n    display: block;\n    position: fixed;\n    z-index: 1031;\n    top: 15px;\n    right: 15px;\n  }\n\n  #nprogress .spinner-icon {\n    width: 18px;\n    height: 18px;\n    box-sizing: border-box;\n    z-index: 1031;\n    border: solid 2px transparent;\n    border-top-color: #29d;\n    border-left-color: #29d;\n    border-radius: 50%;\n\n\n            animation: nprogress-spinner 400ms linear infinite;\n  }\n\n  .nprogress-custom-parent {\n    overflow: hidden;\n    position: relative;\n  }\n\n  .nprogress-custom-parent #nprogress .spinner,\n  .nprogress-custom-parent #nprogress .bar {\n    position: absolute;\n  }\n\n  @-webkit-keyframes nprogress-spinner {\n    0%   { -webkit-transform: rotate(0deg); }\n    100% { -webkit-transform: rotate(360deg); }\n  }\n  @keyframes nprogress-spinner {\n    0%   { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n\n  .layout-3 .slider-indicators{\n      top:5px !important;\n  }\n\n  .tablenav .tablenav-pages a:hover, .tablenav .tablenav-pages a:focus {\n    border-color: #f1470d;\n    color: #fff;\n    background: #f1470d;\n    box-shadow: none;\n    outline: none;\n}\n\n.tablenav-pages{\n    margin: 5px 0 0px !important;\n\n}\n.tablenav.top .displaying-num{\n    padding-top: 5px;\n    display: inline-block;\n}\n\n.tablenav.bottom{\n.tablenav-pages{\n    margin: 5px 0 0px !important;\n}\n}\n\nthead, tfoot{\n    background:#fff;\n}\n\n.tablenav.bottom{\n    margin-top:8px !important\n}\n\n.widefat th input, .updates-table td input, .widefat thead td input, .widefat tfoot td input {\n    margin: 0 0 0 8px !important;\n    padding: 0 !important;\n    vertical-align: text-top !important;\n}\n\n.row-actions span.delete a{\n    color: #ff1010 !important;\n}\n\n#seedprod-giveway-details{\n        border-bottom: 1px solid #ddd;\n        padding-bottom: 15px;\n        margin: 0 0 10px;\n}\n\n#seedprod-settings-nav{\n    height: 58px;\n    margin-left: -20px;\n    border-top:none;\n    background: #fff;\n    margin-bottom:13px;\n    border-radius: 0;\n    a{\n        height: 58px;\n        padding-top: 18px;\n        padding-left: 10px;\n        padding-right: 10px;\n        font-size:14px;\n        span{\n            padding: 0 10px 24px;\n        }\n    }\n    .active a{\n        box-shadow: none !important;\n        color: #b3b3b3 !important;\n    }\n    .router-link-exact-active a{\n        color:#222 !important;\n        span{\n            box-shadow: inset 0 -7px 0 0 #f1470d !important;\n        }\n\n    }\n\n}\n\n.form-table{\n    margin-top:0;\n    background-color:transparent !important;\n    color: #666;\n    tr{\n        border-bottom:1px solid #e4e4e4;\n    }\n    p{\n        margin: 8px 0 0;\n    }\n    label{\n        padding-left: 0 !important;\n    }\n    .help-block{\n        padding-top:8px;\n        font-style: italic;\n    color: #666;\n    max-width:600px;\n    }\n    h4{\n        margin-top:0;\n        margin-bottom:0;\n        font-size: 20px;\n    font-weight: 700;\n    color: #444;\n    }\n    strong{\n        padding-top: 8px;\n        color: #444;\n        display:inline-block;\n    }\n    button{\n        font-weight:600 !important;\n    }\n    .form-control{\n        margin-bottom:0 !important;\n        margin-right:10px;\n        width:400px !important;\n    }\n}\n\n.seedprod-settings-title{\n    th,td{\n    padding:20px 0;\n    }\n}\n\n.seedprod-settings-form{\n    th,td{\n    padding:30px 0;\n    }\n}\n\n.seedprod-settings-submit{\n    border-bottom:none !important;\n    td{\n        padding:20px 0;\n    }\n}\n\n#seedprod-settings-integrations .seedprod-settings-form{\n    td:first-child{\n        width:160px\n    }\n    i{\n        display: inline-block;\n        margin-left: 10px;\n    }\n    img{\n        background: #fff;\n        border: 1px solid #ddd;\n        max-width: 90px;\n        display: inline;\n        margin: 0 0 0 20px;\n    }\n}\n\n#seedprod-settings-lite-cta{\n    background-color: #fff;\n    border: 1px solid #dadada;\n    padding: 25px 20px;\n    margin: 10px 0 0 0;\n    position: relative;\n    .dismiss {\n        position: absolute;\n        top: 10px;\n        right: 10px;\n        color: #666;\n        font-size: 16px;\n    }\n    h5{\n        margin: 0 0 16px;\n        font-size: 18px;\n        font-weight: 700;\n    }\n    p{\n        color: #555;\n    font-size: 14px;\n    margin: 0 0 16px;\n    }\n    a{\n        color:  #f1470d;\n    }\n    h6{\n        font-weight: 700;\n        font-size: 14px;\n        margin: 0 0 16px;\n    }\n    p:last-of-type {\n        margin: 0;\n    }\n    .list {\n        margin: 0 0 16px 0;\n        overflow: auto;\n        max-width: 900px;\n    }\n    ul {\n        margin: 0;\n        padding: 0;\n        width: 50%;\n        float: left;\n    }\n    li {\n        margin: 0;\n        padding: 0 0 2px 16px;\n        color: #555;\n        font-size: 14px;\n        position: relative;\n    }\n    li:before {\n        content: '+';\n        position: absolute;\n        top: -1px;\n        left: 0;\n    }\n}\n\n\n\n.btn-facebook{\n    background: #3C5A99;\n    color: #fff;\n    border-radius: 4px;\n    font-size: 14px;\n    padding: 12px 26px;\n    line-height: 1 !important;\n    border: 0;\n    outline: 0;\n    cursor: pointer;\n    color:#fff !important;\n}\n.btn-facebook.seedprod-highlight-option::before{\n    bottom:-13px;\n}\n\n.green{\n    color: #218900;\n    font-weight: 700;\n}\n\n/* Welcome */\n.seedprod-welcome-video{\n    .swal2-popup {\n        width: 600px !important;\n        max-width:100% !important;\n    }\n\n}\n\n.swal2-popup .swal2-styled.swal2-confirm{\n    background-color: #DD4A1F !important;\n}\n#seedprod-welcome {\n    color: #555;\n    padding-top: 110px;\n    .btn{\n        font-size:15px;\n\n    }\n    hr{\n        width:115px;\n        color: #ddd;\n        margin-top: 40px;\n    margin-bottom: 40px;\n    }\n    #license-info{\n        text-align:center;\n        p{\n            color: #666;\n            font-size: 15px;\n        }\n        #license-sub{\n            font-size:12px;\n            a{\n                color: #f1470d;\n                text-decoration: underline;\n            }\n        }\n        .input-lg{\n            min-width: 350px !important;\n            height: 42px !important;\n            margin-right: 8px;\n        }\n        .fa-question-circle{\n            margin-left:20px;\n            color: #999;\n            font-size:16px;\n            cursor: pointer;\n            vertical-align:middle;\n        }\n        .btn {\n            font-size: 14px;\n            padding: 10px 30px;\n        }\n        .form-inline{\n            margin-bottom:10px\n        }\n\n    }\n\n    #sub-welcome{\n        padding-bottom:50px;\n        h6, .btn{\n            font-size:15px;\n        }\n        .button-wrap {\n            margin-left:40px;\n            margin-right: 40px;\n        }\n    }\n    .btn-default{\n        background:#f1f1f1;\n        border:1px solid #f1f1f1;\n        color:#666;\n    }\n    .btn-block{\n        font-weight: 500;\n    }\n    .btn-default:hover{\n        background:#ddd;\n        border:1px solid #f1f1f1;\n    }\n    .intro {\n        background-color: #fff;\n        border: 1px solid #ddd;\n        border-top: 2px solid #ddd;\n        border-bottom: 2px solid #ddd;\n        border-radius: 2px;\n        margin-bottom: 30px;\n        position: relative;\n        padding-top: 40px;\n        border-radius: 7px;\n    }\n    .button-wrap {\n        margin-top: 25px;\n        .left {\n            float: left;\n            width: 50%;\n            padding-right: 6px;\n\n        }\n        .right {\n            float: right;\n            width: 50%;\n            padding-left: 6px;\n\n        }\n    }\n    .welcome-container {\n        margin: 0 auto;\n        max-width: 676px;\n        padding: 0;\n    }\n    .robbie {\n        background-color: #fff;\n        border: 2px solid #e1e1e1;\n        border-radius: 50%;\n        height: 114px;\n        width: 114px;\n        padding: 12px 14px 0 14px;\n        position: absolute;\n        top: -58px;\n        left: 50%;\n        margin-left: -55px;\n    }\n    img {\n        max-width: 100%;\n        height: auto;\n    }\n    .block {\n        padding: 40px;\n    }\n    h1 {\n        color: #2d2d2d;\n        font-size: 24px;\n        text-align: center;\n        margin: 0 0 16px 0;\n        font-weight: 700;\n    }\n    h6 {\n        font-size: 16px;\n        font-weight: 400;\n        line-height: 1.6;\n        text-align: center;\n        margin: 0;\n        color:#7c7c7c;\n    }\n\n    .features {\n        background-color: #fff;\n        border: 1px solid #ddd;\n        border-top: 2px solid #ddd;\n        border-bottom: 0;\n\n        border-radius: 7px 7px 0 0;\n        position: relative;\n        padding-top: 10px;\n        padding-bottom: 0px;\n        margin-bottom:0px;\n        .feature-list {\n            margin-top: 60px;\n        }\n        .feature-block {\n            float: left;\n            width: 50%;\n            padding-bottom: 35px;\n            overflow: auto;\n\n            img {\n                float: left;\n                max-width: 58px;\n            }\n            h5 {\n                margin-left: 68px;\n                margin-top:0;\n                color:#2d2d2d;\n            }\n            p {\n                margin: 0;\n                margin-left: 68px;\n                font-size:12px;\n                color:#666;\n            }\n        }\n        .feature-block.first {\n            padding-right: 16px;\n            clear: both;\n        }\n        .feature-block.last {\n            padding-left: 16px;\n        }\n    }\n    .seedprod-clear:before {\n        content: \" \";\n        display: table;\n    }\n\n    .seedprod-clear:after {\n        clear: both;\n        content: \" \";\n        display: table;\n    }\n\n    .footer {\n        background-color: #f7f7f7;\n        border: 1px solid #ddd;\n        border-bottom: 2px solid #ddd;\n        border-radius: 0 0 7px 7px;\n        .block {\n            padding: 45px 80px;\n        }\n        .button-wrap{\n            margin-top: 0px;\n        }\n    }\n\n    .btn-trans-green {\n        background-color: none;\n        color: #0f8000;\n    }\n\n    .underline {\n        position: relative;\n    }\n\n    .btn-trans-green .dashicons {\n        height: 18px;\n    }\n\n    .btn-trans-green .underline:after {\n        content: \" \";\n        border-bottom: 1px dashed #0f8000;\n        position: absolute;\n        bottom: -5px;\n        left: 0;\n        width: 100%;\n    }\n\n    .testimonial-block {\n        margin: 50px 0 0 0;\n        color:#666;\n        .sig{\n            margin-top:35px\n        }\n    }\n    .testimonial-block img {\n        border-radius: 50%;\n        float: left;\n        max-width: 116px;\n    }\n\n    .testimonial-block p {\n        font-size: 15px;\n        margin: 0 0 12px 140px;\n    }\n\n}\n\n/* About Us */\n\n#seedprod-aboutus{\n    .row{\n        margin-left:0 !important;\n        margin-right:0  !important;\n    }\n    h2{\n        margin-top:0 !important;\n        margin-bottom: 24px;\n        color: #23282d;\n        font-size: 24px;\n    }\n    h3{\n        margin-top:0 !important;\n        font-size: 18px;\n    margin-bottom: 30px;\n    color: #23282C;\n    line-height: 1.6;\n    }\n    p {\n        font-size: 16px;\n        line-height: 1.52;\n        color:#444;\n        margin: 1em 0;\n    }\n    p:last-child {\n        margin-bottom: 0;\n    }\n    figcaption {\n        font-size: 14px;\n        color: #888888;\n        margin-top: 5px;\n        text-align: center;\n        line-height: initial;\n    }\n}\n\n#seedprod-aboutus-section{\n    margin-top:20px;\n    background:#fff;\n    padding: 30px 15px;\n    border: 1px solid #DDDDDD;\n\n}\n\n#seedprod-plugin-recommendations{\n    margin-top:20px;\n    .col-md-4:first-child{\n        padding-left:0;\n    }\n    .col-md-4:nth-child(2){\n        padding-left:7px;\n        padding-right:7px;\n    }\n    .col-md-4:last-child{\n        padding-right:0;\n    }\n}\n\n.seedprod-plugin-recommendations{\n    display: flex;\n    flex-flow: row wrap;\n    justify-content: space-between;\n    margin:20px -10px 0 !important;\n    li{\n        background-color:#fff;\n        border: 1px solid #ddd;\n        flex: 1;\n        margin:0 10px 20px;\n        display: flex;\n        flex-flow: column ;\n        flex-basis:30%;\n    }\n    .seedprod-plugin-recommendations-block{\n        display:flex;\n        padding:20px;\n    }\n    .seedprod-media-img{\n        margin-right:20px;\n        img{\n            border: 1px solid #eee;\n            max-width: 75px;\n            padding: 13px;\n        }\n    }\n    .seedprod-media-body{\n        flex:1;\n        h4{\n            margin-top:0;\n            margin-bottom:16px;\n            color:#2d2d2d;\n            font-size:16px;\n        }\n        p{\n            font-size:13px !important;\n        }\n    }\n    .seedprod-plugin-install{\n        display:flex;\n        margin-top: auto;\n        background-color: #f7f7f7;\n        border-top: 1px solid #ddd;\n        padding: 20px;\n        justify-content: space-between;\n        align-items: center;\n    }\n\n}\n\n\n#seedprod-aboutus-getting-started-section{\n    margin-top:20px;\n    background:#fff;\n    padding: 30px 15px;\n    border: 1px solid #DDDDDD;\n    .embed-responsive{\n        border: 1px solid #DDDDDD;\n    }\n    ul{\n        margin-top:20px;\n        margin-bottom:0;\n    }\n    a{\n        text-decoration:underline;\n    }\n}\n\n#seedprod-aboutus-getting-upsell-section{\n    margin-top:20px;\n    background:#FAFAFA;\n\n    border: 1px solid #DDDDDD;\n    font-size: 18px;\n    .row:first-child{\n        padding: 30px 15px;\n    }\n    .row:last-child{\n        padding: 30px 15px;\n    }\n    p:last-child{\n        margin-top: 15px;\n    }\n    hr {\n        margin:0 30px;\n        border: 0;\n        border-top: 1px solid #ddd;\n    }\n    h2{\n        font-size: 24px;\n        line-height: 1.6;\n        margin-bottom: 24px;\n    }\n    #upsell-features{\n        background: #fff;\n        border-top: 1px solid #ddd;\n        a{\n            text-decoration: underline;\n            font-weight: 600;\n        }\n    }\n\n    .fa {\n        color: #2a9b39;\n        margin: 0 8px 0 0;\n        font-size:16px;\n    }\n    li{\n        line-height:2;\n        font-size:16px;\n        color: #444;\n        margin-bottom:0;\n\n    }\n}\n\n#seedprod-aboutus-getting-started-docs1-section{\n    margin-top:20px;\n\n}\n\n.docs-section{\n    background:#fff;\n    border: 1px solid #DDDDDD;\n    padding: 30px;\n    line-height: 2;\n    img{\n        margin-right:20px;\n    }\n    .media-heading{\n        font-size:24px;\n        margin-bottom:16px;\n    }\n    .media-body{\n        font-size:16px;\n        line-height: 1.5;\n        p{\n            margin-bottom:30px;\n        }\n\n    }\n}\n\n#seedprod-aboutus-lite-vs-pro-section{\n    margin-top:20px;\n    table{\n        border: 1px solid #DDDDDD;\n        caption{\n            background:#fff;\n            text-align:center;\n            border: 1px solid #DDDDDD;\n            border-bottom: 0;\n            padding: 30px;\n            font-size:16px;\n            color:#444;\n            h1{\n                color: #23282d;\n                font-size:23px;\n            }\n\n        }\n    }\n    td{\n        border: 1px solid #DDDDDD;\n        background:#fff;\n        padding: 30px;\n        vertical-align: top;\n        font-size: 16px;\n        color:#444;\n        width:33.33333%\n    }\n    th{\n        border: 0;\n        background:#FAFAFA;\n        padding: 30px;\n        font-size: 18px;\n        color: #23282C;\n    }\n    tfoot{\n        text-align:center;\n        a{\n            font-size:18px;\n            text-decoration: underline;\n            font-weight: bold;\n        }\n        p{\n            margin-top:20px;\n        }\n    }\n\n    .features-none{\n        padding-left: 30px;\n        background-position: 0px 4px;\n        background-size: 15px auto;\n        background-repeat: no-repeat;\n        background-image: url(../img/icon-none.svg);\n    }\n\n    .features-partial{\n        padding-left: 30px;\n        background-position: -3px 0;\n        background-size: 23px auto;\n        background-repeat: no-repeat;\n        background-image: url(../img/icon-partial.svg);\n    }\n\n    .features-full{\n        padding-left: 30px;\n        background-position: 0 6px;\n        background-size: 15px auto;\n        background-repeat: no-repeat;\n        background-image: url(../img/icon-full.svg);\n    }\n\n}\n\n.clearfix{\n    opacity: 1 !important;\n\n}\n\n/* Google Charts */\nsvg > g > g:last-child { pointer-events: none }\n\n.seedprod-charts{\n    background: #fff;\n    border: 1px solid #e7e8e7;\n    padding: 40px 0 0;\n    min-height: 120px;\n\n}\n\n/* New Builder */\n.sp-btn{\n    padding: 11px 15px;\n    border-radius: 6px;\n    i{    margin-right: 10px;}\n}\n\n#seedprod-builder-view{\n    scroll-behavior: smooth;\n}\n\n#sp-revision-history{\n    padding: 20px;\n    p{\n        font-weight: 600;\n        font-size: 13px;\n        padding: 0px 0 16px 0px;\n    }\n    .active-current{\n        border: 2px solid #dd4a1f;\n    }\n    .sp-revision-item{\n        display: flex;\n        align-items: center;\n        margin-bottom:10px;\n        margin-left: -6px;\n    }\n    .active.sp-revision-item{\n        margin-left: -21px;\n        button{\n            border: 2px solid #DD4A1F;\n            background: #fff;\n        }\n    }\n    .active .sp-revision-item-circle{\n        border: 2px solid #dd4a1f;\n        width: 10px;\n        height: 10px;\n        display: inline-block;\n        border-radius: 9999px;\n    }\n    .active .sp-revision-item-line{\n        border-bottom: 2px solid #dd4a1f;\n        width: 5px;\n        display: inline-block;\n    }\n    button{\n        padding-left:16px;\n        display: flex;\n        align-items: center;\n        background: #EDEBED;\n        height: 70px;\n        width: 100%;\n        border-radius: 6px;\n        color: #4F394D;\n        font-weight: 600;\n        font-size: 13px;\n        img{\n            border: none;\n            border-radius: 9999px;\n            width:39px;\n            height: 39px;\n            margin-right:16px;\n        }\n        .sp-rev-date{\n            color: #7b6b7a;\n            font-size: 10px;\n        }\n    }\n    button:hover{\n        background: #fff;\n    }\n\n}\n\n\n\n#sp-revision-timeline{\n    margin-top:-3px;\n    padding-top: 16px;\n    border-left: 2px solid #EDEBED;\n    padding-left:15px;\n}\n\n#sp-bottom-nav-actions{\n    position: fixed;\n    height:53px;\n    width:inherit;\n    bottom: 0;\n    background: #4F394D;\n    color: #D3CED2;\n    z-index:20;\n    font-size: 16px;\n\n}\n\n#sp-bottom-nav-actions button{\n    width: 38px;\n    height: 38px;\n    border-radius: 6px;\n}\n\n#sp-bottom-nav-actions button.active{\n    background: #fff !important;\n    color: #4f394d !important;\n}\n\n#sp-bottom-nav-actions button:hover{\n    color: #fff ;\n    background: #392037;\n}\n\n#seedprod-builder-preview{\n    margin-left:10px;\n    font-size:14px;\n    font-weight: 600;\n    border:1px solid #a79ca6;\n    color: #a79ca6;\n    height:40px;\n}\n\n#seedprod-builder-preview:hover{\n    border:1px solid #0b6142;\n    background: #0b6142;\n    color: #fff;\n    opacity: 1;\n}\n\n#seedprod-builder-save{\n    font-size:14px;\n    font-weight: 600;\n    border-top-right-radius:0;\n    border-bottom-right-radius:0;\n    background:#0F8A5E;\n    height:40px;\n}\n\n#seedprod-builder-save:hover{\n    background: #0b6142;\n}\n\n#seedprod-builder-save-dropdown{\n    background:#0F8A5E;\n    display:flex;\n    height:40px;\n    font-size:15px;\n    border-left: 1px rgba(0, 0, 0, 0.3) solid;\n    border-top-left-radius:0;\n    border-bottom-left-radius:0;\n    padding-right: 12px;\n    padding-left: 12px;\n    i{    margin-right: 0px;}\n}\n\n#seedprod-builder-save-dropdown:hover{\n    background: #0b6142;\n}\n\n#seedprod-builder-save-dropdown.active{\n    background: #0b6142;\n}\n\n#seedprod-builder-save-dropdown-menu{\n    background: #fff;\n    font-size:14px;\n    font-weight:600;\n    width: 179px;\n    margin-top: 2px;\n    z-index: 99999;\n    color: #4F394D ;\n    border-radius: 6px;\n    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;\n    .sp-btn{\n        width: 100%;\n        text-align:left;\n    }\n    button:hover{\n        color: #0F8A5E;\n        background:rgba(15, 138, 94, 0.05);\n    }\n    button{\n        display: flex;\n        align-items: center;\n    }\n}\n\n\n\n#sp-builder-top{\n    background: #230820;\n    color: #fff;\n}\n\n#sp-builder-top > div{\n    height: 72px;\n}\n\n#sp-builder-top-main-nav-utlity{\n    display: flex;\n    align-items: center;\n    justify-content: flex-end;\n    height: 100%;\n}\n\n#sp-builder-top-main-nav{\n    height: 100%;\n    > div{\n\n    display: flex;\n    height: 100%;\n    align-items: stretch;\n\n    }\n    a{\n    display: flex;\n    align-items: center;\n    padding:2px 20px;\n    font-size: 16px;\n    color: #A79CA6;\n    font-weight:600;\n    }\n    a.active{\n    font-size: 16px;\n    color:#fff;\n    box-shadow: inset 0 -3px 0 0 #fff !important;\n    }\n    a:hover{\n        color: #fff;\n    }\n}\n\n\n\n.sp-el-toolbar{\n    top: -24px;\n    left: 0;\n    width: inherit;\n    justify-content: center;\n}\n\n.sp-el-toolbar-add{\n    height: 0px;\n    bottom: 0px;\n    left: 0;\n    width: inherit;\n    justify-content: center;\n    z-index:100;\n}\n\n.sp-el-toolbar-add > span{\n    position: relative;\n    top: -11px;\n}\n\n.sp-toolbar-bottom{\n    top: 0px;\n\n     > div{\n        border-top-left-radius: 0px !important;\n        border-top-right-radius: 0px !important;\n        border-bottom-left-radius: 6px;\n        border-bottom-right-radius: 6px;\n    }\n\n}\n\n.sp-el-toolbar > div{\n    border-top-left-radius: 6px;\n    border-top-right-radius: 6px;\n}\n\n.sp-toolbar-left{\n    width: 22px;\n    height: inherit;\n    background: linear-gradient(135deg, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 50%,#7036BB 50%,#7036BB 100%);\n    background-position-y: 2px;\n    background-repeat: no-repeat;\n    background-position-x: 0px;\n    position: relative;\n    right: -1px;\n}\n\n.sp-toolbar-bottom{\n    .sp-toolbar-left{\n        width: 22px;\n        height: inherit;\n        background: linear-gradient(45deg, rgba(255,255,255,0) 0%,rgba(255,255,255,0) 50%,#7036BB 50%,#7036BB 100%);\n        background-position-y: 0px;\n        background-repeat: no-repeat;\n        background-position-x: 0px;\n        position: relative;\n        right: -3px;\n    }\n}\n\n.sp-toolbar-right{\n    width: 22px;\n    height: inherit;\n    background: linear-gradient(45deg, #7036BB 0%,#7036BB 50%,rgba(255,255,255,0) 50%,rgba(255,255,255,0) 100%);\n    background-position-y: 0px;\n    background-repeat: no-repeat;\n    background-position-x: -1px;\n    position: relative;\n    left: -1px;\n}\n\n.sp-toolbar-bottom{\n    .sp-toolbar-right{\n        width: 22px;\n        height: inherit;\n        background: linear-gradient(135deg, #7036BB 0%,#7036BB 50%,rgba(255,255,255,0) 50%,rgba(255,255,255,0) 100%);\n        background-position-y: 0px;\n        background-repeat: no-repeat;\n        background-position-x: -1px;\n        position: relative;\n        left: -1px;\n    }\n}\n\n.color-picker-float{\n    position: absolute;\n    z-index: 10;\n    right: 7px;\n    margin-top:5px;\n    background:#fff;\n    border-radius: 10px;\n    padding:16px;\n    box-shadow: 0px 15px 50px rgba(81, 75, 104, 0.3);\n\n}\n\n.color-picker-float-overflow{\n    //position: absolute;\n    z-index: 50;\n    left: 20px;\n    margin-left:18px;\n    margin-top:5px;\n    background:#fff;\n    border-radius: 10px;\n    padding:16px;\n    box-shadow: 0px 15px 50px rgba(81, 75, 104, 0.3);\n    top:0;\n    width: 364px;\n    .vc-chrome{\n        width:180px\n    }\n    button{\n        box-shadow: 0 0 1px 1px #dedede;\n    }\n}\n\n.vc-chrome{\n    box-shadow:none !important;\n}\n\n.sp-form-group{\n    margin-bottom:10px;\n    input[type=text],select{\n        width:100%;\n        display:block;\n    }\n    .help_tip i{\n        vertical-align: text-top;\n    }\n   .color-picker-container{\n        left:5px;\n    }\n    .simple-color-picker{\n        .sp-cp-label{\n            font-weight: 600;\n            font-size: 14px;\n        }\n    }\n    .simple-color-picker-container{\n\n        .sp-bg-cv-transparent{\n            width:105px;\n            height:28px;\n            border-radius: 20px;\n        }\n        .current-color{\n            width:105px;\n            height:28px;\n            border-radius: 20px;\n            border: 2px solid #FFFFFF;\n        }\n    }\n    .current-color{\n        //border:1px solid #DEDADE;\n        display: inherit;\n    }\n    .sp-color-value{\n        padding-left:28px;\n    }\n    // .vc-sketch{\n    //     position: absolute;\n    //     z-index: 10;\n    //     left:20px;\n    // }\n    .sp-color-value-clear{\n        display:inline-block;\n        right:7px;\n        color: #efefef;\n    }\n    .sp-btn-primary{\n        background-color: #38A169;\n        color:#fff;\n        padding:10px 10px;\n        border-radius:4px;\n        line-height: 1;\n        cursor: pointer;\n    }\n}\n\n.sp-bg-cv-transparent{\n    background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%);\n    background-size: 10px 10px;\n    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;\n    width: 1.25rem;\n    height: 1.25rem;\n    border-radius: 9999px;\n}\n\n\n\n  .gutter.gutter-horizontal {\n    cursor: ew-resize;\n    margin-left: -1px !important;\n    margin-right: -1px !important;\n    min-height:75px;\n  }\n\n//   .sp-el-row {\n//      margin-left: -2px !important;\n//      margin-right: -2px !important;\n//   }\n\n//   .sp-el-col {\n//     margin-left: 2px !important;\n//     margin-right: 2px !important;\n//   }\n\n  .sp-btn-group{\n      span{\n        padding:7px;\n        background: #eee;\n        width: 50px;\n        display:inline-block;\n        text-align:center;\n        cursor: pointer;\n      }\n      span.active{\n        background: #ccc;\n      }\n      span:hover{\n        background: #ddd;\n      }\n      span:first-child{\n        border-top-left-radius: 4px;\n        border-bottom-left-radius: 4px;\n      }\n      span:last-child{\n        border-top-right-radius: 4px;\n        border-bottom-right-radius: 4px;\n      }\n  }\n\n  .mce-i-help{\n      display: none !important;\n  }\n\n  .mce-panel .mce-btn i.mce-caret {\n    border-top: 6px solid #7b6b7a;\n    margin-left: 2px;\n    margin-right: 2px;\n}\n\n.mce-panel .mce-btn i.mce-caret {\n    border-top: 6px solid #7b6b7a;\n    margin-left: 2px;\n    margin-right: 2px;\n}\n\n.mce-panel .mce-btn:focus i.mce-caret, .mce-panel .mce-btn:hover i.mce-caret {\n    border-top-color: #ffffff;\n}\n\n\n  .mce-tinymce-inline .mce-flow-layout-item .mce-first,.mce-tinymce-inline .mce-flow-layout-item .mce-last{\n    pointer-events: none  !important;\n  }\n\n  .sp-section-categories{\n      li{\n          padding:16px 0;\n          color: #7B6B7A;\n          font-weight: 600;\n          font-size: 16px;\n          margin-bottom:0;\n          //border-bottom: 1px solid\n          box-shadow: inset 0px -1px 0px rgba(167, 156, 166, 0.25);\n      }\n      .sp-w-4{\n        width:24px;\n      }\n      .sp-h-4{\n        height:24px;\n      }\n  }\n\n.mce-toolbar-grp{\n    background: #EDEBED !important;\n    border-bottom: 1px solid #EDEBED !important;\n  }\n\n  .mce-tinymce-inline.mce-panel{\n      background: transparent !important;\n      box-shadow: none !important;\n  }\n.mce-tinymce-inline .mce-toolbar-grp{\n    background: #230820 !important;\n    border-bottom: 1px solid #230820 !important;\n    border-radius: 50px;\n  }\n\n  .mce-tinymce-inline .mce-btn:hover{\n    color: #fff !important;\n    background: #230820 !important;\n    border-color: #7B6B7A !important;\n    box-shadow: none !important;\n    .mce-ico{\n        color:#fff !important;\n          }\n\n  }\n\n  .mce-tinymce-inline .mce-ico{\ncolor:#7B6B7A !important;\n  }\n\n  .mce-tinymce-inline .mce-toolbar-grp .mce-active {\n\n    background: #230820 !important;\n    border-color: #7B6B7A !important;\n\n\n    .mce-ico{\n        color:#fff !important;\n          }\n  }\n\n  .mce-inline-toolbar-grp{\n    background: #230820 !important;\n    border-bottom: 1px solid #230820 !important;\n     .mce-ico{\n        color:#7B6B7A  !important;\n          }\n          .mce-ico:hover{\n            color:#fff  !important;\n              }\n    .mce-btn:hover{\n        color:#fff !important;\n        background: #230820 !important;\n        border-color: #7B6B7A !important;\n        box-shadow: none !important;\n\n    }\n\n    .mce-primary{\n        color:#fff !important;\n        background: #0f8a5e !important;\n        border-color: #0f8a5e !important;\n        box-shadow: none !important;\n        .mce-ico{\n            color: #fff !important;\n        }\n    }\n\n    .mce-primary:hover{\n        color:#fff !important;\n        background: #0b6142 !important;\n        border-color: #0b6142 !important;\n        box-shadow: none !important;\n        .mce-ico{\n            color: #fff !important;\n        }\n    }\n\n    div.wp-link-preview a {\n        color: #fff !important;\n    }\n  }\n\n  div.mce-inline-toolbar-grp.mce-arrow-up:after {\n    border-bottom-color: #230820 !important;\n\n}\n\n\n\n\n.vue-slider-process{\n    background-color: #dd4a1f !important;\n}\n\n.vue-slider-dot-tooltip-inner{\n    border-color: #dd4a1f !important;\n    background-color: #dd4a1f !important;\n}\n\n.seedprod-sidebar-form::-webkit-scrollbar {\n    display: none;\n  }\n\n\n  #wpforms-builder-elementor-popup {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    padding: 30px;\n    z-index: 10000;\n    background: rgba( 0, 0, 0, 0.75 );\n}\n\n#wpforms-builder-elementor-popup iframe {\n    width: 100%;\n    height: 100%;\n    background-color: #ffffff;\n}\n\n.fade-enter-active, .fade-leave-active {\n    transition: opacity 0.25s ease-out;\n}\n\n.fade-enter, .fade-leave-to {\n    opacity: 0;\n}\n\n.sp-preview-hidden {\n    display: none !important;\n}\n\n.sp-wp-button {\n    display: inline-block;\n    padding: .5em .75em;\n    font-weight: 600;\n    border-radius: 3px;\n\n    &-transparent {\n        background-color: #7b6b7a;\n        border: 1px solid #7b6b7a;\n        color: #fff;\n\n        &:hover {\n            background-color: #4f394d;\n            border-color: #4f394d;\n            color: #fff;\n        }\n    }\n}\n\n.loading:after {\n    content: ' .';\n    animation: dots 1s steps(5, end) infinite;}\n  \n  @keyframes dots {\n    0%, 20% {\n      color: rgba(0,0,0,0);\n      text-shadow:\n        .25em 0 0 rgba(0,0,0,0),\n        .5em 0 0 rgba(0,0,0,0);}\n    40% {\n      color: black;\n      text-shadow:\n        .25em 0 0 rgba(0,0,0,0),\n        .5em 0 0 rgba(0,0,0,0);}\n    60% {\n      text-shadow:\n        .25em 0 0 black,\n        .5em 0 0 rgba(0,0,0,0);}\n    80%, 100% {\n      text-shadow:\n        .25em 0 0 black,\n        .5em 0 0 black;}}"]}