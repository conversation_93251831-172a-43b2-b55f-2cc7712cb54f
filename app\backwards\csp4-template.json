{"enable_recaptcha": false, "template_id": 71, "post_title": "cspv4", "post_name": "cspv4", "post_status": "draft", "show_powered_by_link": false, "affiliate_url": "", "header_scripts": "", "footer_scripts": "", "conversion_scripts": "", "disable_default_excluded_urls": false, "no_conflict_mode": true, "include_exclude_type": "0", "include_list": "", "exclude_list": "", "bypass_phrase": "", "bypass_expires": "2", "bypass_cookie": false, "access_by_ip": "", "access_by_role": [], "redirect_mode": false, "redirect_url": "", "domain_mapping_status": false, "domain_mapping": "", "exclude_list_404": "", "email_integration_id": "", "is_new": false, "page_type": "lp", "document": {"sections": [{"id": "ks8a8s", "type": "section", "rows": [{"id": "vw3kvy", "type": "row", "colType": "1-col", "cols": [{"id": "wz5ajq", "type": "col", "blocks": [{"id": "k4accv", "elType": "block", "type": "image", "settings": {"unit": "px", "src": "", "altTxt": "", "link": "", "width": "", "height": "", "marginTop": "0", "paddingTop": "", "paddingBottom": "", "paddingLeft": "", "paddingRight": "", "paddingSync": true, "imagePadding": "", "align": "center", "shadow": "", "imageBorderRadius": "", "imageBorderTop": "0", "imageBorderBottom": "0", "imageBorderLeft": "0", "imageBorderRight": "0", "imageBorderSync": true, "imageBorderStyle": "solid", "imageBorderColor": "", "blockTemplateId": false}}, {"id": "rchwtv", "elType": "block", "type": "header", "settings": {"headerTxt": "My Awesome Headline", "tag": "h1", "textColor": "", "bgColor": "", "beforeIcon": "", "afterIcon": "", "marginTop": "0", "paddingTop": "", "paddingBottom": "", "paddingLeft": "", "paddingRight": "", "paddingSync": true, "borderRadius": "", "border": "", "borderStyle": "", "borderSize": "", "borderColor": "", "shadow": "", "textShadow": "", "font": "", "fontVariant": "", "fontSize": "", "fontSize_mobile": "", "lineHeight": "", "lineHeight_mobile": "", "letterSpacing": "", "letterSpacing_mobile": "", "typographyBold": "", "typographyItalic": "", "typographyUnderline": "", "align": "center", "typographyLetterCase": ""}}, {"id": "ss42wh", "elType": "block", "type": "text", "settings": {"txt": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. <PERSON>ullam commodo velit ex, non ultrices leo auctor at. Integer blandit ex velit, vel aliquam sem tempor eu. Pellentesque sem tortor, elementum et nisi sed, convallis pharetra lorem. Aenean rhoncus rhoncus ex, in dictum massa dictum et. Morbi at nisl fermentum, condimentum tortor a, laoreet leo. Curabitur laoreet diam a metus tincidunt, sed dapibus orci venenatis.", "textColor": "", "bgColor": "", "beforeIcon": "", "afterIcon": "", "marginTop": "0", "paddingTop": "", "paddingBottom": "", "paddingLeft": "", "paddingRight": "", "paddingSync": true, "borderRadius": "", "border": "", "borderStyle": "", "borderSize": "", "borderColor": "", "shadow": "", "textShadow": "", "font": "", "fontVariant": "", "fontSize": "", "fontSize_mobile": "", "lineHeight": "", "lineHeight_mobile": "", "letterSpacing": "", "letterSpacing_mobile": "", "typographyBold": "", "typographyItalic": "", "typographyUnderline": "", "align": "center", "typographyLetterCase": ""}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "colWidth": "", "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "", "shadow": "", "paddingTop": "", "paddingBottom": "", "paddingLeft": "", "paddingRight": "", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "colGutter": 0, "width": "1000", "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "", "shadow": "", "paddingTop": "0", "paddingBottom": "0", "paddingLeft": "0", "paddingRight": "0", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "contentWidth": "2", "width": 600, "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "60", "shadow": "", "paddingTop": "10", "paddingBottom": "10", "paddingLeft": "10", "paddingRight": "10", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}, {"id": "qy2ukz", "type": "section", "rows": [{"id": "y2q7z4", "type": "row", "colType": "1-col", "cols": [{"id": "kc98i6", "type": "col", "blocks": [{"id": "x7mrh1", "elType": "block", "type": "spacer", "settings": {"height": 60}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "colWidth": "", "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "", "shadow": "", "paddingTop": "", "paddingBottom": "", "paddingLeft": "", "paddingRight": "", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "colGutter": 0, "width": "1000", "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "", "shadow": "", "paddingTop": "0", "paddingBottom": "0", "paddingLeft": "0", "paddingRight": "0", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "contentWidth": 1, "width": "1000", "bgColor": "", "bgImage": "", "bgPosition": "", "marginTop": "", "shadow": "", "paddingTop": "10", "paddingBottom": "10", "paddingLeft": "10", "paddingRight": "10", "paddingSync": true, "borderRadiusTL": "", "borderRadiusTR": "", "borderRadiusBL": "", "borderRadiusBR": "", "borderRadiusSync": true, "borderTop": "0", "borderBottom": "0", "borderLeft": "0", "borderRight": "0", "borderSync": true, "borderStyle": "solid"}}], "settings": {"bgStyle": "s", "bgGradient": {"type": "linear", "position": "center", "angle": 0, "color1": "", "color1location": 0, "color2": "", "color2location": 100}, "bgColor": "#FFFFFF", "bgImage": "", "bgDimming": 0, "bgOverlayColor": "", "bgPosition": "cover", "buttonColor": "#000000", "headerColor": "#000000", "linkColor": "#FF0000", "linkDarkerColor": "#cc0000", "textColor": "#272727", "textFont": "'Helvetica Neue', <PERSON><PERSON>, sans-serif", "textFontVariant": "400", "headerFont": "'Helvetica Neue', <PERSON><PERSON>, sans-serif", "headerFontVariant": "400", "contentPosition": "1", "customCss": "", "headCss": "#sp-page{color:#272727} #sp-page .sp-header-tag-h1,#sp-page .sp-header-tag-h2,#sp-page .sp-header-tag-h3,#sp-page .sp-header-tag-h4,#sp-page .sp-header-tag-h5,#sp-page .sp-header-tag-h6{color:#000000}#sp-page h1,#sp-page h2,#sp-page h3,#sp-page h4,#sp-page h5,#sp-page h6{color:#000000; font-family:'Helvetica Neue', Arial, sans-serif;font-weight:400;font-style:normal} #sp-page a{color:#FF0000} #sp-page a:hover{color:#cc0000}#sp-page .btn{background-color:#000000}body{background-color:#FFFFFF; background-image:;}", "mobileCss": "", "mobileVisibilityCss": "", "desktopVisibilityCss": "", "placeholderCss": "", "useVideoBg": false}}}