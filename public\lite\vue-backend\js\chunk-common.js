(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-common"],{"476a":function(t,e,s){"use strict";var o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.show_inline_help?s("div",{staticClass:"\n    seedprod-inline-help-top-container\n    sp-antialiased\n    sp-font-display\n  "},[s("div",{staticClass:"\n      sp-flex\n      sp-justify-between\n      sp-text-neutral\n      sp-p-8\n      seedprod-inline-help-title-container\n    "},[s("img",{staticClass:"seedprod-builder-help-logo",attrs:{src:t.shared.plugin_path+"public/svg/seedprod-logo.svg",title:t.txt_1,alt:t.txt_1}}),s("button",{staticClass:"seedprod-builder-help-close",attrs:{title:t.txt_2},on:{click:function(e){return t.hideInlineHelp(!1)}}},[s("svg",{attrs:{width:"24",height:"24",viewBox:"0 0 1792 1792",xmlns:"http://www.w3.org/2000/svg"}},[s("path",{attrs:{d:"M1490 1322q0 40-28 68l-136 136q-28 28-68 28t-68-28l-294-294-294 294q-28 28-68 28t-68-28l-136-136q-28-28-28-68t28-68l294-294-294-294q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 294 294-294q28-28 68-28t68 28l136 136q28 28 28 68t-28 68l-294 294 294 294q28 28 28 68z"}})])])]),s("div",{staticClass:"seedprod-builder-help-content sp-flex"},[s("div",{staticClass:"\n        seedprod-builder-help-search\n        sp-pt-5\n        sp-pr-5\n        sp-flex\n        sp-justify-center\n        sp-items-center\n        sp-top-0\n        sp-z-10\n      "},[s("span",{staticClass:"\n          sp-text-neutral-40\n          sp-relative\n          seedprod-help-search-icon\n        "},[s("svg",{staticClass:"sp-fill-current sp-w-6 sp-h-6",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M0 0h24v24H0V0z",fill:"none"}}),s("path",{attrs:{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}})])]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.help_search,expression:"help_search"}],staticClass:"sp-w-full sp-pl-12",attrs:{type:"text",placeholder:t.txt_3},domProps:{value:t.help_search},on:{input:function(e){e.target.composing||(t.help_search=e.target.value)}}}),""!=t.help_search?s("span",{staticClass:"\n          sp-text-neutral-40\n          sp-z-10\n          sp-relative\n          sp-cursor-pointer\n          seedprod-help-search-cancel-icon\n        ",on:{click:t.clear_search}},[s("svg",{staticClass:"sp-fill-current sp-w-4 sp-h-4",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"}}),s("path",{attrs:{d:"M0 0h24v24H0z",fill:"none"}})])]):t._e()]),""!=t.help_search?s("div",{staticClass:"\n        seedprod-builder-help-result\n      "},[t.searchArticles.length>0?s("ul",{staticClass:"seedprod-builder-category-accordion"},t._l(t.searchArticles,function(e,o){return s("li",{key:o,staticClass:"seedprod-inline-help-accordion-article link"},[s("i",{staticClass:"fa fa-file-alt",attrs:{"aria-hidden":"true"}}),s("a",{attrs:{href:e.link+"?utm_source=WordPress&utm_campaign=liteplugin&utm_medium=inlinehelp",target:"_blank"}},[t._v("\n            "+t._s(e.title)+"\n          ")])])}),0):s("span",{staticClass:"seedprod-builder-help-no-articles"},[t._v("\n        No articles found.\n      ")])]):t._e(),s("div",{staticClass:"seedprod-builder-help-categories"},t._l(t.shared.inline_help_articles_data.category_articles,function(e,o){return s("div",{key:o},[e.category_details.article_count>0?s("ul",{staticClass:"seedprod-builder-category-accordion",class:t.accordion_data[o]&&!0===t.accordion_data[o].open?"opened":""},[s("header",{on:{click:function(e){return t.set_open_close(o)}}},[s("i",{staticClass:"fa fa-folder-open",class:"folder-open",attrs:{"aria-hidden":"true"}}),s("span",[t._v(t._s(e.category_details.name))]),s("i",{staticClass:"fa fa-angle-right accordion-arrow",attrs:{"aria-hidden":"true"}})]),t._l(e.articles,function(e,i){return s("li",{key:i,staticClass:"seedprod-inline-help-accordion-article link",class:t.accordion_data[o]&&!0===t.accordion_data[o].open?"":"close-accordion"},[s("i",{staticClass:"fa fa-file-alt",attrs:{"aria-hidden":"true"}}),s("a",{attrs:{href:e.link+"?utm_source=WordPress&utm_campaign=liteplugin&utm_medium=inlinehelp",target:"_blank"}},[t._v("\n              "+t._s(e.title.rendered)+"\n            ")])])})],2):t._e()])}),0),s("div",{staticClass:"seedprod-builder-help-footer"},[s("div",{staticClass:"seedprod-builder-help-footer-block"},[s("i",{staticClass:"fa fa-file-alt",attrs:{"aria-hidden":"true"}}),s("h3",[t._v(t._s(t.txt_6))]),s("p",[t._v(t._s(t.txt_7))]),s("a",{staticClass:"button sp-notification-alt-button sp-px-3 sp-alt-notification-button-y sp-text-sm sp-mr-2",attrs:{href:"https://www.seedprod.com/docs",rel:"noopener noreferrer",target:"_blank"}},[t._v("\n          "+t._s(t.txt_8)+"\n        ")])]),s("div",{staticClass:"seedprod-builder-help-footer-block"},[s("i",{staticClass:"fa fa-life-ring",attrs:{"aria-hidden":"true"}}),s("h3",[t._v(t._s(t.txt_9))]),"seedprod_pro"==t.shared.page_path?s("div",[s("p",[t._v(t._s(t.txt_10))]),s("a",{staticClass:"button sp-notification-alt-button sp-px-3 sp-alt-notification-button-y sp-text-sm sp-mr-2",attrs:{href:"https://app.seedprod.com/login",rel:"noopener noreferrer",target:"_blank"}},[t._v("\n            "+t._s(t.txt_11)+"\n          ")])]):s("div",[s("p",[t._v(t._s(t.txt_12))]),s("a",{staticClass:"button sp-notification-alt-button sp-px-3 sp-alt-notification-button-y sp-text-sm sp-mr-2",attrs:{href:"https://app.seedprod.com/downloads?utm_source=WordPress&utm_campaign=liteplugin&utm_medium=pluginbuilder-"+t.feature_source,rel:"noopener noreferrer",target:"_blank"}},[t._v("\n            "+t._s(t.txt_13)+"\n          ")])])])])])]):t._e()},i=[],n=(s("6762"),s("2fdb"),s("b54a"),s("ac6a"),s("b132")),a=s("561c"),l={comments:!0,name:"InlineHelp",mixins:[n["a"]],data:function(){return{txt_1:Object(a["a"])("SeedProd Logo","coming-soon"),txt_2:Object(a["a"])("Close","coming-soon"),txt_3:Object(a["a"])("Ask a question or search the docs...","coming-soon"),txt_4:Object(a["a"])("Clear","coming-soon"),txt_5:Object(a["a"])("No docs found","coming-soon"),txt_6:Object(a["a"])("View Documentation","coming-soon"),txt_7:Object(a["a"])("Browse documentation, reference material, and tutorials for SeedProd.","coming-soon"),txt_8:Object(a["a"])("View All Documentation","coming-soon"),txt_9:Object(a["a"])("Get Support","coming-soon"),txt_10:Object(a["a"])("Submit a ticket and our world class support team will be in touch soon.","coming-soon"),txt_11:Object(a["a"])("Submit a Support Ticket","coming-soon"),txt_12:Object(a["a"])("Upgrade to SeedProd Pro to access our world class customer support","coming-soon"),txt_13:Object(a["a"])("Upgrade to SeedProd Pro","coming-soon"),show_inline_help:this.value,feature_source:"inline-help",shared:inline_help_data,help_search:"",all_articles_array:[],accordion_data:[]}},mounted:function(){var t=this;t.get_all_articles()},methods:{set_open_close:function(t){var e=this;e.accordion_data[t].open=!e.accordion_data[t].open},clear_search:function(){this.help_search=""},hideInlineHelp:function(t){this.$emit("input",t)},get_all_articles:function(){var t=this,e=[],s=t.shared.inline_help_articles_data.category_articles,o=[];s.forEach(function(t){var s=t.articles;o.push({open:!1}),s.forEach(function(t){e.push({title:t.title.rendered,link:t.link})})}),t.all_articles_array=e,t.accordion_data=o}},computed:{searchArticles:function(){var t=this;return this.all_articles_array.filter(function(e){return e.title.toLowerCase().includes(t.help_search.toLowerCase())})}},components:{},props:{value:Boolean}},r=l,c=s("2877"),d=Object(c["a"])(r,o,i,!1,null,null,null);e["a"]=d.exports},"714b":function(t,e,s){"use strict";var o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("transition",{attrs:{name:"seedprod-modal"}},[s("div",{staticClass:"seedprod-modal-mask",on:{click:function(e){return e.target!==e.currentTarget?null:t.$emit("close")}}},[s("div",{staticClass:"seedprod-modal-wrapper"},[s("div",{staticClass:"seedprod-modal-container"},[s("div",{staticClass:"seedprod-modal-header"},[t._t("header",[t._v(t._s(t.txt_1))])],2),s("div",{staticClass:"seedprod-modal-body"},[t._t("body",[t._v(t._s(t.txt_2))])],2)])])])])},i=[],n=s("561c"),a={data:function(){return{txt_1:Object(n["a"])("default header","coming-soon"),txt_2:Object(n["a"])("default body","coming-soon")}},name:"modal"},l=a,r=s("2877"),c=Object(r["a"])(l,o,i,!1,null,null,null);e["a"]=c.exports},b132:function(t,e,s){"use strict";s.d(e,"a",function(){return O});var o,i=s("bd86"),n=(s("f559"),s("768b")),a=s("a4bb"),l=s.n(a),r=s("7618"),c=s("a745"),d=s.n(c),u=s("75fc"),p=(s("4917"),s("7f7f"),s("96cf"),s("3b8d")),h=s("f499"),g=s.n(h),m=(s("ac6a"),s("6762"),s("2fdb"),s("e814")),b=s.n(m),f=(s("6b54"),s("28a5"),s("a481"),s("66cb")),_=s.n(f),v=s("2ef0"),y=s.n(v),x=s("4328"),w=s.n(x),j=s("561c"),O={methods:(o={set_default_val:function(t,e,s){void 0==t[e]&&this.$set(t,e,s)},stripTags:function(t){return t?t.replace(/<\/?[^>]+(>|$)/g,""):t},hexToRgbA:function(t){var e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return t&&"string"===typeof t&&/^#([A-Fa-f0-9]{3}){1,2}$/.test(t)?(e=t.substring(1).split(""),3===e.length&&(e=[e[0],e[0],e[1],e[1],e[2],e[2]]),e="0x"+e.join(""),"rgba("+[e>>16&255,e>>8&255,255&e].join(",")+","+s+")"):"rgba(0, 0, 0, ".concat(s,")")},update_head_css:y.a.debounce(function(t){var e=_()(this.shared.settings.document.settings.linkColor).darken().toString();this.shared.settings.document.settings.linkDarkerColor=e;var s="";if(""!=this.shared.settings.document.settings.bgImage&&(s="url('"+this.shared.settings.document.settings.bgImage+"')"),""!=this.shared.settings.document.settings.bgImage&&""!=this.shared.settings.document.settings.bgDimming){var o=.01*this.shared.settings.document.settings.bgDimming;s="linear-gradient(0deg, rgba(0,0,0,"+o+"), rgba(0,0,0,"+o+")),url('"+this.shared.settings.document.settings.bgImage+"')"}""==s&&"g"==this.shared.settings.document.settings.bgStyle&&(s="linear"==this.shared.settings.document.settings.bgGradient.type?"linear-gradient("+this.shared.settings.document.settings.bgGradient.angle+"deg, "+this.shared.settings.document.settings.bgGradient.color1+" "+this.shared.settings.document.settings.bgGradient.color1location+"%, "+this.shared.settings.document.settings.bgGradient.color2+" "+this.shared.settings.document.settings.bgGradient.color2location+"%)":"radial-gradient(circle at "+this.shared.settings.document.settings.bgGradient.position+", "+this.shared.settings.document.settings.bgGradient.color1+" "+this.shared.settings.document.settings.bgGradient.color1location+"%, "+this.shared.settings.document.settings.bgGradient.color2+" "+this.shared.settings.document.settings.bgGradient.color2location+"%)");var i="#sp-page{color:"+this.shared.settings.document.settings.textColor+"} #sp-page .sp-header-tag-h1,#sp-page .sp-header-tag-h2,#sp-page .sp-header-tag-h3,#sp-page .sp-header-tag-h4,#sp-page .sp-header-tag-h5,#sp-page .sp-header-tag-h6{color:"+this.shared.settings.document.settings.headerColor+"}#sp-page h1,#sp-page h2,#sp-page h3,#sp-page h4,#sp-page h5,#sp-page h6{color:"+this.shared.settings.document.settings.headerColor+"; font-family:"+this.font_render(this.shared.settings.document.settings.headerFont)+";font-weight:"+this.font_variant_render(this.shared.settings.document.settings.headerFontVariant,"weight")+";font-style:"+this.font_variant_render(this.shared.settings.document.settings.headerFontVariant,"style")+"} #sp-page a{color:"+this.shared.settings.document.settings.linkColor+"} #sp-page a:hover{color:"+e+"}#sp-page .btn{background-color:"+this.shared.settings.document.settings.buttonColor+"}body{background-color:"+this.shared.settings.document.settings.bgColor+" !important; background-image:"+s+";}",n=this,a="";if(!0===this.shared.is_landing_page){var l=w.a.stringify({css:this.shared.settings.document.settings.customCss});n.axios.post(seedprod_get_namespaced_custom_css_url,l,{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}).then(function(t){a=t.data,jQuery("#tmp-custom-css-style").remove(),jQuery("head").append("<style id='tmp-custom-css-style' type='text/css'></style>"),jQuery("#tmp-custom-css-style").html(i+a)})}this.shared.settings.document.settings.headCss=i},100),highlight_option_target:function(t){this.shared.highlight_option_target=t},moving:function(t,e){},scroll:function(t){function e(e){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(t){var e=jQuery("#seedprod-builder-view").scrollTop();jQuery("#seedprod-builder-view").scrollTop(e+t),this.shared.stop||setTimeout(function(){scroll(t)},20)}),start_move:function(){this.shared.is_moving=!0},end_move:function(t){this.shared.is_moving=!1,this.shared.stop=!1},mousePosition:function(t){console.log("event",t)},width_height_render:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return/^\d+$/.test(t)&&(t+="px"),t},text_shadow:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#000000",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o="",i="0,0,0",n=_()(e);return n.getLuminance(),1==t&&(o="1px 1px 0px rgba("+i+",0.5)"),2==t&&(o="1px 1px 3px rgba("+i+",0.5)"),3==t&&(o="2px 2px 4px rgba("+i+",0.4)"),4==t&&(o="3px 3px 6px rgba("+i+",0.3)"),5==t&&(o="3px 4px 12px rgba("+i+",0.3)"),6==t&&(o="5px 5px 20px rgba("+i+",0.3)"),"custom"==t&&(o=s),o},divider_shadow:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e="";return 1==t&&(e="0 1px 1px rgba(0,0,0,0.2)"),2==t&&(e="0 2px 2px rgba(0,0,0,0.4)"),3==t&&(e="0 4px 4px rgba(0,0,0,0.4)"),4==t&&(e="0 6px 6px rgba(0,0,0,0.4)"),e},box_shadow:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s="";return 1==t&&(s="0 1px 2px 0 rgba(0, 0, 0, 0.5)"),2==t&&(s="0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.6)"),3==t&&(s="0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.6)"),4==t&&(s="0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.5)"),5==t&&(s="0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.4)"),6==t&&(s="0 25px 50px -12px rgba(0, 0, 0, 0.25)"),7==t&&(s="0 10px 6px -6px #777"),"custom"==t&&(s=e),s},border_render:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return""==t&&(t="1px"),""==e&&(e="solid"),"#666666"==s&&(s="#666666"),t+"px "+e+" "+s},font_variant_render:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"weight",s="",o="";return""!=t&&("weight"==e&&(s=b()(t)),"style"==e&&(o=t.replace(/[0-9]/g,""),""==o&&(o="normal"))),"weight"==e?s:o},font_render:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return""!=t&&!1===t.includes(",")&&(t="'"+t+"'"),t}},Object(i["a"])(o,"border_render",function(t,e,s){return""==t||0==t||""==s?"":t+"px "+e+" "+s}),Object(i["a"])(o,"padding_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return""==t&&""==e&&""==s&&""==o?(this.shared.is_theme_template&&this.shared.is_theme_template,""):(""==t&&(t=0),""==e&&(e=0),""==s&&(s=0),""==o&&(o=0),t+"px "+e+"px "+s+"px "+o+"px ")}),Object(i["a"])(o,"margin_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return""==t&&""==e&&""==s&&""==o?(t=0,s=0,o=0,e=0,t+"px "+e+"px "+s+"px "+o+"px "):(""==t&&(t=0),""==e&&(e=0),""==s&&(s=0),""==o&&(o=0),t+"px "+e+"px "+s+"px "+o+"px ")}),Object(i["a"])(o,"border_radius_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return""==t&&""==e&&""==s&&""==o?(t=0,s=0,o=0,e=0,t+"px "+e+"px "+s+"px "+o+"px "):(""==t&&(t=0),""==e&&(e=0),""==s&&(s=0),""==o&&(o=0),t+"px "+e+"px "+s+"px "+o+"px ")}),Object(i["a"])(o,"align_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="left"),"right"==t&&(s="right"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align?"100%":"auto"),s}),Object(i["a"])(o,"align_render_mobile",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="left"),"right"==t&&(s="right"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align_mobile?"100%":"auto"),s}),Object(i["a"])(o,"align_render_tablet",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="left"),"right"==t&&(s="right"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align_tablet?"100%":"auto"),s}),Object(i["a"])(o,"align_flex_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="flex-start"),"right"==t&&(s="flex-end"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align?"100%":"auto"),s}),Object(i["a"])(o,"align_flex_render_mobile",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="flex-start"),"right"==t&&(s="flex-end"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align?"100%":"auto"),s}),Object(i["a"])(o,"align_flex_render_tablet",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"align",s="";return"align"==e&&("left"==t&&(s="flex-start"),"right"==t&&(s="flex-end"),"center"==t&&(s="center")),"width"==e&&(s="full"==this.block.settings.align?"100%":"auto"),s}),Object(i["a"])(o,"mobile_margin_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n="";return""==t&&""==e&&""==s&&""==o||(i?""!=t&&(n=this.margin_render(t,t,t,t)):n=this.margin_render(t,e,s,o)),n}),Object(i["a"])(o,"mobile_padding_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n="";return""==t&&""==e&&""==s&&""==o||(i?""!=t&&(n=this.padding_render(t,t,t,t)):n=this.padding_render(t,e,s,o)),n}),Object(i["a"])(o,"mobile_space_between_padding",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s="";return t&&("v"==e&&(s=this.padding_render("0","0",t,"0")),"h"==e&&(s=this.padding_render("0",t,"0","0"))),s}),Object(i["a"])(o,"tablet_margin_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n="";return""==t&&""==e&&""==s&&""==o||(i?""!=t&&(n=this.margin_render(t,t,t,t)):n=this.margin_render(t,e,s,o)),n}),Object(i["a"])(o,"tablet_padding_render",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n="";return""==t&&""==e&&""==s&&""==o||(i?""!=t&&(n=this.padding_render(t,t,t,t)):n=this.padding_render(t,e,s,o)),n}),Object(i["a"])(o,"tablet_space_between_padding",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",s="";return t&&("v"==e&&(s=this.padding_render("0","0",t,"0")),"h"==e&&(s=this.padding_render("0",t,"0","0"))),s}),Object(i["a"])(o,"load_font",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(""!=t&&this.shared.setup_page_meta.googlefonts["Google Fonts"][t]){if(""!=e){var s={},o=!1;try{this.shared.setup_page_meta.googlefonts["Google Fonts"][t].variants.forEach(function(t){if(t.id==e)throw s})}catch(a){o=!0}!1===o&&(e="")}var i="https://fonts.googleapis.com/css?family="+t.split(" ").join("+")+":"+e+"&display=swap",n=document.createElement("link");n.rel="stylesheet",n.href=i,document.head.appendChild(n)}}),Object(i["a"])(o,"paste_sections",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=this,s=seedprod_import_cross_site_url;xdLocalStorage.getItem("seedprod_section_data",function(o){if(""==o.value||null==o.value)e.$swal({imageUrl:e.shared.plugin_path+"public/svg/error-24px-white.svg",text:Object(j["a"])("There is no content available to paste. Please try copy section first!","coming-soon"),toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:5e3});else{var i=JSON.parse(o.value);i.id=e.uid(),i.rows.forEach(function(t){t.id=e.uid(),t.cols.forEach(function(t){t.id=e.uid(),t.blocks.forEach(function(t){t.id=e.uid()})})});var n,a,l=i;n=l;var r=!1;null!=t?(a=t+1,r=!1):(t=e.shared.settings.document.sections.length,a=t+1,r=!0),e.shared.settings.document.sections.splice(a,0,n);new FormData;var c=/\.(jpg|png|jpeg|gif|svg)/gi.test(g()(n));if(c){e.$swal({imageUrl:e.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Image uploading will take some time.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3});var d=w.a.stringify({settings:g()(n)});e.axios.post(s,d,{headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(function(t){r?e.$delete(e.shared.settings.document.sections,a-1):e.$delete(e.shared.settings.document.sections,a),e.shared.settings.document.sections.splice(a,0,t.data),e.$swal({imageUrl:e.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Section Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}).catch(function(t){e.$swal({imageUrl:e.shared.plugin_path+"public/svg/success-24px-white.svg",text:"Paste Error",toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:3e3})})}else e.$swal({imageUrl:e.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Section Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}})}),Object(i["a"])(o,"paste_element",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=JSON.parse(g()(t)),l=this;if(null!==e&&null!==s&&null!==o&&null!==i){var r=seedprod_import_cross_site_url;xdLocalStorage.getItem("seedprod_block_data",function(t){if(""==t.value||null==t.value)l.$swal({imageUrl:l.shared.plugin_path+"public/svg/error-24px-white.svg",text:Object(j["a"])("There is no content available to paste. Please try copy row first!","coming-soon"),toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:5e3});else{var n=JSON.parse(t.value);n.id=l.uid();var a,c,d=n;a=d,c=i+1,l.shared.settings.document.sections[e].rows[s].cols[o].blocks.splice(c,0,a);new FormData;var u=/\.(jpg|png|jpeg|gif|svg)/gi.test(g()(a));if(u){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Image uploading will take some time.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3});var p=w.a.stringify({settings:g()(a)});l.axios.post(r,p,{headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(function(t){l.$delete(l.shared.settings.document.sections[e].rows[s].cols[o].blocks,c),l.shared.settings.document.sections[e].rows[s].cols[o].blocks.splice(c,0,t.data),l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Block Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}).catch(function(t){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:"Paste Error",toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:3e3})})}else l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Block Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}})}else if(null!==e&&null!==s&&null!==o){var c=seedprod_import_cross_site_url;xdLocalStorage.getItem("seedprod_column_data",function(t){if(""==t.value||null==t.value)l.$swal({imageUrl:l.shared.plugin_path+"public/svg/error-24px-white.svg",text:Object(j["a"])("There is no content available to paste. Please try copy row first!","coming-soon"),toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:5e3});else{var i=JSON.parse(t.value);i.id=l.uid(),i.blocks.forEach(function(t){t.id=l.uid()});var n,a,r=i;n=r,a=o+1,l.shared.settings.document.sections[e].rows[s].cols.splice(a,0,n);new FormData;var d=/\.(jpg|png|jpeg|gif|svg)/gi.test(g()(n));if(d){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Image uploading will take some time.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3});var u=w.a.stringify({settings:g()(n)});l.axios.post(c,u,{headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(function(t){l.$delete(l.shared.settings.document.sections[e].rows[s].cols,a),l.shared.settings.document.sections[e].rows[s].cols.splice(a,0,t.data),l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Column Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}).catch(function(t){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:"Paste Error",toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:3e3})})}else l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Column Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}})}else if(null!==e&&null!==s){var d=seedprod_import_cross_site_url;xdLocalStorage.getItem("seedprod_row_data",function(t){if(""==t.value||null==t.value)l.$swal({imageUrl:l.shared.plugin_path+"public/svg/error-24px-white.svg",text:Object(j["a"])("There is no content available to paste. Please try copy row first!","coming-soon"),toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:5e3});else{var o=JSON.parse(t.value);o.id=l.uid(),o.cols.forEach(function(t){t.id=l.uid(),t.blocks.forEach(function(t){t.id=l.uid()})});var i,n,a=o;i=a,n=s+1,l.shared.settings.document.sections[e].rows.splice(n,0,i);new FormData;var r=/\.(jpg|png|jpeg|gif|svg)/gi.test(g()(i));if(r){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Image uploading will take some time.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3});var c=w.a.stringify({settings:g()(i)});l.axios.post(d,c,{headers:{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"}}).then(function(t){l.$delete(l.shared.settings.document.sections[e].rows,n),l.shared.settings.document.sections[e].rows.splice(n,0,t.data),l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Row Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}).catch(function(t){l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:"Paste Error",toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:3e3})})}else l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Row Uploaded successfully.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}})}!1===n&&(this.focus_block(a.id),this.edit_block(a.id,null,!0))}),Object(i["a"])(o,"copy_element",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=JSON.parse(g()(t)),l=this;if(null!==e&&null!==s&&null!==o&&null!==i)a.id=this.uid(),setxdLocalStorageKeyValue("seedprod_block_data",g()(a)),l.$swal({imageUrl:l.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Block Copied.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3});else if(null!==e&&null!==s&&null!==o){var r=this;a.id=this.uid(),a.blocks.forEach(function(t){t.id=r.uid()}),setxdLocalStorageKeyValue("seedprod_column_data",g()(a)),r.$swal({imageUrl:r.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Column Copied.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}else if(null!==e&&null!==s){var c=this;a.id=this.uid(),a.cols.forEach(function(t){t.id=c.uid(),t.blocks.forEach(function(t){t.id=c.uid()})}),setxdLocalStorageKeyValue("seedprod_row_data",g()(a)),c.$swal({imageUrl:c.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Row Copied.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}else if(null!==e){var d=this;a.id=this.uid(),a.rows.forEach(function(t){t.id=d.uid(),t.cols.forEach(function(t){t.id=d.uid(),t.blocks.forEach(function(t){t.id=d.uid()})})}),setxdLocalStorageValue(g()(a)),d.$swal({imageUrl:d.shared.plugin_path+"public/svg/success-24px-white.svg",text:Object(j["a"])("Section Copied.","coming-soon"),toast:!0,type:null,customClass:"sp-toast-success",position:"top-end",showConfirmButton:!1,timer:5e3})}!1===n&&(this.focus_block(a.id),this.edit_block(a.id,null,!0))}),Object(i["a"])(o,"add_section",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s={id:this.uid(),type:"section",rows:[],settings:JSON.parse(g()(this.shared.block_templates.section))};this.shared.settings.document.sections.splice(e+1,0,s),this.focus_block(s.id,"bottom"),this.$router.push({name:"setup_block_options"})}),Object(i["a"])(o,"focus_layout",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",s=!0;"bottom"===e&&(s=!1),this.$nextTick(function(){this.shared.layout_container;if(void 0!==t&&null!==t)try{document.querySelector("#sp-nav-"+t).scrollIntoView(s)}catch(e){}})}),Object(i["a"])(o,"focus_block",function(){var t=Object(p["a"])(regeneratorRuntime.mark(function t(e){var s,o,i=arguments;return regeneratorRuntime.wrap(function(t){while(1)switch(t.prev=t.next){case 0:s=i.length>1&&void 0!==i[1]?i[1]:"top",o=!0,"bottom"===s&&(o=!1),this.$nextTick(function(){var t=this.shared.code_container;if(void 0!==e&&null!==e)try{t.querySelector("#sp-"+e).scrollIntoView(o)}catch(s){}});case 4:case"end":return t.stop()}},t,this)}));function e(e){return t.apply(this,arguments)}return e}()),Object(i["a"])(o,"add_row",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o={id:this.uid(),type:"row",colType:"1-col",cols:[],settings:JSON.parse(g()(this.shared.block_templates.row))};this.shared.settings.document.sections[e].rows.splice(s+1,0,o),this.focus_block(o.id,"bottom"),this.$router.push({name:"setup_block_options"})}),Object(i["a"])(o,"add_col",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i={id:this.uid(),type:"col",blocks:[],settings:JSON.parse(g()(this.shared.block_templates.col))};this.shared.settings.document.sections[e].rows[s].cols.splice(o+1,0,i),this.focus_block(i.id,"bottom"),this.$router.push({name:"setup_block_options"})}),Object(i["a"])(o,"generate_cols",function(t){var e,s;"1-col"==t&&(e=1),"2-col"!=t&&"left-sidebar"!=t&&"right-sidebar"!=t||(e=2),"3-col"==t&&(e=3),"4-col"==t&&(e=4),"5-col"==t&&(e=5),"6-col"==t&&(e=6);var o=[];for(s=0;s<e;s++){var i={id:this.uid(),type:"col",blocks:[],settings:JSON.parse(g()(this.shared.block_templates.col))};("left-sidebar"==t&&0==s||"right-sidebar"==t&&1==s)&&(i.settings.colWidth=35),("left-sidebar"==t&&1==s||"right-sidebar"==t&&0==s)&&(i.settings.colWidth=65),o.push(i)}return o}),Object(i["a"])(o,"add_cols",function(t,e){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=this.generate_cols(t);this.shared.settings.document.sections[s].rows[o].cols=i}),Object(i["a"])(o,"goto",function(t){this.$route.name!==t&&this.$router.push({name:t})}),Object(i["a"])(o,"add_block",function(){this.$router.push({name:"setup_block_options"})}),Object(i["a"])(o,"duplicate_element",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,n=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=JSON.parse(g()(t));if(null!==e&&null!==s&&null!==o&&null!==i)a.id=this.uid(),this.shared.settings.document.sections[e].rows[s].cols[o].blocks.splice(i+1,0,a);else if(null!==e&&null!==s&&null!==o){var l=this;a.id=this.uid(),a.blocks.forEach(function(t){t.id=l.uid()}),this.shared.settings.document.sections[e].rows[s].cols.splice(o+1,0,a)}else if(null!==e&&null!==s){var r=this;a.id=this.uid(),a.cols.forEach(function(t){t.id=r.uid(),t.blocks.forEach(function(t){t.id=r.uid()})}),this.shared.settings.document.sections[e].rows.splice(s+1,0,a)}else if(null!==e){var c=this;a.id=this.uid(),a.rows.forEach(function(t){t.id=c.uid(),t.cols.forEach(function(t){t.id=c.uid(),t.blocks.forEach(function(t){t.id=c.uid()})})}),this.shared.settings.document.sections.splice(e+1,0,a)}!1===n&&(this.focus_block(a.id),this.edit_block(a.id,null,!0))}),Object(i["a"])(o,"delete_blank_element",function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&void 0!==arguments[3]&&arguments[3];null!==t&&null!==e?this.$delete(this.shared.settings.document.sections[t].rows,e):null!==t&&this.$delete(this.shared.settings.document.sections,t)}),Object(i["a"])(o,"delete_element",function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this.$swal({title:Object(j["a"])("Are you sure you want to delete?","coming-soon"),type:null,showCancelButton:!0,confirmButtonColor:"#d33",confirmButtonText:Object(j["a"])("Yes, delete it!","coming-soon"),cancelButtonText:Object(j["a"])("Cancel","coming-soon")}).then(function(n){n.value&&(null!==e&&null!==s&&null!==o&&null!==i?t.$delete(t.shared.settings.document.sections[e].rows[s].cols[o].blocks,i):null!==e&&null!==s&&null!==o?1==t.shared.settings.document.sections[e].rows[s].cols.length?t.shared.settings.document.sections[e].rows[0].cols=[]:t.$delete(t.shared.settings.document.sections[e].rows[s].cols,o):null!==e&&null!==s?1==t.shared.settings.document.sections[e].rows.length?t.shared.settings.document.sections[e].rows[0].cols=[]:t.$delete(t.shared.settings.document.sections[e].rows,s):null!==e&&t.$delete(t.shared.settings.document.sections,e),t.$router.push({name:"setup_block_options"}),t.$swal({imageUrl:t.shared.plugin_path+"public/svg/success-24px-white.svg",text:"Deleted",toast:!0,type:null,customClass:"sp-toast-error",position:"top-end",showConfirmButton:!1,timer:3e3}))})}),Object(i["a"])(o,"edit_block",function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return 0==o&&this.focus_layout(t),(this.shared.highlight_option_target==t||!1!==s)&&(0!==e&&void(this.$route.params.blockid!=t&&this.$router.push({name:"setup_block_options",params:{id:this.shared.lpage.id,blockid:t}})))}),Object(i["a"])(o,"uid",function(){var t=String.fromCharCode(97+Math.floor(26*Math.random()))+Math.random().toString(36).substring(2,7).toLowerCase();return t}),Object(i["a"])(o,"help_iframe",function(t){var e=t.split("#"),s="";e[1]&&(t=e[0],s=e[1]),this.$swal({width:600,html:"<iframe class='iframe_loading' id='inline-help' src='https://staging.seedprod.com/docs/"+t+"?iframe=1&hash="+s+"' style='width:100%;' onload='help_iframe()'></iframe>",toast:!1,showCancelButton:!0,cancelButtonText:"Close",confirmButtonText:Object(j["a"])('Visit Docs&nbsp;<i class="fas fa-external-link-alt"></i>',"coming-soon")}).then(function(e){if(e.value){var o="https://staging.seedprod.com/docs/"+t+"#"+s;window.open(o,"_blank")}})}),Object(i["a"])(o,"show_upgrade_notice",function(t,e){var s=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"link",i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=this;i?(t=Object(j["a"])("Upgrade to PRO","coming-soon"),e=Object(j["a"])("Increase traffic, engagement, and get more email subscribers. Click below to learn more about all our awesome features.","coming-soon")):(t+=Object(j["a"])(" is a PRO Feature","coming-soon"),e=Object(j["a"])("We're sorry, the ","coming-soon")+e+Object(j["a"])(" feature is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features.","coming-soon")),this.$swal({customContainerClass:"seedprod-upgrade-popup",imageUrl:n.shared.plugin_path+"public/img/lock.svg",title:t,text:e,type:null,showCancelButton:!1,confirmButtonColor:"#4CAF50",cancelButtonColor:"#d33",confirmButtonText:Object(j["a"])("UPGRADE TO PRO","coming-soon"),showCloseButton:!0,footer:'<i class="fas fa-check-circle"></i><div style="text-align:center;margin-left:40px;margin-right:40px;">'+Object(j["a"])("<strong>Bonus:</strong>&nbsp;SeedProd Lite users get a discount off the regular price, automatically applied at checkout.","coming-soon")+"</div>"}).then(function(t){t.value&&(window.open(s.shared.upgrade_link+o,"_blank"),s.$swal.fire({customContainerClass:"seedprod-moreinfo-popup",imageUrl:n.shared.plugin_path+"public/img/info-with-circle.svg",type:null,html:Object(j["a"])("Thanks for your interest in SeedProd Pro!<br>If you have any questions or issues just <a href='https://www.seedprod.com/?contact=1' target='_blank'>let us know</a>.<br><br>After purchasing SeedProd Pro, you'll need to download and install the Pro version of the plugin, and then remove the free plugin. <br><br>(Don't worry, all your settings will be preserved.)","coming-soon")}))})}),Object(i["a"])(o,"show_uplock_notice",function(t,e){arguments.length>2&&void 0!==arguments[2]&&arguments[2];var s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=this;s?(t=Object(j["a"])("Upgrade to PRO","coming-soon"),e=Object(j["a"])("Increase traffic, engagement, and get more email subscribers. Click below to learn more about all our awesome features.","coming-soon")):(t=Object(j["a"])("Upgrade to Unlock ","coming-soon")+t,e=Object(j["a"])("We're sorry, the ","coming-soon")+e+Object(j["a"])(" feature is not available on your plan. Please upgrade your plan to unlock this feature and more!","coming-soon")),this.$swal({customContainerClass:"seedprod-upgrade-popup",imageUrl:o.shared.plugin_path+"public/img/lock.svg",title:t,text:e,type:null,showCancelButton:!1,confirmButtonColor:"#4CAF50",cancelButtonColor:"#d33",confirmButtonText:"UPGRADE",showCloseButton:!0,footer:Object(j["a"])("Upgrade with just a click of a button!","coming-soon")}).then(function(t){t.value&&window.open("https://app.seedprod.com/upgrade-license?license_key="+o.shared.license_key+"&api_token="+o.shared.api_token,"_blank")})}),Object(i["a"])(o,"debounce_update_mobile_css",y.a.debounce(function(t){jQuery("#tmp-custom-mobile-css-style").remove(),jQuery("head").append("<style id='tmp-custom-mobile-css-style' type='text/css'></style>"),jQuery("#tmp-custom-mobile-css-style").html(this.generate_mobile_css())},100)),Object(i["a"])(o,"update_mobile_css",function(){jQuery("#tmp-custom-mobile-css-style").remove(),jQuery("head").append("<style id='tmp-custom-mobile-css-style' type='text/css'></style>"),jQuery("#tmp-custom-mobile-css-style").html(this.generate_mobile_css())}),Object(i["a"])(o,"generate_mobile_css",function(){var t=document.documentElement.innerHTML,e=/data-mobile-css="([^"]*)"/g,s=[];lodash.isEmpty(t.match(e))||(s=Object(u["a"])(t.match(e)));var o="";return lodash.isEmpty(s)||s.forEach(function(t){var e=t.match(/"([^"]+)"/);if(d()(e)){for(var s=e[1].split("|"),i=s[0],n=s[1],a=i.split(","),l=a.length,r="",c=0;c<l;c++)lodash.isEmpty(a[c])||(r+=0===c?".sp-mobile-view ".concat(a[c]):", .sp-mobile-view ".concat(a[c]));for(var u=n.split(";"),p=u.length,h="".concat(r," {"),g=0;g<p;g++)lodash.isEmpty(u[g])||(h+="".concat(u[g].replace(";","")," !important;"));h+="}",o+=h}}),this.shared.settings.document.settings.mobileCss=o,o}),Object(i["a"])(o,"generate_mobile_css_old",function(){var t="",e=this.traverse(seedprod_store.settings.document,this.process);return e.forEach(function(e){var s=e.split(",");if(!1===lodash.isEmpty(s[2])){o=="sp-"+s[0]&&!1;var o="sp-"+s[0],i=lodash.kebabCase(s[1].replace("_mobile","")),n=s[2];t=t+".sp-mobile-view #"+o+".sp-css-target{",t="line-height"==i?t+i+":"+n+" !important;":t+i+":"+n+"px !important;",t+=" }",t=t+".sp-mobile-view #"+o+" .sp-css-target{",t="line-height"==i?t+i+":"+n+" !important;":t+i+":"+n+"px !important;",t+=" }"}}),this.shared.settings.document.settings.mobileCss=t,t}),Object(i["a"])(o,"debounce_update_tablet_css",y.a.debounce(function(t){jQuery("#tmp-custom-tablet-css-style").remove(),jQuery("head").append("<style id='tmp-custom-tablet-css-style' type='text/css'></style>"),jQuery("#tmp-custom-tablet-css-style").html(this.generate_tablet_css())},100)),Object(i["a"])(o,"update_tablet_css",function(){jQuery("#tmp-custom-tablet-css-style").remove(),jQuery("head").append("<style id='tmp-custom-tablet-css-style' type='text/css'></style>"),jQuery("#tmp-custom-tablet-css-style").html(this.generate_tablet_css())}),Object(i["a"])(o,"generate_tablet_css",function(){var t=document.documentElement.innerHTML,e=/data-tablet-css="([^"]*)"/g,s=[];lodash.isEmpty(t.match(e))||(s=Object(u["a"])(t.match(e)));var o="";return lodash.isEmpty(s)||s.forEach(function(t){var e=t.match(/"([^"]+)"/);if(d()(e)){for(var s=e[1].split("|"),i=s[0],n=s[1],a=i.split(","),l=a.length,r="",c=0;c<l;c++)lodash.isEmpty(a[c])||(r+=0===c?".sp-tablet-view ".concat(a[c]):", .sp-tablet-view ".concat(a[c]));for(var u=n.split(";"),p=u.length,h="".concat(r," {"),g=0;g<p;g++)lodash.isEmpty(u[g])||(h+="".concat(u[g].replace(";","")," !important;"));h+="}",o+=h}}),this.shared.settings.document.settings.tabletCss=o,o}),Object(i["a"])(o,"process",function(t,e,s,o){-1!==t.indexOf("_mobile")&&!1===!!~o.indexOf(e)&&o.push(s+","+t+","+e)}),Object(i["a"])(o,"traverse",function(t,e){var s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[];for(var i in t)0===i.indexOf("id")&&(s=t[i]),e.apply(this,[i,t[i],s,o]),null!==t[i]&&"object"==Object(r["a"])(t[i])&&this.traverse(t[i],e,s,o);return o}),Object(i["a"])(o,"debounce_update_mobile_visibility_css",y.a.debounce(function(t){jQuery("#tmp-custom-mobile-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-mobile-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-mobile-visibility-css-style").html(this.generate_mobile_visibility_css())},100)),Object(i["a"])(o,"update_mobile_visibility_css",function(){jQuery("#tmp-custom-mobile-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-mobile-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-mobile-visibility-css-style").html(this.generate_mobile_visibility_css())}),Object(i["a"])(o,"generate_mobile_visibility_css",function(){var t=document.documentElement.innerHTML,e=/data-mobile-visibility="([^"]*)"/g,s=[];lodash.isEmpty(t.match(e))||(s=Object(u["a"])(t.match(e)));var o="",i="";return lodash.isEmpty(s)||s.forEach(function(t){var e=t.match(/"([^"]+)"/);if(d()(e)){var s=e[1].split("|"),n=s[0],a=s[1];"true"===a&&(o+=".sp-mobile-view ".concat(n," {"),o+="opacity: .2;",o+="border: 1px solid rgba(0,0,0,.02);",o+="}",i+="".concat(n," {"),i+="display: none !important;",i+="}")}}),this.shared.settings.document.settings.mobileVisibilityCss=i,o}),Object(i["a"])(o,"debounce_update_tablet_visibility_css",y.a.debounce(function(t){jQuery("#tmp-custom-tablet-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-tablet-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-tablet-visibility-css-style").html(this.generate_tablet_visibility_css())},100)),Object(i["a"])(o,"update_tablet_visibility_css",function(){jQuery("#tmp-custom-tablet-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-tablet-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-tablet-visibility-css-style").html(this.generate_tablet_visibility_css())}),Object(i["a"])(o,"generate_tablet_visibility_css",function(){var t=document.documentElement.innerHTML,e=/data-tablet-visibility="([^"]*)"/g,s=[];lodash.isEmpty(t.match(e))||(s=Object(u["a"])(t.match(e)));var o="",i="";return lodash.isEmpty(s)||s.forEach(function(t){var e=t.match(/"([^"]+)"/);if(d()(e)){var s=e[1].split("|"),n=s[0],a=s[1];"true"===a&&(o+=".sp-tablet-view ".concat(n," {"),o+="opacity: .2;",o+="border: 1px solid rgba(0,0,0,.02);",o+="}",i+="".concat(n," {"),i+="display: none !important;",i+="}")}}),this.shared.settings.document.settings.tabletVisibilityCss=i,o}),Object(i["a"])(o,"debounce_update_desktop_visibility_css",y.a.debounce(function(t){jQuery("#tmp-custom-desktop-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-desktop-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-desktop-visibility-css-style").html(this.generate_desktop_visibility_css())},100)),Object(i["a"])(o,"update_desktop_visibility_css",function(){jQuery("#tmp-custom-desktop-visibility-css-style").remove(),jQuery("head").append("<style id='tmp-custom-desktop-visibility-css-style' type='text/css'></style>"),jQuery("#tmp-custom-desktop-visibility-css-style").html(this.generate_desktop_visibility_css())}),Object(i["a"])(o,"generate_desktop_visibility_css",function(){var t=document.documentElement.innerHTML,e=/data-desktop-visibility="([^"]*)"/g,s=[];lodash.isEmpty(t.match(e))||(s=Object(u["a"])(t.match(e)));var o="",i="";return lodash.isEmpty(s)||s.forEach(function(t){var e=t.match(/"([^"]+)"/);if(d()(e)){var s=e[1].split("|"),n=s[0],a=s[1];"true"===a&&(o+=".sp-desktop-view ".concat(n," {"),o+="opacity: .2;",o+="border: 1px solid rgba(0,0,0,.02);",o+="}",i+="".concat(n," {"),i+="display: none !important;",i+="}")}}),this.shared.settings.document.settings.desktopVisibilityCss=i,o}),Object(i["a"])(o,"debounce_update_placeholder_css",y.a.debounce(function(t){jQuery("#tmp-custom-placeholder-css-style").remove(),jQuery("head").append("<style id='tmp-custom-placeholder-css-style' type='text/css'></style>"),jQuery("#tmp-custom-placeholder-css-style").html(this.generate_placeholder_css())},100)),Object(i["a"])(o,"update_placeholder_css",function(){jQuery("#tmp-custom-placeholder-css-style").remove(),jQuery("head").append("<style id='tmp-custom-placeholder-css-style' type='text/css'></style>"),jQuery("#tmp-custom-placeholder-css-style").html(this.generate_placeholder_css())}),Object(i["a"])(o,"generate_placeholder_css",function(){var t="",e=this.traverse(seedprod_store.settings.document,this.process_placeholder);return e.forEach(function(e){var s=e.split(",");if(!1===lodash.isEmpty(s[2])){o=="sp-"+s[0]&&!1;var o="sp-"+s[0],i=s[2],n=_()(i).setAlpha(.7);t=t+"input::placeholder, #"+o+" input::placeholder {",t=t+"color:"+n,t+=" }"}}),this.shared.settings.document.settings.placeholderCss=t,t}),Object(i["a"])(o,"process_placeholder",function(t,e,s,o){-1!==t.indexOf("fieldTextColor")&&!1===!!~o.indexOf(e)&&o.push(s+","+t+","+e)}),Object(i["a"])(o,"generate_css_from_object",function(t){var e=JSON.parse(g()(t));return l()(e).forEach(function(t){return!e[t]&&void 0!==e[t]&&delete e[t]}),g()(e).replace(/['"]+/g,"").replace(/[,]+/g,";")}),Object(i["a"])(o,"add_custom_attributes",function(t){var e=this,s={};if(t){var o=t.split("\n");o.forEach(function(t){var o=t.split("|"),i=Object(n["a"])(o,2),a=i[0],l=i[1];if(a&&l){var r=e.sanitizeAttributeKey(a.trim()),c=e.sanitizeAttribute(l.trim());r&&c&&(s[r]=c)}})}return s}),Object(i["a"])(o,"sanitizeAttributeKey",function(t){return t=t.replace(/<[^>]*>/g,""),"href"===t.toLowerCase()||t.toLowerCase().startsWith("on")?"":this.encodeSpecialChars(t)}),Object(i["a"])(o,"sanitizeAttribute",function(t){return t=t.replace(/<[^>]*>/g,""),t=t.replace(/(?:javascript|data):/gi,""),this.encodeSpecialChars(t)}),Object(i["a"])(o,"encodeSpecialChars",function(t){return t.replace(/[&<>"']/g,function(t){switch(t){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";default:return"&#039;"}})}),o)}},f408:function(t,e,s){"use strict";var o=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"sp-text-center sp-w-full"},[s("div",[s("h1",{staticClass:"sp-mb-4 sp-leading-tight sp-text-2xl sp-font-bold sp-text-neutral",style:{color:"saved-templates"==t.feature_source?"#230820 !important":""}},[t._v("\n        "+t._s(this.feature)+" "+t._s(t.txt_21)+"\n      ")]),s("p",{staticClass:"sp-mb-8 sp-text-base"},[t._v("\n        "+t._s(t.feature)+" "+t._s(t.txt_22)+"\n      ")]),t.feature_video&&void 0!=t.feature_video&&""!=t.feature_video?s("div",{staticClass:"sp-video-responsive sp-mb-8"},[s("iframe",{attrs:{width:"100%",height:"315",src:"https://www.youtube.com/embed/"+t.feature_video,title:"YouTube video player",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""}})]):t._e(),s("a",{staticClass:"sp-bg-green sp-px-8 sp-py-4 sp-rounded sp-leading-none sp-text-white sp-no-underline sp-text-base hover:sp-bg-green-lighter sp-cursor-pointer sp-font-semibold sp-inline-flex sp-items-center sp-justify-center hover:sp-text-white",staticStyle:{color:"#fff !important"},attrs:{href:"https://seedprod.com/lite-upgrade/?utm_source=WordPress&utm_campaign=liteplugin&utm_medium=pluginbuilder-"+t.feature_source,target:"_blank"}},[s("svg",{staticClass:"sp-fill-current sp-mr-2 sp-w-5 sp-h-5",attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"}}),s("path",{attrs:{d:"M0 0h24v24H0z",fill:"none"}})]),t._v(" "+t._s(t.txt_20)+"\n      ")]),s("div",{staticClass:"sp-mt-3 sp-text-primary sp-text-lg sp-font-bold"},[t._v("\n        "+t._s(t.txt_23)+"\n      ")])])])])},i=[],n=s("561c"),a=s("4328"),l=s.n(a),r={name:"LiteCTABuilder",data:function(){return{txt_1:Object(n["a"])("Dismiss this message","coming-soon"),txt_2:Object(n["a"])("Get SeedProd Pro and Unlock all the Powerful Features","coming-soon"),txt_3:Object(n["a"])("Thanks for being a loyal SeedProd Lite user. Upgrade to\nSeedProd Pro to unlock all the awesome features and\nexperience why SeedProd is the best WordPress landing\npage plugin.","coming-soon"),txt_4:Object(n["a"])("Pro Features:","coming-soon"),txt_995:Object(n["a"])("Drag & Drop Page Builder","coming-soon"),txt_996:Object(n["a"])("80+ PRO Page Blocks","coming-soon"),txt_997:Object(n["a"])("PRO Email Marketing Integrations","coming-soon"),txt_998:Object(n["a"])("Custom 404 Pages","coming-soon"),txt_999:Object(n["a"])("Page Access Controls","coming-soon"),txt_9910:Object(n["a"])("200+ PRO Page Templates","coming-soon"),txt_9911:Object(n["a"])("PRO Smart Sections","coming-soon"),txt_9912:Object(n["a"])("Email Subscriber Management","coming-soon"),txt_9913:Object(n["a"])("Saved Templates","coming-soon"),txt_9914:Object(n["a"])("Plus much more...","coming-soon"),txt_15:Object(n["a"])("Bonus:","coming-soon"),txt_16:Object(n["a"])("SeedProd Lite users get","coming-soon"),txt_17:Object(n["a"])("a discount off the regular price","coming-soon"),txt_18:Object(n["a"])("automatically applied at checkout.","coming-soon"),txt_19:Object(n["a"])("Get SeedProd Pro Today and Unlock all the Powerful Features »","coming-soon"),txt_20:Object(n["a"])("Upgrade to SeedProd PRO Now","coming-soon"),txt_21:Object(n["a"])("is a PRO Feature","coming-soon"),txt_22:Object(n["a"])("is not available on your plan. Please upgrade to the PRO version to unlock all these awesome features.","coming-soon"),txt_23:Object(n["a"])("Special Upgrade Offer - Save 50% Off","coming-soon")}},props:{feature:{type:String,default:""},feature_source:{type:String,default:""},feature_video:{type:String,default:""}},methods:{dismiss:function(){var t=this,e=l.a.stringify({dismiss:!0});this.shared.settings_page_meta.dismiss_settings_lite_cta=!0,t.axios.post(seedprod_dismiss_settings_lite_cta_url,e,{"Content-Type":"application/x-www-form-urlencoded;charset=UTF-8"})}}},c=r,d=s("2877"),u=Object(d["a"])(c,o,i,!1,null,null,null);e["a"]=u.exports}}]);