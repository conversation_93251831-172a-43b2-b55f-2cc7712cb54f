<?php
$seedprod_global_css = '{
	"page_type": "css",
	"document": {
		"settings": {
			"customs_colors": [],
			"customCss": "",
			"bodyfont": "",
			"bodyfontVariant": "",
			"bodyfontSize": "",
			"bodyfontSize_mobile": "",
			"bodyfontSize_tablet": "",
			"bodylineHeight": "",
			"bodylineHeight_mobile": "",
			"bodylineHeight_tablet": "",
			"bodyletterSpacing": "",
			"bodyletterSpacing_mobile": "",
			"bodyletterSpacing_tablet": "",
			"bodytypographyBold": "",
			"bodytypographyItalic": "",
			"bodytypographyUnderline": "",
			"bodytypographyLetterCase": "",
			"h1font": "",
			"h1fontVariant": "",
			"h1fontSize": "",
			"h1fontSize_mobile": "",
			"h1fontSize_tablet": "",
			"h1lineHeight": "",
			"h1lineHeight_mobile": "",
			"h1lineHeight_tablet": "",
			"h1letterSpacing": "",
			"h1letterSpacing_mobile": "",
			"h1letterSpacing_tablet": "",
			"h1typographyBold": "",
			"h1typographyItalic": "",
			"h1typographyUnderline": "",
			"h1typographyLetterCase": "",
			"h2font": "",
			"h2fontVariant": "",
			"h2fontSize": "",
			"h2fontSize_mobile": "",
			"h2fontSize_tablet": "",
			"h2lineHeight": "",
			"h2lineHeight_mobile": "",
			"h2lineHeight_tablet": "",
			"h2letterSpacing": "",
			"h2letterSpacing_mobile": "",
			"h2letterSpacing_tablet": "",
			"h2typographyBold": "",
			"h2typographyItalic": "",
			"h2typographyUnderline": "",
			"h2typographyLetterCase": "",
			"h3font": "",
			"h3fontVariant": "",
			"h3fontSize": "",
			"h3fontSize_mobile": "",
			"h3fontSize_tablet": "",
			"h3lineHeight": "",
			"h3lineHeight_mobile": "",
			"h3lineHeight_tablet": "",
			"h3letterSpacing": "",
			"h3letterSpacing_mobile": "",
			"h3letterSpacing_tablet": "",
			"h3typographyBold": "",
			"h3typographyItalic": "",
			"h3typographyUnderline": "",
			"h3typographyLetterCase": "",
			"h4font": "",
			"h4fontVariant": "",
			"h4fontSize": "",
			"h4fontSize_mobile": "",
			"h4fontSize_tablet": "",
			"h4lineHeight": "",
			"h4lineHeight_mobile": "",
			"h4lineHeight_tablet": "",
			"h4letterSpacing": "",
			"h4letterSpacing_mobile": "",
			"h4letterSpacing_tablet": "",
			"h4typographyBold": "",
			"h4typographyItalic": "",
			"h4typographyUnderline": "",
			"h4typographyLetterCase": "",
			"h5font": "",
			"h5fontVariant": "",
			"h5fontSize": "",
			"h5fontSize_mobile": "",
			"h5fontSize_tablet": "",
			"h5lineHeight": "",
			"h5lineHeight_mobile": "",
			"h5lineHeight_tablet": "",
			"h5letterSpacing": "",
			"h5letterSpacing_mobile": "",
			"h5letterSpacing_tablet": "",
			"h5typographyBold": "",
			"h5typographyItalic": "",
			"h5typographyUnderline": "",
			"h5typographyLetterCase": "",
			"h6font": "",
			"h6fontVariant": "",
			"h6fontSize": "",
			"h6fontSize_mobile": "",
			"h6fontSize_tablet": "",
			"h6lineHeight": "",
			"h6lineHeight_mobile": "",
			"h6lineHeight_tablet": "",
			"h6letterSpacing": "",
			"h6letterSpacing_mobile": "",
			"h6letterSpacing_tablet": "",
			"h6typographyBold": "",
			"h6typographyItalic": "",
			"h6typographyUnderline": "",
			"h6typographyLetterCase": "",
			"linkfont": "",
			"linkfontVariant": "",
			"linkfontSize": "",
			"linkfontSize_mobile": "",
			"linkfontSize_tablet": "",
			"linklineHeight": "",
			"linklineHeight_mobile": "",
			"linklineHeight_tablet": "",
			"linkletterSpacing": "",
			"linkletterSpacing_mobile": "",
			"linkletterSpacing_tablet": "",
			"linktypographyBold": "",
			"linktypographyItalic": "",
			"linktypographyUnderline": "",
			"linktypographyLetterCase": "",
			"linkhoverfont": "",
			"linkhoverfontVariant": "",
			"linkhoverfontSize": "",
			"linkhoverfontSize_mobile": "",
			"linkhoverfontSize_tablet": "",
			"linkhoverlineHeight": "",
			"linkhoverlineHeight_mobile": "",
			"linkhoverlineHeight_tablet": "",
			"linkhoverletterSpacing": "",
			"linkhoverletterSpacing_mobile": "",
			"linkhoverletterSpacing_tablet": "",
			"linkhovertypographyBold": "",
			"linkhovertypographyItalic": "",
			"linkhovertypographyUnderline": "",
			"linkhovertypographyLetterCase": "",
			"labelfont": "",
			"labelfontVariant": "",
			"labelfontSize": "",
			"labelfontSize_mobile": "",
			"labelfontSize_tablet": "",
			"labellineHeight": "",
			"labellineHeight_mobile": "",
			"labellineHeight_tablet": "",
			"labelletterSpacing": "",
			"labelletterSpacing_mobile": "",
			"labelletterSpacing_tablet": "",
			"labeltypographyBold": "",
			"labeltypographyItalic": "",
			"labeltypographyUnderline": "",
			"labeltypographyLetterCase": "",
			"fieldfont": "",
			"fieldfontVariant": "",
			"fieldfontSize": "",
			"fieldfontSize_mobile": "",
			"fieldfontSize_tablet": "",
			"fieldlineHeight": "",
			"fieldlineHeight_mobile": "",
			"fieldlineHeight_tablet": "",
			"fieldletterSpacing": "",
			"fieldletterSpacing_mobile": "",
			"fieldletterSpacing_tablet": "",
			"fieldtypographyBold": "",
			"fieldtypographyItalic": "",
			"fieldtypographyUnderline": "",
			"fieldtypographyLetterCase": "",
			"fieldborderRadiusTL": "4",
			"fieldborderRadiusTR": "",
			"fieldborderRadiusBL": "",
			"fieldborderRadiusBR": "",
			"fieldborderRadiusSync": true,
			"fieldborderTop": "0",
			"fieldborderBottom": "0",
			"fieldborderLeft": "0",
			"fieldborderRight": "0",
			"fieldborderSync": true,
			"fieldborderStyle": "solid",
			"buttonborderRadiusTL": "4",
			"buttonborderRadiusTR": "",
			"buttonborderRadiusBL": "",
			"buttonborderRadiusBR": "",
			"buttonborderRadiusSync": true,
			"buttonborderTop": "0",
			"buttonborderBottom": "0",
			"buttonborderLeft": "0",
			"buttonborderRight": "0",
			"buttonborderSync": true,
			"buttonborderStyle": "solid",
			"buttonColor": "#0074E4",
			"buttonTextColor": "#FFFFFF",
			"buttonHoverColor": "",
			"buttonTextHoverColor": "",
			"buttonNormalBGtype": "",
			"buttonNormalBGangle": "",
			"buttonNormalBGposition": "",
			"buttonNormalBGcolor1": "",
			"buttonNormalBGcolor1location": "",
			"buttonNormalBGcolor2": "",
			"buttonNormalBGcolor2location": "",
			"buttonHoverBGtype": "",
			"buttonHoverBGangle": "",
			"buttonHoverBGposition": "",
			"buttonHoverBGcolor1": "",
			"buttonHoverBGcolor1location": "",
			"buttonHoverBGcolor2": "",
			"buttonHoverBGcolor2location": "",
			"buttonNormalStyle": "s",
			"buttonHoverStyle": "s",
			"layoutMaxWidth": "1100",
			"bgStyle": "s",
			"bgGradient": {
				"type": "linear",
				"position": "center",
				"angle": 0,
				"color1": "",
				"color1location": 0,
				"color2": "",
				"color2location": 100
			},
			"bgColor": "#FFFFFF",
			"bgImage": "",
			"bgDimming": 0,
			"bgOverlayColor": "",
			"bgPosition": "cover",
			"bgCustomDimming":0,
			"bgCustomDimmingOverlay":"",
			"bgCustomXPositionUnit":"px",
			"bgCustomXPosition":"",
			"bgCustomYPositionUnit":"px",
			"bgCustomYPosition":"",
			"bgCustomAttachment":"",
			"bgCustomRepeat":"",
			"bgCustomSize":"",
			"bgCustomSizeWidthUnit":"%",
			"bgCustomSizeWidth":"100",
			"headerColor": "",
			"linkColor": "",
			"linkDarkerColor": "",
			"textColor": "",
			"textFont": "",
			"textFontVariant": "",
			"headerFont": "",
			"headerFontVariant": "",
			"contentPosition": "1",
			"headCss": "",
			"mobileCss": "",
			"tabletCss":"",
			"placeholderCss": "",
			"useVideoBg": false,
			"useVideoBgUrl": "",
			"useSlideshowBg": false,
			"useSlideshowImgs": [""],
			"globalHeadCssBuilder": "",
			"buttonborderColor": "",
			"h1Color": "",
			"h2Color": "",
			"h3Color": "",
			"h4Color": "",
			"h5Color": "",
			"h6Color": "",
			"labelColor": "",
			"fieldColor": "",
			"fieldbgColor": "",
			"fieldborderColor": "#CCCCCC",
			"fieldpadding": 5,
			"fieldborderWidth": 1
		}
	},
	"post_title": "Global CSS",
	"post_name": "global-css",
	"post_status": "publish"
}';
