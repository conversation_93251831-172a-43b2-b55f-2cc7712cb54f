<?php
/* THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY. */
$generated_i18n_strings = array(
	// Reference: src/components/PostsOptions.vue:1978
	// Reference: src/components/TestimonialOptions.vue:651
	__( '1', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1979
	// Reference: src/components/TestimonialOptions.vue:652
	__( '2', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1980
	// Reference: src/components/TestimonialOptions.vue:653
	__( '3', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1981
	// Reference: src/components/TestimonialOptions.vue:654
	__( '4', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1982
	// Reference: src/components/TestimonialOptions.vue:655
	__( '5', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1983
	// Reference: src/components/TestimonialOptions.vue:656
	__( '6', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1984
	// Reference: src/components/TestimonialOptions.vue:657
	__( '7', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1985
	// Reference: src/components/TestimonialOptions.vue:658
	__( '8', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1986
	// Reference: src/components/TestimonialOptions.vue:659
	__( '9', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1987
	// Reference: src/components/TestimonialOptions.vue:660
	__( '10', 'coming-soon' ),

	// Reference: src/App.vue:238
	// Reference: src/views/Setup.vue:1734
	__( 'Changes not saved, are you sure you want to leave?', 'coming-soon' ),

	// Reference: src/admin/Aboutus.vue:36
	// Reference: src/admin/App.vue:364
	__( 'About Us', 'coming-soon' ),

	// Reference: src/admin/Aboutus.vue:37
	__( 'Getting Started', 'coming-soon' ),

	// Reference: src/admin/Aboutus.vue:38
	__( 'Lite vs Pro', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:103
	__( 'Yup, we know a thing or two about building awesome products that customers love.', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:107
	__( 'the most popular lead-generation software', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:111
	__( 'the best WordPress analytics plugin', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:115
	__( 'the best WordPress forms plugin', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:119
	__( 'the best WordPress giveaway plugin', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:123
	__( 'and finally the best WordPress FOMO plugin', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:127
	__( 'SeedProd Team photo', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:128
	__( 'The SeedProd Team', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:87
	__( 'Hello and welcome to SeedProd, the most beginner friendly drag & drop WordPress landing page plugin. At SeedProd, we build software that helps you create beautiful responsive landing pages for your website in minutes.', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:91
	__( 'Over the years, we found that most WordPress landing page plugins were bloated, buggy, slow, and very hard to use. So we started with a simple goal: build a WordPress landing page plugin that’s both easy and powerful.', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:95
	__( 'Our goal is to take the pain out of creating landing pages and make it easy.', 'coming-soon' ),

	// Reference: src/admin/AboutusAbout.vue:99
	__( 'SeedProd is brought to you by the same team that’s behind the largest WordPress resource site,', 'coming-soon' ),

	// Reference: src/admin/App.vue:362
	__( 'Pages', 'coming-soon' ),

	// Reference: src/admin/App.vue:363
	__( 'Growth Tools', 'coming-soon' ),

	// Reference: src/admin/App.vue:365
	// Reference: src/admin/Dashboard-Lite.vue:644
	// Reference: src/admin/Subscribers-Lite.vue:272
	__( 'Subscribers', 'coming-soon' ),

	// Reference: src/admin/App.vue:366
	// Reference: src/components/CountdownOptions.vue:853
	// Reference: src/components/CounterOptions.vue:329
	// Reference: src/components/CustomHTMLOptions.vue:201
	// Reference: src/components/EDDAddToCartOptions.vue:842
	// Reference: src/components/EDDBuyNowButtonOptions.vue:872
	// Reference: src/components/EDDDownloadsGridOptions.vue:818
	// Reference: src/components/FormOptions.vue:178
	// Reference: src/components/NavOptions.vue:573
	// Reference: src/components/PostsOptions.vue:1949
	// Reference: src/components/ProductRelatedOptions.vue:451
	// Reference: src/components/ShortcodeOptions.vue:145
	// Reference: src/components/TemplatetagOptions.vue:151
	// Reference: src/components/WCAddToCartOptions.vue:739
	// Reference: src/components/WCCustomProductsGridOptions.vue:921
	// Reference: src/views/Layoutnav.vue:1495
	__( 'Settings', 'coming-soon' ),

	// Reference: src/admin/App.vue:367
	// Reference: src/views/InlineHelpView.vue:263
	__( 'SeedProd Logo', 'coming-soon' ),

	// Reference: src/admin/App.vue:368
	__( 'Previous message ', 'coming-soon' ),

	// Reference: src/admin/App.vue:369
	__( 'Next message', 'coming-soon' ),

	// Reference: src/admin/App.vue:370
	__( 'Theme Builder', 'coming-soon' ),

	// Reference: src/admin/App.vue:371
	__( 'Import / Export', 'coming-soon' ),

	// Reference: src/admin/App.vue:372
	__( 'Setup', 'coming-soon' ),

	// Reference: src/admin/App.vue:373
	__( 'Theme Template Kit Chooser', 'coming-soon' ),

	// Reference: src/admin/App.vue:374
	__( 'You’re using SeedProd Lite. To unlock more features consider <a href=\'%s\' class=\'%s\' target=\'_blank\'>upgrading to Pro</a>', 'coming-soon' ),

	// Reference: src/admin/App.vue:375
	// Reference: src/views/BuilderView.vue:1049
	__( 'Your license key is invalid', 'coming-soon' ),

	// Reference: src/admin/App.vue:376
	// Reference: src/views/BuilderView.vue:1050
	__( 'Click Here to Enter Your License Key', 'coming-soon' ),

	// Reference: src/admin/App.vue:377
	__( 'We have migrated your page settings from the old version to our new builder. Don\'t worry nothing has changed with your page. Your old version will continue to display until you click "Edit" on the Coming Soon or Maintenance Page and then "Save". After Saving the new version will be displayed.', 'coming-soon' ),

	// Reference: src/admin/App.vue:378
	__( 'Learn more about the all new SeedProd plugin and how to complete the migration process.', 'coming-soon' ),

	// Reference: src/admin/App.vue:379
	__( 'We have migrated your page settings from the old version to our new builder. Don\'t worry nothing has changed with your pages. However, a lot has changed in the plugin.', 'coming-soon' ),

	// Reference: src/admin/App.vue:380
	__( 'Suggest a Feature', 'coming-soon' ),

	// Reference: src/admin/App.vue:381
	__( 'Support & Docs', 'coming-soon' ),

	// Reference: src/admin/App.vue:382
	__( 'Upgrade to Pro', 'coming-soon' ),

	// Reference: src/admin/App.vue:383
	__( 'See quick links', 'coming-soon' ),

	// Reference: src/admin/App.vue:470
	// Reference: src/mixins/helpers.js:1969
	__( 'Upgrade to PRO', 'coming-soon' ),

	// Reference: src/admin/App.vue:472
	// Reference: src/mixins/helpers.js:1970
	__( 'Increase traffic, engagement, and get more email subscribers. Click below to learn more about all our awesome features.', 'coming-soon' ),

	// Reference: src/admin/App.vue:474
	// Reference: src/mixins/helpers.js:1918
	__( ' is a PRO Feature', 'coming-soon' ),

	// Reference: src/admin/App.vue:476
	// Reference: src/mixins/helpers.js:1978
	__( 'We\'re sorry, the ', 'coming-soon' ),

	// Reference: src/admin/App.vue:478
	// Reference: src/mixins/helpers.js:1922
	__( ' feature is not available on your plan. Please upgrade to the PRO plan to unlock all these awesome features.', 'coming-soon' ),

	// Reference: src/admin/App.vue:487
	// Reference: src/mixins/helpers.js:1936
	__( 'UPGRADE TO PRO', 'coming-soon' ),

	// Reference: src/admin/App.vue:493
	// Reference: src/mixins/helpers.js:1953
	__( 'Thanks for your interest in SeedProd Lite!<br>If you have any questions or issues just <a href=\'https://www.seedprod.com/?contact=1\' target=\'_blank\'>let us know</a>.<br><br>After purchasing SeedProd Lite, you\'ll need to download and install the Pro version of the plugin, and then remove the free plugin. <br><br>(Don\'t worry, all your settings will be preserved.)', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:133
	__( 'Unlock Your Website’s Potential with WPCode', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:134
	__( 'Effortlessly Integrate Custom Code and Transform Your Site’s Functionality', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:135
	__( 'Header &amp; <br>Footer Scripts', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:136
	__( 'Conversion <br>Pixels', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:137
	__( 'Custom Code <br>Snippets', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:138
	__( 'Conditional <br>Logic', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:139
	__( 'Error <br>Handling', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:140
	__( 'Snippets <br>Library', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:141
	__( 'Finally, a WordPress Snippet Plugin that’s both Easy and Powerful', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:142
	__( 'WPCode makes it easy and safe to add custom WordPress features through code snippets,<br>so you can reduce the number of plugins on your site.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:143
	__( 'Expert-Vetted Code Snippets Library', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:144
	__( 'Access a library of 100+ pre-tested snippets that you can use to enhance your site instantly without coding from scratch.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:145
	__( 'Page-Specific Scripts', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:146
	__( 'Adding scripts to specific pages has never been easier. Use WPCode to extend your site-wide scripts in specific pages without having to code.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:147
	__( 'Effortless Conversion Pixels Setup', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:148
	__( 'Integrating conversion pixels in your websites is straightforward and using the WPCode Conversion Pixels addon you also get automated eCommerce conversion tracking.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:149
	__( 'Advanced Error Handling', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:150
	__( 'Stop worrying about your PHP snippets throwing errors. WPCode automatically catches most errors for full control and you can also get notified via email when that happens.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:151
	__( 'Powerful Conditional Logic', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:152
	__( 'Improve performance and your site’s UX with a variety of conditional logic rules available in WPCode. Combine rules and even use other snippets as conditions.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:161
	__( 'WPCode makes it easy and safe to add custom WordPress features through code snippets, so you can reduce the number of plugins on your site.', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:255
	// Reference: src/admin/GrowthTools.vue:409
	// Reference: src/admin/Popups.vue:239
	__( 'Opening plugin installation page...', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:317
	// Reference: src/admin/Popups.vue:301
	__( 'Plugin Installed, Activating...', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:417
	__( 'Get WPCode Now', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:420
	__( 'WPCode is Active - Go to Settings', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:424
	__( 'Activate WPCode', 'coming-soon' ),

	// Reference: src/admin/CustomCode.vue:426
	__( 'Activating WPCode', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:1009
	// Reference: src/admin/Subscribers-Lite.vue:403
	__( 'Are you sure?', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:1015
	__( 'Yes, empty trash!', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:1039
	__( 'Pages Deleted!', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:1069
	__( 'Page Duplicated', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:615
	__( 'Coming Soon Mode', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:616
	__( 'Search Landing Pages', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:617
	__( 'The Coming Soon Page will be available to search engines if your site is not private.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:621
	__( 'Set up a Coming Soon Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:622
	// Reference: src/admin/Setup-Lite.vue:874
	__( 'Edit Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:623
	// Reference: src/views/Setup.vue:1066
	__( 'Preview', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:624
	// Reference: src/components/ProductDataTabsOptions.vue:334
	// Reference: src/components/ProductGalleryImagesOptions.vue:205
	__( 'Active', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:625
	__( 'Inactive', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:626
	__( 'Maintenance Mode', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:627
	__( 'The Maintenance Mode Page will notify search engines that the site is unavailable.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:631
	__( 'Set up a Maintenance Mode Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:635
	// Reference: src/admin/Subscribers-Lite.vue:256
	__( '404 Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:636
	__( 'Replace your default theme 404 page with a custom high converting 404 page.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:640
	__( 'Set up a 404 Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:641
	// Reference: src/admin/Subscribers-Lite.vue:257
	__( 'Landing Pages', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:642
	__( 'Add New Landing Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:643
	// Reference: src/components/TypographyControl.vue:318
	__( 'Edit', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:645
	// Reference: src/components/PostinfoOptions.vue:614
	// Reference: src/components/ProductMetaOptions.vue:342
	__( 'Duplicate', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:648
	__( 'Last Modified', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:649
	__( 'You do not have any landing pages yet.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:653
	__( 'Create New Landing Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:656
	__( 'SeedProd Coming Soon Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:657
	__( 'SeedProd Maintenance Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:658
	__( 'SeedProd 404 Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:659
	__( 'Create Optin, Sales, Webinar, Thank You or any type of Landing Page you need.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:663
	// Reference: src/admin/Subscribers-Lite.vue:266
	__( 'Login Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:664
	__( 'Create a Custom Login Page for your website. Optionally replace the default login page.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:665
	__( 'Set up a Login Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:666
	__( 'SeedProd Login Page', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:685
	// Reference: src/admin/ThemeChooser.vue:803
	// Reference: src/admin/ThemeTemplates-Lite.vue:816
	// Reference: src/views/TemplateChooser-Lite.vue:1338
	__( 'All', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:689
	__( 'Published', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:693
	__( 'Drafts', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:702
	__( 'Trash', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:709
	__( 'Move To Trash', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:713
	__( 'Restore', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:717
	__( 'Delete Permanently', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:722
	// Reference: src/admin/Subscribers-Lite.vue:308
	// Reference: src/components/AnchorOptions.vue:76
	// Reference: src/components/PostauthorboxOptions.vue:340
	// Reference: src/components/TeamMemberOptions.vue:1095
	// Reference: src/components/TestimonialOptions.vue:640
	__( 'Name', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:727
	__( 'URL', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:732
	// Reference: src/components/EDDDownloadsGridOptions.vue:887
	// Reference: src/components/PostinfoOptions.vue:583
	// Reference: src/components/PostsOptions.vue:1970
	// Reference: src/components/ProductRelatedOptions.vue:503
	// Reference: src/components/WCCustomProductsGridOptions.vue:1036
	__( 'Date', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:744
	// Reference: src/admin/Subscribers-Lite.vue:331
	// Reference: src/components/ListTable.vue:468
	__( 'Loading', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:957
	__( 'Pages Moved to Trash.', 'coming-soon' ),

	// Reference: src/admin/Dashboard-Lite.vue:990
	__( 'Pages Restored.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:102
	// Reference: src/components/Mypaykit.vue:174
	__( 'MyPayKit', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:104
	__( 'MyPayKit seamlessly integrates with Square to help you collect payments directly on your WordPress site. Just one click to connect your existing Square account.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:116
	__( 'WPForms', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:118
	__( 'The most beginner friendly drag & drop WordPress forms plugin allowing you to create beautifdivcontact forms, subscription forms, payment forms, and more in minutes, not hours!', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:129
	__( 'OptinMonster', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:131
	// Reference: src/admin/Popups.vue:148
	__( 'Our high-converting optin forms like Exit-Intent® popups, Fullscreen Welcome Mats, and Scroll boxes help you dramatically boost conversions and get more email subscribers.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:142
	__( 'WP Mail SMTP', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:144
	__( 'SMTP (Simple Mail Transfer Protocol) is an industry standard for sending emails. SMTP helps increase email deliverability by using proper authentication.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:155
	__( 'MonsterInsights', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:157
	__( 'MonsterInsights makes it “effortless” to properly connect your WordPress site with Google Analytics, so you can start making data-driven decisions to grow your business.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:171
	__( 'TrustPulse', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:173
	__( 'TrustPulse uses FOMO (Fear of Missing Out) to boost your sales and conversions with social proof notifications. Use it to boost sales on your Woocommerce store, increase signups on your membership site, get more email subscribers, and more.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:185
	// Reference: src/views/SettingsSEO.vue:189
	__( 'All in One SEO', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:187
	__( 'The original WordPress SEO plugin. Improve your WordPress SEO rankings and traffic with our comprehensive SEO tools and smart SEO optimizations.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:199
	__( 'ExactMetrics', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:201
	__( 'The ExactMetrics Google Analytics for WordPress plugin helps you properly setup all the powerful Google Analytics tracking features without writing any code or hiring a developer.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:213
	__( 'Smash Balloon Social Photo Feed', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:215
	__( 'Easily display Instagram content on your WordPress site without writing any code. Comes with multiple templates, ability to show content from multiple accounts, hashtags, and more. Trusted by 1 million websites.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:227
	__( 'Smash Balloon Social Post Feed', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:229
	__( 'Easily display Facebook content on your WordPress site without writing any code. Comes with multiple templates, ability to embed albums, group content, reviews, live videos, comments, and reactions.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:241
	__( 'Smash Balloon Twitter Feeds', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:243
	__( 'Easily display Twitter content in WordPress without writing any code. Comes with multiple layouts, ability to combine multiple Twitter feeds, Twitter card support, tweet moderation, and more.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:255
	__( 'Smash Balloon YouTube Feeds', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:257
	__( 'Easily display YouTube videos on your WordPress site without writing any code. Comes with multiple layouts, ability to embed live streams, video filtering, ability to combine multiple channel videos, and more.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:269
	// Reference: src/components/PushNotificationOptions.vue:188
	__( 'PushEngage', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:271
	__( 'Connect with your visitors after they leave your website with the leading web push notification software. Over 10,000+ businesses worldwide use PushEngage to send 9 billion notifications each month.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:283
	__( 'Sugar Calendar', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:285
	__( 'A simple & powerful event calendar plugin for WordPress that comes with all the event management features including payments, scheduling, timezones, ticketing, recurring events, and more.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:297
	__( 'WP Simple Pay', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:299
	__( 'The #1 Stripe payments plugin for WordPress. Start accepting one-time and recurring payments on your WordPress site without setting up a shopping cart. No code required.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:311
	// Reference: src/views/SetupBlockOptions-Lite.vue:1437
	__( 'Easy Digital Downloads', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:313
	__( 'The best WordPress eCommerce plugin for selling digital downloads. Start selling eBooks, software, music, digital art, and more within minutes. Accept payments, manage subscriptions, advanced access control, and more.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:325
	__( 'SearchWP', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:327
	__( 'The most advanced WordPress search plugin. Customize your WordPress search algorithm, reorder search results, track search metrics, and everything you need to leverage search to grow your business.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:340
	__( 'AffiliateWP', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:342
	__( 'The #1 affiliate management plugin for WordPress. Easily create an affiliate program for your eCommerce store or membership site within minutes and start growing your sales with the power of referral marketing.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:355
	__( 'Envira Gallery', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:357
	__( 'Envira Gallery is the best responsive WordPress gallery plugin. Envira gallery is a perfect solution for photographers, designers, bloggers, and small businesses.', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:471
	__( 'Plugin Installed, click Activate', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:566
	// Reference: src/views/SettingsAnalytics.vue:124
	// Reference: src/views/SettingsSEO.vue:192
	__( 'Install', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:569
	__( 'Deactivate', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:572
	// Reference: src/views/SettingsAnalytics.vue:125
	// Reference: src/views/SettingsSEO.vue:193
	__( 'Activate', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:81
	// Reference: src/admin/Popups.vue:129
	__( 'Status', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:82
	__( 'PRO Version installed', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:89
	__( 'RafflePress', 'coming-soon' ),

	// Reference: src/admin/GrowthTools.vue:91
	__( 'Turn your visitors into brand ambassadors! Easily grow your email list, website traffic, and social media followers with powerful viral giveaways & contests.', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:130
	__( 'Convert and Monetize Your Website Traffic', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:131
	__( 'Stop losing visitors! Instantly grow your email list, get more leads and increase sales with the #1 most powerful conversion optimization toolkit in the world.', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:132
	__( 'How Does OptinMonster Work?', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:133
	__( 'OptinMonster generates more subscribers, leads and sales from the traffic you already have. All it takes is 3 simple steps...', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:134
	__( 'Step 1: Create a Visually Stunning Offer', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:135
	__( 'Choose a pre-built template designed for maximum conversions, or start from scratch with a blank canvas. Customize all the details with our easy to use drag-and-drop builder - no code needed.', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:136
	__( 'Step 2: Target and Personalize Your Offers with Behavior Automation', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:137
	__( 'Our powerful targeting and segmentation engine lets you show your perfect offer to the right people at the exact right time to skyrocket your website conversions.', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:138
	__( 'Step 3: Test and Adjust in Real Time', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:139
	__( 'Get all the stats you need to improve your lead generation strategy, then easily split test all your ideas to keep increasing conversions.', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:398
	__( 'Get OptinMonster Now', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:401
	__( 'OptinMonster is Active - Go to Settings', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:405
	__( 'Activate OptinMonster', 'coming-soon' ),

	// Reference: src/admin/Popups.vue:407
	__( 'Activating OptinMonster', 'coming-soon' ),

	// Reference: src/admin/Settings.vue:19
	// Reference: src/components/EDDDownloadsGridOptions.vue:896
	// Reference: src/components/TeamMemberOptions.vue:1091
	// Reference: src/components/WCCustomProductsGridOptions.vue:999
	// Reference: src/views/SettingsGeneral.vue:273
	// Reference: src/views/SetupSettings.vue:253
	__( 'General', 'coming-soon' ),

	// Reference: src/admin/Settings.vue:20
	__( 'Emails', 'coming-soon' ),

	// Reference: src/admin/Settings.vue:21
	__( 'Integrations', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:357
	__( 'License', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:358
	__( 'Your license key provides access to updates and addons.', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:359
	__( 'You\'re using <strong>SeedProd Lite</strong> - No License needed. Enjoy!', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:360
	__( 'To unlock more features consider <a href=\'%s\' target=\'_blank\'>upgrading to PRO</a> . As a valued SeedProd Lite user you\'ll receive <strong>a discount off the regular price</strong>, automatically applied at checkout! ', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:361
	__( 'If you already have a license key for <a href=\'%s\' target=\'_blank\'>SeedProd Lite</a>, please enter it to Upgrade to the Pro Features. SeedProd.com will be used to verify and connect you to SeedProd.', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:362
	__( 'License Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:363
	__( 'Enter Your License Key Here', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:364
	__( 'Recheck Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:365
	__( 'Verify Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:366
	__( 'Deactivate Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:367
	__( 'Connect to SeedProd to Install the Pro Version', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:368
	__( 'You currently have the <strong>%s</strong> license.', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:369
	__( 'Debug Information', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:370
	__( 'Facebook APP ID', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:371
	// Reference: src/views/Setup.vue:1076
	// Reference: src/views/SetupDesign-Lite.vue:730
	__( 'Global Settings', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:373
	__( 'Save Settings', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:374
	__( 'Disable Edit with SeedProd Button', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:375
	__( 'Enable Usage Tracking', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:376
	__( 'Google Places API Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:377
	__( 'Yelp API Key', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:378
	__( 'View Debug Information', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:379
	__( 'Learn More', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:380
	__( 'Disable SeedProd Notifications', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:381
	// Reference: src/components/AlertBoxOptions.vue:769
	// Reference: src/components/BeforeAfterToggleOptions.vue:547
	// Reference: src/components/BusinessHoursOptions.vue:746
	// Reference: src/components/ContentToggleOptions.vue:617
	// Reference: src/components/PostsOptions.vue:1989
	// Reference: src/components/PriceListOptions.vue:937
	// Reference: src/components/TeamMemberOptions.vue:1147
	// Reference: src/components/TestimonialOptions.vue:645
	// Reference: src/views/SettingsAccess-Lite.vue:325
	__( 'Yes', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:382
	// Reference: src/components/AlertBoxOptions.vue:770
	// Reference: src/components/BeforeAfterToggleOptions.vue:548
	// Reference: src/components/BusinessHoursOptions.vue:747
	// Reference: src/components/ContentToggleOptions.vue:618
	// Reference: src/components/PostsOptions.vue:1990
	// Reference: src/components/PriceListOptions.vue:938
	// Reference: src/components/TeamMemberOptions.vue:1148
	// Reference: src/components/TestimonialOptions.vue:646
	// Reference: src/views/SettingsAccess-Lite.vue:326
	__( 'No', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:505
	// Reference: src/views/TemplateChooser-Lite.vue:2048
	__( 'Could not be saved. Please contact Support if you continue to experience this issue.', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:570
	// Reference: src/views/Setup.vue:1628
	__( 'Saved!', 'coming-soon' ),

	// Reference: src/admin/SettingsGeneral.vue:591
	__( 'Looks like you don\'t have permission to save.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:853
	__( '<h1 class=\'sp-text-2xl sp-text-neutral sp-m-0 sp-mb-4 sp-font-bold\'>Template Setup Complete</h1>', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:854
	__( 'You can close this window and get started editing you page<br> or setup a new page.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:855
	// Reference: src/components/ColorPicker.vue:323
	// Reference: src/components/CustomHTMLOptions.vue:205
	// Reference: src/components/TypographyControl.vue:319
	// Reference: src/views/GlobalCSS.vue:2174
	// Reference: src/views/InlineHelpView.vue:264
	// Reference: src/views/Setup.vue:1054
	// Reference: src/views/SetupDesign-Lite.vue:740
	__( 'Close', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:856
	__( 'Click the button below to install and activate the following free plugins:', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:857
	__( 'Install and Activate Plugins', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:858
	__( 'I\'ll do it later', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:864
	__( '<h1 class="sp-text-2xl sp-text-neutral sp-m-0 sp-mb-4 sp-font-bold">Finishing Up!</h1><p class="sp-text-sm sp-text-neutral sp-font-normal">Please do not refresh or exit this page<br>until this process is complete.<p>', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:869
	__( 'Setup a Coming Soon Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:870
	__( 'A Coming Soon Page will hide your site from public but you\'ll still be able to see it and work on it if logged in.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:876
	__( 'Setup a Maintenance Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:877
	__( 'A Maintenance Page will notify search engines that the site is unavailable.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:881
	__( 'Setup a Landing Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:882
	__( 'Landing Pages are meant to be standalone pages separate from the design of your site and theme.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:886
	__( 'Edit Landing Pages', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:887
	__( 'Build a Website', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:888
	__( 'Build your entire Website. Create Headers, Footers, Pages, Posts, Archives, Sidebars, and more.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:892
	__( 'Select a Theme for my Website', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:896
	__( 'Edit Theme', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:897
	__( 'Build a WooCommerce Store', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:898
	__( 'Create an entire WooCommerce store. Customize product pages, checkout, cart, product grids, and more.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:902
	__( 'Select a Theme for my Store', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:903
	__( 'Setup a Login Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:904
	__( 'Create a custom Login page for your website.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:908
	__( 'Setup a 404 Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:909
	__( 'Create a custom 404 page for your website.', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:913
	__( 'Dismiss This Setup Page', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:914
	__( 'Finish Setup', 'coming-soon' ),

	// Reference: src/admin/Setup-Lite.vue:948
	__( 'There was an issue completing the setup. Please refresh the page and try again.', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:251
	__( 'Export to CSV', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:254
	__( 'Coming Soon Page', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:255
	__( 'Maintenance Mode Page', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:258
	__( 'Days', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:260
	__( 'You do not have any subscribers yet.', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:264
	__( 'Go to Pages', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:265
	__( 'Search Emails', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:271
	__( 'Subscribers Overview', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:279
	__( 'All Pages', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:299
	__( 'Delete Subscribers', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:304
	// Reference: src/components/DynamicTagsControl.vue:1000
	// Reference: src/components/SocialProfilesOptions.vue:687
	__( 'Email', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:318
	__( 'Created', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:325
	// Reference: src/components/PostinfoOptions.vue:615
	// Reference: src/components/ProductMetaOptions.vue:343
	__( 'Delete', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:441
	__( 'Subscribers Deleted', 'coming-soon' ),

	// Reference: src/admin/Subscribers-Lite.vue:491
	__( 'Export Started', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:800
	// Reference: src/admin/ThemeTemplates-Lite.vue:813
	// Reference: src/views/TemplateChooser-Lite.vue:1332
	__( 'All Templates', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:801
	// Reference: src/admin/ThemeTemplates-Lite.vue:814
	// Reference: src/views/TemplateChooser-Lite.vue:1333
	__( 'Favorite Templates', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:802
	// Reference: src/admin/ThemeTemplates-Lite.vue:815
	// Reference: src/views/TemplateChooser-Lite.vue:1337
	__( 'Filter:', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:804
	// Reference: src/admin/ThemeTemplates-Lite.vue:817
	// Reference: src/views/SectionTemplateOptions-Lite.vue:434
	// Reference: src/views/TemplateChooser-Lite.vue:1339
	__( 'No Templates Found', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:805
	// Reference: src/admin/ThemeTemplates-Lite.vue:818
	// Reference: src/components/ImageControl.vue:336
	// Reference: src/views/TemplateChooser-Lite.vue:1353
	__( 'First Page', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:806
	// Reference: src/admin/ThemeTemplates-Lite.vue:819
	// Reference: src/components/ImageControl.vue:337
	// Reference: src/views/TemplateChooser-Lite.vue:1354
	__( 'Prev', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:807
	// Reference: src/admin/ThemeTemplates-Lite.vue:820
	// Reference: src/components/ImageControl.vue:338
	// Reference: src/views/TemplateChooser-Lite.vue:1355
	__( 'Next', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:808
	// Reference: src/admin/ThemeTemplates-Lite.vue:821
	// Reference: src/components/ImageControl.vue:339
	// Reference: src/views/TemplateChooser-Lite.vue:1356
	__( 'Last Page', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:809
	// Reference: src/admin/ThemeTemplates-Lite.vue:822
	// Reference: src/views/TemplateChooser-Lite.vue:1357
	__( 'Search templates...', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:810
	// Reference: src/admin/ThemeTemplates-Lite.vue:823
	// Reference: src/views/SetupBlockOptions-Lite.vue:1435
	__( 'WooCommerce', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:811
	// Reference: src/admin/ThemeTemplates-Lite.vue:824
	__( 'Sort:', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:812
	// Reference: src/admin/ThemeTemplates-Lite.vue:828
	// Reference: src/components/BackgroundControl.vue:485
	// Reference: src/components/ColOptions.vue:701
	// Reference: src/components/CounterOptions.vue:338
	// Reference: src/components/EDDCartOptions.vue:403
	// Reference: src/components/EDDCheckoutOptions.vue:690
	// Reference: src/components/FontControl.vue:25
	// Reference: src/components/ImageOptions.vue:569
	// Reference: src/components/PostinfoOptions.vue:605
	// Reference: src/components/PostsOptions.vue:1953
	// Reference: src/components/PriceListOptions.vue:965
	// Reference: src/components/ProductMetaOptions.vue:339
	// Reference: src/components/RowOptions.vue:707
	// Reference: src/components/SocialProfilesOptions.vue:690
	// Reference: src/components/TeamMemberOptions.vue:1177
	// Reference: src/components/WCCartOptions.vue:546
	// Reference: src/components/WCCheckoutOptions.vue:816
	__( 'Default', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:813
	// Reference: src/admin/ThemeTemplates-Lite.vue:829
	__( 'Popular', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:814
	// Reference: src/admin/ThemeTemplates-Lite.vue:830
	__( 'Newest to Oldest', 'coming-soon' ),

	// Reference: src/admin/ThemeChooser.vue:815
	// Reference: src/admin/ThemeTemplates-Lite.vue:831
	__( 'Oldest to Newest', 'coming-soon' ),

	// Reference: src/admin/ThemeTemplates-Lite.vue:825
	// Reference: src/components/LiteCTABuilder.vue:110
	// Reference: src/components/LiteCTASubscribers.vue:250
	// Reference: src/components/LiteCTATemplates.vue:90
	// Reference: src/components/SettingsLiteCTA.vue:264
	__( 'Upgrade to SeedProd PRO Now', 'coming-soon' ),

	// Reference: src/admin/ThemeTemplates-Lite.vue:826
	__( 'Get a Full Website With One-Click Without Being a Developer', 'coming-soon' ),

	// Reference: src/admin/ThemeTemplates-Lite.vue:827
	__( 'Say “hello” to the future with SeedProd’s custom WordPress theme builder and One Click Websites.', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:120
	__( 'Thank you for choosing SeedProd - The Best Website Builder, Landing Page Builder, Coming Soon, Maintenance Mode & more...', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:124
	__( 'Get Started →', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:125
	__( '← Exit Setup', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:126
	__( 'Note: You will be transfered to an SeedProd.com to complete the setup wizard.', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:139
	__( 'Use our setup wizard to get started in less than 2 minutes and unlock free templates!', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:144
	__( 'Use our setup wizard to get started in less than 2 minutes!', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:156
	__( 'Are you sure you want to exit the setup wizard?

You will miss out on our free templates. 😬', 'coming-soon' ),

	// Reference: src/admin/Welcome-Lite.vue:161
	__( 'Are you sure you want to exit the setup wizard? 😬', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:608
	// Reference: src/components/AddToCartOptions.vue:1072
	// Reference: src/components/AdditionalInformationOptions.vue:131
	// Reference: src/components/AlertBoxOptions.vue:776
	// Reference: src/components/BeforeAfterToggleOptions.vue:536
	// Reference: src/components/BulletListOptions.vue:500
	// Reference: src/components/BusinessHoursOptions.vue:716
	// Reference: src/components/ButtonOptions.vue:1238
	// Reference: src/components/ColOptions.vue:667
	// Reference: src/components/ContactFormOptions.vue:293
	// Reference: src/components/ContentToggleOptions.vue:606
	// Reference: src/components/CountdownOptions.vue:850
	// Reference: src/components/CounterOptions.vue:326
	// Reference: src/components/CustomHTMLOptions.vue:199
	// Reference: src/components/DividerOptions.vue:361
	// Reference: src/components/EDDAddToCartOptions.vue:788
	// Reference: src/components/EDDBuyNowButtonOptions.vue:817
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:432
	// Reference: src/components/EDDDownloadPriceOptions.vue:103
	// Reference: src/components/EDDDownloadsGridOptions.vue:866
	// Reference: src/components/EnviraGalleryOptions.vue:215
	// Reference: src/components/FeatureOptions.vue:579
	// Reference: src/components/FormOptions.vue:176
	// Reference: src/components/GiveawayOptions.vue:165
	// Reference: src/components/GoogleMapsOptions.vue:234
	// Reference: src/components/HeaderOptions.vue:529
	// Reference: src/components/IconFeatureOptions.vue:567
	// Reference: src/components/IconOptions.vue:274
	// Reference: src/components/ImageOptions.vue:528
	// Reference: src/components/LoginOptions.vue:878
	// Reference: src/components/MenuCartOptions.vue:184
	// Reference: src/components/MypaykitOptions.vue:120
	// Reference: src/components/NavOptions.vue:571
	// Reference: src/components/OptinFormOptions.vue:850
	// Reference: src/components/PostauthorboxOptions.vue:338
	// Reference: src/components/PostcommentsOptions.vue:186
	// Reference: src/components/PostfeaturedimageOptions.vue:432
	// Reference: src/components/PostinfoOptions.vue:594
	// Reference: src/components/PostnavigationOptions.vue:95
	// Reference: src/components/PriceListOptions.vue:900
	// Reference: src/components/PricingTableOptions.vue:1291
	// Reference: src/components/ProductDataTabsOptions.vue:344
	// Reference: src/components/ProductFeaturedImageOptions.vue:432
	// Reference: src/components/ProductGalleryImagesOptions.vue:215
	// Reference: src/components/ProductMetaOptions.vue:333
	// Reference: src/components/ProductPriceOptions.vue:183
	// Reference: src/components/ProductRelatedOptions.vue:463
	// Reference: src/components/ProgressBarOptions.vue:322
	// Reference: src/components/RowOptions.vue:673
	// Reference: src/components/SectionOptions.vue:396
	// Reference: src/components/ShortcodeOptions.vue:143
	// Reference: src/components/SocialProfilesOptions.vue:720
	// Reference: src/components/SocialSharingOptions.vue:401
	// Reference: src/components/StarRatingOptions.vue:299
	// Reference: src/components/StripepaymentOptions.vue:1677
	// Reference: src/components/TeamMemberOptions.vue:1085
	// Reference: src/components/TemplatetagOptions.vue:149
	// Reference: src/components/TestimonialOptions.vue:634
	// Reference: src/components/TextOptions.vue:371
	// Reference: src/components/VideoOptions.vue:231
	// Reference: src/components/VideoPopUpOptions.vue:1127
	// Reference: src/components/WCAddToCartOptions.vue:690
	// Reference: src/components/WCCustomProductsGridOptions.vue:969
	// Reference: src/components/WpWidgetBlockOptions.vue:215
	__( 'Content', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:609
	// Reference: src/components/AddToCartOptions.vue:1073
	// Reference: src/components/AdditionalInformationOptions.vue:132
	// Reference: src/components/AlertBoxOptions.vue:726
	// Reference: src/components/BeforeAfterToggleOptions.vue:537
	// Reference: src/components/BulletListOptions.vue:501
	// Reference: src/components/BusinessHoursOptions.vue:721
	// Reference: src/components/ButtonOptions.vue:1239
	// Reference: src/components/ColOptions.vue:684
	// Reference: src/components/ContactFormOptions.vue:294
	// Reference: src/components/ContentToggleOptions.vue:607
	// Reference: src/components/CountdownOptions.vue:852
	// Reference: src/components/CounterOptions.vue:328
	// Reference: src/components/CustomHTMLOptions.vue:200
	// Reference: src/components/DividerOptions.vue:362
	// Reference: src/components/EDDAddToCartOptions.vue:789
	// Reference: src/components/EDDBuyNowButtonOptions.vue:818
	// Reference: src/components/EDDCartOptions.vue:360
	// Reference: src/components/EDDCheckoutOptions.vue:637
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:434
	// Reference: src/components/EDDDownloadPriceOptions.vue:104
	// Reference: src/components/EDDDownloadsGridOptions.vue:819
	// Reference: src/components/EnviraGalleryOptions.vue:216
	// Reference: src/components/FeatureOptions.vue:580
	// Reference: src/components/FormOptions.vue:177
	// Reference: src/components/GiveawayOptions.vue:166
	// Reference: src/components/GoogleMapsOptions.vue:235
	// Reference: src/components/HeaderOptions.vue:530
	// Reference: src/components/IconFeatureOptions.vue:568
	// Reference: src/components/IconOptions.vue:275
	// Reference: src/components/ImageOptions.vue:530
	// Reference: src/components/LoginOptions.vue:879
	// Reference: src/components/MenuCartOptions.vue:185
	// Reference: src/components/MypaykitOptions.vue:121
	// Reference: src/components/NavOptions.vue:572
	// Reference: src/components/OptinFormOptions.vue:852
	// Reference: src/components/PostauthorboxOptions.vue:339
	// Reference: src/components/PostcommentsOptions.vue:187
	// Reference: src/components/PostfeaturedimageOptions.vue:434
	// Reference: src/components/PostinfoOptions.vue:595
	// Reference: src/components/PostnavigationOptions.vue:96
	// Reference: src/components/PostsOptions.vue:1950
	// Reference: src/components/PriceListOptions.vue:905
	// Reference: src/components/PricingTableOptions.vue:1292
	// Reference: src/components/ProductDataTabsOptions.vue:345
	// Reference: src/components/ProductFeaturedImageOptions.vue:434
	// Reference: src/components/ProductGalleryImagesOptions.vue:216
	// Reference: src/components/ProductMetaOptions.vue:334
	// Reference: src/components/ProductPriceOptions.vue:184
	// Reference: src/components/ProductRelatedOptions.vue:452
	// Reference: src/components/ProgressBarOptions.vue:324
	// Reference: src/components/RowOptions.vue:690
	// Reference: src/components/SectionOptions.vue:397
	// Reference: src/components/ShortcodeOptions.vue:144
	// Reference: src/components/SocialProfilesOptions.vue:722
	// Reference: src/components/SocialSharingOptions.vue:402
	// Reference: src/components/StarRatingOptions.vue:300
	// Reference: src/components/StripepaymentOptions.vue:1678
	// Reference: src/components/TeamMemberOptions.vue:1090
	// Reference: src/components/TemplatetagOptions.vue:150
	// Reference: src/components/TestimonialOptions.vue:635
	// Reference: src/components/TextOptions.vue:372
	// Reference: src/components/VideoOptions.vue:232
	// Reference: src/components/VideoPopUpOptions.vue:1128
	// Reference: src/components/WCAddToCartOptions.vue:691
	// Reference: src/components/WCCartOptions.vue:499
	// Reference: src/components/WCCheckoutOptions.vue:768
	// Reference: src/components/WCCustomProductsGridOptions.vue:922
	// Reference: src/components/WpWidgetBlockOptions.vue:216
	// Reference: src/views/SetupBlockOptions-Lite.vue:1431
	__( 'Advanced', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:610
	__( 'Accordion', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:611
	// Reference: src/components/BulletListOptions.vue:503
	// Reference: src/components/BusinessHoursOptions.vue:723
	// Reference: src/components/FormOptions.vue:179
	// Reference: src/components/NavOptions.vue:574
	// Reference: src/components/PriceListOptions.vue:907
	// Reference: src/components/PricingTableOptions.vue:1294
	// Reference: src/components/TeamMemberOptions.vue:1113
	__( 'Add New Item', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:612
	// Reference: src/components/BulletListOptions.vue:504
	// Reference: src/components/FontSizeControl.vue:114
	// Reference: src/components/HeaderOptions.vue:535
	// Reference: src/components/NavOptions.vue:575
	// Reference: src/components/OptinFormOptions.vue:900
	// Reference: src/components/ProductGalleryImagesOptions.vue:223
	// Reference: src/components/TextOptions.vue:376
	// Reference: src/components/TypographyControl.vue:321
	__( 'Font Size', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:613
	// Reference: src/components/AddToCartOptions.vue:1076
	// Reference: src/components/BusinessHoursOptions.vue:726
	// Reference: src/components/ColOptions.vue:696
	// Reference: src/components/CountdownOptions.vue:880
	// Reference: src/components/NavOptions.vue:576
	// Reference: src/components/PostinfoOptions.vue:592
	// Reference: src/components/PriceListOptions.vue:910
	// Reference: src/components/PricingTableOptions.vue:1355
	// Reference: src/components/ProductMetaOptions.vue:331
	// Reference: src/components/ProductPriceOptions.vue:188
	// Reference: src/components/RowOptions.vue:702
	// Reference: src/components/SocialProfilesOptions.vue:699
	// Reference: src/components/SocialSharingOptions.vue:423
	// Reference: src/components/SpaceBetweenControl.vue:111
	// Reference: src/components/StarRatingOptions.vue:307
	// Reference: src/components/TeamMemberOptions.vue:1123
	__( 'Space Between', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:614
	// Reference: src/components/AddToCartOptions.vue:1078
	// Reference: src/components/AlertBoxOptions.vue:728
	// Reference: src/components/BulletListOptions.vue:505
	// Reference: src/components/BusinessHoursOptions.vue:729
	// Reference: src/components/ButtonOptions.vue:1243
	// Reference: src/components/CountdownOptions.vue:890
	// Reference: src/components/CounterOptions.vue:349
	// Reference: src/components/DividerOptions.vue:364
	// Reference: src/components/EDDAddToCartOptions.vue:794
	// Reference: src/components/EDDBuyNowButtonOptions.vue:823
	// Reference: src/components/EDDCartOptions.vue:358
	// Reference: src/components/EDDCheckoutOptions.vue:635
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:437
	// Reference: src/components/EDDDownloadsGridOptions.vue:817
	// Reference: src/components/FeatureOptions.vue:584
	// Reference: src/components/GoogleMapsOptions.vue:236
	// Reference: src/components/HeaderOptions.vue:532
	// Reference: src/components/IconFeatureOptions.vue:571
	// Reference: src/components/IconOptions.vue:276
	// Reference: src/components/ImageOptions.vue:534
	// Reference: src/components/MenuCartOptions.vue:187
	// Reference: src/components/PostauthorboxOptions.vue:358
	// Reference: src/components/PostfeaturedimageOptions.vue:438
	// Reference: src/components/PostsOptions.vue:1948
	// Reference: src/components/PriceListOptions.vue:914
	// Reference: src/components/ProductFeaturedImageOptions.vue:437
	// Reference: src/components/ProductGalleryImagesOptions.vue:219
	// Reference: src/components/ProductRelatedOptions.vue:450
	// Reference: src/components/ProgressBarOptions.vue:342
	// Reference: src/components/RowOptions.vue:676
	// Reference: src/components/SocialProfilesOptions.vue:706
	// Reference: src/components/SocialSharingOptions.vue:405
	// Reference: src/components/StarRatingOptions.vue:302
	// Reference: src/components/StripepaymentOptions.vue:1682
	// Reference: src/components/TeamMemberOptions.vue:1126
	// Reference: src/components/TestimonialOptions.vue:666
	// Reference: src/components/TextOptions.vue:374
	// Reference: src/components/VideoOptions.vue:234
	// Reference: src/components/VideoPopUpOptions.vue:1130
	// Reference: src/components/WCAddToCartOptions.vue:695
	// Reference: src/components/WCCartOptions.vue:497
	// Reference: src/components/WCCheckoutOptions.vue:766
	// Reference: src/components/WCCustomProductsGridOptions.vue:920
	// Reference: src/components/WpWidgetBlockOptions.vue:218
	__( 'Styles', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:615
	// Reference: src/components/BulletListOptions.vue:506
	// Reference: src/components/BusinessHoursOptions.vue:730
	// Reference: src/components/NavOptions.vue:578
	// Reference: src/components/PriceListOptions.vue:915
	// Reference: src/components/TeamMemberOptions.vue:1127
	__( 'List Layout', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:616
	// Reference: src/components/BeforeAfterToggleOptions.vue:540
	// Reference: src/components/BulletListOptions.vue:507
	// Reference: src/components/BusinessHoursOptions.vue:731
	// Reference: src/components/ColOptions.vue:686
	// Reference: src/components/ContentToggleOptions.vue:610
	// Reference: src/components/NavOptions.vue:579
	// Reference: src/components/PriceListOptions.vue:916
	// Reference: src/components/RowOptions.vue:692
	// Reference: src/components/ShadowControl.vue:143
	// Reference: src/components/TeamMemberOptions.vue:1128
	__( 'Vertical', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:617
	// Reference: src/components/BeforeAfterToggleOptions.vue:541
	// Reference: src/components/BulletListOptions.vue:508
	// Reference: src/components/BusinessHoursOptions.vue:732
	// Reference: src/components/ColOptions.vue:685
	// Reference: src/components/ContentToggleOptions.vue:611
	// Reference: src/components/NavOptions.vue:580
	// Reference: src/components/PriceListOptions.vue:917
	// Reference: src/components/RowOptions.vue:691
	// Reference: src/components/ShadowControl.vue:142
	// Reference: src/components/TeamMemberOptions.vue:1129
	__( 'Horizontal', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:618
	// Reference: src/components/AdditionalInformationOptions.vue:129
	// Reference: src/components/BulletListOptions.vue:509
	// Reference: src/components/ContactFormOptions.vue:297
	// Reference: src/components/DividerOptions.vue:393
	// Reference: src/components/EDDDownloadsGridOptions.vue:861
	// Reference: src/components/EnviraGalleryOptions.vue:219
	// Reference: src/components/GiveawayOptions.vue:169
	// Reference: src/components/MypaykitOptions.vue:124
	// Reference: src/components/PostinfoOptions.vue:618
	// Reference: src/components/ProductDataTabsOptions.vue:335
	// Reference: src/components/ProductGalleryImagesOptions.vue:206
	// Reference: src/components/ProductMetaOptions.vue:346
	// Reference: src/components/StarRatingOptions.vue:309
	// Reference: src/components/WCCartOptions.vue:541
	// Reference: src/components/WCCheckoutOptions.vue:810
	// Reference: src/components/WCCustomProductsGridOptions.vue:964
	// Reference: src/components/WpWidgetBlockOptions.vue:213
	__( 'Text Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:619
	// Reference: src/components/BulletListOptions.vue:510
	// Reference: src/components/BusinessHoursOptions.vue:734
	// Reference: src/components/EDDDownloadsGridOptions.vue:868
	// Reference: src/components/PriceListOptions.vue:919
	// Reference: src/components/SpacingSectionControl.vue:74
	// Reference: src/components/TeamMemberOptions.vue:1131
	// Reference: src/components/WCCustomProductsGridOptions.vue:971
	__( 'Spacing', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:620
	// Reference: src/components/BulletListOptions.vue:511
	// Reference: src/components/BusinessHoursOptions.vue:735
	// Reference: src/components/PriceListOptions.vue:920
	// Reference: src/components/SpacingSectionControl.vue:75
	// Reference: src/components/TeamMemberOptions.vue:1132
	__( 'Top Margin', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:621
	// Reference: src/components/BeforeAfterToggleOptions.vue:532
	// Reference: src/components/BulletListOptions.vue:512
	// Reference: src/components/ContentToggleOptions.vue:596
	// Reference: src/components/DividerOptions.vue:384
	// Reference: src/components/FeatureOptions.vue:575
	// Reference: src/components/HeaderOptions.vue:534
	// Reference: src/components/IconFeatureOptions.vue:563
	// Reference: src/components/NavOptions.vue:582
	// Reference: src/components/PostsOptions.vue:2025
	// Reference: src/components/PricingTableOptions.vue:1305
	// Reference: src/components/TestimonialOptions.vue:636
	// Reference: src/components/TextOptions.vue:375
	// Reference: src/components/WpWidgetBlockOptions.vue:222
	// Reference: src/views/GlobalCSS.vue:2180
	// Reference: src/views/SetupDesign-Lite.vue:746
	__( 'Text', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:622
	// Reference: src/components/AlertBoxOptions.vue:759
	// Reference: src/components/BeforeAfterToggleOptions.vue:526
	// Reference: src/components/BulletListOptions.vue:513
	// Reference: src/components/ContentToggleOptions.vue:586
	// Reference: src/components/DividerOptions.vue:383
	// Reference: src/components/FeatureOptions.vue:570
	// Reference: src/components/IconFeatureOptions.vue:557
	// Reference: src/components/IconOptions.vue:277
	// Reference: src/components/PostinfoOptions.vue:609
	// Reference: src/components/PricingTableOptions.vue:1306
	// Reference: src/components/SocialProfilesOptions.vue:705
	// Reference: src/components/TeamMemberOptions.vue:1133
	// Reference: src/components/TestimonialOptions.vue:628
	__( 'Icon', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:623
	// Reference: src/components/AddToCartOptions.vue:1088
	// Reference: src/components/AlertBoxOptions.vue:778
	// Reference: src/components/BulletListOptions.vue:514
	// Reference: src/components/BusinessHoursOptions.vue:739
	// Reference: src/components/ButtonOptions.vue:1256
	// Reference: src/components/CountdownOptions.vue:876
	// Reference: src/components/EDDAddToCartOptions.vue:806
	// Reference: src/components/EDDBuyNowButtonOptions.vue:835
	// Reference: src/components/EDDCartOptions.vue:366
	// Reference: src/components/EDDCheckoutOptions.vue:645
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:445
	// Reference: src/components/EDDDownloadsGridOptions.vue:827
	// Reference: src/components/ImageOptions.vue:543
	// Reference: src/components/OptinFormOptions.vue:876
	// Reference: src/components/PostfeaturedimageOptions.vue:446
	// Reference: src/components/PriceListOptions.vue:929
	// Reference: src/components/ProductFeaturedImageOptions.vue:445
	// Reference: src/components/ProgressBarOptions.vue:328
	// Reference: src/components/SocialProfilesOptions.vue:674
	// Reference: src/components/StripepaymentOptions.vue:1695
	// Reference: src/components/TeamMemberOptions.vue:1137
	// Reference: src/components/WCAddToCartOptions.vue:708
	// Reference: src/components/WCCartOptions.vue:507
	// Reference: src/components/WCCheckoutOptions.vue:776
	// Reference: src/components/WCCustomProductsGridOptions.vue:930
	__( 'Choose Your Style', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:624
	__( 'Closed Icon Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:625
	__( 'Closed Icon', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:626
	__( 'Open Icon Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:627
	__( 'Open Icon', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:628
	// Reference: src/components/AdditionalInformationOptions.vue:128
	// Reference: src/components/EDDCartOptions.vue:377
	// Reference: src/components/EDDCheckoutOptions.vue:656
	// Reference: src/components/EDDDownloadsGridOptions.vue:865
	// Reference: src/components/FeatureOptions.vue:578
	// Reference: src/components/IconFeatureOptions.vue:566
	// Reference: src/components/WCCartOptions.vue:545
	// Reference: src/components/WCCheckoutOptions.vue:814
	// Reference: src/components/WCCustomProductsGridOptions.vue:968
	// Reference: src/views/GlobalCSS.vue:2206
	__( 'Header Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:629
	// Reference: src/components/PricingTableOptions.vue:1313
	__( 'Header Open Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:630
	// Reference: src/components/BusinessHoursOptions.vue:753
	// Reference: src/components/PricingTableOptions.vue:1314
	// Reference: src/components/ProductMetaOptions.vue:349
	// Reference: src/components/TeamMemberOptions.vue:1154
	__( 'Divider Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:631
	// Reference: src/components/AddToCartOptions.vue:1107
	// Reference: src/components/AlertBoxOptions.vue:753
	// Reference: src/components/BackgroundControl.vue:473
	// Reference: src/components/BeforeAfterToggleOptions.vue:533
	// Reference: src/components/BusinessHoursOptions.vue:743
	// Reference: src/components/ButtonOptions.vue:1275
	// Reference: src/components/ColOptions.vue:678
	// Reference: src/components/ContentToggleOptions.vue:597
	// Reference: src/components/EDDAddToCartOptions.vue:825
	// Reference: src/components/EDDBuyNowButtonOptions.vue:854
	// Reference: src/components/EDDCartOptions.vue:392
	// Reference: src/components/EDDCheckoutOptions.vue:677
	// Reference: src/components/EDDDownloadsGridOptions.vue:860
	// Reference: src/components/FeatureOptions.vue:576
	// Reference: src/components/HeaderOptions.vue:549
	// Reference: src/components/IconFeatureOptions.vue:564
	// Reference: src/components/OptinFormOptions.vue:887
	// Reference: src/components/PostauthorboxOptions.vue:362
	// Reference: src/components/PostsOptions.vue:2098
	// Reference: src/components/PriceListOptions.vue:934
	// Reference: src/components/PricingTableOptions.vue:1351
	// Reference: src/components/ProductDataTabsOptions.vue:336
	// Reference: src/components/ProductGalleryImagesOptions.vue:225
	// Reference: src/components/ProgressBarOptions.vue:341
	// Reference: src/components/RowOptions.vue:687
	// Reference: src/components/SectionOptions.vue:394
	// Reference: src/components/SocialProfilesOptions.vue:719
	// Reference: src/components/StripepaymentOptions.vue:1714
	// Reference: src/components/TeamMemberOptions.vue:1143
	// Reference: src/components/TestimonialOptions.vue:631
	// Reference: src/components/TextOptions.vue:386
	// Reference: src/components/WCAddToCartOptions.vue:727
	// Reference: src/components/WCCartOptions.vue:540
	// Reference: src/components/WCCheckoutOptions.vue:809
	// Reference: src/components/WCCustomProductsGridOptions.vue:963
	__( 'Background Color', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:633
	// Reference: src/components/AddToCartOptions.vue:1114
	// Reference: src/components/AlertBoxOptions.vue:779
	// Reference: src/components/BulletListOptions.vue:517
	// Reference: src/components/BusinessHoursOptions.vue:762
	// Reference: src/components/ButtonOptions.vue:1284
	// Reference: src/components/ColOptions.vue:670
	// Reference: src/components/EDDAddToCartOptions.vue:832
	// Reference: src/components/EDDBuyNowButtonOptions.vue:861
	// Reference: src/components/HeaderOptions.vue:555
	// Reference: src/components/IconOptions.vue:291
	// Reference: src/components/NavOptions.vue:604
	// Reference: src/components/PriceListOptions.vue:973
	// Reference: src/components/PricingTableOptions.vue:1380
	// Reference: src/components/ProgressBarOptions.vue:343
	// Reference: src/components/StripepaymentOptions.vue:1723
	// Reference: src/components/TeamMemberOptions.vue:1197
	// Reference: src/components/TextOptions.vue:391
	// Reference: src/components/WCAddToCartOptions.vue:728
	__( 'Text Shadow', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:641
	// Reference: src/components/BulletListOptions.vue:525
	// Reference: src/components/BusinessHoursOptions.vue:770
	// Reference: src/components/PaddingControl.vue:454
	// Reference: src/components/PriceListOptions.vue:981
	// Reference: src/components/PricingTableOptions.vue:1388
	// Reference: src/components/TeamMemberOptions.vue:1205
	__( 'Padding', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:642
	// Reference: src/components/AnimationEffectControl.vue:273
	// Reference: src/components/BorderRadiusControl.vue:150
	// Reference: src/components/BorderWidthControl.vue:142
	// Reference: src/components/BulletListOptions.vue:526
	// Reference: src/components/BusinessHoursOptions.vue:771
	// Reference: src/components/DisplaySectionControl.vue:792
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:439
	// Reference: src/components/EDDDownloadsGridOptions.vue:926
	// Reference: src/components/ImageBorderControl.vue:260
	// Reference: src/components/ImageOptions.vue:536
	// Reference: src/components/MarginControl.vue:482
	// Reference: src/components/PaddingControl.vue:455
	// Reference: src/components/ParticlesBackgroundControl.vue:311
	// Reference: src/components/PostfeaturedimageOptions.vue:440
	// Reference: src/components/PostsOptions.vue:2113
	// Reference: src/components/PriceListOptions.vue:982
	// Reference: src/components/PricingTableOptions.vue:1389
	// Reference: src/components/ProductFeaturedImageOptions.vue:439
	// Reference: src/components/ProductRelatedOptions.vue:496
	// Reference: src/components/ShapeDividerControl.vue:347
	// Reference: src/components/TeamMemberOptions.vue:1206
	// Reference: src/components/WCCustomProductsGridOptions.vue:1029
	__( 'Top', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:643
	// Reference: src/components/AnimationEffectControl.vue:272
	// Reference: src/components/BorderRadiusControl.vue:153
	// Reference: src/components/BorderWidthControl.vue:144
	// Reference: src/components/BulletListOptions.vue:527
	// Reference: src/components/BusinessHoursOptions.vue:772
	// Reference: src/components/DisplaySectionControl.vue:794
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:441
	// Reference: src/components/EDDDownloadsGridOptions.vue:928
	// Reference: src/components/ImageBorderControl.vue:262
	// Reference: src/components/ImageOptions.vue:538
	// Reference: src/components/MarginControl.vue:484
	// Reference: src/components/PaddingControl.vue:457
	// Reference: src/components/ParticlesBackgroundControl.vue:312
	// Reference: src/components/PostfeaturedimageOptions.vue:442
	// Reference: src/components/PostsOptions.vue:2114
	// Reference: src/components/PriceListOptions.vue:983
	// Reference: src/components/PricingTableOptions.vue:1390
	// Reference: src/components/ProductFeaturedImageOptions.vue:441
	// Reference: src/components/ProductRelatedOptions.vue:498
	// Reference: src/components/ShapeDividerControl.vue:348
	// Reference: src/components/TeamMemberOptions.vue:1207
	// Reference: src/components/WCCustomProductsGridOptions.vue:1031
	__( 'Bottom', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:644
	// Reference: src/components/BorderRadiusControl.vue:152
	// Reference: src/components/BorderWidthControl.vue:143
	// Reference: src/components/BulletListOptions.vue:528
	// Reference: src/components/BusinessHoursOptions.vue:773
	// Reference: src/components/DisplaySectionControl.vue:793
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:440
	// Reference: src/components/EDDDownloadsGridOptions.vue:927
	// Reference: src/components/ImageBorderControl.vue:261
	// Reference: src/components/ImageOptions.vue:537
	// Reference: src/components/MarginControl.vue:483
	// Reference: src/components/PaddingControl.vue:456
	// Reference: src/components/ParticlesBackgroundControl.vue:314
	// Reference: src/components/PostfeaturedimageOptions.vue:441
	// Reference: src/components/PostsOptions.vue:2115
	// Reference: src/components/PriceListOptions.vue:984
	// Reference: src/components/PricingTableOptions.vue:1391
	// Reference: src/components/ProductFeaturedImageOptions.vue:440
	// Reference: src/components/ProductRelatedOptions.vue:497
	// Reference: src/components/TeamMemberOptions.vue:1208
	// Reference: src/components/WCCustomProductsGridOptions.vue:1030
	__( 'Right', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:645
	// Reference: src/components/BorderRadiusControl.vue:151
	// Reference: src/components/BorderWidthControl.vue:145
	// Reference: src/components/BulletListOptions.vue:529
	// Reference: src/components/BusinessHoursOptions.vue:774
	// Reference: src/components/DisplaySectionControl.vue:795
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:442
	// Reference: src/components/EDDDownloadsGridOptions.vue:929
	// Reference: src/components/ImageBorderControl.vue:263
	// Reference: src/components/ImageOptions.vue:539
	// Reference: src/components/MarginControl.vue:485
	// Reference: src/components/PaddingControl.vue:458
	// Reference: src/components/ParticlesBackgroundControl.vue:313
	// Reference: src/components/PostfeaturedimageOptions.vue:443
	// Reference: src/components/PostsOptions.vue:2116
	// Reference: src/components/PriceListOptions.vue:985
	// Reference: src/components/PricingTableOptions.vue:1392
	// Reference: src/components/ProductFeaturedImageOptions.vue:442
	// Reference: src/components/ProductRelatedOptions.vue:499
	// Reference: src/components/TeamMemberOptions.vue:1209
	// Reference: src/components/WCCustomProductsGridOptions.vue:1032
	__( 'Left', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:646
	// Reference: src/components/AddToCartOptions.vue:1106
	// Reference: src/components/ButtonOptions.vue:1274
	// Reference: src/components/CountdownOptions.vue:882
	// Reference: src/components/DividerOptions.vue:373
	// Reference: src/components/EDDAddToCartOptions.vue:824
	// Reference: src/components/EDDBuyNowButtonOptions.vue:853
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:446
	// Reference: src/components/EDDDownloadsGridOptions.vue:911
	// Reference: src/components/GoogleMapsOptions.vue:238
	// Reference: src/components/IconOptions.vue:279
	// Reference: src/components/ImageOptions.vue:544
	// Reference: src/components/PostfeaturedimageOptions.vue:447
	// Reference: src/components/PostsOptions.vue:2050
	// Reference: src/components/PricingTableOptions.vue:1393
	// Reference: src/components/ProductFeaturedImageOptions.vue:446
	// Reference: src/components/ProductRelatedOptions.vue:481
	// Reference: src/components/ProgressBarOptions.vue:339
	// Reference: src/components/RowOptions.vue:679
	// Reference: src/components/SectionOptions.vue:381
	// Reference: src/components/ShadowControl.vue:132
	// Reference: src/components/SocialProfilesOptions.vue:700
	// Reference: src/components/SocialSharingOptions.vue:424
	// Reference: src/components/StripepaymentOptions.vue:1713
	// Reference: src/components/TextOptions.vue:385
	// Reference: src/components/VideoOptions.vue:241
	// Reference: src/components/VideoPopUpOptions.vue:1137
	// Reference: src/components/WCAddToCartOptions.vue:726
	// Reference: src/components/WCCustomProductsGridOptions.vue:1014
	__( 'Shadow', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:647
	// Reference: src/components/AddToCartOptions.vue:1115
	// Reference: src/components/AlertBoxOptions.vue:742
	// Reference: src/components/BulletListOptions.vue:518
	// Reference: src/components/BusinessHoursOptions.vue:763
	// Reference: src/components/ButtonOptions.vue:1287
	// Reference: src/components/ColOptions.vue:671
	// Reference: src/components/CountdownOptions.vue:883
	// Reference: src/components/DividerOptions.vue:381
	// Reference: src/components/DynamicTagsControl.vue:1013
	// Reference: src/components/EDDAddToCartOptions.vue:833
	// Reference: src/components/EDDBuyNowButtonOptions.vue:862
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:447
	// Reference: src/components/EDDDownloadsGridOptions.vue:916
	// Reference: src/components/GoogleMapsOptions.vue:239
	// Reference: src/components/HeaderOptions.vue:542
	// Reference: src/components/IconOptions.vue:280
	// Reference: src/components/ImageOptions.vue:567
	// Reference: src/components/NavOptions.vue:605
	// Reference: src/components/PostauthorboxOptions.vue:355
	// Reference: src/components/PostfeaturedimageOptions.vue:448
	// Reference: src/components/PostinfoOptions.vue:610
	// Reference: src/components/PostsOptions.vue:2055
	// Reference: src/components/PriceListOptions.vue:974
	// Reference: src/components/PricingTableOptions.vue:1394
	// Reference: src/components/ProductFeaturedImageOptions.vue:447
	// Reference: src/components/ProductRelatedOptions.vue:486
	// Reference: src/components/ProgressBarOptions.vue:332
	// Reference: src/components/RowOptions.vue:680
	// Reference: src/components/SectionOptions.vue:382
	// Reference: src/components/ShadowControl.vue:133
	// Reference: src/components/ShapeDividerControl.vue:350
	// Reference: src/components/SocialProfilesOptions.vue:701
	// Reference: src/components/SocialSharingOptions.vue:416
	// Reference: src/components/StripepaymentOptions.vue:1724
	// Reference: src/components/TeamMemberOptions.vue:1198
	// Reference: src/components/TextOptions.vue:378
	// Reference: src/components/VideoOptions.vue:242
	// Reference: src/components/VideoPopUpOptions.vue:1138
	// Reference: src/components/WCAddToCartOptions.vue:729
	// Reference: src/components/WCCustomProductsGridOptions.vue:1019
	__( 'None', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:648
	// Reference: src/components/AddToCartOptions.vue:1116
	// Reference: src/components/AlertBoxOptions.vue:743
	// Reference: src/components/BulletListOptions.vue:519
	// Reference: src/components/BusinessHoursOptions.vue:764
	// Reference: src/components/ButtonOptions.vue:1288
	// Reference: src/components/ColOptions.vue:672
	// Reference: src/components/CountdownOptions.vue:884
	// Reference: src/components/DividerOptions.vue:375
	// Reference: src/components/EDDAddToCartOptions.vue:834
	// Reference: src/components/EDDBuyNowButtonOptions.vue:863
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:448
	// Reference: src/components/EDDDownloadsGridOptions.vue:917
	// Reference: src/components/GoogleMapsOptions.vue:240
	// Reference: src/components/HeaderOptions.vue:543
	// Reference: src/components/IconOptions.vue:281
	// Reference: src/components/ImageOptions.vue:546
	// Reference: src/components/NavOptions.vue:606
	// Reference: src/components/PostfeaturedimageOptions.vue:449
	// Reference: src/components/PostsOptions.vue:2056
	// Reference: src/components/PriceListOptions.vue:975
	// Reference: src/components/PricingTableOptions.vue:1395
	// Reference: src/components/ProductFeaturedImageOptions.vue:448
	// Reference: src/components/ProductRelatedOptions.vue:487
	// Reference: src/components/ProgressBarOptions.vue:333
	// Reference: src/components/RowOptions.vue:681
	// Reference: src/components/SectionOptions.vue:383
	// Reference: src/components/ShadowControl.vue:134
	// Reference: src/components/SocialProfilesOptions.vue:702
	// Reference: src/components/SocialSharingOptions.vue:417
	// Reference: src/components/StripepaymentOptions.vue:1725
	// Reference: src/components/TeamMemberOptions.vue:1199
	// Reference: src/components/TextOptions.vue:379
	// Reference: src/components/VideoOptions.vue:243
	// Reference: src/components/VideoPopUpOptions.vue:1139
	// Reference: src/components/WCAddToCartOptions.vue:730
	// Reference: src/components/WCCustomProductsGridOptions.vue:1020
	__( 'Hairline', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:649
	// Reference: src/components/AddToCartOptions.vue:1117
	// Reference: src/components/AlertBoxOptions.vue:744
	// Reference: src/components/BulletListOptions.vue:520
	// Reference: src/components/BusinessHoursOptions.vue:765
	// Reference: src/components/ButtonOptions.vue:1289
	// Reference: src/components/ColOptions.vue:673
	// Reference: src/components/ContentToggleOptions.vue:632
	// Reference: src/components/CountdownOptions.vue:885
	// Reference: src/components/DividerOptions.vue:376
	// Reference: src/components/EDDAddToCartOptions.vue:835
	// Reference: src/components/EDDBuyNowButtonOptions.vue:864
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:449
	// Reference: src/components/EDDDownloadsGridOptions.vue:918
	// Reference: src/components/GoogleMapsOptions.vue:241
	// Reference: src/components/HeaderOptions.vue:544
	// Reference: src/components/IconOptions.vue:282
	// Reference: src/components/ImageOptions.vue:547
	// Reference: src/components/LoginOptions.vue:924
	// Reference: src/components/NavOptions.vue:607
	// Reference: src/components/OptinFormOptions.vue:864
	// Reference: src/components/PostfeaturedimageOptions.vue:450
	// Reference: src/components/PostsOptions.vue:2057
	// Reference: src/components/PriceListOptions.vue:976
	// Reference: src/components/PricingTableOptions.vue:1396
	// Reference: src/components/ProductFeaturedImageOptions.vue:449
	// Reference: src/components/ProductRelatedOptions.vue:488
	// Reference: src/components/ProgressBarOptions.vue:334
	// Reference: src/components/RowOptions.vue:682
	// Reference: src/components/SectionOptions.vue:384
	// Reference: src/components/ShadowControl.vue:135
	// Reference: src/components/SocialProfilesOptions.vue:693
	// Reference: src/components/SocialSharingOptions.vue:418
	// Reference: src/components/StripepaymentOptions.vue:1726
	// Reference: src/components/TeamMemberOptions.vue:1200
	// Reference: src/components/TextOptions.vue:380
	// Reference: src/components/VideoOptions.vue:244
	// Reference: src/components/VideoPopUpOptions.vue:1140
	// Reference: src/components/WCAddToCartOptions.vue:731
	// Reference: src/components/WCCustomProductsGridOptions.vue:1021
	__( 'Small', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:650
	// Reference: src/components/AddToCartOptions.vue:1118
	// Reference: src/components/AlertBoxOptions.vue:745
	// Reference: src/components/BulletListOptions.vue:521
	// Reference: src/components/BusinessHoursOptions.vue:766
	// Reference: src/components/ButtonOptions.vue:1290
	// Reference: src/components/ColOptions.vue:674
	// Reference: src/components/ContentToggleOptions.vue:631
	// Reference: src/components/CountdownOptions.vue:886
	// Reference: src/components/DividerOptions.vue:377
	// Reference: src/components/EDDAddToCartOptions.vue:836
	// Reference: src/components/EDDBuyNowButtonOptions.vue:865
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:450
	// Reference: src/components/EDDDownloadsGridOptions.vue:919
	// Reference: src/components/GoogleMapsOptions.vue:242
	// Reference: src/components/HeaderOptions.vue:545
	// Reference: src/components/IconOptions.vue:283
	// Reference: src/components/ImageOptions.vue:548
	// Reference: src/components/LoginOptions.vue:925
	// Reference: src/components/NavOptions.vue:608
	// Reference: src/components/OptinFormOptions.vue:865
	// Reference: src/components/PostfeaturedimageOptions.vue:451
	// Reference: src/components/PostsOptions.vue:2058
	// Reference: src/components/PriceListOptions.vue:977
	// Reference: src/components/PricingTableOptions.vue:1397
	// Reference: src/components/ProductFeaturedImageOptions.vue:450
	// Reference: src/components/ProductRelatedOptions.vue:489
	// Reference: src/components/ProgressBarOptions.vue:335
	// Reference: src/components/RowOptions.vue:683
	// Reference: src/components/SectionOptions.vue:385
	// Reference: src/components/ShadowControl.vue:136
	// Reference: src/components/SocialProfilesOptions.vue:708
	// Reference: src/components/SocialSharingOptions.vue:419
	// Reference: src/components/StripepaymentOptions.vue:1727
	// Reference: src/components/TeamMemberOptions.vue:1201
	// Reference: src/components/TextOptions.vue:381
	// Reference: src/components/VideoOptions.vue:245
	// Reference: src/components/VideoPopUpOptions.vue:1141
	// Reference: src/components/WCAddToCartOptions.vue:732
	// Reference: src/components/WCCustomProductsGridOptions.vue:1022
	__( 'Medium', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:651
	// Reference: src/components/AddToCartOptions.vue:1119
	// Reference: src/components/AlertBoxOptions.vue:746
	// Reference: src/components/BulletListOptions.vue:522
	// Reference: src/components/BusinessHoursOptions.vue:767
	// Reference: src/components/ButtonOptions.vue:1291
	// Reference: src/components/ColOptions.vue:675
	// Reference: src/components/ContentToggleOptions.vue:630
	// Reference: src/components/CountdownOptions.vue:887
	// Reference: src/components/DividerOptions.vue:378
	// Reference: src/components/EDDAddToCartOptions.vue:837
	// Reference: src/components/EDDBuyNowButtonOptions.vue:866
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:451
	// Reference: src/components/EDDDownloadsGridOptions.vue:920
	// Reference: src/components/GoogleMapsOptions.vue:243
	// Reference: src/components/HeaderOptions.vue:546
	// Reference: src/components/IconOptions.vue:284
	// Reference: src/components/ImageOptions.vue:549
	// Reference: src/components/LoginOptions.vue:926
	// Reference: src/components/NavOptions.vue:609
	// Reference: src/components/OptinFormOptions.vue:866
	// Reference: src/components/PostfeaturedimageOptions.vue:452
	// Reference: src/components/PostsOptions.vue:2059
	// Reference: src/components/PriceListOptions.vue:978
	// Reference: src/components/PricingTableOptions.vue:1398
	// Reference: src/components/ProductFeaturedImageOptions.vue:451
	// Reference: src/components/ProductRelatedOptions.vue:490
	// Reference: src/components/ProgressBarOptions.vue:336
	// Reference: src/components/RowOptions.vue:684
	// Reference: src/components/SectionOptions.vue:386
	// Reference: src/components/ShadowControl.vue:137
	// Reference: src/components/SocialProfilesOptions.vue:695
	// Reference: src/components/SocialSharingOptions.vue:420
	// Reference: src/components/StripepaymentOptions.vue:1728
	// Reference: src/components/TeamMemberOptions.vue:1202
	// Reference: src/components/TextOptions.vue:382
	// Reference: src/components/VideoOptions.vue:246
	// Reference: src/components/VideoPopUpOptions.vue:1142
	// Reference: src/components/WCAddToCartOptions.vue:733
	// Reference: src/components/WCCustomProductsGridOptions.vue:1023
	__( 'Large', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:652
	// Reference: src/components/AddToCartOptions.vue:1120
	// Reference: src/components/AlertBoxOptions.vue:747
	// Reference: src/components/BulletListOptions.vue:523
	// Reference: src/components/BusinessHoursOptions.vue:768
	// Reference: src/components/ButtonOptions.vue:1292
	// Reference: src/components/ColOptions.vue:676
	// Reference: src/components/CountdownOptions.vue:888
	// Reference: src/components/EDDAddToCartOptions.vue:838
	// Reference: src/components/EDDBuyNowButtonOptions.vue:867
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:452
	// Reference: src/components/EDDDownloadsGridOptions.vue:921
	// Reference: src/components/GoogleMapsOptions.vue:244
	// Reference: src/components/HeaderOptions.vue:547
	// Reference: src/components/IconOptions.vue:285
	// Reference: src/components/ImageOptions.vue:550
	// Reference: src/components/LoginOptions.vue:927
	// Reference: src/components/NavOptions.vue:610
	// Reference: src/components/OptinFormOptions.vue:867
	// Reference: src/components/PostfeaturedimageOptions.vue:453
	// Reference: src/components/PostsOptions.vue:2060
	// Reference: src/components/PriceListOptions.vue:979
	// Reference: src/components/PricingTableOptions.vue:1399
	// Reference: src/components/ProductFeaturedImageOptions.vue:452
	// Reference: src/components/ProductRelatedOptions.vue:491
	// Reference: src/components/ProgressBarOptions.vue:337
	// Reference: src/components/RowOptions.vue:685
	// Reference: src/components/SectionOptions.vue:387
	// Reference: src/components/ShadowControl.vue:138
	// Reference: src/components/SocialProfilesOptions.vue:703
	// Reference: src/components/SocialSharingOptions.vue:421
	// Reference: src/components/StripepaymentOptions.vue:1729
	// Reference: src/components/TeamMemberOptions.vue:1203
	// Reference: src/components/TextOptions.vue:383
	// Reference: src/components/VideoOptions.vue:247
	// Reference: src/components/VideoPopUpOptions.vue:1143
	// Reference: src/components/WCAddToCartOptions.vue:734
	// Reference: src/components/WCCustomProductsGridOptions.vue:1024
	__( 'X Large', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:653
	// Reference: src/components/AddToCartOptions.vue:1121
	// Reference: src/components/AlertBoxOptions.vue:748
	// Reference: src/components/BulletListOptions.vue:524
	// Reference: src/components/BusinessHoursOptions.vue:769
	// Reference: src/components/ButtonOptions.vue:1293
	// Reference: src/components/ColOptions.vue:677
	// Reference: src/components/CountdownOptions.vue:889
	// Reference: src/components/EDDAddToCartOptions.vue:839
	// Reference: src/components/EDDBuyNowButtonOptions.vue:868
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:453
	// Reference: src/components/EDDDownloadsGridOptions.vue:922
	// Reference: src/components/GoogleMapsOptions.vue:245
	// Reference: src/components/HeaderOptions.vue:548
	// Reference: src/components/IconOptions.vue:286
	// Reference: src/components/ImageOptions.vue:551
	// Reference: src/components/LoginOptions.vue:928
	// Reference: src/components/NavOptions.vue:611
	// Reference: src/components/OptinFormOptions.vue:868
	// Reference: src/components/PostfeaturedimageOptions.vue:454
	// Reference: src/components/PostsOptions.vue:2061
	// Reference: src/components/PriceListOptions.vue:980
	// Reference: src/components/PricingTableOptions.vue:1400
	// Reference: src/components/ProductFeaturedImageOptions.vue:453
	// Reference: src/components/ProductRelatedOptions.vue:492
	// Reference: src/components/ProgressBarOptions.vue:338
	// Reference: src/components/RowOptions.vue:686
	// Reference: src/components/SectionOptions.vue:388
	// Reference: src/components/ShadowControl.vue:139
	// Reference: src/components/SocialSharingOptions.vue:422
	// Reference: src/components/StripepaymentOptions.vue:1730
	// Reference: src/components/TeamMemberOptions.vue:1204
	// Reference: src/components/TextOptions.vue:384
	// Reference: src/components/VideoOptions.vue:248
	// Reference: src/components/VideoPopUpOptions.vue:1144
	// Reference: src/components/WCAddToCartOptions.vue:735
	// Reference: src/components/WCCustomProductsGridOptions.vue:1025
	__( '2X Large', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:654
	// Reference: src/components/AdditionalInformationOptions.vue:130
	// Reference: src/components/DividerOptions.vue:395
	// Reference: src/components/EDDCheckoutOptions.vue:683
	// Reference: src/components/FeatureOptions.vue:585
	// Reference: src/components/IconFeatureOptions.vue:572
	__( 'Header Typography', 'coming-soon' ),

	// Reference: src/components/AccordionOptions.vue:697
	__( 'Accordion ', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1074
	// Reference: src/components/ButtonOptions.vue:1240
	// Reference: src/components/EDDAddToCartOptions.vue:790
	// Reference: src/components/EDDBuyNowButtonOptions.vue:819
	// Reference: src/components/EDDDownloadsGridOptions.vue:901
	// Reference: src/components/LoginOptions.vue:904
	// Reference: src/components/OptinFormOptions.vue:858
	// Reference: src/components/ProductRelatedOptions.vue:471
	// Reference: src/components/StripepaymentOptions.vue:1679
	// Reference: src/components/WCAddToCartOptions.vue:692
	// Reference: src/components/WCCustomProductsGridOptions.vue:1004
	// Reference: src/views/GlobalCSS.vue:2250
	__( 'Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1075
	// Reference: src/components/ButtonOptions.vue:1241
	// Reference: src/components/EDDAddToCartOptions.vue:791
	// Reference: src/components/EDDBuyNowButtonOptions.vue:820
	// Reference: src/components/LoginOptions.vue:889
	// Reference: src/components/StripepaymentOptions.vue:1680
	// Reference: src/components/WCAddToCartOptions.vue:693
	__( 'Button Text', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1077
	__( 'Quantity Typography', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1079
	// Reference: src/components/BackgroundControl.vue:492
	// Reference: src/components/BeforeAfterToggleOptions.vue:528
	// Reference: src/components/ButtonOptions.vue:1247
	// Reference: src/components/ContentToggleOptions.vue:592
	// Reference: src/components/CountdownOptions.vue:861
	// Reference: src/components/EDDAddToCartOptions.vue:797
	// Reference: src/components/EDDBuyNowButtonOptions.vue:826
	// Reference: src/components/FeatureOptions.vue:572
	// Reference: src/components/IconFeatureOptions.vue:559
	// Reference: src/components/IconOptions.vue:278
	// Reference: src/components/OptinFormOptions.vue:862
	// Reference: src/components/PostinfoOptions.vue:590
	// Reference: src/components/SocialProfilesOptions.vue:692
	// Reference: src/components/SocialSharingOptions.vue:415
	// Reference: src/components/StarRatingOptions.vue:306
	// Reference: src/components/StripepaymentOptions.vue:1686
	// Reference: src/components/TestimonialOptions.vue:630
	// Reference: src/components/WCAddToCartOptions.vue:699
	__( 'Size', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1080
	// Reference: src/components/ButtonOptions.vue:1248
	// Reference: src/components/EDDAddToCartOptions.vue:798
	// Reference: src/components/EDDBuyNowButtonOptions.vue:827
	// Reference: src/components/StripepaymentOptions.vue:1687
	// Reference: src/components/WCAddToCartOptions.vue:700
	__( 'Vertical Padding', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1081
	// Reference: src/components/ButtonOptions.vue:1249
	// Reference: src/components/EDDAddToCartOptions.vue:799
	// Reference: src/components/EDDBuyNowButtonOptions.vue:828
	// Reference: src/components/StripepaymentOptions.vue:1688
	// Reference: src/components/WCAddToCartOptions.vue:701
	__( 'Horizontal Padding', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1082
	// Reference: src/components/BorderRadiusControl.vue:149
	// Reference: src/components/BorderSectionControl.vue:107
	// Reference: src/components/ButtonOptions.vue:1250
	// Reference: src/components/CountdownOptions.vue:881
	// Reference: src/components/EDDAddToCartOptions.vue:800
	// Reference: src/components/EDDBuyNowButtonOptions.vue:829
	// Reference: src/components/EDDCartOptions.vue:391
	// Reference: src/components/EDDCheckoutOptions.vue:666
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:455
	// Reference: src/components/EDDDownloadsGridOptions.vue:859
	// Reference: src/components/ImageBorderControl.vue:264
	// Reference: src/components/ImageOptions.vue:553
	// Reference: src/components/OptinFormOptions.vue:878
	// Reference: src/components/PostfeaturedimageOptions.vue:456
	// Reference: src/components/PostsOptions.vue:2096
	// Reference: src/components/PricingTableOptions.vue:1315
	// Reference: src/components/ProductDataTabsOptions.vue:339
	// Reference: src/components/ProductFeaturedImageOptions.vue:455
	// Reference: src/components/ProductGalleryImagesOptions.vue:210
	// Reference: src/components/ProductRelatedOptions.vue:457
	// Reference: src/components/ProgressBarOptions.vue:330
	// Reference: src/components/SectionOptions.vue:389
	// Reference: src/components/SocialProfilesOptions.vue:697
	// Reference: src/components/StripepaymentOptions.vue:1689
	// Reference: src/components/WCAddToCartOptions.vue:702
	// Reference: src/components/WCCartOptions.vue:539
	// Reference: src/components/WCCheckoutOptions.vue:808
	// Reference: src/components/WCCustomProductsGridOptions.vue:962
	__( 'Border Radius', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1083
	// Reference: src/components/AlertBoxOptions.vue:729
	// Reference: src/components/ButtonOptions.vue:1251
	// Reference: src/components/EDDAddToCartOptions.vue:801
	// Reference: src/components/EDDBuyNowButtonOptions.vue:830
	// Reference: src/components/HeaderOptions.vue:533
	// Reference: src/components/StripepaymentOptions.vue:1690
	// Reference: src/components/WCAddToCartOptions.vue:703
	__( 'Icons', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1084
	// Reference: src/components/AlertBoxOptions.vue:750
	// Reference: src/components/ButtonOptions.vue:1252
	// Reference: src/components/EDDAddToCartOptions.vue:802
	// Reference: src/components/EDDBuyNowButtonOptions.vue:831
	// Reference: src/components/HeaderOptions.vue:550
	// Reference: src/components/OptinFormOptions.vue:885
	// Reference: src/components/StripepaymentOptions.vue:1691
	// Reference: src/components/WCAddToCartOptions.vue:704
	__( 'Before Text Icon', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1085
	// Reference: src/components/AlertBoxOptions.vue:751
	// Reference: src/components/ButtonOptions.vue:1253
	// Reference: src/components/EDDAddToCartOptions.vue:803
	// Reference: src/components/EDDBuyNowButtonOptions.vue:832
	// Reference: src/components/HeaderOptions.vue:551
	// Reference: src/components/OptinFormOptions.vue:886
	// Reference: src/components/StripepaymentOptions.vue:1692
	// Reference: src/components/WCAddToCartOptions.vue:705
	__( 'After Text Icon', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1086
	// Reference: src/components/AlertBoxOptions.vue:777
	// Reference: src/components/BusinessHoursOptions.vue:717
	// Reference: src/components/ButtonOptions.vue:1254
	// Reference: src/components/CountdownOptions.vue:851
	// Reference: src/components/CounterOptions.vue:327
	// Reference: src/components/EDDAddToCartOptions.vue:804
	// Reference: src/components/EDDBuyNowButtonOptions.vue:833
	// Reference: src/components/EDDCartOptions.vue:359
	// Reference: src/components/EDDCheckoutOptions.vue:636
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:433
	// Reference: src/components/ImageOptions.vue:529
	// Reference: src/components/LoginOptions.vue:930
	// Reference: src/components/OptinFormOptions.vue:851
	// Reference: src/components/PostfeaturedimageOptions.vue:433
	// Reference: src/components/PriceListOptions.vue:901
	// Reference: src/components/ProductFeaturedImageOptions.vue:433
	// Reference: src/components/ProgressBarOptions.vue:323
	// Reference: src/components/SocialProfilesOptions.vue:721
	// Reference: src/components/StripepaymentOptions.vue:1693
	// Reference: src/components/TeamMemberOptions.vue:1086
	// Reference: src/components/WCAddToCartOptions.vue:706
	// Reference: src/components/WCCartOptions.vue:498
	// Reference: src/components/WCCheckoutOptions.vue:767
	__( 'Templates', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1087
	// Reference: src/components/ButtonOptions.vue:1255
	// Reference: src/components/CountdownOptions.vue:862
	// Reference: src/components/OptinFormOptions.vue:863
	// Reference: src/components/StripepaymentOptions.vue:1694
	// Reference: src/components/WCAddToCartOptions.vue:707
	__( 'Tiny', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1089
	// Reference: src/components/ButtonOptions.vue:1257
	// Reference: src/components/EDDAddToCartOptions.vue:807
	// Reference: src/components/EDDBuyNowButtonOptions.vue:836
	// Reference: src/components/StripepaymentOptions.vue:1696
	// Reference: src/components/WCAddToCartOptions.vue:709
	__( 'Pill Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1090
	// Reference: src/components/ButtonOptions.vue:1258
	// Reference: src/components/EDDAddToCartOptions.vue:808
	// Reference: src/components/EDDBuyNowButtonOptions.vue:837
	// Reference: src/components/StripepaymentOptions.vue:1697
	// Reference: src/components/WCAddToCartOptions.vue:710
	__( 'Flat Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1091
	// Reference: src/components/ButtonOptions.vue:1259
	// Reference: src/components/EDDAddToCartOptions.vue:809
	// Reference: src/components/EDDBuyNowButtonOptions.vue:838
	// Reference: src/components/StripepaymentOptions.vue:1698
	// Reference: src/components/WCAddToCartOptions.vue:711
	__( 'Blue Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1092
	// Reference: src/components/ButtonOptions.vue:1260
	// Reference: src/components/EDDAddToCartOptions.vue:810
	// Reference: src/components/EDDBuyNowButtonOptions.vue:839
	// Reference: src/components/StripepaymentOptions.vue:1699
	// Reference: src/components/WCAddToCartOptions.vue:712
	__( 'Light Green Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1093
	// Reference: src/components/ButtonOptions.vue:1261
	// Reference: src/components/EDDAddToCartOptions.vue:811
	// Reference: src/components/EDDBuyNowButtonOptions.vue:840
	// Reference: src/components/StripepaymentOptions.vue:1700
	// Reference: src/components/WCAddToCartOptions.vue:713
	__( 'Green Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1094
	// Reference: src/components/ButtonOptions.vue:1262
	// Reference: src/components/EDDAddToCartOptions.vue:812
	// Reference: src/components/EDDBuyNowButtonOptions.vue:841
	// Reference: src/components/StripepaymentOptions.vue:1701
	// Reference: src/components/WCAddToCartOptions.vue:714
	__( 'Orange Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1095
	// Reference: src/components/ButtonOptions.vue:1263
	// Reference: src/components/EDDAddToCartOptions.vue:813
	// Reference: src/components/EDDBuyNowButtonOptions.vue:842
	// Reference: src/components/StripepaymentOptions.vue:1702
	// Reference: src/components/WCAddToCartOptions.vue:715
	__( 'Red Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1096
	// Reference: src/components/ButtonOptions.vue:1264
	// Reference: src/components/EDDAddToCartOptions.vue:814
	// Reference: src/components/EDDBuyNowButtonOptions.vue:843
	// Reference: src/components/StripepaymentOptions.vue:1703
	// Reference: src/components/WCAddToCartOptions.vue:716
	__( 'Yellow Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1097
	// Reference: src/components/ButtonOptions.vue:1265
	// Reference: src/components/EDDAddToCartOptions.vue:815
	// Reference: src/components/EDDBuyNowButtonOptions.vue:844
	// Reference: src/components/StripepaymentOptions.vue:1704
	// Reference: src/components/WCAddToCartOptions.vue:717
	__( 'White Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1098
	// Reference: src/components/ButtonOptions.vue:1266
	// Reference: src/components/EDDAddToCartOptions.vue:816
	// Reference: src/components/EDDBuyNowButtonOptions.vue:845
	// Reference: src/components/StripepaymentOptions.vue:1705
	// Reference: src/components/WCAddToCartOptions.vue:718
	__( 'Grey Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1099
	// Reference: src/components/ButtonOptions.vue:1267
	// Reference: src/components/EDDAddToCartOptions.vue:817
	// Reference: src/components/EDDBuyNowButtonOptions.vue:846
	// Reference: src/components/StripepaymentOptions.vue:1706
	// Reference: src/components/WCAddToCartOptions.vue:719
	__( 'Black Button', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1100
	// Reference: src/components/ButtonOptions.vue:1268
	// Reference: src/components/EDDAddToCartOptions.vue:818
	// Reference: src/components/EDDBuyNowButtonOptions.vue:847
	// Reference: src/components/EDDCartOptions.vue:381
	// Reference: src/components/EDDCheckoutOptions.vue:664
	// Reference: src/components/EDDDownloadsGridOptions.vue:902
	// Reference: src/components/OptinFormOptions.vue:880
	// Reference: src/components/ProductRelatedOptions.vue:472
	// Reference: src/components/StripepaymentOptions.vue:1707
	// Reference: src/components/WCAddToCartOptions.vue:720
	// Reference: src/components/WCCartOptions.vue:526
	// Reference: src/components/WCCheckoutOptions.vue:795
	// Reference: src/components/WCCustomProductsGridOptions.vue:1005
	__( 'Button Style', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1101
	// Reference: src/components/ButtonOptions.vue:1269
	// Reference: src/components/EDDAddToCartOptions.vue:819
	// Reference: src/components/EDDBuyNowButtonOptions.vue:848
	// Reference: src/components/EDDCartOptions.vue:384
	// Reference: src/components/EDDCheckoutOptions.vue:667
	// Reference: src/components/EDDDownloadsGridOptions.vue:849
	// Reference: src/components/OptinFormOptions.vue:881
	// Reference: src/components/ProductRelatedOptions.vue:458
	// Reference: src/components/StripepaymentOptions.vue:1708
	// Reference: src/components/WCAddToCartOptions.vue:721
	// Reference: src/components/WCCartOptions.vue:529
	// Reference: src/components/WCCheckoutOptions.vue:798
	// Reference: src/components/WCCustomProductsGridOptions.vue:952
	__( 'Flat', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1102
	// Reference: src/components/ButtonOptions.vue:1270
	// Reference: src/components/EDDAddToCartOptions.vue:820
	// Reference: src/components/EDDBuyNowButtonOptions.vue:849
	// Reference: src/components/EDDCartOptions.vue:385
	// Reference: src/components/EDDCheckoutOptions.vue:668
	// Reference: src/components/EDDDownloadsGridOptions.vue:850
	// Reference: src/components/OptinFormOptions.vue:883
	// Reference: src/components/ProductRelatedOptions.vue:459
	// Reference: src/components/StripepaymentOptions.vue:1709
	// Reference: src/components/WCAddToCartOptions.vue:722
	// Reference: src/components/WCCartOptions.vue:530
	// Reference: src/components/WCCheckoutOptions.vue:799
	// Reference: src/components/WCCustomProductsGridOptions.vue:953
	__( '2D', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1103
	// Reference: src/components/ButtonOptions.vue:1271
	// Reference: src/components/EDDAddToCartOptions.vue:821
	// Reference: src/components/EDDBuyNowButtonOptions.vue:850
	// Reference: src/components/EDDCartOptions.vue:386
	// Reference: src/components/EDDCheckoutOptions.vue:669
	// Reference: src/components/EDDDownloadsGridOptions.vue:851
	// Reference: src/components/OptinFormOptions.vue:884
	// Reference: src/components/ProductRelatedOptions.vue:460
	// Reference: src/components/StripepaymentOptions.vue:1710
	// Reference: src/components/WCAddToCartOptions.vue:723
	// Reference: src/components/WCCartOptions.vue:531
	// Reference: src/components/WCCheckoutOptions.vue:800
	// Reference: src/components/WCCustomProductsGridOptions.vue:954
	__( 'Vintage', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1104
	// Reference: src/components/ButtonOptions.vue:1272
	// Reference: src/components/EDDAddToCartOptions.vue:822
	// Reference: src/components/EDDBuyNowButtonOptions.vue:851
	// Reference: src/components/EDDCartOptions.vue:387
	// Reference: src/components/EDDCheckoutOptions.vue:670
	// Reference: src/components/EDDDownloadsGridOptions.vue:852
	// Reference: src/components/OptinFormOptions.vue:882
	// Reference: src/components/ProductRelatedOptions.vue:461
	// Reference: src/components/StripepaymentOptions.vue:1711
	// Reference: src/components/WCAddToCartOptions.vue:724
	// Reference: src/components/WCCartOptions.vue:532
	// Reference: src/components/WCCheckoutOptions.vue:801
	// Reference: src/components/WCCustomProductsGridOptions.vue:955
	__( 'Ghost', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1108
	// Reference: src/components/ButtonOptions.vue:1276
	// Reference: src/components/LoginOptions.vue:896
	// Reference: src/components/StripepaymentOptions.vue:1715
	// Reference: src/views/GlobalCSS.vue:2252
	__( 'Button Text Color', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1109
	// Reference: src/components/ButtonOptions.vue:1277
	// Reference: src/components/StripepaymentOptions.vue:1716
	// Reference: src/views/GlobalCSS.vue:2257
	__( 'Button Background Style', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1110
	// Reference: src/components/BackgroundControl.vue:446
	// Reference: src/components/BorderSectionControl.vue:109
	// Reference: src/components/BusinessHoursOptions.vue:750
	// Reference: src/components/ButtonOptions.vue:1278
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:457
	// Reference: src/components/EDDDownloadsGridOptions.vue:913
	// Reference: src/components/ImageBorderControl.vue:266
	// Reference: src/components/ImageOptions.vue:555
	// Reference: src/components/PostfeaturedimageOptions.vue:458
	// Reference: src/components/PostsOptions.vue:2052
	// Reference: src/components/PriceListOptions.vue:941
	// Reference: src/components/PricingTableOptions.vue:1371
	// Reference: src/components/ProductFeaturedImageOptions.vue:457
	// Reference: src/components/ProductRelatedOptions.vue:483
	// Reference: src/components/SectionOptions.vue:391
	// Reference: src/components/StripepaymentOptions.vue:1717
	// Reference: src/components/TeamMemberOptions.vue:1151
	// Reference: src/components/WCCustomProductsGridOptions.vue:1016
	// Reference: src/views/GlobalCSS.vue:2255
	__( 'Solid', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1111
	// Reference: src/components/BackgroundControl.vue:447
	// Reference: src/components/ButtonOptions.vue:1279
	// Reference: src/components/StripepaymentOptions.vue:1718
	// Reference: src/views/GlobalCSS.vue:2268
	__( 'Gradient', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1112
	// Reference: src/components/ButtonOptions.vue:1282
	// Reference: src/components/ProductDataTabsOptions.vue:333
	// Reference: src/components/ProductGalleryImagesOptions.vue:204
	// Reference: src/components/StripepaymentOptions.vue:1721
	// Reference: src/components/TypographyControl.vue:327
	// Reference: src/views/GlobalCSS.vue:2223
	__( 'Normal', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1113
	// Reference: src/components/ButtonOptions.vue:1283
	// Reference: src/components/StripepaymentOptions.vue:1722
	// Reference: src/views/GlobalCSS.vue:2224
	__( 'Hover', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1122
	// Reference: src/components/ButtonOptions.vue:1294
	// Reference: src/components/DynamicTagsControl.vue:960
	// Reference: src/components/EDDAddToCartOptions.vue:840
	// Reference: src/components/EDDBuyNowButtonOptions.vue:869
	// Reference: src/components/EDDCartOptions.vue:388
	// Reference: src/components/EDDCheckoutOptions.vue:671
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:463
	// Reference: src/components/EDDDownloadsGridOptions.vue:853
	// Reference: src/components/HeaderOptions.vue:556
	// Reference: src/components/IconOptions.vue:290
	// Reference: src/components/ImageOptions.vue:561
	// Reference: src/components/PostauthorboxOptions.vue:354
	// Reference: src/components/PostfeaturedimageOptions.vue:464
	// Reference: src/components/PostinfoOptions.vue:591
	// Reference: src/components/PriceListOptions.vue:927
	// Reference: src/components/ProductFeaturedImageOptions.vue:463
	// Reference: src/components/ProductRelatedOptions.vue:462
	// Reference: src/components/StripepaymentOptions.vue:1731
	// Reference: src/components/TeamMemberOptions.vue:1135
	// Reference: src/components/WCAddToCartOptions.vue:736
	// Reference: src/components/WCCartOptions.vue:533
	// Reference: src/components/WCCheckoutOptions.vue:802
	// Reference: src/components/WCCustomProductsGridOptions.vue:956
	// Reference: src/views/GlobalCSS.vue:2222
	// Reference: src/views/SettingsGeneral.vue:266
	__( 'Link', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1123
	// Reference: src/components/BackgroundControl.vue:448
	// Reference: src/components/ButtonOptions.vue:1295
	// Reference: src/components/StripepaymentOptions.vue:1737
	// Reference: src/views/GlobalCSS.vue:2269
	__( 'Gradient Type', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1124
	// Reference: src/components/BackgroundControl.vue:449
	// Reference: src/components/ButtonOptions.vue:1296
	// Reference: src/components/StripepaymentOptions.vue:1738
	// Reference: src/views/GlobalCSS.vue:2270
	__( 'Radial', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1125
	// Reference: src/components/BackgroundControl.vue:450
	// Reference: src/components/ButtonOptions.vue:1297
	// Reference: src/components/StripepaymentOptions.vue:1739
	// Reference: src/views/GlobalCSS.vue:2271
	__( 'Linear', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1126
	// Reference: src/components/BackgroundControl.vue:451
	// Reference: src/components/ButtonOptions.vue:1298
	// Reference: src/components/StripepaymentOptions.vue:1740
	// Reference: src/views/GlobalCSS.vue:2272
	__( 'Angle', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1127
	// Reference: src/components/BackgroundControl.vue:481
	// Reference: src/components/ButtonOptions.vue:1299
	// Reference: src/components/DisplaySectionControl.vue:786
	// Reference: src/components/ShadowControl.vue:146
	// Reference: src/components/StripepaymentOptions.vue:1741
	// Reference: src/components/TeamMemberOptions.vue:1105
	// Reference: src/views/GlobalCSS.vue:2273
	__( 'Position', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1128
	// Reference: src/components/BackgroundControl.vue:453
	// Reference: src/components/ButtonOptions.vue:1300
	// Reference: src/components/StripepaymentOptions.vue:1742
	// Reference: src/views/GlobalCSS.vue:2274
	__( 'Top Center', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1129
	// Reference: src/components/BackgroundControl.vue:454
	// Reference: src/components/ButtonOptions.vue:1301
	// Reference: src/components/ParticlesBackgroundControl.vue:315
	// Reference: src/components/PostsOptions.vue:2071
	// Reference: src/components/StripepaymentOptions.vue:1743
	// Reference: src/views/GlobalCSS.vue:2275
	__( 'Top Left', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1130
	// Reference: src/components/BackgroundControl.vue:455
	// Reference: src/components/ButtonOptions.vue:1302
	// Reference: src/components/ParticlesBackgroundControl.vue:316
	// Reference: src/components/PostsOptions.vue:2070
	// Reference: src/components/StripepaymentOptions.vue:1744
	// Reference: src/views/GlobalCSS.vue:2276
	__( 'Top Right', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1131
	// Reference: src/components/BackgroundControl.vue:456
	// Reference: src/components/ButtonOptions.vue:1303
	// Reference: src/components/StripepaymentOptions.vue:1745
	// Reference: src/views/GlobalCSS.vue:2277
	__( 'Center Center', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1132
	// Reference: src/components/BackgroundControl.vue:457
	// Reference: src/components/ButtonOptions.vue:1304
	// Reference: src/components/StripepaymentOptions.vue:1746
	// Reference: src/views/GlobalCSS.vue:2278
	__( 'Center Left', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1133
	// Reference: src/components/BackgroundControl.vue:458
	// Reference: src/components/ButtonOptions.vue:1305
	// Reference: src/components/StripepaymentOptions.vue:1747
	// Reference: src/views/GlobalCSS.vue:2279
	__( 'Center Right', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1134
	// Reference: src/components/BackgroundControl.vue:459
	// Reference: src/components/ButtonOptions.vue:1306
	// Reference: src/components/StripepaymentOptions.vue:1748
	// Reference: src/views/GlobalCSS.vue:2280
	__( 'Bottom Center', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1135
	// Reference: src/components/BackgroundControl.vue:460
	// Reference: src/components/ButtonOptions.vue:1307
	// Reference: src/components/ParticlesBackgroundControl.vue:317
	// Reference: src/components/PostsOptions.vue:2073
	// Reference: src/components/StripepaymentOptions.vue:1749
	// Reference: src/views/GlobalCSS.vue:2281
	__( 'Bottom Left', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1136
	// Reference: src/components/BackgroundControl.vue:461
	// Reference: src/components/ButtonOptions.vue:1308
	// Reference: src/components/ParticlesBackgroundControl.vue:318
	// Reference: src/components/PostsOptions.vue:2072
	// Reference: src/components/StripepaymentOptions.vue:1750
	// Reference: src/views/GlobalCSS.vue:2282
	__( 'Bottom Right', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1137
	// Reference: src/components/BackgroundControl.vue:462
	// Reference: src/components/ButtonOptions.vue:1309
	// Reference: src/components/StripepaymentOptions.vue:1751
	// Reference: src/views/GlobalCSS.vue:2283
	__( 'First Color Location', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1138
	// Reference: src/components/BackgroundControl.vue:463
	// Reference: src/components/ButtonOptions.vue:1310
	// Reference: src/components/StripepaymentOptions.vue:1752
	// Reference: src/views/GlobalCSS.vue:2284
	__( 'Second Color Location', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1139
	// Reference: src/components/BackgroundControl.vue:471
	// Reference: src/components/ButtonOptions.vue:1311
	// Reference: src/components/StripepaymentOptions.vue:1753
	// Reference: src/views/GlobalCSS.vue:2285
	__( 'First Color', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1140
	// Reference: src/components/BackgroundControl.vue:472
	// Reference: src/components/ButtonOptions.vue:1312
	// Reference: src/components/StripepaymentOptions.vue:1754
	// Reference: src/views/GlobalCSS.vue:2286
	__( 'Second Color', 'coming-soon' ),

	// Reference: src/components/AddToCartOptions.vue:1141
	// Reference: src/components/BackgroundControl.vue:496
	// Reference: src/components/ButtonOptions.vue:1313
	// Reference: src/components/DynamicTagsControl.vue:973
	// Reference: src/components/ParticlesBackgroundControl.vue:307
	// Reference: src/components/PostinfoOptions.vue:606
	// Reference: src/components/PostsOptions.vue:1954
	// Reference: src/components/PriceListOptions.vue:968
	// Reference: src/components/PricingTableOptions.vue:1335
	// Reference: src/components/ProductMetaOptions.vue:340
	// Reference: src/components/ShadowControl.vue:140
	// Reference: src/components/SocialProfilesOptions.vue:717
	// Reference: src/components/StripepaymentOptions.vue:1755
	// Reference: src/components/TeamMemberOptions.vue:1180
	// Reference: src/components/VideoOptions.vue:237
	// Reference: src/components/VideoPopUpOptions.vue:1133
	__( 'Custom', 'coming-soon' ),

	// Reference: src/components/AdditionalInformationOptions.vue:127
	// Reference: src/components/PricingTableOptions.vue:1298
	__( 'Header', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:727
	__( 'Alert', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:730
	// Reference: src/components/CounterOptions.vue:337
	// Reference: src/components/DynamicTagsControl.vue:1004
	// Reference: src/components/EDDDownloadsGridOptions.vue:939
	// Reference: src/components/PostsOptions.vue:1972
	// Reference: src/components/PriceListOptions.vue:933
	// Reference: src/components/ProductRelatedOptions.vue:509
	// Reference: src/components/TeamMemberOptions.vue:1134
	// Reference: src/components/TestimonialOptions.vue:641
	// Reference: src/components/WCCustomProductsGridOptions.vue:1042
	__( 'Title', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:731
	// Reference: src/components/DynamicTagsControl.vue:1007
	// Reference: src/components/EDDDownloadsGridOptions.vue:898
	// Reference: src/components/PriceListOptions.vue:932
	// Reference: src/components/PricingTableOptions.vue:1341
	// Reference: src/components/TeamMemberOptions.vue:1182
	// Reference: src/components/WCCustomProductsGridOptions.vue:1001
	__( 'Description', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:732
	__( 'Title Font Size', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:733
	// Reference: src/components/CounterOptions.vue:344
	__( 'Title Align', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:734
	// Reference: src/components/TeamMemberOptions.vue:1171
	__( 'Description Align', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:735
	// Reference: src/components/PostsOptions.vue:2002
	// Reference: src/components/TeamMemberOptions.vue:1183
	__( 'Title Tag', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:736
	__( 'Description Font Size', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:737
	// Reference: src/components/HeaderOptions.vue:537
	__( 'h1', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:738
	// Reference: src/components/HeaderOptions.vue:538
	__( 'h2', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:739
	// Reference: src/components/HeaderOptions.vue:539
	__( 'h3', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:740
	// Reference: src/components/HeaderOptions.vue:540
	__( 'h4', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:752
	// Reference: src/components/BeforeAfterToggleOptions.vue:534
	// Reference: src/components/ContentToggleOptions.vue:601
	// Reference: src/components/DividerOptions.vue:379
	// Reference: src/components/EDDDownloadPriceOptions.vue:105
	// Reference: src/components/FeatureOptions.vue:577
	// Reference: src/components/HeaderOptions.vue:552
	// Reference: src/components/IconFeatureOptions.vue:565
	// Reference: src/components/IconOptions.vue:287
	// Reference: src/components/MenuCartOptions.vue:189
	// Reference: src/components/ParticlesBackgroundControl.vue:325
	// Reference: src/components/PostnavigationOptions.vue:97
	// Reference: src/components/PostsOptions.vue:2097
	// Reference: src/components/PriceListOptions.vue:935
	// Reference: src/components/ProductPriceOptions.vue:185
	// Reference: src/components/ProgressBarOptions.vue:340
	// Reference: src/components/ShadowControl.vue:141
	// Reference: src/components/ShapeDividerControl.vue:369
	// Reference: src/components/SocialProfilesOptions.vue:718
	// Reference: src/components/TeamMemberOptions.vue:1111
	// Reference: src/components/TestimonialOptions.vue:632
	// Reference: src/components/TextOptions.vue:387
	__( 'Color', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:754
	// Reference: src/components/EDDCheckoutOptions.vue:654
	// Reference: src/components/EDDDownloadsGridOptions.vue:972
	// Reference: src/components/PriceListOptions.vue:931
	// Reference: src/components/PricingTableOptions.vue:1350
	// Reference: src/components/TeamMemberOptions.vue:1140
	__( 'Description Color', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:755
	__( 'Title Background', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:756
	__( 'Dismiss Color', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:757
	// Reference: src/components/DividerOptions.vue:390
	// Reference: src/components/HeaderOptions.vue:553
	// Reference: src/components/PostauthorboxOptions.vue:349
	// Reference: src/components/PostsOptions.vue:2007
	// Reference: src/components/TeamMemberOptions.vue:1188
	__( 'H5', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:758
	// Reference: src/components/DividerOptions.vue:391
	// Reference: src/components/HeaderOptions.vue:554
	// Reference: src/components/PostauthorboxOptions.vue:350
	// Reference: src/components/PostsOptions.vue:2008
	// Reference: src/components/TeamMemberOptions.vue:1189
	__( 'H6', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:760
	__( 'Show Icon', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:761
	// Reference: src/components/BeforeAfterToggleOptions.vue:519
	// Reference: src/components/ContentToggleOptions.vue:576
	// Reference: src/components/DynamicTagsControl.vue:987
	// Reference: src/components/EDDDownloadsGridOptions.vue:874
	// Reference: src/components/FeatureOptions.vue:568
	// Reference: src/components/IconFeatureOptions.vue:555
	// Reference: src/components/PostinfoOptions.vue:596
	// Reference: src/components/ProductMetaOptions.vue:335
	// Reference: src/components/ShapeDividerControl.vue:349
	// Reference: src/components/TestimonialOptions.vue:626
	// Reference: src/components/VideoOptions.vue:235
	// Reference: src/components/WCCustomProductsGridOptions.vue:977
	__( 'Type', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:762
	__( 'Info', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:763
	__( 'Success', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:764
	// Reference: src/views/SettingsGeneral.vue:285
	__( 'Warning', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:765
	__( 'Danger', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:766
	__( 'Dismiss Button', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:767
	// Reference: src/components/ContactFormOptions.vue:301
	// Reference: src/components/EnviraGalleryOptions.vue:222
	// Reference: src/components/GiveawayOptions.vue:173
	// Reference: src/components/MypaykitOptions.vue:129
	// Reference: src/views/SettingsGeneral.vue:267
	__( 'Show', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:768
	// Reference: src/components/ContactFormOptions.vue:302
	// Reference: src/components/EnviraGalleryOptions.vue:223
	// Reference: src/components/GiveawayOptions.vue:174
	// Reference: src/components/MypaykitOptions.vue:130
	__( 'Hide', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:771
	// Reference: src/components/BusinessHoursOptions.vue:749
	// Reference: src/components/DividerOptions.vue:365
	// Reference: src/components/ParticlesBackgroundControl.vue:300
	// Reference: src/components/PriceListOptions.vue:940
	// Reference: src/components/SocialProfilesOptions.vue:689
	// Reference: src/components/TeamMemberOptions.vue:1150
	// Reference: src/components/TypographyControl.vue:324
	__( 'Style', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:772
	__( 'Style 1', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:773
	__( 'Style 2', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:774
	__( 'Style 3', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:775
	__( 'Style 4', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:780
	// Reference: src/components/PostinfoOptions.vue:633
	// Reference: src/components/ProductMetaOptions.vue:350
	// Reference: src/components/WpWidgetBlockOptions.vue:223
	__( 'Text Typography', 'coming-soon' ),

	// Reference: src/components/AlertBoxOptions.vue:781
	// Reference: src/components/DynamicTextControl.vue:314
	// Reference: src/components/HeaderOptions.vue:559
	// Reference: src/components/PostcommentsOptions.vue:190
	// Reference: src/components/TextOptions.vue:388
	__( 'Insert Dynamic Text', 'coming-soon' ),

	// Reference: src/components/AlignControl.vue:499
	// Reference: src/components/BeforeAfterToggleOptions.vue:529
	// Reference: src/components/ContentToggleOptions.vue:593
	__( 'Align', 'coming-soon' ),

	// Reference: src/components/AnchorOptions.vue:75
	__( 'Anchor', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:264
	__( 'Animation Effects', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:265
	__( 'Scrolling Effect', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:266
	__( 'Mouse Effect', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:267
	__( 'Vertical Scroll', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:269
	__( 'Speed', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:270
	__( 'Viewport', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:271
	__( 'Viewport Top %', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:274
	__( 'Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:275
	__( 'Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:276
	__( 'Horizontal Scroll', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:277
	__( 'Direction', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:278
	__( 'To Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:279
	__( 'To Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:280
	__( 'Transparency', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:282
	__( 'Fade Out', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:283
	// Reference: src/components/BeforeAfterToggleOptions.vue:531
	// Reference: src/components/ContentToggleOptions.vue:595
	// Reference: src/components/FeatureOptions.vue:574
	// Reference: src/components/HeaderOptions.vue:536
	// Reference: src/components/IconFeatureOptions.vue:562
	__( 'Level', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:284
	// Reference: src/components/ShadowControl.vue:144
	__( 'Blur', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:285
	__( 'Rotate', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:286
	// Reference: src/components/StarRatingOptions.vue:303
	__( 'Scale', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:287
	__( 'Scale Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:288
	__( 'Scale Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:289
	__( 'Mouse Track', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:290
	__( 'Opposite', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:291
	// Reference: src/components/OpenAIControl.vue:829
	__( 'Direct', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:292
	__( '3D Tilt', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:293
	__( 'Entrance Animation', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:294
	__( 'Select Animation', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:295
	__( 'Fading', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:296
	__( 'Fade In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:297
	__( 'Fade In Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:298
	__( 'Fade In Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:299
	__( 'Fade In Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:300
	__( 'Fade In Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:301
	__( 'Zooming', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:302
	__( 'Zoom In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:303
	__( 'Zoom In Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:304
	__( 'Zoom In Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:305
	__( 'Zoom In Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:306
	__( 'Zoom In Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:307
	__( 'Rotating', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:308
	__( 'Rotate In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:309
	__( 'Rotate In Down Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:310
	__( 'Rotate In Down Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:311
	__( 'Rotate In Up Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:312
	__( 'Rotate In Up Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:314
	__( 'Attention Seekers', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:315
	__( 'Bounce', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:316
	__( 'Flash', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:317
	__( 'Pulse', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:318
	__( 'Rubber Band', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:319
	__( 'Shake X', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:320
	__( 'Shake Y', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:321
	__( 'Head Shake', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:322
	__( 'Swing', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:323
	__( 'Tada', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:324
	__( 'Wobble', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:325
	__( 'Jello', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:326
	__( 'Heart Beat', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:328
	__( 'Back In Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:329
	__( 'Back In Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:330
	__( 'Back In Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:331
	__( 'Back In Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:334
	__( 'Bounce In Down', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:335
	__( 'Bounce In Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:336
	__( 'Bounce In Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:337
	__( 'Bounce In Up', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:339
	__( 'Fade In Down Big', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:340
	__( 'Fade In Left Big', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:341
	__( 'Fade In Right Big', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:342
	__( 'Fade In Up Big', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:343
	__( 'Fade In Top Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:344
	__( 'Fade In Top Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:345
	__( 'Fade In Bottom Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:346
	__( 'Fade In Bottom Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:348
	// Reference: src/components/ShapeDividerControl.vue:372
	__( 'Flip', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:349
	__( 'Flip In X', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:350
	__( 'Flip In Y', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:351
	__( 'Light Speed In Left', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:352
	__( 'Light Speed In Right', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:354
	__( 'Roll In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:356
	__( 'Back In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:357
	__( 'Bounce In', 'coming-soon' ),

	// Reference: src/components/AnimationEffectControl.vue:358
	__( 'Note: In the preview the animation is applied when you select it. On the live page the animation will be applied when the element is scrolled into view.', 'coming-soon' ),

	// Reference: src/components/AttributeSectionControl.vue:93
	__( 'Attributes', 'coming-soon' ),

	// Reference: src/components/AttributeSectionControl.vue:94
	__( 'Custom Class', 'coming-soon' ),

	// Reference: src/components/AttributeSectionControl.vue:95
	__( 'CSS ID', 'coming-soon' ),

	// Reference: src/components/AttributeSectionControl.vue:96
	__( 'Custom Attributes', 'coming-soon' ),

	// Reference: src/components/AttributeSectionControl.vue:97
	__( 'Add custom attributes to the wrapper element. Enter each attribute on a new line. Use the | character to separate the attribute key and value.', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:445
	__( 'Background Style', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:464
	__( 'Background Position', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:465
	__( 'Full Screen Cover - Fixed', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:466
	__( '100% Width Top', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:468
	__( 'Repeat Horizontal Top', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:469
	__( 'Repeat Horizontal Bottom', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:470
	__( 'Repeat Vertical Center', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:474
	__( '100% Width Bottom', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:476
	__( 'Custom Position', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:477
	__( 'Background Image', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:478
	__( 'Full Screen Contain - Fixed', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:479
	__( 'Full Screen Cover', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:480
	__( 'Full Screen Contain', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:482
	__( 'X Position', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:483
	__( 'Y Position', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:484
	__( 'Attachment', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:486
	// Reference: src/components/DisplaySectionControl.vue:800
	__( 'Scroll', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:487
	// Reference: src/components/DisplaySectionControl.vue:789
	__( 'Fixed', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:488
	__( 'Repeat', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:489
	__( 'No-repeat', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:490
	__( 'Repeat-x', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:491
	__( 'Repeat-y', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:493
	// Reference: src/components/DisplaySectionControl.vue:801
	// Reference: src/components/VideoPopUpOptions.vue:1162
	__( 'Auto', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:494
	// Reference: src/components/ImageOptions.vue:571
	__( 'Cover', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:495
	// Reference: src/components/ImageOptions.vue:572
	__( 'Contain', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:497
	// Reference: src/components/DividerOptions.vue:370
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:436
	// Reference: src/components/FeatureOptions.vue:582
	// Reference: src/components/IconFeatureOptions.vue:569
	// Reference: src/components/ImageOptions.vue:532
	// Reference: src/components/OptinFormOptions.vue:860
	// Reference: src/components/PostfeaturedimageOptions.vue:436
	// Reference: src/components/PriceListOptions.vue:969
	// Reference: src/components/ProductFeaturedImageOptions.vue:436
	// Reference: src/components/RowOptions.vue:677
	// Reference: src/components/ShapeDividerControl.vue:370
	// Reference: src/components/TeamMemberOptions.vue:1119
	// Reference: src/components/VideoOptions.vue:240
	// Reference: src/components/VideoPopUpOptions.vue:1136
	__( 'Width', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:498
	// Reference: src/components/ColOptions.vue:703
	// Reference: src/components/RowOptions.vue:713
	// Reference: src/components/SectionOptions.vue:402
	// Reference: src/views/GlobalCSS.vue:2164
	// Reference: src/views/SetupDesign-Lite.vue:733
	__( 'Dim Background', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:499
	// Reference: src/components/BeforeAfterToggleOptions.vue:543
	// Reference: src/components/ColOptions.vue:704
	// Reference: src/components/ContentToggleOptions.vue:613
	// Reference: src/components/RowOptions.vue:714
	// Reference: src/components/SectionOptions.vue:403
	__( 'Overlay Color', 'coming-soon' ),

	// Reference: src/components/BackgroundControl.vue:500
	// Reference: src/components/BeforeAfterToggleOptions.vue:525
	// Reference: src/components/FeatureOptions.vue:587
	// Reference: src/components/ImageOptions.vue:542
	// Reference: src/components/PriceListOptions.vue:928
	// Reference: src/components/SocialSharingOptions.vue:414
	// Reference: src/components/TeamMemberOptions.vue:1100
	// Reference: src/components/TestimonialOptions.vue:638
	// Reference: src/components/VideoPopUpOptions.vue:1211
	__( 'Image Src', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:520
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:435
	// Reference: src/components/EDDDownloadsGridOptions.vue:910
	// Reference: src/components/FeatureOptions.vue:569
	// Reference: src/components/IconFeatureOptions.vue:556
	// Reference: src/components/ImageOptions.vue:531
	// Reference: src/components/PostfeaturedimageOptions.vue:435
	// Reference: src/components/PostinfoOptions.vue:613
	// Reference: src/components/PostsOptions.vue:2049
	// Reference: src/components/PriceListOptions.vue:926
	// Reference: src/components/ProductFeaturedImageOptions.vue:435
	// Reference: src/components/ProductMetaOptions.vue:336
	// Reference: src/components/ProductRelatedOptions.vue:480
	// Reference: src/components/SocialSharingOptions.vue:413
	// Reference: src/components/TeamMemberOptions.vue:1092
	// Reference: src/components/TestimonialOptions.vue:637
	// Reference: src/components/WCCustomProductsGridOptions.vue:1013
	__( 'Image', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:521
	__( 'Before Image', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:522
	__( 'After Image', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:523
	// Reference: src/components/ContentToggleOptions.vue:584
	__( 'Before Label', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:524
	// Reference: src/components/ContentToggleOptions.vue:585
	__( 'After Label', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:527
	// Reference: src/components/ContentToggleOptions.vue:591
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:444
	// Reference: src/components/FeatureOptions.vue:571
	// Reference: src/components/IconFeatureOptions.vue:558
	// Reference: src/components/ImageOptions.vue:541
	// Reference: src/components/PostfeaturedimageOptions.vue:445
	// Reference: src/components/ProductFeaturedImageOptions.vue:444
	// Reference: src/components/TeamMemberOptions.vue:1101
	// Reference: src/components/VideoPopUpOptions.vue:1168
	__( 'Image Size', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:530
	// Reference: src/components/ContentToggleOptions.vue:594
	// Reference: src/components/FeatureOptions.vue:573
	// Reference: src/components/IconFeatureOptions.vue:561
	__( 'Header Text', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:535
	// Reference: src/components/CountdownOptions.vue:892
	// Reference: src/components/EDDCartOptions.vue:378
	// Reference: src/components/EDDCheckoutOptions.vue:661
	// Reference: src/components/EDDDownloadsGridOptions.vue:843
	// Reference: src/components/WCCartOptions.vue:523
	// Reference: src/components/WCCheckoutOptions.vue:792
	// Reference: src/components/WCCustomProductsGridOptions.vue:946
	// Reference: src/views/GlobalCSS.vue:2230
	__( 'Label Color', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:538
	// Reference: src/components/ContentToggleOptions.vue:608
	__( 'Orientation', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:539
	// Reference: src/components/ContentToggleOptions.vue:609
	__( 'Slider Orientation', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:542
	// Reference: src/components/ContentToggleOptions.vue:612
	__( 'Move on Hover', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:544
	// Reference: src/components/ContentToggleOptions.vue:614
	__( 'Comparison Handle', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:545
	// Reference: src/components/ContentToggleOptions.vue:615
	__( 'Handle Initial Offset', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:546
	// Reference: src/components/ContentToggleOptions.vue:616
	__( 'Handle Color', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:549
	// Reference: src/components/ContentToggleOptions.vue:619
	__( 'Handle Thickness', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:550
	// Reference: src/components/ContentToggleOptions.vue:620
	__( 'Circle Width', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:551
	// Reference: src/components/ContentToggleOptions.vue:621
	__( 'Circle Radius', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:552
	// Reference: src/components/ContentToggleOptions.vue:622
	__( 'Triangle Size', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:553
	__( 'Before / After Label Styles', 'coming-soon' ),

	// Reference: src/components/BeforeAfterToggleOptions.vue:554
	// Reference: src/components/EDDCheckoutOptions.vue:686
	// Reference: src/components/WCCheckoutOptions.vue:815
	// Reference: src/views/GlobalCSS.vue:2266
	__( 'Label Typography', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:106
	// Reference: src/components/PostsOptions.vue:2023
	// Reference: src/components/SectionOptions.vue:400
	__( 'Border', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:108
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:456
	// Reference: src/components/EDDDownloadsGridOptions.vue:912
	// Reference: src/components/ImageBorderControl.vue:265
	// Reference: src/components/ImageOptions.vue:554
	// Reference: src/components/PostfeaturedimageOptions.vue:457
	// Reference: src/components/PostsOptions.vue:2051
	// Reference: src/components/PricingTableOptions.vue:1370
	// Reference: src/components/ProductFeaturedImageOptions.vue:456
	// Reference: src/components/ProductRelatedOptions.vue:482
	// Reference: src/components/SectionOptions.vue:390
	// Reference: src/components/WCCustomProductsGridOptions.vue:1015
	__( 'Border Style', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:110
	// Reference: src/components/BusinessHoursOptions.vue:751
	// Reference: src/components/DividerOptions.vue:367
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:458
	// Reference: src/components/EDDDownloadsGridOptions.vue:914
	// Reference: src/components/ImageBorderControl.vue:267
	// Reference: src/components/ImageOptions.vue:556
	// Reference: src/components/PostfeaturedimageOptions.vue:459
	// Reference: src/components/PostsOptions.vue:2053
	// Reference: src/components/PriceListOptions.vue:942
	// Reference: src/components/PricingTableOptions.vue:1372
	// Reference: src/components/ProductFeaturedImageOptions.vue:458
	// Reference: src/components/ProductRelatedOptions.vue:484
	// Reference: src/components/SectionOptions.vue:392
	// Reference: src/components/TeamMemberOptions.vue:1152
	// Reference: src/components/WCCustomProductsGridOptions.vue:1017
	// Reference: src/views/GlobalCSS.vue:2237
	__( 'Dotted', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:111
	// Reference: src/components/BusinessHoursOptions.vue:752
	// Reference: src/components/DividerOptions.vue:368
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:459
	// Reference: src/components/EDDDownloadsGridOptions.vue:915
	// Reference: src/components/ImageBorderControl.vue:268
	// Reference: src/components/ImageOptions.vue:557
	// Reference: src/components/PostfeaturedimageOptions.vue:460
	// Reference: src/components/PostsOptions.vue:2054
	// Reference: src/components/PriceListOptions.vue:943
	// Reference: src/components/PricingTableOptions.vue:1373
	// Reference: src/components/ProductFeaturedImageOptions.vue:459
	// Reference: src/components/ProductRelatedOptions.vue:485
	// Reference: src/components/SectionOptions.vue:393
	// Reference: src/components/TeamMemberOptions.vue:1153
	// Reference: src/components/WCCustomProductsGridOptions.vue:1018
	// Reference: src/views/GlobalCSS.vue:2238
	__( 'Dashed', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:112
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:462
	// Reference: src/components/ImageBorderControl.vue:271
	// Reference: src/components/ImageOptions.vue:560
	// Reference: src/components/PostfeaturedimageOptions.vue:463
	// Reference: src/components/PostsOptions.vue:2039
	// Reference: src/components/PricingTableOptions.vue:1374
	// Reference: src/components/ProductDataTabsOptions.vue:337
	// Reference: src/components/ProductFeaturedImageOptions.vue:462
	// Reference: src/components/ProductGalleryImagesOptions.vue:208
	// Reference: src/components/SectionOptions.vue:395
	__( 'Border Color', 'coming-soon' ),

	// Reference: src/components/BorderSectionControl.vue:113
	// Reference: src/components/BorderWidthControl.vue:141
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:460
	// Reference: src/components/ImageBorderControl.vue:269
	// Reference: src/components/ImageOptions.vue:558
	// Reference: src/components/PostfeaturedimageOptions.vue:461
	// Reference: src/components/PostsOptions.vue:2040
	// Reference: src/components/PricingTableOptions.vue:1375
	// Reference: src/components/ProductDataTabsOptions.vue:346
	// Reference: src/components/ProductFeaturedImageOptions.vue:460
	// Reference: src/components/ProductGalleryImagesOptions.vue:217
	__( 'Border Width', 'coming-soon' ),

	// Reference: src/components/BulletListOptions.vue:502
	__( 'List', 'coming-soon' ),

	// Reference: src/components/BulletListOptions.vue:515
	// Reference: src/components/BusinessHoursOptions.vue:740
	// Reference: src/components/DividerOptions.vue:394
	// Reference: src/components/PostinfoOptions.vue:619
	// Reference: src/components/PriceListOptions.vue:930
	// Reference: src/components/PricingTableOptions.vue:1354
	// Reference: src/components/ProductGalleryImagesOptions.vue:224
	// Reference: src/components/SocialProfilesOptions.vue:704
	// Reference: src/components/TeamMemberOptions.vue:1144
	// Reference: src/components/VideoPopUpOptions.vue:1190
	__( 'Icon Color', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:718
	// Reference: src/components/TeamMemberOptions.vue:1087
	__( 'Striped Effect Template', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:719
	// Reference: src/components/TeamMemberOptions.vue:1088
	__( 'Divider Template', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:720
	// Reference: src/components/TeamMemberOptions.vue:1089
	__( 'Background Template', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:722
	__( 'Business Days and Timing', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:724
	// Reference: src/components/PriceListOptions.vue:908
	__( 'Date Font Size', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:725
	// Reference: src/components/PriceListOptions.vue:909
	__( 'Time Font Size', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:727
	// Reference: src/components/TeamMemberOptions.vue:1124
	__( 'Date Align', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:728
	// Reference: src/components/TeamMemberOptions.vue:1125
	__( 'Time Align', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:736
	__( 'Enter Day', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:737
	__( 'Enter Time', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:738
	// Reference: src/components/TeamMemberOptions.vue:1136
	__( 'Style Day/Time', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:742
	// Reference: src/components/PostinfoOptions.vue:584
	__( 'Time', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:744
	// Reference: src/components/TeamMemberOptions.vue:1145
	__( 'Day Color', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:745
	// Reference: src/components/TeamMemberOptions.vue:1146
	__( 'Time Color', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:748
	// Reference: src/components/DividerOptions.vue:363
	// Reference: src/components/NavOptions.vue:590
	// Reference: src/components/PostinfoOptions.vue:617
	// Reference: src/components/PriceListOptions.vue:939
	// Reference: src/components/ProductMetaOptions.vue:345
	// Reference: src/components/TeamMemberOptions.vue:1149
	__( 'Divider', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:754
	// Reference: src/components/PriceListOptions.vue:945
	// Reference: src/components/TeamMemberOptions.vue:1155
	__( 'Weight', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:755
	// Reference: src/components/PriceListOptions.vue:946
	// Reference: src/components/TeamMemberOptions.vue:1156
	__( 'Day and Time', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:756
	// Reference: src/components/PriceListOptions.vue:947
	// Reference: src/components/TeamMemberOptions.vue:1157
	__( 'Striped Effect', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:757
	// Reference: src/components/PriceListOptions.vue:948
	// Reference: src/components/TeamMemberOptions.vue:1158
	__( 'Striped Odd Rows Color', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:758
	// Reference: src/components/PriceListOptions.vue:949
	// Reference: src/components/TeamMemberOptions.vue:1159
	__( 'Striped Even Rows Color', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:760
	__( 'Time Typography', 'coming-soon' ),

	// Reference: src/components/BusinessHoursOptions.vue:761
	// Reference: src/components/ColOptions.vue:679
	// Reference: src/components/PostauthorboxOptions.vue:361
	// Reference: src/components/PostcommentsOptions.vue:189
	// Reference: src/components/PostinfoOptions.vue:620
	// Reference: src/components/PriceListOptions.vue:955
	// Reference: src/components/ProductMetaOptions.vue:348
	// Reference: src/components/RowOptions.vue:675
	// Reference: src/components/SectionOptions.vue:398
	// Reference: src/components/TeamMemberOptions.vue:1160
	// Reference: src/views/GlobalCSS.vue:2189
	// Reference: src/views/SetupDesign-Lite.vue:755
	__( 'Background', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1242
	// Reference: src/components/EDDAddToCartOptions.vue:793
	// Reference: src/components/EDDBuyNowButtonOptions.vue:822
	// Reference: src/components/NavOptions.vue:585
	// Reference: src/components/PricingTableOptions.vue:1346
	// Reference: src/components/StripepaymentOptions.vue:1681
	// Reference: src/components/WCAddToCartOptions.vue:694
	__( 'Add "No Follow"', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1244
	// Reference: src/components/OptinFormOptions.vue:869
	// Reference: src/components/StripepaymentOptions.vue:1683
	// Reference: src/components/WCAddToCartOptions.vue:696
	__( 'Button Sub Text', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1246
	// Reference: src/components/EDDAddToCartOptions.vue:796
	// Reference: src/components/EDDBuyNowButtonOptions.vue:825
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:464
	// Reference: src/components/HeaderOptions.vue:557
	// Reference: src/components/IconOptions.vue:288
	// Reference: src/components/ImageOptions.vue:562
	// Reference: src/components/NavOptions.vue:584
	// Reference: src/components/PostfeaturedimageOptions.vue:465
	// Reference: src/components/PricingTableOptions.vue:1345
	// Reference: src/components/ProductFeaturedImageOptions.vue:464
	// Reference: src/components/StripepaymentOptions.vue:1685
	// Reference: src/components/WCAddToCartOptions.vue:698
	__( 'Open In New Window', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1280
	// Reference: src/components/StripepaymentOptions.vue:1719
	// Reference: src/views/GlobalCSS.vue:2289
	__( 'Button Border Width', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1281
	// Reference: src/components/StripepaymentOptions.vue:1720
	// Reference: src/views/GlobalCSS.vue:2254
	__( 'Button Border Color', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1285
	__( 'Use the Global CSS template part to edit the button styles for your whole site. To edit the button styles for this specific button only, edit the options below and use the "Custom" drop down option to add any custom styles including hover styles.', 'coming-soon' ),

	// Reference: src/components/ButtonOptions.vue:1286
	__( 'The border color is required for the border width to take effect.', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:669
	__( 'Column Width', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:680
	// Reference: src/components/EDDCartOptions.vue:361
	// Reference: src/components/EDDCheckoutOptions.vue:638
	// Reference: src/components/EDDDownloadsGridOptions.vue:820
	// Reference: src/components/PostinfoOptions.vue:612
	// Reference: src/components/PostsOptions.vue:2085
	// Reference: src/components/PriceListOptions.vue:950
	// Reference: src/components/ProductMetaOptions.vue:332
	// Reference: src/components/WCCartOptions.vue:500
	// Reference: src/components/WCCheckoutOptions.vue:769
	// Reference: src/components/WCCustomProductsGridOptions.vue:923
	// Reference: src/views/GlobalCSS.vue:2225
	__( 'Layout', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:681
	// Reference: src/components/ContactFormOptions.vue:295
	// Reference: src/components/EnviraGalleryOptions.vue:217
	// Reference: src/components/GiveawayOptions.vue:167
	// Reference: src/components/MypaykitOptions.vue:122
	// Reference: src/components/NavOptions.vue:577
	// Reference: src/components/SectionOptions.vue:399
	__( 'Advanced Styles', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:682
	// Reference: src/components/RowOptions.vue:688
	__( 'Content Alignment', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:683
	// Reference: src/components/NavOptions.vue:592
	// Reference: src/components/RowOptions.vue:689
	__( 'Simple', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:687
	// Reference: src/components/RowOptions.vue:693
	__( 'Display', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:688
	// Reference: src/components/RowOptions.vue:694
	__( 'Flex Direction', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:689
	// Reference: src/components/RowOptions.vue:695
	__( 'Justify Content', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:690
	// Reference: src/components/RowOptions.vue:696
	__( 'Align Items', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:691
	// Reference: src/components/RowOptions.vue:697
	// Reference: src/views/Layoutnav.vue:1487
	__( 'Column', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:692
	// Reference: src/components/RowOptions.vue:698
	// Reference: src/views/Layoutnav.vue:1486
	__( 'Row', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:693
	// Reference: src/components/RowOptions.vue:699
	__( 'Flex Start', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:694
	// Reference: src/components/RowOptions.vue:700
	__( 'Center', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:695
	// Reference: src/components/RowOptions.vue:701
	__( 'Flex End', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:697
	// Reference: src/components/RowOptions.vue:703
	__( 'Space Around', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:698
	// Reference: src/components/RowOptions.vue:704
	__( 'Space Evenly', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:699
	// Reference: src/components/RowOptions.vue:705
	__( 'Baseline', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:700
	// Reference: src/components/RowOptions.vue:706
	__( 'Stretch', 'coming-soon' ),

	// Reference: src/components/ColOptions.vue:702
	// Reference: src/components/RowOptions.vue:712
	// Reference: src/components/SectionOptions.vue:401
	// Reference: src/components/ShapeDividerControl.vue:346
	__( 'Shape Divider', 'coming-soon' ),

	// Reference: src/components/ColorPicker.vue:319
	__( 'Global Colors', 'coming-soon' ),

	// Reference: src/components/ColorPicker.vue:320
	__( 'Common Colors', 'coming-soon' ),

	// Reference: src/components/ColorPicker.vue:321
	__( 'Recently Used', 'coming-soon' ),

	// Reference: src/components/ColorPicker.vue:322
	// Reference: src/views/InlineHelpView.vue:266
	__( 'Clear', 'coming-soon' ),

	// Reference: src/components/ColorPicker.vue:324
	__( 'Global Theme Colors', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:172
	__( 'Install Contact Form plugin:', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:173
	__( 'Install WPForms', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:174
	__( 'Activate WPForms', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:175
	__( 'Contact Form', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:176
	// Reference: src/components/EnviraGallery.vue:161
	// Reference: src/components/Giveaway.vue:177
	// Reference: src/components/Mypaykit.vue:175
	__( '(This shortcode will be rendered on the live preview.)', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:180
	// Reference: src/components/ContactFormOptions.vue:296
	__( 'Select a Form', 'coming-soon' ),

	// Reference: src/components/ContactForm.vue:181
	__( 'You can use WPForms to build contact forms, surveys, payment forms, and more with just a few clicks.', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:298
	// Reference: src/components/EnviraGalleryOptions.vue:220
	__( 'Select', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:299
	__( 'Need to make changes? Edit the selected form.', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:300
	__( '+ New Form', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:303
	// Reference: src/components/GiveawayOptions.vue:175
	// Reference: src/components/MypaykitOptions.vue:131
	__( 'Form Name', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:304
	// Reference: src/components/GiveawayOptions.vue:176
	// Reference: src/components/MypaykitOptions.vue:132
	__( 'Form Description', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:305
	// Reference: src/components/GiveawayOptions.vue:177
	// Reference: src/components/LoginOptions.vue:907
	// Reference: src/components/MypaykitOptions.vue:133
	// Reference: src/components/OptinFormOptions.vue:856
	__( 'Form', 'coming-soon' ),

	// Reference: src/components/ContactFormOptions.vue:306
	// Reference: src/components/GiveawayOptions.vue:178
	// Reference: src/components/MypaykitOptions.vue:134
	__( 'Display Options', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:577
	__( 'Heading 1', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:578
	__( 'Heading 2', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:579
	__( 'Content Area 1', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:580
	__( 'Content Area 2', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:581
	__( 'Toggle', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:582
	__( 'Content 1', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:583
	__( 'Content 2', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:588
	__( 'Templates Parts', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:589
	__( 'Section Content', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:598
	__( 'First Background Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:599
	__( 'Second Background Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:600
	__( 'Switcher Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:602
	__( 'Heading 1 Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:603
	__( 'Heading 2 Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:604
	__( 'Content 1 Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:605
	__( 'Content 2 Color', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:623
	__( 'Heading / Content', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:624
	__( 'Switcher', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:625
	__( 'Switch Style', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:626
	__( 'Round', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:627
	__( 'Square', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:628
	__( 'Label Box', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:629
	__( 'Switch Size', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:633
	__( 'Mini', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:634
	__( 'No template parts available in SeedProd', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:638
	__( 'Select Part', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:639
	__( 'Loading Template Parts...', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:640
	__( 'Edit This Template Part', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:641
	__( 'Header 1 Typography', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:642
	__( 'Header 2 Typography', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:643
	__( 'Content 1 Typography', 'coming-soon' ),

	// Reference: src/components/ContentToggleOptions.vue:644
	__( 'Content 2 Typography', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:854
	__( 'Countdown Type', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:855
	__( 'Visitor Timer (Evergreen)', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:856
	__( 'DateTime Countdown', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:857
	__( 'Set Timer For', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:858
	__( 'End Date', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:859
	// Reference: src/components/VideoPopUpOptions.vue:1149
	__( 'End Time', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:860
	__( 'Timezone', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:866
	__( 'Action To Take On Expires', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:867
	// Reference: src/components/OptinFormOptions.vue:872
	__( 'Show Message', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:868
	// Reference: src/components/OptinFormOptions.vue:873
	__( 'Redirect', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:869
	// Reference: src/components/OptinFormOptions.vue:874
	__( 'Message', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:870
	// Reference: src/components/LoginOptions.vue:884
	// Reference: src/components/OptinFormOptions.vue:875
	__( 'Redirect URL', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:871
	__( 'Customize Labels', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:872
	__( 'Day Label', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:873
	__( 'Hour Label', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:874
	__( 'Minute Label', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:875
	__( 'Second Label', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:877
	__( 'Hours', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:878
	__( 'Minutes', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:879
	__( 'Seconds', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:891
	__( 'Highlight Color', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:893
	__( 'Restart', 'coming-soon' ),

	// Reference: src/components/CountdownOptions.vue:894
	__( 'Select Date', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:330
	__( 'Starting Number', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:331
	__( 'Ending Number', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:332
	__( 'Number Prefix', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:333
	__( 'Number Suffix', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:334
	__( 'Animation Duration', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:335
	__( 'Thousands Separator', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:336
	// Reference: src/components/DynamicTagsControl.vue:958
	// Reference: src/components/PostsOptions.vue:2018
	// Reference: src/components/TeamMemberOptions.vue:1104
	__( 'Separator', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:339
	__( 'Dot', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:340
	// Reference: src/components/ParticlesBackgroundControl.vue:302
	__( 'Space', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:341
	__( 'Title Text Color', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:342
	__( 'Number Text Color', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:343
	__( 'Number Align', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:345
	__( 'Number Shadow', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:346
	__( 'Title Shadow', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:347
	__( 'Number Typography', 'coming-soon' ),

	// Reference: src/components/CounterOptions.vue:348
	// Reference: src/components/EDDCheckoutOptions.vue:687
	// Reference: src/components/EDDDownloadsGridOptions.vue:975
	// Reference: src/components/PostsOptions.vue:2041
	// Reference: src/components/PriceListOptions.vue:970
	// Reference: src/components/ProductRelatedOptions.vue:453
	__( 'Title Typography', 'coming-soon' ),

	// Reference: src/components/CustomHTML.vue:53
	__( 'Enter Your HTML', 'coming-soon' ),

	// Reference: src/components/CustomHTMLOptions.vue:202
	// Reference: src/views/GlobalCSS.vue:2186
	// Reference: src/views/SetupDesign-Lite.vue:752
	__( 'Expand Editor', 'coming-soon' ),

	// Reference: src/components/CustomHTMLOptions.vue:203
	__( 'Custom Code', 'coming-soon' ),

	// Reference: src/components/CustomHTMLOptions.vue:204
	__( 'Edit Custom HTML', 'coming-soon' ),

	// Reference: src/components/DeviceVisibilityControl.vue:83
	__( 'Device Visibility', 'coming-soon' ),

	// Reference: src/components/DeviceVisibilityControl.vue:84
	__( 'Hide on Desktop', 'coming-soon' ),

	// Reference: src/components/DeviceVisibilityControl.vue:85
	__( 'Hide on Mobile', 'coming-soon' ),

	// Reference: src/components/DeviceVisibilityControl.vue:86
	__( 'Hide on Tablet', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:785
	__( 'Z-Index', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:787
	__( 'Static', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:788
	__( 'Relative', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:790
	__( 'Absolute', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:791
	__( 'Sticky', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:796
	__( 'Offset', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:797
	__( 'Overflow', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:798
	// Reference: src/components/EDDDownloadsGridOptions.vue:962
	// Reference: src/components/WCCustomProductsGridOptions.vue:1065
	__( 'Visible', 'coming-soon' ),

	// Reference: src/components/DisplaySectionControl.vue:799
	// Reference: src/components/EDDDownloadsGridOptions.vue:965
	// Reference: src/components/WCCustomProductsGridOptions.vue:1068
	__( 'Hidden', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:366
	__( 'Solid Line', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:369
	__( 'Double', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:371
	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:466
	// Reference: src/components/FeatureOptions.vue:583
	// Reference: src/components/HeightControl.vue:111
	// Reference: src/components/IconFeatureOptions.vue:570
	// Reference: src/components/ImageOptions.vue:533
	// Reference: src/components/PostfeaturedimageOptions.vue:437
	// Reference: src/components/ProductFeaturedImageOptions.vue:466
	// Reference: src/components/ShapeDividerControl.vue:371
	// Reference: src/components/SpacerOptions.vue:68
	// Reference: src/components/TeamMemberOptions.vue:1094
	// Reference: src/components/VideoPopUpOptions.vue:1169
	__( 'Height', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:372
	// Reference: src/components/SocialProfilesOptions.vue:696
	__( 'Icon Size', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:380
	__( 'Add Element', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:385
	// Reference: src/components/PostauthorboxOptions.vue:344
	__( 'HTML Tag', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:386
	// Reference: src/components/PostauthorboxOptions.vue:345
	// Reference: src/components/PostsOptions.vue:2003
	// Reference: src/components/TeamMemberOptions.vue:1184
	__( 'H1', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:387
	// Reference: src/components/PostauthorboxOptions.vue:346
	// Reference: src/components/PostsOptions.vue:2004
	// Reference: src/components/TeamMemberOptions.vue:1185
	__( 'H2', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:388
	// Reference: src/components/PostauthorboxOptions.vue:347
	// Reference: src/components/PostsOptions.vue:2005
	// Reference: src/components/TeamMemberOptions.vue:1186
	__( 'H3', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:389
	// Reference: src/components/PostauthorboxOptions.vue:348
	// Reference: src/components/PostsOptions.vue:2006
	// Reference: src/components/TeamMemberOptions.vue:1187
	__( 'H4', 'coming-soon' ),

	// Reference: src/components/DividerOptions.vue:392
	// Reference: src/components/PostauthorboxOptions.vue:352
	// Reference: src/components/PostsOptions.vue:2010
	// Reference: src/components/TeamMemberOptions.vue:1191
	__( 'span', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1001
	// Reference: src/components/PostauthorboxOptions.vue:356
	__( 'Website', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1002
	__( 'User Meta', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1003
	__( 'Data', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1005
	__( 'Alt', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1006
	// Reference: src/components/PostinfoOptions.vue:599
	// Reference: src/components/ProductMetaOptions.vue:338
	__( 'Caption', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1008
	__( 'File URL', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1009
	__( 'Attachment URL', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1010
	__( 'No Comments Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1011
	__( 'One Comment Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1012
	__( 'Many Comments Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1014
	__( 'Comments Link', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1015
	__( 'g:i a', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1016
	__( 'g:i A', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1017
	__( 'H:i', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1018
	__( 'e.g. g:i a', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1019
	// Reference: src/components/PostinfoOptions.vue:630
	__( 'Custom Time Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1024
	__( 'Post ID', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1025
	__( 'Post Date', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1026
	__( 'Post Time', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1027
	__( 'Post Title', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1028
	__( 'Post Excerpt', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1029
	__( 'Post Terms', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1030
	__( 'Post Custom Field', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1036
	__( 'Archive Meta', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1037
	__( 'Archive Description', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1038
	__( 'Archive Title', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1044
	__( 'Page Title', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1045
	__( 'Site Tagline', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1046
	__( 'Site Title', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1047
	__( 'Site Logo', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1048
	__( 'Current Date Time', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1049
	__( 'Request Parameter', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1050
	// Reference: src/components/ShortcodeOptions.vue:142
	__( 'Shortcode', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1051
	__( 'User Info', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1057
	__( 'Featured Image Data', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1063
	__( 'Author Info', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1064
	__( 'Author Meta', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1065
	__( 'Author Name', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1066
	__( 'Author Profile Picture', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1067
	__( 'User Profile Picture', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:1073
	__( 'Comments Number', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:940
	__( 'Insert Dynamic Tags', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:941
	__( 'Add Dynamic Tags', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:942
	__( 'Dynamic Tags draw content from the website, or from the current page/post, changing dynamically according to the Page or Post it’s on. <a href="https://www.seedprod.com/docs/dynamic-text" target="_blank" class="sp-text-primary hover:sp-underline">Learn More</a>', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:943
	__( 'Select Tag', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:944
	__( 'Select Key', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:945
	__( 'Custom Key', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:946
	__( '- Select -', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:947
	// Reference: src/components/DynamicTextControl.vue:318
	// Reference: src/components/OpenAIControl.vue:728
	// Reference: src/components/OpenAIImageControl.vue:603
	__( 'Insert', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:948
	// Reference: src/components/DynamicTextControl.vue:319
	// Reference: src/components/OpenAIControl.vue:729
	// Reference: src/components/OpenAIImageControl.vue:604
	// Reference: src/components/Row.vue:1119
	// Reference: src/components/Section.vue:689
	// Reference: src/mixins/helpers.js:1770
	// Reference: src/views/Layoutnav.vue:1489
	// Reference: src/views/SectionTemplateOptions-Lite.vue:491
	// Reference: src/views/Setup.vue:1052
	// Reference: src/views/TemplateChooser-Lite.vue:1543
	__( 'Cancel', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:949
	__( 'Please select a tag', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:951
	__( 'ACF Field', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:953
	__( 'Archive', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:954
	// Reference: src/components/PostsOptions.vue:2100
	__( 'Taxonomy', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:955
	__( 'Categories', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:956
	// Reference: src/components/PostinfoOptions.vue:623
	// Reference: src/components/PostsOptions.vue:2083
	__( 'Tags', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:957
	__( 'Select Taxonomy', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:959
	__( 'e.g. ,', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:961
	__( 'Show Advanced Settings', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:962
	// Reference: src/components/PricingTableOptions.vue:1378
	__( 'Before', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:963
	// Reference: src/components/PricingTableOptions.vue:1379
	__( 'After', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:964
	__( 'Fallback', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:966
	__( 'Post Published', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:967
	__( 'Post Modified', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:968
	__( 'Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:969
	__( 'F j, Y', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:970
	__( 'Y-m-d', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:971
	__( 'm/d/Y', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:972
	__( 'd/m/Y', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:974
	// Reference: src/components/PostinfoOptions.vue:607
	__( 'Custom Date Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:975
	__( 'e.g. Y-m-d', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:976
	// Reference: src/components/PostsOptions.vue:2020
	__( 'Excerpt Length', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:977
	__( 'Meta Key', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:978
	__( 'Include Context', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:979
	__( 'Show Home Title', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:980
	// Reference: src/components/PostinfoOptions.vue:600
	__( 'Date Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:981
	// Reference: src/components/PostinfoOptions.vue:626
	__( 'Time Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:985
	__( 'Custom Format', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:986
	__( 'e.g. Y-m-d g:i a', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:988
	__( 'Get', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:989
	// Reference: src/components/PostsOptions.vue:2122
	__( 'Post', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:990
	__( 'Query Var', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:991
	// Reference: src/components/DynamicTextControl.vue:333
	__( 'Parameter Name', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:993
	// Reference: src/views/GlobalCSS.vue:2231
	__( 'Field', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:994
	// Reference: src/components/EDDDownloadsGridOptions.vue:934
	// Reference: src/components/ProductRelatedOptions.vue:504
	// Reference: src/components/WCCustomProductsGridOptions.vue:1037
	__( 'ID', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:995
	// Reference: src/components/PostauthorboxOptions.vue:342
	__( 'Display Name', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:996
	__( 'Username', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:997
	__( 'First Name', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:998
	__( 'Last Name', 'coming-soon' ),

	// Reference: src/components/DynamicTagsControl.vue:999
	__( 'Bio', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:316
	__( 'Please enter date/time format', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:317
	__( 'Please select date/time format', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:320
	__( 'Dynamic Text Replacement', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:321
	__( 'DateTime', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:322
	__( 'Query Parameter', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:323
	__( 'Today', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:324
	__( 'Tomorrow', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:325
	__( 'Today Date', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:326
	__( 'Tomorrow Date', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:327
	__( 'Month', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:328
	__( 'Next Month', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:329
	__( 'Year', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:330
	__( 'Select Date Time Format', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:331
	__( 'Query Parameters', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:332
	__( 'Dynamic Text allows to create evergreen text. You can create date based dynamic text or pass in by query parameter. <a href="https://www.seedprod.com/docs/dynamic-text" target="_blank" class="sp-text-primary hover:sp-underline">Learn More</a>', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:334
	__( 'Default Value', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:335
	__( 'Please enter parameter name', 'coming-soon' ),

	// Reference: src/components/DynamicTextControl.vue:336
	__( 'Don\'t see the date format you need? Click the Learn More link above to learn how to create any date.', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:792
	// Reference: src/components/EDDBuyNowButtonOptions.vue:821
	// Reference: src/components/EDDCheckoutOptions.vue:640
	// Reference: src/components/EDDDownloadsGridOptions.vue:822
	// Reference: src/components/LoginOptions.vue:880
	// Reference: src/components/OptinFormOptions.vue:857
	// Reference: src/components/WCCartOptions.vue:502
	// Reference: src/components/WCCheckoutOptions.vue:771
	// Reference: src/components/WCCustomProductsGridOptions.vue:925
	__( 'Fields', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:805
	// Reference: src/components/EDDBuyNowButtonOptions.vue:834
	// Reference: src/components/EDDCheckoutOptions.vue:657
	// Reference: src/components/EDDDownloadsGridOptions.vue:839
	// Reference: src/components/LoginOptions.vue:894
	// Reference: src/components/OptinFormOptions.vue:888
	// Reference: src/components/WCCartOptions.vue:519
	// Reference: src/components/WCCheckoutOptions.vue:788
	// Reference: src/components/WCCustomProductsGridOptions.vue:942
	// Reference: src/views/GlobalCSS.vue:2233
	__( 'Field Background Color', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:826
	// Reference: src/components/EDDBuyNowButtonOptions.vue:855
	// Reference: src/components/EDDCheckoutOptions.vue:658
	// Reference: src/components/EDDDownloadsGridOptions.vue:840
	// Reference: src/components/LoginOptions.vue:893
	// Reference: src/components/OptinFormOptions.vue:889
	// Reference: src/components/WCCartOptions.vue:520
	// Reference: src/components/WCCheckoutOptions.vue:789
	// Reference: src/components/WCCustomProductsGridOptions.vue:943
	// Reference: src/views/GlobalCSS.vue:2232
	__( 'Field Text Color', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:827
	// Reference: src/components/EDDBuyNowButtonOptions.vue:856
	// Reference: src/components/EDDCheckoutOptions.vue:659
	// Reference: src/components/EDDDownloadsGridOptions.vue:841
	// Reference: src/components/LoginOptions.vue:895
	// Reference: src/components/OptinFormOptions.vue:890
	// Reference: src/components/WCCartOptions.vue:521
	// Reference: src/components/WCCheckoutOptions.vue:790
	// Reference: src/components/WCCustomProductsGridOptions.vue:944
	// Reference: src/views/GlobalCSS.vue:2234
	__( 'Field Border Color', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:828
	// Reference: src/components/EDDBuyNowButtonOptions.vue:857
	// Reference: src/components/EDDCheckoutOptions.vue:660
	// Reference: src/components/EDDDownloadsGridOptions.vue:842
	// Reference: src/components/LoginOptions.vue:901
	// Reference: src/components/OptinFormOptions.vue:902
	// Reference: src/components/WCCartOptions.vue:522
	// Reference: src/components/WCCheckoutOptions.vue:791
	// Reference: src/components/WCCustomProductsGridOptions.vue:945
	// Reference: src/views/GlobalCSS.vue:2239
	__( 'Field Border Width', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:829
	// Reference: src/components/EDDBuyNowButtonOptions.vue:858
	// Reference: src/components/EDDCartOptions.vue:380
	// Reference: src/components/EDDCheckoutOptions.vue:663
	// Reference: src/components/EDDDownloadsGridOptions.vue:845
	// Reference: src/components/LoginOptions.vue:899
	// Reference: src/components/WCCartOptions.vue:525
	// Reference: src/components/WCCheckoutOptions.vue:794
	// Reference: src/components/WCCustomProductsGridOptions.vue:948
	__( 'Row Spacing', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:830
	// Reference: src/components/EDDBuyNowButtonOptions.vue:859
	// Reference: src/components/LoginOptions.vue:923
	__( 'Field Width', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:831
	// Reference: src/components/EDDBuyNowButtonOptions.vue:860
	__( 'These settings are used to style item quantity fields. To enable them, enable "Misc" &rarr; "General" &rarr; "Cart Item Quantities" in the Downloads Plugin settings. Also uncheck the "Disable quantity input for this product" checkbox in the item download page if it has been checked.', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:841
	// Reference: src/components/EDDBuyNowButtonOptions.vue:871
	// Reference: src/components/WCAddToCartOptions.vue:738
	__( 'Direct To Checkout', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:843
	// Reference: src/components/EDDBuyNowButtonOptions.vue:873
	__( '- Select ID -', 'coming-soon' ),

	// Reference: src/components/EDDAddToCartOptions.vue:844
	// Reference: src/components/EDDBuyNowButtonOptions.vue:874
	// Reference: src/components/EDDDownloadsGridOptions.vue:951
	__( 'Show Price', 'coming-soon' ),

	// Reference: src/components/EDDBuyNowButtonOptions.vue:870
	// Reference: src/components/WCAddToCartOptions.vue:737
	__( 'Product ID', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:362
	// Reference: src/components/EDDCheckoutOptions.vue:639
	// Reference: src/components/EDDDownloadsGridOptions.vue:821
	// Reference: src/components/WCCartOptions.vue:501
	// Reference: src/components/WCCheckoutOptions.vue:770
	// Reference: src/components/WCCustomProductsGridOptions.vue:924
	// Reference: src/views/GlobalCSS.vue:2179
	// Reference: src/views/SetupDesign-Lite.vue:745
	__( 'Headers', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:363
	// Reference: src/components/EDDCheckoutOptions.vue:641
	// Reference: src/components/EDDDownloadsGridOptions.vue:823
	// Reference: src/components/PricingTableOptions.vue:1300
	// Reference: src/components/WCCartOptions.vue:503
	// Reference: src/components/WCCheckoutOptions.vue:772
	// Reference: src/components/WCCustomProductsGridOptions.vue:926
	// Reference: src/views/GlobalCSS.vue:2227
	// Reference: src/views/SetupDesign-Lite.vue:747
	__( 'Buttons', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:364
	// Reference: src/components/EDDCheckoutOptions.vue:643
	// Reference: src/components/EDDDownloadsGridOptions.vue:825
	// Reference: src/components/WCCartOptions.vue:505
	// Reference: src/components/WCCheckoutOptions.vue:774
	// Reference: src/components/WCCustomProductsGridOptions.vue:928
	__( 'Cart', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:365
	// Reference: src/components/EDDCheckoutOptions.vue:644
	// Reference: src/components/EDDDownloadsGridOptions.vue:826
	// Reference: src/components/WCCartOptions.vue:506
	// Reference: src/components/WCCheckoutOptions.vue:775
	// Reference: src/components/WCCustomProductsGridOptions.vue:929
	__( 'Payment Section', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:367
	// Reference: src/components/EDDCheckoutOptions.vue:646
	// Reference: src/components/EDDDownloadsGridOptions.vue:828
	// Reference: src/components/LoginOptions.vue:931
	// Reference: src/components/OptinFormOptions.vue:891
	// Reference: src/components/WCCartOptions.vue:508
	// Reference: src/components/WCCheckoutOptions.vue:777
	// Reference: src/components/WCCustomProductsGridOptions.vue:931
	__( 'Light Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:368
	// Reference: src/components/EDDCheckoutOptions.vue:647
	// Reference: src/components/EDDDownloadsGridOptions.vue:829
	// Reference: src/components/LoginOptions.vue:932
	// Reference: src/components/OptinFormOptions.vue:892
	// Reference: src/components/WCCartOptions.vue:509
	// Reference: src/components/WCCheckoutOptions.vue:778
	// Reference: src/components/WCCustomProductsGridOptions.vue:932
	__( 'No Border Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:369
	// Reference: src/components/EDDCheckoutOptions.vue:648
	// Reference: src/components/EDDDownloadsGridOptions.vue:830
	// Reference: src/components/LoginOptions.vue:933
	// Reference: src/components/OptinFormOptions.vue:893
	// Reference: src/components/WCCartOptions.vue:510
	// Reference: src/components/WCCheckoutOptions.vue:779
	// Reference: src/components/WCCustomProductsGridOptions.vue:933
	__( 'Wide Border Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:370
	// Reference: src/components/EDDCheckoutOptions.vue:649
	// Reference: src/components/EDDDownloadsGridOptions.vue:831
	// Reference: src/components/LoginOptions.vue:934
	// Reference: src/components/OptinFormOptions.vue:894
	// Reference: src/components/WCCartOptions.vue:511
	// Reference: src/components/WCCheckoutOptions.vue:780
	// Reference: src/components/WCCustomProductsGridOptions.vue:934
	__( 'Inner Shadow Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:371
	// Reference: src/components/EDDCheckoutOptions.vue:650
	// Reference: src/components/EDDDownloadsGridOptions.vue:832
	// Reference: src/components/LoginOptions.vue:935
	// Reference: src/components/OptinFormOptions.vue:895
	// Reference: src/components/WCCartOptions.vue:512
	// Reference: src/components/WCCheckoutOptions.vue:781
	// Reference: src/components/WCCustomProductsGridOptions.vue:935
	__( 'Grey Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:372
	// Reference: src/components/EDDCheckoutOptions.vue:651
	// Reference: src/components/EDDDownloadsGridOptions.vue:833
	// Reference: src/components/LoginOptions.vue:936
	// Reference: src/components/OptinFormOptions.vue:896
	// Reference: src/components/WCCartOptions.vue:513
	// Reference: src/components/WCCheckoutOptions.vue:782
	// Reference: src/components/WCCustomProductsGridOptions.vue:936
	__( 'Dark Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:373
	// Reference: src/components/EDDCheckoutOptions.vue:652
	// Reference: src/components/EDDDownloadsGridOptions.vue:834
	// Reference: src/components/LoginOptions.vue:937
	// Reference: src/components/OptinFormOptions.vue:897
	// Reference: src/components/WCCartOptions.vue:514
	// Reference: src/components/WCCheckoutOptions.vue:783
	// Reference: src/components/WCCustomProductsGridOptions.vue:937
	__( 'Bottom Border Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:374
	// Reference: src/components/EDDCheckoutOptions.vue:653
	// Reference: src/components/EDDDownloadsGridOptions.vue:835
	// Reference: src/components/LoginOptions.vue:938
	// Reference: src/components/OptinFormOptions.vue:898
	// Reference: src/components/WCCartOptions.vue:515
	// Reference: src/components/WCCheckoutOptions.vue:784
	// Reference: src/components/WCCustomProductsGridOptions.vue:938
	__( 'Transparent Input Field', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:375
	// Reference: src/components/EDDDownloadsGridOptions.vue:836
	// Reference: src/components/WCCartOptions.vue:516
	// Reference: src/components/WCCheckoutOptions.vue:785
	// Reference: src/components/WCCustomProductsGridOptions.vue:939
	__( 'One Column', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:376
	// Reference: src/components/EDDDownloadsGridOptions.vue:837
	// Reference: src/components/WCCartOptions.vue:517
	// Reference: src/components/WCCheckoutOptions.vue:786
	// Reference: src/components/WCCustomProductsGridOptions.vue:940
	__( 'Two Column', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:379
	// Reference: src/components/EDDCheckoutOptions.vue:662
	// Reference: src/components/EDDDownloadsGridOptions.vue:844
	// Reference: src/components/LoginOptions.vue:912
	// Reference: src/components/WCCartOptions.vue:524
	// Reference: src/components/WCCheckoutOptions.vue:793
	// Reference: src/components/WCCustomProductsGridOptions.vue:947
	__( 'Label Font', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:382
	// Reference: src/components/EDDCheckoutOptions.vue:665
	// Reference: src/components/EDDDownloadsGridOptions.vue:903
	// Reference: src/components/ProductRelatedOptions.vue:473
	// Reference: src/components/WCCartOptions.vue:527
	// Reference: src/components/WCCheckoutOptions.vue:796
	// Reference: src/components/WCCustomProductsGridOptions.vue:1006
	// Reference: src/views/GlobalCSS.vue:2251
	__( 'Button Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:389
	// Reference: src/components/EDDCheckoutOptions.vue:675
	// Reference: src/components/EDDDownloadsGridOptions.vue:857
	// Reference: src/components/WCCartOptions.vue:537
	// Reference: src/components/WCCheckoutOptions.vue:806
	// Reference: src/components/WCCustomProductsGridOptions.vue:960
	__( 'Cart Border Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:390
	// Reference: src/components/EDDCheckoutOptions.vue:676
	// Reference: src/components/EDDDownloadsGridOptions.vue:858
	// Reference: src/components/WCCartOptions.vue:538
	// Reference: src/components/WCCheckoutOptions.vue:807
	// Reference: src/components/WCCustomProductsGridOptions.vue:961
	__( 'Cart Border Width', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:393
	__( 'Product Text Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:394
	// Reference: src/components/EDDDownloadsGridOptions.vue:862
	// Reference: src/components/OptinFormOptions.vue:877
	// Reference: src/components/TypographyControl.vue:320
	// Reference: src/components/WCCartOptions.vue:542
	// Reference: src/components/WCCheckoutOptions.vue:811
	// Reference: src/components/WCCustomProductsGridOptions.vue:965
	__( 'Font Family', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:395
	__( 'Price Text Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:396
	// Reference: src/components/EDDDownloadsGridOptions.vue:864
	// Reference: src/components/WCCartOptions.vue:544
	// Reference: src/components/WCCheckoutOptions.vue:813
	// Reference: src/components/WCCustomProductsGridOptions.vue:967
	__( 'Header Font Family', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:397
	__( 'Total Text Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:398
	__( 'Links Color', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:399
	__( 'Product Typography', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:400
	// Reference: src/components/EDDDownloadsGridOptions.vue:977
	// Reference: src/components/PriceListOptions.vue:972
	// Reference: src/components/PricingTableOptions.vue:1404
	// Reference: src/components/ProductRelatedOptions.vue:455
	// Reference: src/components/WCCustomProductsGridOptions.vue:1078
	__( 'Price Typography', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:401
	// Reference: src/components/EDDCheckoutOptions.vue:689
	__( 'Total Typography', 'coming-soon' ),

	// Reference: src/components/EDDCartOptions.vue:402
	// Reference: src/components/EDDCheckoutOptions.vue:685
	// Reference: src/components/ProductMetaOptions.vue:351
	// Reference: src/views/GlobalCSS.vue:2265
	__( 'Link Typography', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:642
	// Reference: src/components/EDDDownloadsGridOptions.vue:824
	// Reference: src/components/WCCartOptions.vue:504
	// Reference: src/components/WCCheckoutOptions.vue:773
	// Reference: src/components/WCCustomProductsGridOptions.vue:927
	__( 'Alerts', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:655
	__( 'Total Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:672
	// Reference: src/components/EDDDownloadsGridOptions.vue:854
	// Reference: src/components/WCCartOptions.vue:534
	// Reference: src/components/WCCheckoutOptions.vue:803
	// Reference: src/components/WCCustomProductsGridOptions.vue:957
	__( 'Info Highlight Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:673
	// Reference: src/components/EDDDownloadsGridOptions.vue:855
	// Reference: src/components/WCCartOptions.vue:535
	// Reference: src/components/WCCheckoutOptions.vue:804
	// Reference: src/components/WCCustomProductsGridOptions.vue:958
	__( 'Error Highlight Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:674
	// Reference: src/components/EDDDownloadsGridOptions.vue:856
	// Reference: src/components/WCCartOptions.vue:536
	// Reference: src/components/WCCheckoutOptions.vue:805
	// Reference: src/components/WCCustomProductsGridOptions.vue:959
	__( 'Success Highlight Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:678
	// Reference: src/components/EDDDownloadsGridOptions.vue:974
	// Reference: src/components/PostsOptions.vue:2026
	// Reference: src/components/PriceListOptions.vue:918
	__( 'Title Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:679
	__( 'Cart Item Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:680
	__( 'Cart Links Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:681
	__( 'Payments Border Color', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:682
	__( 'Payments Border Radius', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:684
	__( 'Item Typography', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:688
	// Reference: src/components/EDDDownloadsGridOptions.vue:976
	// Reference: src/components/PriceListOptions.vue:971
	// Reference: src/components/PricingTableOptions.vue:1403
	// Reference: src/components/ProductRelatedOptions.vue:454
	// Reference: src/components/TeamMemberOptions.vue:1195
	// Reference: src/components/WCCustomProductsGridOptions.vue:1077
	__( 'Description Typography', 'coming-soon' ),

	// Reference: src/components/EDDCheckoutOptions.vue:691
	__( 'Payments Border Width', 'coming-soon' ),

	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:438
	// Reference: src/components/ImageBorderControl.vue:259
	// Reference: src/components/ImageOptions.vue:535
	// Reference: src/components/PostfeaturedimageOptions.vue:439
	// Reference: src/components/ProductFeaturedImageOptions.vue:438
	// Reference: src/components/ProductGalleryImagesOptions.vue:226
	__( 'Image Border', 'coming-soon' ),

	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:443
	// Reference: src/components/FeatureOptions.vue:581
	// Reference: src/components/ImageOptions.vue:540
	// Reference: src/components/PostfeaturedimageOptions.vue:444
	// Reference: src/components/ProductFeaturedImageOptions.vue:443
	// Reference: src/components/TeamMemberOptions.vue:1193
	__( 'Alt Text', 'coming-soon' ),

	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:454
	// Reference: src/components/EDDDownloadsGridOptions.vue:923
	// Reference: src/components/ImageOptions.vue:552
	// Reference: src/components/ParticlesBackgroundControl.vue:324
	// Reference: src/components/PostfeaturedimageOptions.vue:455
	// Reference: src/components/PostsOptions.vue:2062
	// Reference: src/components/ProductFeaturedImageOptions.vue:454
	// Reference: src/components/ProductRelatedOptions.vue:493
	// Reference: src/components/ShadowControl.vue:149
	// Reference: src/components/WCCustomProductsGridOptions.vue:1026
	__( 'Bottom Drop Shadow', 'coming-soon' ),

	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:461
	// Reference: src/components/EDDDownloadsGridOptions.vue:930
	// Reference: src/components/ImageBorderControl.vue:270
	// Reference: src/components/ImageOptions.vue:559
	// Reference: src/components/PostfeaturedimageOptions.vue:462
	// Reference: src/components/PostsOptions.vue:2069
	// Reference: src/components/PricingTableOptions.vue:1376
	// Reference: src/components/ProductFeaturedImageOptions.vue:461
	// Reference: src/components/ProductRelatedOptions.vue:500
	// Reference: src/components/WCCustomProductsGridOptions.vue:1033
	__( 'Image Whitespace Padding', 'coming-soon' ),

	// Reference: src/components/EDDDownloadFeaturedImageOptions.vue:465
	// Reference: src/components/HeaderOptions.vue:558
	// Reference: src/components/IconOptions.vue:289
	// Reference: src/components/ImageOptions.vue:563
	// Reference: src/components/PostfeaturedimageOptions.vue:466
	// Reference: src/components/ProductFeaturedImageOptions.vue:465
	__( 'No Follow', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:863
	// Reference: src/components/WCCartOptions.vue:543
	// Reference: src/components/WCCheckoutOptions.vue:812
	// Reference: src/components/WCCustomProductsGridOptions.vue:966
	__( 'Header Background Color', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:867
	// Reference: src/components/WCCustomProductsGridOptions.vue:970
	__( 'Query', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:869
	// Reference: src/components/PostsOptions.vue:1997
	// Reference: src/components/ProductRelatedOptions.vue:464
	// Reference: src/components/WCCustomProductsGridOptions.vue:972
	__( 'Columns', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:870
	// Reference: src/components/WCCustomProductsGridOptions.vue:973
	__( 'All Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:875
	// Reference: src/components/WCCustomProductsGridOptions.vue:978
	__( 'Featured Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:876
	// Reference: src/components/WCCustomProductsGridOptions.vue:979
	__( 'Sale Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:877
	// Reference: src/components/WCCustomProductsGridOptions.vue:980
	__( 'Best Selling Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:878
	// Reference: src/components/WCCustomProductsGridOptions.vue:981
	__( 'Recent Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:879
	// Reference: src/components/WCCustomProductsGridOptions.vue:982
	__( 'Top Rated Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:880
	// Reference: src/components/WCCustomProductsGridOptions.vue:983
	__( 'Include', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:881
	// Reference: src/components/WCCustomProductsGridOptions.vue:984
	__( 'Exclude', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:882
	// Reference: src/components/WCCustomProductsGridOptions.vue:985
	__( 'Include By', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:883
	// Reference: src/components/WCCustomProductsGridOptions.vue:986
	__( 'Exclude By', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:884
	// Reference: src/components/WCCustomProductsGridOptions.vue:987
	__( 'Term', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:885
	// Reference: src/components/PostsOptions.vue:1968
	// Reference: src/components/ProductRelatedOptions.vue:465
	// Reference: src/components/WCCustomProductsGridOptions.vue:988
	__( 'Order By', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:886
	// Reference: src/components/PostsOptions.vue:1975
	// Reference: src/components/ProductRelatedOptions.vue:466
	// Reference: src/components/WCCustomProductsGridOptions.vue:989
	__( 'Order', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:890
	// Reference: src/components/ProductRelatedOptions.vue:506
	// Reference: src/components/WCCustomProductsGridOptions.vue:1039
	__( 'Popularity', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:891
	// Reference: src/components/ProductRelatedOptions.vue:508
	// Reference: src/components/StarRatingOptions.vue:304
	// Reference: src/components/WCCustomProductsGridOptions.vue:1041
	__( 'Rating', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:892
	__( 'If orderby is set to random, pagination will be disabled to prevent subsequent pages from showing already-displayed products.', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:893
	// Reference: src/components/PostsOptions.vue:1974
	// Reference: src/components/ProductRelatedOptions.vue:505
	// Reference: src/components/WCCustomProductsGridOptions.vue:1038
	__( 'Menu Order', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:894
	// Reference: src/components/PostsOptions.vue:1993
	// Reference: src/components/ProductRelatedOptions.vue:467
	// Reference: src/components/WCCustomProductsGridOptions.vue:997
	__( 'ASC', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:895
	// Reference: src/components/PostsOptions.vue:1994
	// Reference: src/components/ProductRelatedOptions.vue:468
	// Reference: src/components/WCCustomProductsGridOptions.vue:998
	__( 'DESC', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:897
	// Reference: src/components/PostinfoOptions.vue:616
	// Reference: src/components/ProductMetaOptions.vue:344
	// Reference: src/components/ProductRelatedOptions.vue:469
	// Reference: src/components/TypographyControl.vue:325
	// Reference: src/components/WCCustomProductsGridOptions.vue:1000
	__( 'Alignment', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:899
	// Reference: src/components/PriceListOptions.vue:960
	// Reference: src/components/PricingTableOptions.vue:1308
	// Reference: src/components/WCCustomProductsGridOptions.vue:1002
	__( 'Price', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:900
	// Reference: src/components/PriceListOptions.vue:961
	// Reference: src/components/PricingTableOptions.vue:1366
	// Reference: src/components/ProductRelatedOptions.vue:470
	// Reference: src/components/WCCustomProductsGridOptions.vue:1003
	__( 'Price Color', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:904
	// Reference: src/components/LoginOptions.vue:903
	// Reference: src/components/ProductRelatedOptions.vue:474
	// Reference: src/components/WCCustomProductsGridOptions.vue:1007
	__( 'Button Size', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:924
	// Reference: src/components/PostsOptions.vue:2063
	// Reference: src/components/ProductRelatedOptions.vue:494
	// Reference: src/components/WCCustomProductsGridOptions.vue:1027
	__( 'Image Border Color', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:925
	// Reference: src/components/PostsOptions.vue:2064
	// Reference: src/components/ProductGalleryImagesOptions.vue:228
	// Reference: src/components/ProductRelatedOptions.vue:495
	// Reference: src/components/WCCustomProductsGridOptions.vue:1028
	__( 'Image Border Width', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:931
	__( 'Number', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:932
	// Reference: src/components/PostsOptions.vue:1969
	// Reference: src/components/ProductRelatedOptions.vue:502
	// Reference: src/components/WCCustomProductsGridOptions.vue:1035
	__( '- Select Options -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:933
	__( 'Post Date(Default)', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:935
	// Reference: src/components/PostsOptions.vue:1998
	// Reference: src/components/WCCustomProductsGridOptions.vue:974
	__( 'Pagination', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:936
	__( 'Current Page Color', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:937
	// Reference: src/components/ProductRelatedOptions.vue:507
	// Reference: src/components/WCCustomProductsGridOptions.vue:1040
	__( 'Random', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:938
	// Reference: src/components/LoginOptions.vue:890
	// Reference: src/components/ProductMetaOptions.vue:347
	__( 'Link Color', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:940
	__( 'Relation', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:941
	__( '- Select Relation -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:942
	// Reference: src/components/WCCustomProductsGridOptions.vue:1045
	__( 'Select By Category', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:943
	// Reference: src/components/PostsOptions.vue:1961
	// Reference: src/components/WCCustomProductsGridOptions.vue:1046
	__( '- Select Categories -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:944
	// Reference: src/components/WCCustomProductsGridOptions.vue:1047
	__( 'Select By Tags', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:945
	// Reference: src/components/WCCustomProductsGridOptions.vue:1048
	__( '- Select Tags -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:946
	// Reference: src/components/WCCustomProductsGridOptions.vue:1049
	__( 'Select By Group', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:947
	// Reference: src/components/WCCustomProductsGridOptions.vue:1050
	__( '- Select Group -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:948
	__( 'OR', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:949
	__( 'AND', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:950
	__( 'Specify whether the downloads displayed have to be in ALL the categories/tags provided, or just in at least one.', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:952
	// Reference: src/components/PostsOptions.vue:2019
	__( 'Show Excerpt', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:953
	__( 'Show Full Content', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:954
	// Reference: src/components/WCCustomProductsGridOptions.vue:1057
	__( 'Include Selected Terms', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:955
	// Reference: src/components/WCCustomProductsGridOptions.vue:1058
	__( 'Exclude Selected Terms', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:956
	// Reference: src/components/WCCustomProductsGridOptions.vue:1059
	__( 'Include Selected Tags', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:957
	// Reference: src/components/WCCustomProductsGridOptions.vue:1060
	__( 'Exclude Selected Tags', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:958
	// Reference: src/components/PostsOptions.vue:1960
	// Reference: src/components/WCCustomProductsGridOptions.vue:1061
	__( 'Include Selected Categories', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:959
	// Reference: src/components/WCCustomProductsGridOptions.vue:1062
	__( 'Exclude Selected Categories', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:960
	__( 'Show Buy Button', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:961
	__( 'Show Thumbnails', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:963
	// Reference: src/components/WCCustomProductsGridOptions.vue:1066
	__( 'Catalog', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:964
	// Reference: src/components/ImageControl.vue:344
	// Reference: src/components/WCCustomProductsGridOptions.vue:1067
	__( 'Search', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:966
	// Reference: src/components/PostsOptions.vue:2088
	// Reference: src/components/WCCustomProductsGridOptions.vue:1069
	__( 'Featured', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:967
	// Reference: src/components/WCCustomProductsGridOptions.vue:1070
	__( 'Custom Query', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:968
	// Reference: src/components/WCCustomProductsGridOptions.vue:1071
	__( 'Select By ID', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:969
	// Reference: src/components/WCCustomProductsGridOptions.vue:1072
	__( '- Select ID(s) -', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:970
	// Reference: src/components/WCCustomProductsGridOptions.vue:1073
	__( 'Show Items Count', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:971
	// Reference: src/components/WCCustomProductsGridOptions.vue:1074
	__( 'Show Order By', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:973
	// Reference: src/components/WCCustomProductsGridOptions.vue:1076
	__( 'Archive Products', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:978
	// Reference: src/components/ProductRelatedOptions.vue:456
	// Reference: src/components/WCCustomProductsGridOptions.vue:1079
	__( 'Button Typography', 'coming-soon' ),

	// Reference: src/components/EDDDownloadsGridOptions.vue:979
	// Reference: src/components/PostsOptions.vue:2045
	__( 'Pagination Typography', 'coming-soon' ),

	// Reference: src/components/EnviraGallery.vue:158
	__( 'Install Envira Gallery plugin:', 'coming-soon' ),

	// Reference: src/components/EnviraGallery.vue:159
	__( 'Install Envira', 'coming-soon' ),

	// Reference: src/components/EnviraGallery.vue:160
	__( 'Activate Envira', 'coming-soon' ),

	// Reference: src/components/EnviraGallery.vue:165
	__( 'Select a gallery', 'coming-soon' ),

	// Reference: src/components/EnviraGallery.vue:166
	__( 'You can use your Envira gallery from this block.', 'coming-soon' ),

	// Reference: src/components/EnviraGalleryOptions.vue:218
	__( 'Select Gallery', 'coming-soon' ),

	// Reference: src/components/EnviraGalleryOptions.vue:221
	__( '+ New Gallery', 'coming-soon' ),

	// Reference: src/components/EnviraGalleryOptions.vue:224
	__( 'Image Limit', 'coming-soon' ),

	// Reference: src/components/EnviraGalleryOptions.vue:225
	__( 'Gallery', 'coming-soon' ),

	// Reference: src/components/EnviraGalleryOptions.vue:226
	__( 'Image Options', 'coming-soon' ),

	// Reference: src/components/FeatureOptions.vue:586
	// Reference: src/components/IconFeatureOptions.vue:573
	// Reference: src/components/PriceListOptions.vue:913
	__( 'Vertical Alignment', 'coming-soon' ),

	// Reference: src/components/FontAwesomePicker.vue:39
	__( 'Click to select an icon', 'coming-soon' ),

	// Reference: src/components/FontAwesomePicker.vue:40
	__( 'Search icons', 'coming-soon' ),

	// Reference: src/components/FontControl.vue:38
	__( 'Standard Fonts', 'coming-soon' ),

	// Reference: src/components/FontControl.vue:40
	__( 'Google Fonts', 'coming-soon' ),

	// Reference: src/components/FontVariantControl.vue:37
	__( 'Select a Font Weight', 'coming-soon' ),

	// Reference: src/components/FontVariantControl.vue:38
	__( 'Normal 400', 'coming-soon' ),

	// Reference: src/components/FontVariantControl.vue:39
	__( 'Bold 700', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:173
	__( 'Install Giveaway plugin:', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:174
	__( 'Install RafflePress', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:175
	__( 'Activate RafflePress', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:176
	__( 'Giveaway', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:181
	// Reference: src/components/GiveawayOptions.vue:170
	__( 'Select a Giveaway', 'coming-soon' ),

	// Reference: src/components/Giveaway.vue:182
	__( 'You can use RafflePress to build viral giveaways in minutes and explode your email list.', 'coming-soon' ),

	// Reference: src/components/GiveawayOptions.vue:171
	__( 'Need to make changes? Edit the selected giveaway.', 'coming-soon' ),

	// Reference: src/components/GiveawayOptions.vue:172
	__( '+ New Giveaway', 'coming-soon' ),

	// Reference: src/components/GoogleMapsOptions.vue:230
	__( 'Google Maps', 'coming-soon' ),

	// Reference: src/components/GoogleMapsOptions.vue:231
	__( 'Location', 'coming-soon' ),

	// Reference: src/components/GoogleMapsOptions.vue:232
	__( 'Zoom', 'coming-soon' ),

	// Reference: src/components/GoogleMapsOptions.vue:233
	__( 'Height (px)', 'coming-soon' ),

	// Reference: src/components/GoogleMapsOptions.vue:237
	__( 'Width (%)', 'coming-soon' ),

	// Reference: src/components/HeaderOptions.vue:531
	__( 'Headline', 'coming-soon' ),

	// Reference: src/components/IconFeatureOptions.vue:560
	__( 'Icon Gap', 'coming-soon' ),

	// Reference: src/components/IconPicker.vue:242
	__( 'All Icons', 'coming-soon' ),

	// Reference: src/components/IconPicker.vue:243
	__( 'Font Awesome', 'coming-soon' ),

	// Reference: src/components/IconPicker.vue:244
	__( 'Search icons...', 'coming-soon' ),

	// Reference: src/components/IconPicker.vue:245
	// Reference: src/components/PostinfoOptions.vue:611
	__( 'Choose Icon', 'coming-soon' ),

	// Reference: src/components/IconPicker.vue:246
	__( 'Icon Library', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:335
	__( 'Choose New Image', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:340
	__( 'Use Your', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:341
	__( 'Own Image', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:342
	// Reference: src/components/Row.vue:1115
	// Reference: src/components/Section.vue:686
	// Reference: src/views/BuilderView.vue:1041
	__( 'or', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:343
	__( 'Stock Images Library', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:345
	__( 'Use a', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:346
	__( 'Stock Image', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:347
	__( 'Search images...', 'coming-soon' ),

	// Reference: src/components/ImageControl.vue:348
	__( 'Select Image', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:564
	__( 'Link Type', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:565
	__( 'Custom Link', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:566
	__( 'Media - Lightbox', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:568
	__( 'Object Fit', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:570
	__( 'Fill', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:573
	__( 'Border Radius Unit', 'coming-soon' ),

	// Reference: src/components/ImageOptions.vue:574
	__( 'Rotate Image', 'coming-soon' ),

	// Reference: src/components/ListTable.vue:470
	__( 'Bulk Actions', 'coming-soon' ),

	// Reference: src/components/ListTable.vue:471
	// Reference: src/components/WpWidgetBlockOptions.vue:214
	__( 'Apply', 'coming-soon' ),

	// Reference: src/components/ListTable.vue:472
	__( 'items', 'coming-soon' ),

	// Reference: src/components/ListTable.vue:473
	__( 'No items found.', 'coming-soon' ),

	// Reference: src/components/ListTable.vue:475
	__( 'Select bulk action', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:102
	// Reference: src/components/LiteCTASubscribers.vue:239
	// Reference: src/components/LiteCTATemplates.vue:80
	// Reference: src/components/SettingsLiteCTA.vue:248
	// Reference: src/components/UpgradeCTABuilder.vue:81
	__( 'automatically applied at checkout.', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:106
	// Reference: src/components/LiteCTASubscribers.vue:243
	// Reference: src/components/LiteCTATemplates.vue:84
	// Reference: src/components/SettingsLiteCTA.vue:252
	// Reference: src/components/UpgradeCTABuilder.vue:85
	__( 'Get SeedProd Lite Today and Unlock all the Powerful Features »', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:114
	__( 'is a PRO Feature', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:115
	__( 'is not available on your plan. Please upgrade to the PRO version to unlock all these awesome features.', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:116
	// Reference: src/components/LiteCTASubscribers.vue:247
	// Reference: src/components/LiteCTATemplates.vue:91
	// Reference: src/components/SettingsLiteCTA.vue:260
	__( 'Special Upgrade Offer - Save 50% Off', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:67
	// Reference: src/components/LiteCTASubscribers.vue:204
	// Reference: src/components/LiteCTATemplates.vue:45
	// Reference: src/components/SettingsLiteCTA.vue:213
	// Reference: src/components/UpgradeCTABuilder.vue:45
	__( 'Dismiss this message', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:68
	// Reference: src/components/LiteCTASubscribers.vue:205
	// Reference: src/components/LiteCTATemplates.vue:46
	// Reference: src/components/SettingsLiteCTA.vue:214
	// Reference: src/components/UpgradeCTABuilder.vue:46
	__( 'Get SeedProd Lite and Unlock all the Powerful Features', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:79
	// Reference: src/components/LiteCTASubscribers.vue:216
	// Reference: src/components/LiteCTATemplates.vue:57
	// Reference: src/components/SettingsLiteCTA.vue:225
	// Reference: src/components/UpgradeCTABuilder.vue:57
	__( 'Pro Features:', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:80
	// Reference: src/components/LiteCTATemplates.vue:58
	// Reference: src/components/UpgradeCTABuilder.vue:58
	__( 'Drag & Drop Page Builder', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:81
	// Reference: src/components/SettingsLiteCTA.vue:227
	// Reference: src/components/UpgradeCTABuilder.vue:59
	__( '80+ PRO Page Blocks', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:82
	// Reference: src/components/UpgradeCTABuilder.vue:60
	__( 'PRO Email Marketing Integrations', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:86
	// Reference: src/components/LiteCTASubscribers.vue:223
	// Reference: src/components/SettingsLiteCTA.vue:232
	// Reference: src/components/UpgradeCTABuilder.vue:64
	__( 'Custom 404 Pages', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:87
	// Reference: src/components/LiteCTASubscribers.vue:224
	// Reference: src/components/UpgradeCTABuilder.vue:65
	__( 'Page Access Controls', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:88
	// Reference: src/components/SettingsLiteCTA.vue:234
	// Reference: src/components/UpgradeCTABuilder.vue:66
	__( '200+ PRO Page Templates', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:89
	// Reference: src/components/SettingsLiteCTA.vue:235
	// Reference: src/components/UpgradeCTABuilder.vue:67
	__( 'PRO Smart Sections', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:93
	// Reference: src/components/LiteCTASubscribers.vue:230
	// Reference: src/components/UpgradeCTABuilder.vue:71
	__( 'Email Subscriber Management', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:94
	// Reference: src/components/LiteCTASubscribers.vue:231
	// Reference: src/components/SettingsLiteCTA.vue:240
	// Reference: src/components/UpgradeCTABuilder.vue:72
	// Reference: src/views/TemplateChooser-Lite.vue:1334
	__( 'Saved Templates', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:95
	// Reference: src/components/LiteCTASubscribers.vue:232
	// Reference: src/components/LiteCTATemplates.vue:73
	// Reference: src/components/SettingsLiteCTA.vue:241
	// Reference: src/components/UpgradeCTABuilder.vue:73
	__( 'Plus much more...', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:96
	// Reference: src/components/LiteCTASubscribers.vue:233
	// Reference: src/components/LiteCTATemplates.vue:74
	// Reference: src/components/SettingsLiteCTA.vue:242
	// Reference: src/components/UpgradeCTABuilder.vue:75
	__( 'Bonus:', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:97
	// Reference: src/components/LiteCTASubscribers.vue:234
	// Reference: src/components/LiteCTATemplates.vue:75
	// Reference: src/components/SettingsLiteCTA.vue:243
	// Reference: src/components/UpgradeCTABuilder.vue:76
	__( 'SeedProd Lite users get', 'coming-soon' ),

	// Reference: src/components/LiteCTABuilder.vue:98
	// Reference: src/components/LiteCTASubscribers.vue:235
	// Reference: src/components/LiteCTATemplates.vue:76
	// Reference: src/components/SettingsLiteCTA.vue:244
	// Reference: src/components/UpgradeCTABuilder.vue:77
	__( 'a discount off the regular price', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:217
	__( 'Filter by Page', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:218
	__( 'Export to a CSV File', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:219
	__( 'Premium Email Marketing Integrations', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:225
	__( 'Subscribers Over Time', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:226
	__( 'See Names and Emails', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:248
	__( 'Start Collecting Emails with SeedProd Lite', 'coming-soon' ),

	// Reference: src/components/LiteCTASubscribers.vue:249
	__( 'Use our Optin Form Block to start collecting emails from your visitors. All collected emails will show up on this page. Integrate with any of the Email Service Providers below.', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:59
	__( 'More Premium Blocks', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:60
	__( 'Capture Emails and Leads', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:64
	__( 'Marketing & CRM Integrations', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:65
	__( 'Maintenance Access Controls', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:66
	__( 'Growing Library of Templates', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:67
	__( 'Smart Sections', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:71
	__( 'More Design Controls', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:72
	__( 'Coming Soon Access Controls', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:88
	__( 'Templates are a PRO Feature', 'coming-soon' ),

	// Reference: src/components/LiteCTATemplates.vue:89
	__( 'Not all Templates are not available on your plan. Please upgrade to the PRO version to unlock all these awesome features.', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:885
	__( 'Label for User field', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:886
	__( 'Placeholder for User field', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:887
	__( 'Label for Password field', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:888
	__( 'Placeholder for Password field', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:891
	__( 'Link Hover Color', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:892
	__( 'Label Text Color', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:897
	__( 'Button Background Color', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:898
	__( 'Field Size', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:900
	__( 'Label Spacing', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:902
	// Reference: src/views/GlobalCSS.vue:2288
	__( 'Field Border Radius', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:905
	// Reference: src/components/OptinFormOptions.vue:855
	__( 'Success Action', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:906
	__( 'Additional Options', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:909
	__( 'Remember User Label', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:910
	__( 'Lost Password Link Text', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:911
	// Reference: src/views/GlobalCSS.vue:2202
	// Reference: src/views/SetupDesign-Lite.vue:754
	__( 'Colors', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:913
	__( 'Label Font Weight', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:914
	__( 'Field Font', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:915
	__( 'Field Font Weight', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:916
	// Reference: src/views/GlobalCSS.vue:2253
	__( 'Button Font', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:917
	__( 'Button Font Weight', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:918
	__( 'Logged In Text', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:919
	__( 'This text displays in place of a form if the user is already logged in. {user} will be replaced by the user\'s display name.', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:920
	__( '(e.g. \'Remember Me\')', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:921
	__( '(e.g. \'Lost your password?\')', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:922
	// Reference: src/views/GlobalCSS.vue:2287
	__( 'Button Border Radius', 'coming-soon' ),

	// Reference: src/components/LoginOptions.vue:929
	__( 'Label Text Size', 'coming-soon' ),

	// Reference: src/components/MarginControl.vue:481
	__( 'Margin', 'coming-soon' ),

	// Reference: src/components/MarginControl.vue:486
	__( 'auto', 'coming-soon' ),

	// Reference: src/components/MenuCartOptions.vue:186
	__( 'Hide on Empty', 'coming-soon' ),

	// Reference: src/components/MenuCartOptions.vue:188
	__( 'Show Subtotal', 'coming-soon' ),

	// Reference: src/components/MenuCartOptions.vue:190
	__( 'Badge Color', 'coming-soon' ),

	// Reference: src/components/MenuCartOptions.vue:191
	__( 'Badge Text Color', 'coming-soon' ),

	// Reference: src/components/Modal.vue:33
	__( 'default header', 'coming-soon' ),

	// Reference: src/components/Modal.vue:34
	__( 'default body', 'coming-soon' ),

	// Reference: src/components/Mypaykit.vue:171
	__( 'Install MyPayKit plugin:', 'coming-soon' ),

	// Reference: src/components/Mypaykit.vue:172
	__( 'Install MyPayKit', 'coming-soon' ),

	// Reference: src/components/Mypaykit.vue:173
	__( 'Activate MyPayKit', 'coming-soon' ),

	// Reference: src/components/Mypaykit.vue:179
	// Reference: src/components/MypaykitOptions.vue:125
	__( 'Select a MyPayKit Form', 'coming-soon' ),

	// Reference: src/components/Mypaykit.vue:180
	__( 'You can use MyPayKit to build payment forms in minutes and collect payments.', 'coming-soon' ),

	// Reference: src/components/MypaykitOptions.vue:126
	__( 'Need to make changes?', 'coming-soon' ),

	// Reference: src/components/MypaykitOptions.vue:127
	__( 'Click here to edit the selected payment form.', 'coming-soon' ),

	// Reference: src/components/MypaykitOptions.vue:128
	__( '+ New Payment Form', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:581
	__( 'Active Text Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:583
	__( 'URL Link', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:586
	__( 'Mobile Menu', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:587
	__( 'On', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:588
	__( 'Off', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:589
	__( 'Hover Text Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:591
	__( 'Menu Type', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:593
	__( 'WordPress Menu', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:594
	__( 'Sub Menu Background Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:595
	__( 'Sub Menu Border Radius', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:596
	__( 'Sub Menu Line Height', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:597
	__( 'Sub Menu Border Width', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:598
	__( 'Sub Menu Border Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:599
	__( 'Sub Menu Text Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:600
	__( 'Sub Menu Hover Color', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:601
	__( 'Sub Menu', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:603
	__( 'Sub Menu Shadow', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:612
	__( 'Menus', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:613
	// Reference: src/components/PostauthorboxOptions.vue:365
	__( 'Go to the', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:614
	__( 'Menus Screen', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:615
	__( 'to manage your menus', 'coming-soon' ),

	// Reference: src/components/NavOptions.vue:616
	__( 'There are no menus in your site.', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:724
	// Reference: src/components/OpenAIImageControl.vue:597
	__( 'Edit with AI', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:725
	__( 'Generate AI Text', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:726
	__( 'Generate Text', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:727
	// Reference: src/components/OpenAIImageControl.vue:602
	__( 'Your AI Result', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:730
	__( 'Write with AI', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:731
	__( 'Describe your text.', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:732
	__( 'New Prompt', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:733
	__( 'Simple Language', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:734
	__( 'Make it longer', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:735
	__( 'Make it shorter', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:736
	__( 'Change text tone', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:737
	__( 'Translate text to', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:738
	// Reference: src/components/OpenAIImageControl.vue:616
	__( '---', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:739
	__( 'Choose Tone', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:740
	__( 'Choose Language', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:741
	// Reference: src/components/OpenAIImageControl.vue:626
	__( 'Suggested prompts:', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:742
	__( 'Write a catchy slogan for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:743
	__( 'Write a page title for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:744
	__( 'Suggest a 5 word headline for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:745
	__( 'Write an interesting title for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:746
	__( 'Write a section header for', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:747
	__( ' ... ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:748
	__( 'Create a list for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:749
	__( 'Write about us section for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:750
	__( 'Write a product description for', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:751
	__( 'Write a short content for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:752
	__( 'Write 50 words content for ', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:753
	__( 'English', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:754
	__( 'Spanish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:755
	__( 'French', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:756
	__( 'German', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:757
	__( 'Italian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:758
	__( 'Portuguese', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:759
	__( 'Japanese', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:760
	__( 'Korean', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:761
	__( 'Chinese', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:762
	__( 'Arabic', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:763
	__( 'Russian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:764
	__( 'Dutch', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:765
	__( 'Swedish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:766
	__( 'Norwegian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:767
	__( 'Danish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:768
	__( 'Finnish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:769
	__( 'Greek', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:770
	__( 'Hindi', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:771
	__( 'Turkish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:772
	__( 'Vietnamese', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:773
	__( 'Thai', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:774
	__( 'Indonesian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:775
	__( 'Malay', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:776
	__( 'Swahili', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:777
	__( 'Hebrew', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:778
	__( 'Polish', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:779
	__( 'Czech', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:780
	__( 'Hungarian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:781
	__( 'Romanian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:782
	__( 'Bulgarian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:783
	__( 'Ukrainian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:784
	__( 'Croatian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:785
	__( 'Serbian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:786
	__( 'Slovak', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:787
	__( 'Slovenian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:788
	__( 'Estonian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:789
	__( 'Latvian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:790
	__( 'Lithuanian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:791
	__( 'Persian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:792
	__( 'Urdu', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:793
	__( 'Tamil', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:794
	__( 'Bengali', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:795
	__( 'Punjabi', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:796
	__( 'Gujarati', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:797
	__( 'Telugu', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:798
	__( 'Marathi', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:799
	__( 'Kannada', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:800
	__( 'Malayalam', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:801
	__( 'Nepali', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:802
	__( 'Georgian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:803
	__( 'Armenian', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:804
	__( 'Azerbaijani', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:805
	__( 'Kazakh', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:806
	__( 'Uzbek', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:807
	__( 'Turkmen', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:808
	__( 'Kyrgyz', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:809
	__( 'Tajik', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:810
	__( 'Professional', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:811
	__( 'Friendly', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:812
	__( 'Funny', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:813
	__( 'Serious', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:814
	__( 'Excited', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:815
	__( 'Casual', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:816
	__( 'Formal', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:817
	// Reference: src/components/PostsOptions.vue:2090
	__( 'Creative', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:818
	__( 'Analytical', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:819
	__( 'Instructional', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:820
	__( 'Sympathetic', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:821
	__( 'Enthusiastic', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:822
	__( 'Persuasive', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:823
	__( 'Optimistic', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:824
	__( 'Respectful', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:825
	__( 'Polite', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:826
	__( 'Sincere', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:827
	__( 'Encouraging', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:828
	__( 'Calm', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:830
	__( 'Confident', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:831
	__( 'Helpful', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:832
	__( 'Informative', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:833
	__( 'Patient', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:834
	__( 'Conversational', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:835
	__( 'Empathetic', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:836
	__( 'Concise', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:837
	__( 'Courteous', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:838
	__( 'Thoughtful', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:839
	__( 'Inspirational', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:840
	// Reference: src/components/OpenAIImageControl.vue:624
	__( 'Prompt cannot be blank.', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:841
	__( 'An Internal Server error occurred. Please try again later.', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:842
	__( 'An unexpected error occurred. Please try again later.', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:843
	__( 'Your Patience is Appreciated! Expected wait: 45 seconds', 'coming-soon' ),

	// Reference: src/components/OpenAIControl.vue:844
	// Reference: src/components/OpenAIImageControl.vue:633
	__( 'Credits:', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:598
	__( 'Add with AI', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:599
	__( 'Please wait for image to upload.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:600
	__( 'Generate Image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:601
	__( 'Generate new image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:605
	__( 'Generate image with AI', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:606
	__( 'Edit image with AI', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:607
	__( 'Generate variations of your image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:608
	__( 'Remove the background of your image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:609
	__( 'Describe your image prompt.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:610
	__( 'Input your prompt for image generation.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:611
	__( 'Describe your full image prompt, not just the erased area.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:612
	__( 'Provide a detailed prompt for the entire image, including specific instructions for the erased area.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:613
	__( 'Generate with a prompt', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:614
	__( 'Remove Background', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:615
	__( 'Back', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:617
	__( 'Variations', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:618
	__( 'Generate Variations', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:619
	__( 'Click on "Generate Variations" button to produce a variations of your existing image.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:620
	__( 'Edit Image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:621
	__( 'Toggle Eraser Mode', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:622
	__( 'Generate New Image', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:623
	__( 'Erase an area on the canvas and enhance it with a prompt. Simply move the mouse and click to begin erasing.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:625
	__( 'Edit prompt cannot be blank.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:627
	__( 'Generate a futuristic cityscape with flying cars.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:628
	__( 'Generate an abstract art piece with vibrant colors and geometric shapes.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:629
	__( 'Visualize a peaceful beach scene with palm trees and a sunset.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:630
	__( 'Generate a retro-futuristic scene reminiscent of the 1980s sci-fi movies.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:631
	__( 'Create a dreamy celestial scene with stars, galaxies, and nebulae.', 'coming-soon' ),

	// Reference: src/components/OpenAIImageControl.vue:632
	__( 'Your patience is appreciated! Expected wait: 45 seconds', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:854
	__( 'Submit Button', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:859
	// Reference: src/components/SocialSharingOptions.vue:411
	// Reference: src/components/StarRatingOptions.vue:305
	// Reference: src/views/GlobalCSS.vue:2229
	__( 'Label', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:861
	__( 'Required', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:870
	__( 'Input Sizes', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:871
	__( 'Action To Take', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:879
	__( 'Remove Inline Padding', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:899
	__( 'Button text', 'coming-soon' ),

	// Reference: src/components/OptinFormOptions.vue:901
	__( 'All emails captured can be seen on the <a href="admin.php?page=seedprod_lite#/subscribers/0" class="sp-text-primary">Subscribers Page</a>. Click the <strong>Connect</strong> tab on the top menu to send your emails to the email service provider of your choice.', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:298
	__( 'Enable Particle Background', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:299
	__( 'Particle Background', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:301
	__( 'Polygon', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:303
	__( 'Snow', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:304
	__( 'Snowflakes', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:305
	__( 'Christmas', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:306
	__( 'Halloween', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:309
	__( 'Opacity', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:310
	__( 'Flow Direction', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:319
	__( 'Advanced Settings', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:320
	__( 'Number of Particles', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:321
	__( 'Particle Size', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:322
	__( 'Move Speed', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:323
	__( 'Enable Hover Effect', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:326
	__( 'Add custom JSON for the Particle Background. Please follow below steps:', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:327
	__( '- Please visit this link ', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:328
	__( 'here', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:329
	__( ' and choose required attributes for particle', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:330
	__( '- Once a custom style is created, download JSON from "Download current config (json)" link', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:331
	__( '- Copy JSON code and paste below.', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:332
	__( 'Particle hover effect will not work in the following scenarios:', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:333
	__( '- In the builder area.', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:334
	__( '- Content added in the section occupies the entire space and leaves it inaccessible.', 'coming-soon' ),

	// Reference: src/components/ParticlesBackgroundControl.vue:335
	__( 'Note: Increasing the number of particles can slow down your page.', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:341
	__( 'Profile Picture', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:343
	__( 'Biography', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:351
	// Reference: src/components/PostsOptions.vue:2009
	// Reference: src/components/TeamMemberOptions.vue:1190
	__( 'div', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:353
	// Reference: src/components/PostsOptions.vue:2011
	// Reference: src/components/TeamMemberOptions.vue:1192
	__( 'p', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:357
	__( 'Archive Posts', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:359
	// Reference: src/components/TeamMemberOptions.vue:1130
	// Reference: src/components/TestimonialOptions.vue:633
	__( 'Name Color', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:360
	__( 'Biography Color', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:363
	// Reference: src/components/TestimonialOptions.vue:667
	__( 'Name Typography', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:364
	__( 'Biography Typography', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:366
	__( 'Profile Screen', 'coming-soon' ),

	// Reference: src/components/PostauthorboxOptions.vue:367
	__( 'to manage your profile.', 'coming-soon' ),

	// Reference: src/components/PostcommentsOptions.vue:188
	__( 'Content Policy', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:582
	__( 'Author', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:585
	__( 'Comments', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:588
	// Reference: src/components/PostsOptions.vue:2084
	__( 'Avatar', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:589
	__( 'Link to Author', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:593
	// Reference: src/components/ProductMetaOptions.vue:341
	__( 'Layout Settings', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:598
	// Reference: src/components/ProductMetaOptions.vue:337
	__( 'Add Item', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:601
	__( 'May 14, 2021 (F j, Y)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:602
	__( '2021-05-14 (Y-m-d)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:603
	__( '05/14/2021 (m/d/Y)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:604
	__( '14/05/2021 (d/m/Y)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:608
	__( 'Use the letters: l D d j S F m M n Y y', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:621
	__( 'Modified Date', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:622
	__( 'Modified Time', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:624
	// Reference: src/components/PostsOptions.vue:2082
	__( 'Category', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:625
	__( 'Terms', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:627
	__( '9:15 am (g:i a)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:628
	__( '9:15 AM (g:i A)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:629
	__( '09:15 (H:i)', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:631
	__( 'Use the letters: g G H i a A', 'coming-soon' ),

	// Reference: src/components/PostinfoOptions.vue:632
	__( 'Show Icons', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1951
	__( 'Posts Query', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1952
	__( 'Query Type', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1955
	__( 'Manual', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1956
	__( 'Query By Post Type', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1957
	__( 'Include Selected Post Type(s)', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1958
	__( '- Select Post Type(s) -', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1959
	__( 'Query By Category', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1962
	__( 'Query By Tag(s)', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1963
	__( 'Include Selected Tag(s)', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1964
	__( '- Select Tag(s) -', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1965
	__( 'Query By Author(s)', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1966
	__( 'Include Selected Author(s)', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1967
	__( '- Select Author(s) -', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1971
	__( 'Date Last Modified', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1973
	__( 'Comment Count', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1976
	// Reference: src/components/TestimonialOptions.vue:649
	__( 'Autoplay Speed', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1977
	__( 'Slides to Show', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1988
	// Reference: src/components/TestimonialOptions.vue:661
	__( 'seconds', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1991
	// Reference: src/components/TestimonialOptions.vue:647
	__( 'AutoPlay', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1992
	// Reference: src/components/TestimonialOptions.vue:648
	__( 'Pause On Hover', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1995
	__( 'Query Params', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:1999
	__( 'Number Per Page', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2000
	__( 'Show Featured Image', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2001
	__( 'Show Title', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2012
	__( 'Show Meta', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2013
	__( 'Show Date Modified', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2014
	__( 'Show Author', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2015
	__( 'Show Date', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2016
	__( 'Show Time', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2017
	__( 'Show Comment Count', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2021
	__( 'Show Read More', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2027
	__( 'Meta Text', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2028
	__( 'Meta Text Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2029
	__( 'Excerpt', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2030
	__( 'Excerpt Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2031
	__( 'Read More Text', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2032
	__( 'Read More Text Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2033
	__( 'Pagination Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2034
	__( 'Space Bottom', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2042
	__( 'Meta Text Typography', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2043
	__( 'Excerpt Typography', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2044
	__( 'Read More Text Typography', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2046
	__( 'Taxonomy Typography', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2047
	__( 'Post Shadow', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2048
	__( 'Content Shadow', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2074
	__( 'Post Padding', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2075
	__( 'Image Margin', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2076
	__( 'Skin', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2077
	__( 'Classic', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2078
	__( 'Card', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2079
	__( 'Full Content', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2080
	__( 'Badge', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2081
	__( 'Badge Taxonomy', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2086
	__( 'Grid', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2087
	__( 'Carousel', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2089
	__( 'Masonry', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2091
	__( 'Minimal', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2092
	__( 'Business News', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2093
	// Reference: src/components/PriceListOptions.vue:911
	// Reference: src/components/TeamMemberOptions.vue:1161
	__( 'Image Position', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2099
	__( 'Bottom Spacing', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2101
	__( 'Navigation Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2102
	__( 'Navigation Position', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2105
	__( 'Navigation', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2106
	__( 'Arrows and Dots', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2107
	__( 'Arrows', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2108
	__( 'Dots', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2109
	__( 'Image Height', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2111
	__( 'Post Background Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2112
	__( 'Divider Border Color', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2117
	__( 'Content Area', 'coming-soon' ),

	// Reference: src/components/PostsOptions.vue:2118
	__( 'Content Area Border', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:902
	__( 'Left Layout Template', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:903
	__( 'Right Layout Template', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:904
	__( 'No Image Template', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:906
	__( 'Price List Items', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:912
	__( 'Overall Alignment', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:921
	__( 'Enter Title', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:922
	__( 'Enter Description', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:924
	__( 'Actual Price', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:925
	__( 'Offering Discount?', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:936
	__( 'Title Price Separator', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:951
	__( 'Link Complete Box', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:952
	__( 'Price Position', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:953
	__( 'Below Heading and Description', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:954
	__( 'Right of Heading', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:962
	__( 'Discount Color', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:963
	// Reference: src/components/TeamMemberOptions.vue:1175
	__( 'Image Area', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:964
	// Reference: src/components/TeamMemberOptions.vue:1176
	__( 'Shape', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:966
	// Reference: src/components/TeamMemberOptions.vue:1178
	__( 'Rounded', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:967
	// Reference: src/components/TeamMemberOptions.vue:1179
	__( 'Circle', 'coming-soon' ),

	// Reference: src/components/PriceListOptions.vue:986
	// Reference: src/components/ProductGalleryImagesOptions.vue:227
	// Reference: src/components/TeamMemberOptions.vue:1210
	__( 'Image Border Radius', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1293
	__( 'Features List', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1295
	__( 'Plan Name', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1297
	__( 'Currency Symbol', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1302
	__( 'Plan Name Color', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1303
	__( 'Top Button Size', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1304
	__( 'Top Button', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1307
	__( 'Bottom Button', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1309
	__( 'Top Size', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1310
	__( 'Top Button Before Text Icon', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1311
	__( 'Top Button After Text Icon', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1312
	__( 'Regular Price Color', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1316
	__( 'Top Button Border Radius', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1317
	__( 'Bottom Button Border Radius', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1318
	__( '$ Dollar', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1319
	__( '€ Euro', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1320
	__( '฿ Baht', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1321
	__( '₣ Franc', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1322
	__( 'ƒ Guilder', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1323
	__( 'kr Krona', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1324
	__( '₤ Lira', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1325
	__( '₧ Peseta', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1326
	__( '₱ Peso', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1327
	__( '£ Pound Sterling', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1328
	__( 'R$ Real', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1329
	__( '₽ Ruble', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1330
	__( '₨ Rupee', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1331
	__( '₹ Rupee (Indian)', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1332
	__( '₪ Shekel', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1333
	__( '¥ Yen/Yuan', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1334
	__( '₩ Won', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1336
	__( 'Custom Symbol', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1337
	__( 'Period', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1338
	__( 'Show Regular Price', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1339
	__( 'Regular Price', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1340
	__( 'Regular Price Label', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1342
	__( 'Show Top Button', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1343
	__( 'Top Button Text', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1344
	__( 'Top Button Link', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1347
	__( 'Show Bottom Button', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1348
	__( 'Bottom Button Text', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1349
	__( 'Bottom Button Link', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1352
	__( 'Top Button Color', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1353
	__( 'Features List Color', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1356
	__( 'Bottom Button Color', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1357
	__( 'Bottom Button Size', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1358
	__( 'Bottom Button Before Text Icon', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1362
	__( 'Bottom Button After Text Icon', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1367
	__( 'Price Superscript', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1368
	__( 'Price Superscript Top', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1369
	__( 'Block', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1377
	__( 'Currency Symbol Position', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1401
	__( 'Plan Name Typography', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1402
	__( 'Regular Price Typography', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1405
	__( 'Price Superscript Typography', 'coming-soon' ),

	// Reference: src/components/PricingTableOptions.vue:1406
	__( 'Features List Typography', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:338
	// Reference: src/components/ProductGalleryImagesOptions.vue:209
	__( 'Tab', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:340
	// Reference: src/components/ProductGalleryImagesOptions.vue:211
	__( 'Panel', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:341
	// Reference: src/components/ProductGalleryImagesOptions.vue:212
	// Reference: src/components/WpWidgetBlockOptions.vue:220
	__( 'Heading', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:342
	// Reference: src/components/ProductGalleryImagesOptions.vue:213
	__( 'Tabs', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:343
	// Reference: src/components/ProductGalleryImagesOptions.vue:214
	// Reference: src/components/WpWidgetBlockOptions.vue:212
	__( 'Heading Color', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:347
	// Reference: src/components/ProductGalleryImagesOptions.vue:218
	__( 'Panel Padding', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:348
	__( 'Tab Typography', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:349
	__( 'Panel Typography', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:350
	__( 'Panel Header Typography', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:351
	__( 'Panel Shadow', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:352
	__( 'Panel Border Radius', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:353
	__( 'Panel Border Width', 'coming-soon' ),

	// Reference: src/components/ProductDataTabsOptions.vue:354
	__( 'Panel Border', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:220
	__( 'Zoom Button', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:221
	__( 'Position Right', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:222
	__( 'Position Top', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:229
	__( 'Thumbnails Border', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:230
	__( 'Thumbnails Border Radius', 'coming-soon' ),

	// Reference: src/components/ProductGalleryImagesOptions.vue:231
	__( 'Thumbnails Border Width', 'coming-soon' ),

	// Reference: src/components/ProductMetaOptions.vue:327
	__( 'View', 'coming-soon' ),

	// Reference: src/components/ProductMetaOptions.vue:328
	__( 'Table', 'coming-soon' ),

	// Reference: src/components/ProductMetaOptions.vue:329
	// Reference: src/components/ProductPriceOptions.vue:187
	// Reference: src/components/SocialProfilesOptions.vue:691
	__( 'Stacked', 'coming-soon' ),

	// Reference: src/components/ProductMetaOptions.vue:330
	__( 'Inline', 'coming-soon' ),

	// Reference: src/components/ProductMetaOptions.vue:352
	__( 'Divider Typography', 'coming-soon' ),

	// Reference: src/components/ProductPriceOptions.vue:186
	__( 'Sale Price', 'coming-soon' ),

	// Reference: src/components/ProductPriceOptions.vue:189
	__( 'Sale Price Typography', 'coming-soon' ),

	// Reference: src/components/ProductRelatedOptions.vue:501
	__( 'Posts Per Page', 'coming-soon' ),

	// Reference: src/components/ProductRelatedOptions.vue:510
	// Reference: src/components/WCCustomProductsGridOptions.vue:1075
	__( 'Sale Badge Color', 'coming-soon' ),

	// Reference: src/components/ProgressBarOptions.vue:325
	__( 'Progress Bar', 'coming-soon' ),

	// Reference: src/components/ProgressBarOptions.vue:326
	__( 'Bar Text', 'coming-soon' ),

	// Reference: src/components/ProgressBarOptions.vue:327
	__( 'Percent', 'coming-soon' ),

	// Reference: src/components/ProgressBarOptions.vue:329
	__( 'My Text', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:189
	__( 'PushEngage allows you to send personalized campaigns to your website visitors and gives you an easy way to collect subscribers. It’s a marketing plugin that helps you get more web push notification subscribers, increase sales, and grow your business.', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:190
	__( 'Visit Dashboard', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:191
	__( 'Push Broadcasts', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:192
	__( 'Drip Autoresponders', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:193
	__( 'Audience', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:194
	// Reference: src/views/SettingsAnalytics.vue:105
	// Reference: src/views/SetupSettings.vue:250
	__( 'Analytics', 'coming-soon' ),

	// Reference: src/components/PushNotificationOptions.vue:195
	__( 'Documentation', 'coming-soon' ),

	// Reference: src/components/Row.vue:1116
	// Reference: src/components/Section.vue:687
	// Reference: src/views/BuilderView.vue:1042
	__( 'Drag a new block here', 'coming-soon' ),

	// Reference: src/components/Row.vue:1117
	// Reference: src/components/Section.vue:691
	// Reference: src/views/BuilderView.vue:1043
	// Reference: src/views/Layoutnav.vue:1491
	__( 'Choose your layout:', 'coming-soon' ),

	// Reference: src/components/Row.vue:1118
	__( 'Enter a new block template name:', 'coming-soon' ),

	// Reference: src/components/Row.vue:1120
	// Reference: src/components/Section.vue:690
	// Reference: src/views/Layoutnav.vue:1490
	// Reference: src/views/Setup.vue:1060
	__( 'Save Template', 'coming-soon' ),

	// Reference: src/components/Row.vue:1121
	// Reference: src/components/Section.vue:692
	// Reference: src/views/BuilderView.vue:1044
	__( 'Add Columns', 'coming-soon' ),

	// Reference: src/components/Row.vue:1122
	__( 'Row Settings', 'coming-soon' ),

	// Reference: src/components/Row.vue:1123
	__( 'Move Row', 'coming-soon' ),

	// Reference: src/components/Row.vue:1124
	__( 'Resize Columns', 'coming-soon' ),

	// Reference: src/components/Row.vue:1125
	// Reference: src/views/Layoutnav.vue:1500
	__( 'Duplicate Row', 'coming-soon' ),

	// Reference: src/components/Row.vue:1126
	// Reference: src/views/Layoutnav.vue:1501
	__( 'Delete Row', 'coming-soon' ),

	// Reference: src/components/Row.vue:1127
	__( 'Column Settings', 'coming-soon' ),

	// Reference: src/components/Row.vue:1128
	__( 'Move Block', 'coming-soon' ),

	// Reference: src/components/Row.vue:1129
	__( 'Block Settings', 'coming-soon' ),

	// Reference: src/components/Row.vue:1130
	__( 'Save Block', 'coming-soon' ),

	// Reference: src/components/Row.vue:1131
	// Reference: src/views/Layoutnav.vue:1509
	__( 'Duplicate Block', 'coming-soon' ),

	// Reference: src/components/Row.vue:1132
	// Reference: src/views/Layoutnav.vue:1510
	__( 'Delete Block', 'coming-soon' ),

	// Reference: src/components/Row.vue:1133
	__( 'Add Block', 'coming-soon' ),

	// Reference: src/components/Row.vue:1134
	// Reference: src/views/Layoutnav.vue:1499
	__( 'Add Row', 'coming-soon' ),

	// Reference: src/components/RowOptions.vue:678
	__( 'Column Gutter', 'coming-soon' ),

	// Reference: src/components/RowOptions.vue:708
	__( 'Row Width', 'coming-soon' ),

	// Reference: src/components/RowOptions.vue:709
	// Reference: src/components/SectionOptions.vue:378
	__( 'Full Screen', 'coming-soon' ),

	// Reference: src/components/RowOptions.vue:710
	// Reference: src/components/SectionOptions.vue:379
	__( 'Fixed Width', 'coming-soon' ),

	// Reference: src/components/RowOptions.vue:711
	// Reference: src/components/SectionOptions.vue:380
	__( 'Content Width', 'coming-soon' ),

	// Reference: src/components/Section.vue:688
	// Reference: src/views/Layoutnav.vue:1488
	__( 'Enter a new section template name:', 'coming-soon' ),

	// Reference: src/components/Section.vue:693
	// Reference: src/views/BuilderView.vue:1045
	// Reference: src/views/Layoutnav.vue:1492
	__( 'Add Section', 'coming-soon' ),

	// Reference: src/components/Section.vue:694
	__( 'Move Section', 'coming-soon' ),

	// Reference: src/components/Section.vue:695
	__( 'Section Settings', 'coming-soon' ),

	// Reference: src/components/Section.vue:696
	__( 'Save Global Section', 'coming-soon' ),

	// Reference: src/components/Section.vue:697
	// Reference: src/views/Layoutnav.vue:1494
	__( 'Save Section', 'coming-soon' ),

	// Reference: src/components/Section.vue:698
	// Reference: src/views/Layoutnav.vue:1493
	__( 'Duplicate Section', 'coming-soon' ),

	// Reference: src/components/Section.vue:699
	// Reference: src/views/Layoutnav.vue:1496
	__( 'Delete Section', 'coming-soon' ),

	// Reference: src/components/SectionOptions.vue:377
	__( 'Section Width', 'coming-soon' ),

	// Reference: src/components/SettingsLiteCTA.vue:226
	__( 'Powerful Page Editor', 'coming-soon' ),

	// Reference: src/components/SettingsLiteCTA.vue:228
	__( 'Email Marketing Integrations', 'coming-soon' ),

	// Reference: src/components/SettingsLiteCTA.vue:233
	__( 'Access Controls', 'coming-soon' ),

	// Reference: src/components/SettingsLiteCTA.vue:239
	__( 'Subscriber Management', 'coming-soon' ),

	// Reference: src/components/SettingsLiteCTA.vue:256
	__( 'Upgrade to SeedProd Pro<br />Today and Save', 'coming-soon' ),

	// Reference: src/components/ShadowControl.vue:145
	__( 'Spread', 'coming-soon' ),

	// Reference: src/components/ShadowControl.vue:147
	__( 'Outline', 'coming-soon' ),

	// Reference: src/components/ShadowControl.vue:148
	__( 'Inset', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:351
	__( 'Mountains', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:352
	__( 'Drops', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:353
	__( 'Clouds', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:354
	__( 'Zigzag', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:355
	__( 'Pyramids', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:356
	__( 'Triangle', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:357
	__( 'Triangle Asymmetrical', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:358
	__( 'Tilt', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:359
	__( 'Tilt Opacity', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:360
	__( 'Fan Opacity', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:361
	__( 'Curve', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:362
	__( 'Curve Asymmetrical', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:363
	__( 'Waves', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:364
	__( 'Waves Brush', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:365
	__( 'Waves Pattern', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:366
	__( 'Arrow', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:367
	__( 'Split', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:368
	__( 'Book', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:373
	__( 'Invert', 'coming-soon' ),

	// Reference: src/components/ShapeDividerControl.vue:374
	__( 'Bring to Front', 'coming-soon' ),

	// Reference: src/components/ShortcodeOptions.vue:146
	__( 'Show Shortcode Preview', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:675
	// Reference: src/components/SocialSharingOptions.vue:406
	__( 'Select a Type', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:676
	// Reference: src/components/SocialSharingOptions.vue:407
	__( 'Facebook', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:677
	// Reference: src/components/SocialSharingOptions.vue:408
	__( 'X (Twitter)', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:678
	// Reference: src/components/SocialSharingOptions.vue:409
	__( 'LinkedIn', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:679
	// Reference: src/components/SocialSharingOptions.vue:410
	__( 'Pinterest', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:680
	// Reference: src/components/VideoOptions.vue:236
	// Reference: src/components/VideoPopUpOptions.vue:1132
	__( 'YouTube', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:681
	__( 'Instagram', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:682
	__( 'Snapchat', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:683
	__( 'WordPress', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:684
	__( 'Github', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:685
	__( 'SoundCloud', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:686
	__( 'RSS', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:688
	__( 'URL (Include https:// for web links)', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:698
	// Reference: src/components/TeamMemberOptions.vue:1122
	__( 'Icon Padding', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:707
	__( 'Phone', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:709
	__( 'Discord', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:710
	__( 'Telegram', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:711
	__( 'Facebook Messenger', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:712
	__( 'Slack', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:713
	// Reference: src/components/VideoPopUpOptions.vue:1146
	__( 'Vimeo', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:714
	__( 'Weibo', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:715
	__( 'WhatsApp', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:716
	__( 'TikTok', 'coming-soon' ),

	// Reference: src/components/SocialProfilesOptions.vue:723
	// Reference: src/components/SocialSharingOptions.vue:404
	__( 'Add New Share', 'coming-soon' ),

	// Reference: src/components/SocialSharingOptions.vue:403
	__( 'Social Sharing', 'coming-soon' ),

	// Reference: src/components/SocialSharingOptions.vue:412
	__( 'Tweet Text', 'coming-soon' ),

	// Reference: src/components/SpacerOptions.vue:67
	__( 'Spacer', 'coming-soon' ),

	// Reference: src/components/StarRatingOptions.vue:301
	__( 'Star Rating', 'coming-soon' ),

	// Reference: src/components/StarRatingOptions.vue:308
	__( 'Star Color', 'coming-soon' ),

	// Reference: src/components/StarRatingOptions.vue:310
	__( 'Empty Star Color', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1732
	__( 'Connect with Stripe', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1733
	__( 'Connect with', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1734
	__( 'Reconnect', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1735
	__( 'Connect or Create your Stripe account to start collecting payments.', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1736
	__( 'Pay as you go pricing: 3% fee per-transaction + Stripe fees', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1756
	__( 'Payment Setup', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1757
	__( 'Amount', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1758
	__( 'Payment Description', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1759
	__( 'Payment Currency', 'coming-soon' ),

	// Reference: src/components/StripepaymentOptions.vue:1760
	__( 'Success URL', 'coming-soon' ),

	// Reference: src/components/SuperScriptControl.vue:111
	__( 'SuperScript Top', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1096
	__( 'Show Designation', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1098
	__( 'Show Description', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1102
	// Reference: src/components/VideoPopUpOptions.vue:1170
	__( 'PX', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1103
	__( '%', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1106
	__( 'Below Name', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1107
	__( 'Below Designation', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1108
	__( 'Below Description', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1110
	__( 'Thickness', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1112
	__( 'Social Icons', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1139
	__( 'Designation Color', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1141
	__( 'Designation', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1166
	__( 'Social Icon Align', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1167
	__( 'Separator Align', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1168
	__( 'Separator Margin Bottom', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1169
	__( 'Designation Align', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1170
	__( 'Designation Margin Bottom', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1172
	__( 'Name Align', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1173
	__( 'Name Margin Bottom', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1174
	__( 'Description Margin Bottom', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1194
	__( 'Designation Typography', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1196
	__( 'Image Margin Bottom', 'coming-soon' ),

	// Reference: src/components/TeamMemberOptions.vue:1211
	__( 'Social Icon Border Radius', 'coming-soon' ),

	// Reference: src/components/TemplatetagOptions.vue:148
	__( 'Template Tag', 'coming-soon' ),

	// Reference: src/components/TemplatetagOptions.vue:152
	__( 'Show Template Tag Preview', 'coming-soon' ),

	// Reference: src/components/TemplatetagOptions.vue:153
	__( 'Select Type', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:639
	__( 'Add Testimonial', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:642
	__( 'Navigation Color Mode', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:643
	__( 'Dark', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:644
	__( 'Light', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:650
	__( 'Testimonial to Show', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:662
	__( 'Enable Comment Bubble', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:663
	__( 'Bubble Color', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:664
	__( 'Testimonials', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:665
	__( 'Carousel Settings', 'coming-soon' ),

	// Reference: src/components/TestimonialOptions.vue:668
	__( 'Show Image', 'coming-soon' ),

	// Reference: src/components/TextOptions.vue:389
	__( 'Visual Editor', 'coming-soon' ),

	// Reference: src/components/TextOptions.vue:390
	__( 'Edit HTML', 'coming-soon' ),

	// Reference: src/components/TypographyControl.vue:317
	__( 'Typography', 'coming-soon' ),

	// Reference: src/components/TypographyControl.vue:322
	__( 'Line Height', 'coming-soon' ),

	// Reference: src/components/TypographyControl.vue:323
	__( 'Letter Spacing', 'coming-soon' ),

	// Reference: src/components/TypographyControl.vue:326
	__( 'Letter Case', 'coming-soon' ),

	// Reference: src/components/UpgradeCTABuilder.vue:50
	__( 'Thanks for being a loyal SeedProd Lite user. Upgrade to
SeedProd Lite to unlock all the awesome features and
experience why SeedProd is the best WordPress landing
page plugin.', 'coming-soon' ),

	// Reference: src/components/UpgradeCTABuilder.vue:89
	__( 'is not available, upgrade to unlock', 'coming-soon' ),

	// Reference: src/components/UpgradeCTABuilder.vue:90
	__( 'is not available on your plan. Please upgrade to a higher plan to access this feature.', 'coming-soon' ),

	// Reference: src/components/UpgradeCTABuilder.vue:91
	__( 'Upgrade Your License', 'coming-soon' ),

	// Reference: src/components/VideoControl.vue:107
	__( 'Choose New Video', 'coming-soon' ),

	// Reference: src/components/VideoControl.vue:108
	__( 'Select Video', 'coming-soon' ),

	// Reference: src/components/VideoOptions.vue:233
	// Reference: src/components/VideoPopUpOptions.vue:1129
	__( 'Video', 'coming-soon' ),

	// Reference: src/components/VideoOptions.vue:238
	// Reference: src/components/VideoPopUpOptions.vue:1134
	// Reference: src/views/GlobalCSS.vue:2166
	// Reference: src/views/SetupDesign-Lite.vue:735
	__( 'YouTube URL', 'coming-soon' ),

	// Reference: src/components/VideoOptions.vue:239
	// Reference: src/components/VideoPopUpOptions.vue:1135
	__( 'Custom Video Code', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1131
	__( 'Source', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1145
	__( 'Vimeo URL', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1147
	__( 'Start Time', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1148
	__( 'Specify a start time (in seconds)', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1150
	__( 'Specify an end time (in seconds)', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1151
	__( 'External URL', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1152
	__( 'Custom Video URL', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1153
	__( 'Select Custom Video', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1154
	__( 'Video Options', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1155
	__( 'Autoplay', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1156
	__( 'Mute', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1157
	__( 'Loop', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1158
	__( 'Show Player Controls', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1159
	__( 'Show Download Button', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1160
	__( 'Preload', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1161
	__( 'Metadata', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1163
	__( 'Preload attribute lets you specify how the video should be loaded when the page loads.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1164
	__( 'Learn more here.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1165
	__( 'Poster', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1166
	__( 'Image Overlay', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1167
	__( 'Choose Image', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1171
	__( 'Show Play Icon', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1172
	__( 'Enable Lightbox', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1173
	__( 'Enable Sticky Video', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1174
	__( 'Enable Image Overlay', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1175
	__( 'Play on Mobile', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1176
	__( 'Modest Branding', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1177
	__( 'Privacy Mode', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1178
	__( 'When you turn on privacy mode, YouTube/Vimeo won\'t store information about visitors on your website unless they play the video.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1179
	__( 'Lazy Load', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1180
	__( 'Suggested Videos', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1181
	__( 'Current Video Channel', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1182
	__( 'Any Video', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1183
	__( 'Intro Title', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1184
	__( 'Intro Portrait', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1185
	__( 'Intro Byline', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1186
	__( 'Controls Color', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1187
	__( 'Play video inline on mobile devices instead of automatically going into fullscreen mode.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1188
	__( 'Icon Font Size', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1189
	__( 'Icon Opacity', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1191
	__( 'Click on Preview and scroll past the video on the page to see this in action.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1192
	__( 'Aspect Ratio', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1193
	__( '1:1', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1194
	__( '3:2', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1195
	__( '4:3', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1196
	__( '9:16', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1197
	__( '16:9', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1198
	__( '21:9', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1199
	__( 'Teaser Video', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1200
	__( 'Enable Teaser Video', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1201
	__( 'Show Banner', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1202
	__( 'Disabled because Teaser Video feature has been enabled.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1203
	__( 'Disabled because Image Overlay feature has been enabled.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1204
	__( 'Banner Text', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1205
	__( 'Banner Background Color', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1206
	__( 'Banner Text Color', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1207
	__( 'Banner Text Typography', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1208
	__( 'Banner Text Icon', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1209
	__( 'Teaser Video Play Icon', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1210
	__( 'Video must be hosted by a Starter, Standard, Advanced, Plus, Pro, Business, Premium, or Enterprise account.', 'coming-soon' ),

	// Reference: src/components/VideoPopUpOptions.vue:1212
	__( 'Poster Image Src', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1034
	__( 'Limit', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1043
	__( 'Select By SKU', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1044
	__( '- Select SKU(s) -', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1051
	__( 'On Sale', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1052
	__( 'Best Selling', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1053
	__( 'Top Rated', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1054
	__( 'Query By Attribute', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1055
	__( '- Select Attribute -', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1056
	__( '- Select Attribute Terms -', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1063
	__( 'Select By Visibility', 'coming-soon' ),

	// Reference: src/components/WCCustomProductsGridOptions.vue:1064
	__( '- Select Product Visibility -', 'coming-soon' ),

	// Reference: src/components/WpWidgetBlock-Lite.vue:58
	__( '(Click \'Apply\' on the Widget Settings to see changes)', 'coming-soon' ),

	// Reference: src/components/WpWidgetBlockOptions.vue:217
	__( 'Widget Settings', 'coming-soon' ),

	// Reference: src/components/WpWidgetBlockOptions.vue:219
	__( 'Heading Bottom Margin', 'coming-soon' ),

	// Reference: src/components/WpWidgetBlockOptions.vue:221
	__( 'Heading Typography', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1028
	__( 'Block Uploaded successfully.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1162
	__( 'Column Uploaded successfully.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1191
	__( 'There is no content available to paste. Please try copy row first!', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1239
	__( 'Image uploading will take some time.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1303
	__( 'Row Uploaded successfully.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1361
	__( 'Block Copied.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1391
	__( 'Column Copied.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1423
	__( 'Row Copied.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1460
	__( 'Section Copied.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1760
	// Reference: src/views/SectionTemplateOptions-Lite.vue:478
	// Reference: src/views/TemplateChooser-Lite.vue:1530
	__( 'Are you sure you want to delete?', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1769
	// Reference: src/views/SectionTemplateOptions-Lite.vue:487
	// Reference: src/views/TemplateChooser-Lite.vue:1539
	__( 'Yes, delete it!', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1892
	__( 'Visit Docs&nbsp;<i class="fas fa-external-link-alt"></i>', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1940
	__( '<strong>Bonus:</strong>&nbsp;SeedProd Lite users get a discount off the regular price, automatically applied at checkout.', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1976
	__( 'Upgrade to Unlock ', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1980
	__( ' feature is not available on your plan. Please upgrade your plan to unlock this feature and more!', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:1996
	__( 'Upgrade with just a click of a button!', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:756
	__( 'There is no content available to paste. Please try copy section first!', 'coming-soon' ),

	// Reference: src/mixins/helpers.js:881
	__( 'Section Uploaded successfully.', 'coming-soon' ),

	// Reference: src/views/BuilderView.vue:1046
	__( 'Header Template', 'coming-soon' ),

	// Reference: src/views/BuilderView.vue:1047
	__( 'Footer Template', 'coming-soon' ),

	// Reference: src/views/BuilderView.vue:1048
	__( 'You’re using SeedProd Lite. To unlock more features consider <a href=\'%s\' class=\'%s\' target=\'_blank\'>upgrading to Pro</a>.', 'coming-soon' ),

	// Reference: src/views/BuilderView.vue:1051
	__( 'Drag New Block Here', 'coming-soon' ),

	// Reference: src/views/BuilderView.vue:1052
	__( 'Template Dev Mode - Under "Save" select "Save Global Template" to Create/Edit the template. If you selected a Blank Template to start with a New Template will be created otherwise the template will be updated. After creating a new global template you will need to go back and select the template to edit it.', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2160
	// Reference: src/views/SetupBlockOptions-Lite.vue:1433
	// Reference: src/views/SetupDesign-Lite.vue:729
	__( 'Editing:', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2161
	// Reference: src/views/Setup.vue:1077
	__( 'Global CSS Settings', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2162
	// Reference: src/views/SetupDesign-Lite.vue:731
	__( 'Header Font', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2163
	// Reference: src/views/SetupDesign-Lite.vue:732
	__( 'Body Text Font', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2165
	__( 'Use Video Background', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2167
	// Reference: src/views/SetupDesign-Lite.vue:736
	__( 'The video background will not be displayed on mobile devices or in the editor preview.', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2171
	// Reference: src/views/SetupDesign-Lite.vue:737
	__( 'Play on auto-loop', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2172
	// Reference: src/views/SetupDesign-Lite.vue:738
	__( 'Enter your custom css below', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2173
	// Reference: src/views/SetupDesign-Lite.vue:739
	__( 'Edit Custom CSS', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2176
	// Reference: src/views/SetupDesign-Lite.vue:742
	__( 'Font Themes Library', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2177
	// Reference: src/views/SetupDesign-Lite.vue:743
	__( 'Search colors...', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2178
	// Reference: src/views/SetupDesign-Lite.vue:744
	__( 'Search fonts...', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2182
	// Reference: src/views/SetupDesign-Lite.vue:748
	__( 'Links', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2184
	// Reference: src/views/SetupDesign-Lite.vue:750
	__( 'Font Themes', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2185
	// Reference: src/views/SetupDesign-Lite.vue:751
	__( 'Color Palettes', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2187
	// Reference: src/views/SetupDesign-Lite.vue:753
	__( 'Fonts', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2190
	// Reference: src/views/SetupDesign-Lite.vue:756
	__( 'Custom CSS', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2191
	// Reference: src/views/SetupDesign-Lite.vue:757
	__( 'All Palettes', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2192
	// Reference: src/views/SetupDesign-Lite.vue:758
	__( 'All Themes', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2193
	// Reference: src/views/SetupDesign-Lite.vue:759
	__( 'Serif', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2194
	// Reference: src/views/SetupDesign-Lite.vue:760
	__( 'Sans Serif', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2195
	// Reference: src/views/SetupDesign-Lite.vue:761
	__( 'Background Slideshow', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2196
	// Reference: src/views/SetupDesign-Lite.vue:762
	__( 'Slideshow Images', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2197
	// Reference: src/views/SetupDesign-Lite.vue:763
	__( 'Add New Slide', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2198
	// Reference: src/views/SetupDesign-Lite.vue:764
	__( 'The background slideshow will not be shown in the editor preview. The slides will only be displayed in the live preview.', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2203
	__( 'Add Custom Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2204
	__( 'Body Text Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2205
	__( 'Show H1 - H6 Settings', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2207
	__( 'H1 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2208
	__( 'H1 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2209
	__( 'H2 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2210
	__( 'H2 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2211
	__( 'H3 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2212
	__( 'H3 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2213
	__( 'H4 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2214
	__( 'H4 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2215
	__( 'H5 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2216
	__( 'H5 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2217
	__( 'H6 ', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2218
	__( 'H6 Color', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2219
	__( 'Body Text', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2220
	__( 'Desktop BG', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2221
	__( 'Mobile BG', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2226
	__( 'Row Default Max Width', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2228
	__( 'Forms', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2235
	__( 'Field Border Style', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2240
	__( 'Field Padding', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2241
	__( 'Header Default Bottom Margin', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2245
	__( 'Paragraph Default Bottom Margin', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2249
	__( 'Label Default Bottom Margin', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2258
	__( 'H1 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2259
	__( 'H2 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2260
	__( 'H3 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2261
	__( 'H4 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2262
	__( 'H5 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2263
	__( 'H6 Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2264
	__( 'Body Text Typography', 'coming-soon' ),

	// Reference: src/views/GlobalCSS.vue:2267
	__( 'Field Typography', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:265
	__( 'Ask a question or search the docs...', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:267
	__( 'No docs found', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:268
	__( 'View Documentation', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:269
	__( 'Browse documentation, reference material, and tutorials for SeedProd.', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:270
	__( 'View All Documentation', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:271
	__( 'Get Support', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:272
	__( 'Submit a ticket and our world class support team will be in touch soon.', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:273
	__( 'Submit a Support Ticket', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:274
	__( 'Upgrade to SeedProd Pro to access our world class customer support', 'coming-soon' ),

	// Reference: src/views/InlineHelpView.vue:275
	__( 'Upgrade to SeedProd Pro', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1483
	__( 'Expand All', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1484
	__( 'Collapse All', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1485
	__( 'Section', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1497
	__( 'Copy Section', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1498
	__( 'Paste Section', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1502
	__( 'Copy Row', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1503
	__( 'Paste Row', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1504
	__( 'Add Column', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1505
	__( 'Duplicate Column', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1506
	__( 'Delete Column', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1507
	__( 'Copy Column', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1508
	__( 'Paste Column', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1511
	__( 'Copy Block', 'coming-soon' ),

	// Reference: src/views/Layoutnav.vue:1512
	__( 'Paste Block', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:62
	__( 'Loading Revisions', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:63
	__( 'Click to preview version:', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:64
	__( 'Current Version', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:65
	__( 'by', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:66
	__( 'There are no revisions to show.', 'coming-soon' ),

	// Reference: src/views/Revisions.vue:67
	__( 'ago', 'coming-soon' ),

	// Reference: src/views/SectionTemplateOptions-Lite.vue:435
	__( 'Choose This Section', 'coming-soon' ),

	// Reference: src/views/SectionTemplateOptions-Lite.vue:542
	// Reference: src/views/TemplateChooser-Lite.vue:1593
	__( 'Deleted!', 'coming-soon' ),

	// Reference: src/views/SectionTemplates-Lite.vue:100
	__( 'All Sections', 'coming-soon' ),

	// Reference: src/views/SectionTemplates-Lite.vue:101
	__( 'Favorites', 'coming-soon' ),

	// Reference: src/views/SectionTemplates-Lite.vue:102
	__( 'Categories:', 'coming-soon' ),

	// Reference: src/views/SectionTemplates-Lite.vue:98
	// Reference: src/views/SetupBlockOptions-Lite.vue:1428
	__( 'Blocks', 'coming-soon' ),

	// Reference: src/views/SectionTemplates-Lite.vue:99
	// Reference: src/views/SetupBlockOptions-Lite.vue:1429
	__( 'Sections', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:323
	// Reference: src/views/SetupSettings.vue:248
	__( 'Access Control', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:324
	__( 'Exclude Default:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:327
	__( 'By default we exclude urls with the terms: login, admin, dashboard and account to prevent lockouts.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:328
	__( 'Bypass Cookie:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:329
	__( 'Use cookies instead of creating a WordPress user for the bypass. Note: this may not work on sites that are cached. Learn More.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:330
	__( 'Bypass URL:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:331
	__( 'Bypass URL Expires:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:332
	__( 'Access by IP:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:333
	__( 'Access by Role:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:334
	__( 'By default anyone logged in will see the regular website and not the coming soon page. To override this select Roles that will be given access to see the regular website.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:335
	__( 'Include/Exclude URLs:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:336
	__( 'Show on the Entire Website', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:337
	__( 'Show on the Entire Website except for the Blog', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:338
	__( 'Show on the Home Page Only', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:339
	__( 'Include URLs', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:340
	__( 'Exclude URLs', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:341
	__( 'By default the Coming Soon/Maintenance page is shown on every page. Use the \'Show on the Home Page Only\' option to only show on the home page. Alternatively Include or Exclude URLs.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:342
	__( 'Enter a phrase above and give your visitors a secret url that will allow them to bypass the Coming Soon page.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:343
	__( 'After the bypass url expires the user will need to revisit the bypass url to regain access.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:344
	__( 'One IP Address per line', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:345
	__( 'Allow visitors from certain IP\'s to bypass the Coming Soon page.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:346
	__( 'Put each IP on it\'s own line. Your current IP is:', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:347
	__( 'Add Role', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:348
	__( 'Include certain urls to display the Coming Soon or Maintenance Page.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:349
	__( 'One per line. You may also enter a page or post id.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:350
	__( 'Exclude certain urls to display the Coming Soon or Maintenance Page.', 'coming-soon' ),

	// Reference: src/views/SettingsAccess-Lite.vue:351
	__( 'Example: /about-us/', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:106
	__( 'Your Analytics settings are being managed by:', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:110
	__( 'Edit Analytics Settings', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:111
	// Reference: src/views/SettingsSEO.vue:169
	__( 'Your SEO settings are being managed by:', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:115
	__( 'Install Google Analytics plugin:', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:119
	__( 'Install MonsterInsights', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:120
	__( 'Activate MonsterInsights', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:121
	__( 'Pro', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:122
	__( 'Installing', 'coming-soon' ),

	// Reference: src/views/SettingsAnalytics.vue:123
	__( 'Install Analytics plugin:', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:120
	// Reference: src/views/SetupSettings.vue:252
	__( 'Custom Domain', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:121
	__( 'Custom Domain:', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:122
	__( 'OFF', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:123
	__( 'ON', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:124
	__( 'Domain Name:', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:125
	__( 'Please enter your domain.', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:126
	__( 'Click here to learn more', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:127
	__( 'how to map your custom domain.', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:128
	__( 'Force HTTPS', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:129
	__( 'Only enable this if you have an SSL certificate installed and you wish to redirect users to https://', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:130
	__( 'Please enter a valid URL that will be pointed to this landing page, such as', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:131
	__( 'It should look something like, \'mynewdomain.com\' or \'mynewdomain.com/coming-soon\'', 'coming-soon' ),

	// Reference: src/views/SettingsDomain.vue:132
	__( 'You can leave out the \'http://\'. If you are using \'https://\', turn on Force HTTPS below.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:260
	__( 'Page Title:', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:261
	// Reference: src/views/TemplateChooser-Lite.vue:1348
	__( 'Page URL:', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:262
	__( 'Page Status:', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:263
	__( 'Draft', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:264
	// Reference: src/views/Setup.vue:1055
	__( 'Publish', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:265
	__( '"Powered by SeedProd"', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:268
	__( 'Choose New Template:', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:269
	__( 'Redirect Mode', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:270
	__( 'Redirect URL: A temporary redirect (302 status) will be created to the url entered.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:271
	__( 'Enable', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:272
	__( 'Redirect URL: A permanent redirect (301 status) will be created to the url entered.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:274
	__( 'SeedProd Link', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:275
	__( 'Enter Your Affiliate URL and Make Money with SeedProd', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:276
	__( 'Join our affiliate program', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:277
	__( 'and get a 20% commission on all sales generated from your powered by link.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:278
	__( 'Isolation Mode', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:279
	__( 'Redirect the Default Login Page', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:280
	__( 'Facebook App ID:', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:281
	__( 'Isolation Mode prevents two WordPress hooks from running called wp_head and wp_footer. This will prevent conflicts with your theme or other plugins. While it prevents conflicts, it also means other plugins would not run on the page such as SEO and analytics plugins. You can manually set these under the SEO and Scripts menus on the left.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:282
	__( 'The Redirect the Default Login Page option should redirect all calls to the default login page which is located at', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:283
	__( 'however it does not prevent it from being accessed as that default login page will still be used for password resets, login errors, and registration if that is enabled.', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:284
	__( 'Choose Template', 'coming-soon' ),

	// Reference: src/views/SettingsGeneral.vue:286
	__( 'This will delete the current template and content.', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:168
	// Reference: src/views/SetupSettings.vue:249
	__( 'SEO', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:174
	__( 'Edit this page\'s SEO Settings', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:179
	__( 'Install All in One SEO', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:180
	__( 'Activate All in One SEO', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:181
	__( 'SEO Title', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:182
	__( 'SEO Description', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:183
	__( 'Favicon', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:184
	__( 'Install SEO plugin:', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:185
	__( 'Social Media Thumbnail', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:186
	__( 'Enable No Index', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:187
	__( 'Rank Math', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:188
	__( 'The SEO Framework', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:190
	__( 'All in One SEO Pro', 'coming-soon' ),

	// Reference: src/views/SettingsSEO.vue:191
	__( 'Yoast SEO', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:56
	// Reference: src/views/SetupSettings.vue:251
	__( 'Scripts', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:57
	__( 'Header Scripts:', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:58
	__( 'This code will be rendered before the closing &lt;/head&gt; tag.', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:62
	__( 'Footer Scripts:', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:63
	__( 'The code will be rendered before the closing &lt;/body&gt; tag.', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:67
	__( 'Body Scripts:', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:68
	__( 'The code will be rendered after the &lt;body&gt; tag.', 'coming-soon' ),

	// Reference: src/views/SettingsScripts.vue:72
	__( 'You do not have the proper WordPress permissions to add scripts.', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1044
	__( 'Design', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1045
	__( 'Connect', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1046
	__( 'Page Settings', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1047
	__( 'You have unsaved changes. Are you sure you want to lose these changes?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1048
	__( 'Exit Without Saving', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1049
	__( 'template has been saved!', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1050
	__( 'You can find it in your template library when you create a new landing page.', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1051
	__( 'Enter a new template name:', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1053
	__( 'Your page has been published!', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1056
	__( 'Unpublish', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1057
	__( 'Schedule', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1058
	__( 'Save as Template', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1059
	__( 'Return to Page Editor', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1061
	__( 'See Live Page', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1062
	__( 'Save and Exit', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1063
	__( 'Help', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1064
	__( 'Save', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1067
	__( 'You are about to leave this page and go to the Global CSS page. Continue?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1068
	__( 'This page is not active. Would you like to activate it now?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1069
	__( 'Yes, Activate', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1070
	__( 'No, Close', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1071
	__( 'Coming Soon Mode is not active. Would you like to activate it now and show this page to visitors?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1072
	__( 'Maintenance Mode is not active. Would you like to activate it now and show this page to visitors?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1073
	__( 'Note: This page will only be shown to users who are logged out. If you logged out and and do not see the correct page you may need to clear your site cache.', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1074
	__( 'This page is not published. Would you like to publish it now?', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1075
	__( 'Yes, Publish', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1078
	__( 'Layout Navigation', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1079
	__( 'Revision History', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1080
	__( 'Undo', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1081
	__( 'Redo', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1111
	__( 'Hide Sidebar', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1113
	__( 'Show Sidebar', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1127
	__( 'Desktop Preview', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1129
	__( 'Tablet Preview', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1132
	__( 'Mobile Preview', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1181
	__( 'Please make sure to perform a Save before saving as a global template. Click OK to proceed. NOTE: You are about to update a live template, updating this template will deactivate it until it is reviewed and made live again.', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1185
	__( 'Please make sure to perform a Save before saving as a global template. Click OK to proceed.', 'coming-soon' ),

	// Reference: src/views/Setup.vue:1654
	__( 'Something has prevented the page from being saved. Please make sure you are at least an Editor and have the unfiltered_html capability.', 'coming-soon' ),

	// Reference: src/views/SetupBlockOptions-Lite.vue:1430
	__( 'Standard', 'coming-soon' ),

	// Reference: src/views/SetupBlockOptions-Lite.vue:1432
	__( 'Saved Blocks', 'coming-soon' ),

	// Reference: src/views/SetupBlockOptions-Lite.vue:1434
	__( 'Search blocks...', 'coming-soon' ),

	// Reference: src/views/SetupBlockOptions-Lite.vue:1436
	__( 'Widgets', 'coming-soon' ),

	// Reference: src/views/SetupDesign-Lite.vue:734
	__( 'Video Background', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1329
	__( '&larr; Go Back', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1330
	__( '&larr; Go to Dashboard', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1331
	__( 'Choose a New Page Template', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1335
	__( 'No Favorited Templates Found', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1336
	__( 'No Saved Templates Found', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1341
	__( 'Choose This Template', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1342
	__( 'Enter your new page details', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1343
	__( 'You can always change it later in Page Settings.', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1347
	__( 'Page Name:', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1349
	__( 'Save and Start Editing the Page', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1358
	__( 'My Landing Page name goes here', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1362
	__( 'You can favorite any template by clicking the heart icon under the page template.', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1366
	__( 'You can save pages as templates in the builder. Any saved pages will be shown here.', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1370
	__( 'Send Us Your Email and Get 10 Free Templates', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1371
	__( 'Get <strong>FREE</strong> Templates', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1372
	__( 'PRO', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1373
	__( 'SUBSCRIBE ABOVE TO UNLOCK', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1467
	__( 'You now have access to 10 FREE templates.', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1756
	__( 'Blank Template', 'coming-soon' ),

	// Reference: src/views/TemplateChooser-Lite.vue:1926
	__( 'This page url already exisits. Please choose a unique page url.', 'coming-soon' )
);
/* THIS IS THE END OF THE GENERATED FILE */
