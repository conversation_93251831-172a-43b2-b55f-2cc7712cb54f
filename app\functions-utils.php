<?php

/**
 *  Get IP
 */
function seedprod_lite_get_ip() {
	$ip                   = '';
	$http_x_forwarder_for = ! empty( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ? sanitize_text_field( wp_unslash( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) : null;
	$http_client_ip       = ! empty( $_SERVER['HTTP_CLIENT_IP'] ) ? sanitize_text_field( wp_unslash( $_SERVER['HTTP_CLIENT_IP'] ) ) : null;
	$remote_addr          = ! empty( $_SERVER['REMOTE_ADDR'] ) ? sanitize_text_field( wp_unslash( $_SERVER['REMOTE_ADDR'] ) ) : null;

	if ( null !== $http_x_forwarder_for && strlen( $http_x_forwarder_for ) > 6 ) {
		$ip = wp_strip_all_tags( $http_x_forwarder_for );
	} elseif ( null !== $http_client_ip && strlen( $http_client_ip ) > 6 ) {
		$ip = wp_strip_all_tags( $http_client_ip );
	} elseif ( null !== $remote_addr && strlen( $remote_addr ) > 6 ) {
		$ip = wp_strip_all_tags( $remote_addr );
	}

	if ( ! $ip ) {
		$ip = '127.0.0.1';
	}

	return wp_strip_all_tags( $ip );
}

/**
 * Update cookie length for bypass url
 */
function seedprod_lite_change_wp_cookie_logout( $expirein ) {
	global $seed_cspv5_bypass_expires;
	if ( ! empty( $seed_cspv5_bypass_expires ) ) {
		return $seed_cspv5_bypass_expires; // Modify the expire cookie
	} else {
		return $expirein;
	}
}


/**
 * Get roles
 */
function seedprod_lite_get_roles() {
	global $wp_roles;

	if ( ! isset( $wp_roles ) ) {
		$wp_roles = new WP_Roles(); // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
	}
	$roles = $wp_roles->get_names();

	if ( is_multisite() ) {
		$roles['superadmin'] = __( 'SuperAdmin', 'coming-soon' );
	}
	$roles['anyoneloggedin'] = __( 'Anyone Logged In', 'coming-soon' );

	return $roles;
}


/**
 * Get Enviroment
 */
function seedprod_lite_is_localhost() {
	// $localhost = array('127.0.0.1','::1');

	// $is_localhost = false;
	// if (in_array($_SERVER['REMOTE_ADDR'], $localhost) || !empty($_GET['debug'])) {
	// $is_localhost = true;
	// }
	$is_localhost = false;
	if ( defined( 'SEEDPROD_LOCAL_JS' ) ) {
		$is_localhost = true;
	}

	return $is_localhost;
}

/**
 * Get Youtube ID from url.
 *
 * @param string $url Youtube URL.
 * @return string[]|false
 */
function seedprod_lite_youtube_id_from_url( $url ) {
	$pattern =
		'%^# Match any youtube URL
        (?:https?://)?  # Optional scheme. Either http or https
        (?:www\.)?      # Optional www subdomain
        (?:             # Group host alternatives
          youtu\.be/    # Either youtu.be,
        | youtube\.com  # or youtube.com
          (?:           # Group path alternatives
            /embed/     # Either /embed/
          | /v/         # or /v/
          | /watch\?v=  # or /watch\?v=
          )             # End path alternatives.
        )               # End host alternatives.
        ([\w-]{10,12})  # Allow 10-12 for 11 char youtube id.
        $%x';
	$result  = preg_match( $pattern, $url, $matches );
	if ( false !== $result ) {
		if ( isset( $matches[1] ) ) {
			return $matches[1];
		}
	}
	return false;
}

/**
 * Entry Options
 */
function seedprod_lite_block_options() {
	$block_options = array(
		array(
			'name'   => __( 'Column', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'layout',
			'type'   => 'column',
			'id'     => 1,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6  sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M4 5v13h17V5H4zm10 2v9h-3V7h3zM6 7h3v9H6V7zm13 9h-3V7h3v9z"/></svg>',
		),
		// array('name'=>__('2 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'2-col', 'id'=>2),
		// array('name'=>__('3 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'3-col', 'id'=>3),
		// array('name'=>__('4 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'4-col', 'id'=>4),
		// array('name'=>__('5 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'5-col', 'id'=>5),
		// array('name'=>__('6 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'6-col', 'id'=>6),
		// array('name'=>__('Left Sidebar','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'left-sidebar', 'id'=>7),
		// array('name'=>__('Right Sidebar','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'right-sidebar', 'id'=>8),

		// array('name'=>__('6 Column','coming-soon'),'is_pro'=> false ,'cat'=>'layout','type'=>'6-col', 'id'=>6),

		array(
			'name'   => __( 'Headline', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'header',
			'id'     => 9,
			'icon'   => '<svg viewBox="0 0 12 17" class="sp-w-14px  sp-fill-current sp-pb-2" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 0.800049V7.04005H3V0.800049H0V16.4H3V10.16H9V16.4H12V0.800049H9Z"/>
        </svg>
        ',
		),
		// array('name'=>__('Sub Headline','coming-soon'),'is_pro'=> false,'cat'=>'common','type'=>'sub-header', 'id'=>10, 'icon' => 'fas fa-heading'),
		array(
			'name'   => __( 'Text', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'text',
			'id'     => 11,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" class="sp-w-6 sp-fill-current "><rect x="0" fill="none"/><g><path d="M15 2H7.54c-.83 0-1.59.2-2.28.6-.7.41-1.25.96-1.65 1.65C3.2 4.94 3 5.7 3 6.52s.2 1.58.61 2.27c.4.69.95 1.24 1.65 1.64.69.41 1.45.61 2.28.61h.43V17c0 .27.1.51.29.71.2.19.44.29.71.29.28 0 .51-.1.71-.29.2-.2.3-.44.3-.71V5c0-.27.09-.51.29-.71.2-.19.44-.29.71-.29s.51.1.71.29c.19.2.29.44.29.71v12c0 .27.1.51.3.71.2.19.43.29.71.29.27 0 .51-.1.71-.29.19-.2.29-.44.29-.71V4H15c.27 0 .5-.1.7-.3.2-.19.3-.43.3-.7s-.1-.51-.3-.71C15.5 2.1 15.27 2 15 2z"/></g></svg>',
		),
		array(
			'name'   => __( 'List', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'bullet-list',
			'id'     => 12,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current"><g fill="none"><path d="M0 0h24v24H0V0z"/><path d="M0 0h24v24H0V0z" opacity=".87"/></g><path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7zm-4 6h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/></svg>',
		),

		array(
			'name'   => __( 'Button', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'button',
			'id'     => 13,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" class="sp-w-6 sp-fill-current "  viewBox="0 0 24 24" ><g><rect fill="none" /></g><g><g><path d="M18.19,12.44l-3.24-1.62c1.29-1,2.12-2.56,2.12-4.32c0-3.03-2.47-5.5-5.5-5.5s-5.5,2.47-5.5,5.5c0,2.13,1.22,3.98,3,4.89 v3.26c-2.15-0.46-2.02-0.44-2.26-0.44c-0.53,0-1.03,0.21-1.41,0.59L4,16.22l5.09,5.09C9.52,21.75,10.12,22,10.74,22h6.3 c0.98,0,1.81-0.7,1.97-1.67l0.8-4.71C20.03,14.32,19.38,13.04,18.19,12.44z M17.84,15.29L17.04,20h-6.3 c-0.09,0-0.17-0.04-0.24-0.1l-3.68-3.68l4.25,0.89V6.5c0-0.28,0.22-0.5,0.5-0.5c0.28,0,0.5,0.22,0.5,0.5v6h1.76l3.46,1.73 C17.69,14.43,17.91,14.86,17.84,15.29z M8.07,6.5c0-1.93,1.57-3.5,3.5-3.5s3.5,1.57,3.5,3.5c0,0.95-0.38,1.81-1,2.44V6.5 c0-1.38-1.12-2.5-2.5-2.5c-1.38,0-2.5,1.12-2.5,2.5v2.44C8.45,8.31,8.07,7.45,8.07,6.5z"/></g></g></svg>',
		),
		array(
			'name'   => __( 'Image', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'image',
			'id'     => 14,
			'icon'   => '<svg  class="sp-w-6 sp-fill-current " xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-4.86 8.86l-3 3.87L9 13.14 6 17h12l-3.86-5.14z"/></svg>',
		),
		array(
			'name'   => __( 'Video', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'video',
			'id'     => 15,
			'icon'   => '<svg  class="sp-w-6 sp-fill-current " xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" ><path d="M0 0h24v24H0V0z" fill="none"/><path d="M15 8v8H5V8h10m1-2H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4V7c0-.55-.45-1-1-1z"/></svg>',
		),
		array(
			'name'   => __( 'Divider', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'divider',
			'id'     => 17,
			'icon'   => '
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><g><rect fill="none" /></g><g><g><g><path d="M20,9H4v2h16V9z M4,15h16v-2H4V15z"/></g></g></g></svg>',
		),
		array(
			'name'   => __( 'Spacer', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'common',
			'type'   => 'spacer',
			'id'     => 24,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current sp-transform sp-rotate-90" ><path d="M0 0h24v24H0z" fill="none"/><path d="M7.77 6.76L6.23 5.48.82 12l5.41 6.52 1.54-1.28L3.42 12l4.35-5.24zM7 13h2v-2H7v2zm10-2h-2v2h2v-2zm-6 2h2v-2h-2v2zm6.77-7.52l-1.54 1.28L20.58 12l-4.35 5.24 1.54 1.28L23.18 12l-5.41-6.52z"/></svg>',
		),

		array(
			'name'   => __( ' Payment Button', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'stripepayment',
			'id'     => 32,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" class="sp-fill-current "><path d="M11 17h2v-1h1q.425 0 .713-.288Q15 15.425 15 15v-3q0-.425-.287-.713Q14.425 11 14 11h-3v-1h4V8h-2V7h-2v1h-1q-.425 0-.712.287Q9 8.575 9 9v3q0 .425.288.712Q9.575 13 10 13h3v1H9v2h2Zm-7 3q-.825 0-1.412-.587Q2 18.825 2 18V6q0-.825.588-1.412Q3.175 4 4 4h16q.825 0 1.413.588Q22 5.175 22 6v12q0 .825-.587 1.413Q20.825 20 20 20Zm0-2h16V6H4v12Zm0 0V6v12Z"/></svg>',
		),

		array(
			'name'   => __( 'Giveaway', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'giveaway',
			'id'     => 32,
			'icon'   => '<svg class="sp-w-5 sp-fill-current " viewBox="0 0 394 416" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
				<path d="M161.294,281.219 C151.445,281.219 143.462,289.202 143.462,299.049 C143.462,308.896 151.445,316.878 161.294,316.878 C171.139,316.878 179.122,308.896 179.122,299.049 C179.122,289.202 171.139,281.219 161.294,281.219 Z M232.979,281.219 C223.132,281.219 215.149,289.202 215.149,299.049 C215.149,308.896 223.132,316.878 232.979,316.878 C242.826,316.878 250.806,308.896 250.806,299.049 C250.806,289.202 242.826,281.219 232.979,281.219 Z M32.608,123.757 C30.714,158.655 31.726,255.445 32.608,292.617 C32.68,295.618 34.565,297.889 37.042,299.527 C58.017,313.458 79.698,326.395 101.835,338.541 C98.77,308.445 98.261,273.714 107.731,252.542 C111.467,244.191 119.577,237.434 130.383,232.272 C111.019,204.919 98.751,172.762 95.699,143.461 C91.243,100.685 159.191,80.829 161.091,113.506 C163.202,149.839 167.026,185.74 173.214,221.056 C180.966,220.166 188.963,219.72 196.962,219.708 C205.077,219.704 213.195,220.154 221.06,221.056 C227.245,185.74 231.071,149.839 233.18,113.506 C235.079,80.829 303.03,100.685 298.574,143.461 C295.523,172.762 283.254,204.919 263.891,232.272 C274.694,237.434 282.806,244.191 286.542,252.542 C295.99,273.665 295.504,308.286 292.458,338.332 C314.469,326.252 336.023,313.381 356.885,299.527 C359.356,297.889 361.245,295.618 361.316,292.617 C362.199,255.445 363.21,158.655 361.316,123.757 C361.008,120.766 359.356,118.487 356.885,116.846 C307.739,84.205 254.723,57.023 201.025,32.736 C199.667,32.123 198.314,31.818 196.962,31.818 C195.61,31.818 194.257,32.123 192.902,32.736 C139.201,57.023 86.185,84.205 37.042,116.846 C34.565,118.487 32.913,120.766 32.608,123.757 Z M1.328,120.554 C2.595,108.178 9.333,97.499 19.644,90.651 C70.294,57.012 124.602,29.116 179.943,4.087 C190.893,-0.864 203.032,-0.864 213.981,4.087 C269.323,29.116 323.628,57.012 374.28,90.651 C384.913,97.713 392.019,109.24 392.712,122.052 C394.273,150.787 393.913,180.541 393.792,209.337 C393.674,237.33 393.416,265.374 392.75,293.359 C392.432,306.785 385.326,318.385 374.28,325.719 C323.628,359.361 269.323,387.262 213.981,412.29 C203.032,417.237 190.893,417.237 179.943,412.29 C124.602,387.262 70.294,359.361 19.644,325.719 C8.596,318.385 1.493,306.785 1.174,293.359 C0.509,265.374 0.248,237.33 0.132,209.337 C0.047,189.407 -0.464,137.991 1.328,120.554 L1.328,120.554 Z" id="Fill-5"></path>
        </svg>',
		),

		array(
			'name'   => __( 'MyPayKit', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'mypaykit',
			'id'     => 33,
			'icon'   => '<svg class="sp-w-5 sp-fill-current " viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M1.80546 4.55546C1.28973 5.07118 1 5.77065 1 6.5V17.5C1 18.2293 1.28973 18.9288 1.80546 19.4445C2.32118 19.9603 3.02065 20.25 3.75 20.25H20.25C20.9793 20.25 21.6788 19.9603 22.1945 19.4445C22.7103 18.9288 23 18.2293 23 17.5V6.5C23 5.77065 22.7103 5.07118 22.1945 4.55546C21.6788 4.03973 20.9793 3.75 20.25 3.75H3.75C3.02065 3.75 2.32118 4.03973 1.80546 4.55546ZM2.77773 5.52773C3.03559 5.26987 3.38533 5.125 3.75 5.125H20.25C20.6147 5.125 20.9644 5.26987 21.2223 5.52773C21.4801 5.78559 21.625 6.13533 21.625 6.5V7.875H2.375V6.5C2.375 6.13533 2.51987 5.78559 2.77773 5.52773ZM2.375 10.625H21.625V17.5C21.625 17.8647 21.4801 18.2144 21.2223 18.4723C20.9644 18.7301 20.6147 18.875 20.25 18.875H3.75C3.38533 18.875 3.03559 18.7301 2.77773 18.4723C2.51987 18.2144 2.375 17.8647 2.375 17.5V10.625ZM4.15273 13.7777C3.89487 14.0356 3.75 14.3853 3.75 14.75V16.125C3.75 16.4897 3.89487 16.8394 4.15273 17.0973C4.41059 17.3551 4.76033 17.5 5.125 17.5H6.5C6.86467 17.5 7.21441 17.3551 7.47227 17.0973C7.73013 16.8394 7.875 16.4897 7.875 16.125V14.75C7.875 14.3853 7.73013 14.0356 7.47227 13.7777C7.21441 13.5199 6.86467 13.375 6.5 13.375H5.125C4.76033 13.375 4.41059 13.5199 4.15273 13.7777Z" />
		</svg>',
		),

		array(
			'name'   => __( 'Contact Form', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'contact-form',
			'id'     => 23,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" class="sp-w-5 sp-fill-current "><path fill="currentColor" d="M448 75.2v361.7c0 24.3-19 43.2-43.2 43.2H43.2C19.3 480 0 461.4 0 436.8V75.2C0 51.1 18.8 32 43.2 32h361.7c24 0 43.1 18.8 43.1 43.2zm-37.3 361.6V75.2c0-3-2.6-5.8-5.8-5.8h-9.3L285.3 144 224 94.1 162.8 144 52.5 69.3h-9.3c-3.2 0-5.8 2.8-5.8 5.8v361.7c0 3 2.6 5.8 5.8 5.8h361.7c3.2.1 5.8-2.7 5.8-5.8zM150.2 186v37H76.7v-37h73.5zm0 74.4v37.3H76.7v-37.3h73.5zm11.1-147.3l54-43.7H96.8l64.5 43.7zm210 72.9v37h-196v-37h196zm0 74.4v37.3h-196v-37.3h196zm-84.6-147.3l64.5-43.7H232.8l53.9 43.7zM371.3 335v37.3h-99.4V335h99.4z"></path></svg>',
		),

		array(
			'name'   => __( 'Envira Gallery', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'envira-gallery',
			'id'     => 25,
			'icon'   => '<svg viewBox="0 0 40 40" class="sp-w-6 sp-fill-current" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3 3.58007C14.5 2.83007 36.05 7.03006 30.25 29.8301L36.5 36.3301H34.25L29 31.0801C24 32.5801 12.8 32.7801 8 21.5801C6.4827 18.0397 5.77931 15.0173 4.87058 11.1125C4.36758 8.9512 3.80167 6.51953 3 3.58007ZM7.00001 6.33007C9.41667 6.83007 15.2 9.73007 19 17.3301C22.8 24.9301 26.9167 28.6634 28.5 29.5801C26.4167 29.1634 21.3 26.8801 17.5 20.0801C12.75 11.5801 10.75 8.58007 7.00001 6.33007Z" fill="evenodd"/></svg>
',
		),

		array(
			'name'   => __( 'Custom HTML', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'custom-html',
			'id'     => 20,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current " viewBox="0 0 24 24"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/></svg>',
		),

		array(
			'name'   => __( 'Optin Form', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'optin-form',
			'id'     => 22,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6zm-2 0l-8 4.99L4 6h16zm0 12H4V8l8 5 8-5v10z"/></svg>',
		),

		array(
			'name'   => __( 'Tabs', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'tabbedlayout',
			'id'     => 26,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 1H27.5C27.8978 1 28.2794 1.15804 28.5607 1.43934C28.842 1.72064 29 2.10218 29 2.5V27.5C29 27.8978 28.842 28.2794 28.5607 28.5607C28.2794 28.842 27.8978 29 27.5 29H2.5C2.10218 29 1.72064 28.842 1.43934 28.5607C1.15804 28.2794 1 27.8978 1 27.5V2.5C1 2.10218 1.15804 1.72064 1.43934 1.43934C1.72064 1.15804 2.10218 1 2.5 1ZM19 3H13V6H19V3ZM3 27H27V8H12C11.7348 8 11.4804 7.89464 11.2929 7.70711C11.1054 7.51957 11 7.26522 11 7V3H3V27ZM21 6H27V3H21V6ZM6.66997 17H17.33C17.4242 16.9884 17.5197 16.997 17.6103 17.0251C17.7009 17.0533 17.7845 17.1003 17.8556 17.1632C17.9267 17.226 17.9836 17.3033 18.0226 17.3898C18.0615 17.4763 18.0817 17.5701 18.0817 17.665C18.0817 17.7599 18.0615 17.8537 18.0226 17.9402C17.9836 18.0267 17.9267 18.1039 17.8556 18.1668C17.7845 18.2297 17.7009 18.2767 17.6103 18.3049C17.5197 18.333 17.4242 18.3416 17.33 18.33H6.66997C6.5758 18.3416 6.48024 18.333 6.38963 18.3049C6.29901 18.2767 6.2154 18.2297 6.14434 18.1668C6.07327 18.1039 6.01637 18.0267 5.9774 17.9402C5.93843 17.8537 5.91827 17.7599 5.91827 17.665C5.91827 17.5701 5.93843 17.4763 5.9774 17.3898C6.01637 17.3033 6.07327 17.226 6.14434 17.1632C6.2154 17.1003 6.29901 17.0533 6.38963 17.0251C6.48024 16.997 6.5758 16.9884 6.66997 17ZM6 13.67C6.00263 13.846 6.07437 14.0138 6.19974 14.1373C6.32511 14.2608 6.49402 14.33 6.67 14.33H21.33C21.4242 14.3416 21.5197 14.333 21.6103 14.3049C21.701 14.2767 21.7846 14.2297 21.8556 14.1668C21.9267 14.1039 21.9836 14.0267 22.0226 13.9402C22.0615 13.8537 22.0817 13.7599 22.0817 13.665C22.0817 13.5701 22.0615 13.4763 22.0226 13.3898C21.9836 13.3033 21.9267 13.226 21.8556 13.1632C21.7846 13.1003 21.701 13.0533 21.6103 13.0251C21.5197 12.997 21.4242 12.9884 21.33 13H6.67C6.4923 13 6.32189 13.0706 6.19624 13.1962C6.07059 13.3219 6 13.4923 6 13.67ZM6.66997 21H16.33C16.4242 20.9884 16.5197 20.997 16.6103 21.0251C16.7009 21.0533 16.7845 21.1003 16.8556 21.1632C16.9267 21.226 16.9836 21.3033 17.0226 21.3898C17.0615 21.4763 17.0817 21.5701 17.0817 21.665C17.0817 21.7599 17.0615 21.8537 17.0226 21.9402C16.9836 22.0267 16.9267 22.1039 16.8556 22.1668C16.7845 22.2297 16.7009 22.2767 16.6103 22.3049C16.5197 22.333 16.4242 22.3416 16.33 22.33H6.66997C6.5758 22.3416 6.48024 22.333 6.38963 22.3049C6.29901 22.2767 6.2154 22.2297 6.14434 22.1668C6.07327 22.1039 6.01637 22.0267 5.9774 21.9402C5.93843 21.8537 5.91827 21.7599 5.91827 21.665C5.91827 21.5701 5.93843 21.4763 5.9774 21.3898C6.01637 21.3033 6.07327 21.226 6.14434 21.1632C6.2154 21.1003 6.29901 21.0533 6.38963 21.0251C6.48024 20.997 6.5758 20.9884 6.66997 21Z"/></svg>',
		),

		array(
			'name'   => __( 'Countdown', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'countdown',
			'id'     => 16,
			'icon'   => '
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M15.07 1.01h-6v2h6v-2zm-4 13h2v-6h-2v6zm8.03-6.62l1.42-1.42c-.43-.51-.9-.99-1.41-1.41l-1.42 1.42C16.14 4.74 14.19 4 12.07 4c-4.97 0-9 4.03-9 9s4.02 9 9 9 9-4.03 9-9c0-2.11-.74-4.06-1.97-5.61zm-7.03 12.62c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/></svg>',
		),
		array(
			'name'   => __( 'Social Profiles', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'social-profiles',
			'id'     => 18,
			'video'  => 'A4m5hXUGiYg?start=20',
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M9 13.75c-2.34 0-7 1.17-7 3.5V19h14v-1.75c0-2.33-4.66-3.5-7-3.5zM4.34 17c.84-.58 2.87-1.25 4.66-1.25s3.82.67 4.66 1.25H4.34zM9 12c1.93 0 3.5-1.57 3.5-3.5S10.93 5 9 5 5.5 6.57 5.5 8.5 7.07 12 9 12zm0-5c.83 0 1.5.67 1.5 1.5S9.83 10 9 10s-1.5-.67-1.5-1.5S8.17 7 9 7zm7.04 6.81c1.16.84 1.96 1.96 1.96 3.44V19h4v-1.75c0-2.02-3.5-3.17-5.96-3.44zM15 12c1.93 0 3.5-1.57 3.5-3.5S16.93 5 15 5c-.54 0-1.04.13-1.5.35.63.89 1 1.98 1 3.15s-.37 2.26-1 3.15c.46.22.96.35 1.5.35z"/></svg>',
		),
		array(
			'name'   => __( 'Social Sharing', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'social-sharing',
			'id'     => 19,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92c0-1.61-1.31-2.92-2.92-2.92zM18 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM6 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm12 7.02c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/></svg>',
		),
		// array('name'=>__('Form','coming-soon'),'is_pro'=> false  ,'cat'=>'adv','type'=>'form', 'id'=>25, 'icon' => 'far fa-envelope'),

		array(
			'name'   => __( 'Video Pop Up', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'videopopup',
			'id'     => 83,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M9 2C9 1.44772 9.44771 1 10 1H20C20.5523 1 21 1.44772 21 2C21 2.55228 20.5523 3 20 3H10C9.44771 3 9 2.55228 9 2ZM8 4C7.44772 4 7 4.44772 7 5C7 5.55228 7.44772 6 8 6H22C22.5523 6 23 5.55228 23 5C23 4.44772 22.5523 4 22 4H8ZM3 21V9C3 8.46957 3.21071 7.96086 3.58579 7.58579C3.96086 7.21071 4.46957 7 5 7H25C25.5304 7 26.0391 7.21071 26.4142 7.58579C26.7893 7.96086 27 8.46957 27 9V21C27 21.5304 26.7893 22.0391 26.4142 22.4142C26.0391 22.7893 25.5304 23 25 23H5C4.46957 23 3.96086 22.7893 3.58579 22.4142C3.21071 22.0391 3 21.5304 3 21ZM5 21H25V9H5V21ZM12.7473 11.2106L17.8075 14.584C17.876 14.6297 17.9321 14.6915 17.9709 14.7641C18.0098 14.8367 18.0301 14.9177 18.0301 15C18.0301 15.0823 18.0098 15.1633 17.9709 15.2359C17.9321 15.3085 17.876 15.3703 17.8075 15.416L12.7473 18.7894C12.672 18.8396 12.5845 18.8684 12.4941 18.8728C12.4037 18.8772 12.3138 18.8569 12.2341 18.8142C12.1543 18.7715 12.0876 18.708 12.041 18.6304C11.9945 18.5527 11.97 18.4639 11.97 18.3734V11.6266C11.97 11.5361 11.9945 11.4473 12.041 11.3696C12.0876 11.292 12.1543 11.2284 12.2341 11.1857C12.3138 11.143 12.4037 11.1228 12.4941 11.1272C12.5845 11.1315 12.672 11.1604 12.7473 11.2106ZM22 26C22.5523 26 23 25.5523 23 25C23 24.4477 22.5523 24 22 24H8C7.44771 24 7 24.4477 7 25C7 25.5523 7.44771 26 8 26H22ZM21 28C21 28.5523 20.5523 29 20 29H10C9.44771 29 9 28.5523 9 28C9 27.4477 9.44771 27 10 27H20C20.5523 27 21 27.4477 21 28Z" fill="evenodd"/>
			</svg>
			',
		),
		array(
			'name'   => __( 'Animated Headline', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'animatedheadline',
			'id'     => 11,
			'icon'   => '<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
		<path fill-rule="evenodd" clip-rule="evenodd" d="M6.92423 2V22.4525H10.5335V13.297H20.1582V22.4525H23.7675V2H20.1582V10.4216H10.5335V2H6.92423ZM23.178 27.8784C23.3427 28.0179 23.5516 28.0947 23.7675 28.095C23.9501 28.0753 24.1223 28.0004 24.2612 27.8803C24.4001 27.7602 24.499 27.6006 24.5448 27.4227C24.5905 27.2449 24.5809 27.0573 24.5173 26.8851C24.4536 26.7128 24.339 26.5641 24.1886 26.4588L22.1674 24.7263C21.7878 24.3938 21.3004 24.2105 20.7959 24.2105C20.2913 24.2105 19.8039 24.3938 19.4243 24.7263L18.2212 25.785C18.1653 25.831 18.0951 25.8561 18.0227 25.8561C17.9503 25.8561 17.8802 25.831 17.8242 25.785L16.6211 24.7263C16.2416 24.3938 15.7542 24.2105 15.2496 24.2105C14.745 24.2105 14.2576 24.3938 13.8781 24.7263L12.675 25.785C12.6191 25.831 12.5489 25.8561 12.4765 25.8561C12.4041 25.8561 12.3339 25.831 12.278 25.785L11.0749 24.7263C10.6954 24.3938 10.208 24.2105 9.70337 24.2105C9.19879 24.2105 8.71139 24.3938 8.33185 24.7263L6.31067 26.4588C6.12879 26.6183 6.01774 26.8436 6.00195 27.085C5.98615 27.3264 6.06691 27.5642 6.22645 27.7461C6.38599 27.928 6.61124 28.039 6.85266 28.0548C7.09408 28.0706 7.33188 27.9898 7.51375 27.8303L9.67931 26.1339C9.73527 26.088 9.80542 26.0629 9.87782 26.0629C9.95022 26.0629 10.0204 26.088 10.0763 26.1339L11.2794 27.2047C11.6623 27.5302 12.1484 27.7089 12.6509 27.7089C13.1535 27.7089 13.6396 27.5302 14.0225 27.2047L15.2256 26.1339C15.2785 26.0866 15.347 26.0604 15.418 26.0604C15.4891 26.0604 15.5576 26.0866 15.6105 26.1339L16.8136 27.2047C17.1965 27.5302 17.6826 27.7089 18.1851 27.7089C18.6877 27.7089 19.1738 27.5302 19.5567 27.2047L20.7598 26.1339C20.8157 26.088 20.8859 26.0629 20.9583 26.0629C21.0307 26.0629 21.1008 26.088 21.1568 26.1339L23.178 27.8784Z" />
		</svg>',
		),
		array(
			'name'   => __( 'Progress Bar', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'progress-bar',
			'id'     => 24,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24"  class="sp-w-6 sp-fill-current sp-transform sp-rotate-90"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M5 9.2h3V19H5V9.2zM10.6 5h2.8v14h-2.8V5zm5.6 8H19v6h-2.8v-6z"/></svg>',
		),
		array(
			'name'   => __( 'Icon', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'icon',
			'id'     => 24,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M16.5 3c-1.74 0-3.41.81-4.5 2.09C10.91 3.81 9.24 3 7.5 3 4.42 3 2 5.42 2 8.5c0 3.78 3.4 6.86 8.55 11.54L12 21.35l1.45-1.32C18.6 15.36 22 12.28 22 8.5 22 5.42 19.58 3 16.5 3zm-4.4 15.55l-.1.1-.1-.1C7.14 14.24 4 11.39 4 8.5 4 6.5 5.5 5 7.5 5c1.54 0 3.04.99 3.57 2.36h1.87C13.46 5.99 14.96 5 16.5 5c2 0 3.5 1.5 3.5 3.5 0 2.89-3.14 5.74-7.9 10.05z"/></svg>',
		),

		array(
			'name'   => __( 'Testimonials', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'testimonial',
			'id'     => 34,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H6l-2 2V4h16v12z"/></svg>',
		),

		array(
			'name'   => __( 'Image Carousel', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'imagecarousel',
			'id'     => 45,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M6.10999 4H23.89C24.1959 4.01301 24.4843 4.14646 24.6923 4.37126C24.9002 4.59606 25.0108 4.89397 25 5.2V20.76C25.0108 21.066 24.9002 21.3639 24.6923 21.5887C24.4843 21.8135 24.1959 21.947 23.89 21.96H6.10999C5.80405 21.947 5.51565 21.8135 5.30771 21.5887C5.09977 21.3639 4.98916 21.066 5 20.76V5.2C4.98916 4.89397 5.09977 4.59606 5.30771 4.37126C5.51565 4.14646 5.80405 4.01301 6.10999 4ZM22.78 6.4H7.21999V17.2L12.66 9.38L17.22 14.31L22.78 8.31V6.4ZM12.9 13.03L8.32999 19.6H22.78V11.7L17.22 17.7L12.9 13.03ZM16 24.52C16 25.0723 15.5523 25.52 15 25.52C14.4477 25.52 14 25.0723 14 24.52C14 23.9677 14.4477 23.52 15 23.52C15.5523 23.52 16 23.9677 16 24.52ZM11 25.52C11.5523 25.52 12 25.0723 12 24.52C12 23.9677 11.5523 23.52 11 23.52C10.4477 23.52 10 23.9677 10 24.52C10 25.0723 10.4477 25.52 11 25.52ZM20 24.52C20 25.0723 19.5523 25.52 19 25.52C18.4477 25.52 18 25.0723 18 24.52C18 23.9677 18.4477 23.52 19 23.52C19.5523 23.52 20 23.9677 20 24.52ZM26.85 11.52L29 13.67L26.85 15.82V11.52ZM1 13.67L3.15 15.82V11.52L1 13.67Z" />
			</svg>
			',
		),

		array(
			'name'   => __( 'Image Box', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'feature',
			'id'     => 24,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M0 0h24v24H0V0z" fill="none"/><path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zM4 6h9v7H4z"/></svg>',
		),

		array(
			'name'   => __( 'Before After Toggle', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'beforeaftertoggle',
			'id'     => 25,
			'icon'   => '
			<svg viewBox="0 0 30 30"  xmlns="http://www.w3.org/2000/svg"  class="sp-w-6 sp-fill-current ">
<path fill-rule="evenodd" clip-rule="evenodd" d="M27 1H3C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V27C1 27.5304 1.21071 28.0391 1.58579 28.4142C1.96086 28.7893 2.46957 29 3 29H27C27.5304 29 28.0391 28.7893 28.4142 28.4142C28.7893 28.0391 29 27.5304 29 27V3C29 2.46957 28.7893 1.96086 28.4142 1.58579C28.0391 1.21071 27.5304 1 27 1ZM14 27H3V3H14V5.05C11.4765 5.22878 9.11452 6.35736 7.39002 8.20835C5.66552 10.0593 4.70666 12.4952 4.70666 15.025C4.70666 17.5548 5.66552 19.9907 7.39002 21.8416C9.11452 23.6926 11.4765 24.8212 14 25V27ZM14 22.94C12.0623 22.7013 10.2787 21.7623 8.98531 20.2998C7.69191 18.8373 6.97796 16.9524 6.97796 15C6.97796 13.0476 7.69191 11.1627 8.98531 9.70017C10.2787 8.23769 12.0623 7.29867 14 7.06C14.6643 6.97997 15.3357 6.97997 16 7.06C17.9377 7.29867 19.7213 8.23769 21.0147 9.70017C22.3081 11.1627 23.022 13.0476 23.022 15C23.022 16.9524 22.3081 18.8373 21.0147 20.2998C19.7213 21.7623 17.9377 22.7013 16 22.94C15.3357 23.02 14.6643 23.02 14 22.94ZM27 27H16V25C18.4669 24.7524 20.7538 23.5972 22.417 21.7587C24.0803 19.9201 25.0013 17.5293 25.0013 15.05C25.0013 12.5707 24.0803 10.1799 22.417 8.34133C20.7538 6.50277 18.4669 5.34762 16 5.1V3H27V27ZM20 15L17 18V12L20 15ZM10 15L13 18V12L10 15Z" />
</svg>
			',
		),

		array(
			'name'   => __( 'Icon Box', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'iconfeature',
			'id'     => 26,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H3V5h18v14zM5 10h9v2H5zm0-3h9v2H5z"/></svg>',
		),

		array(
			'name'   => __( 'Nav Menu', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'nav',
			'id'     => 25,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0z" fill="none"/><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>',
		),
		array(
			'name'   => __( 'Content Toggle', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'contenttoggle',
			'id'     => 43,
			'icon'   => '			
			<svg viewBox="0 0 30 30" fill="none" class="sp-w-6 sp-fill-current " xmlns="http://www.w3.org/2000/svg">
			<path d="M21 8H9C5.688 8 3 10.688 3 14C3 17.312 5.688 20 9 20H21C24.312 20 27 17.312 27 14C27 10.688 24.312 8 21 8ZM21 17.6C19.008 17.6 17.4 15.992 17.4 14C17.4 12.008 19.008 10.4 21 10.4C22.992 10.4 24.6 12.008 24.6 14C24.6 15.992 22.992 17.6 21 17.6Z" />
			</svg>
			',
		),
		array(
			'name'   => __( 'Pricing Table', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'pricingtable',
			'id'     => 44,
			'icon'   => '<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M21 26.5V3H9.5C9.36739 3 9.24021 3.05268 9.14645 3.14645C9.05268 3.24021 9 3.36739 9 3.5V26.5C9 26.6326 9.05268 26.7598 9.14645 26.8536C9.24021 26.9473 9.36739 27 9.5 27H20.5C20.6326 27 20.7598 26.9473 20.8536 26.8536C20.9473 26.7598 21 26.6326 21 26.5ZM8 1H22C22.2652 1 22.5196 1.10536 22.7071 1.29289C22.8946 1.48043 23 1.73478 23 2V3H27.17C27.6776 3.02336 28.1552 3.24698 28.4983 3.62186C28.8413 3.99675 29.0217 4.49233 29 5V25C29.0217 25.5077 28.8413 26.0033 28.4983 26.3781C28.1552 26.753 27.6776 26.9766 27.17 27H23V28C23 28.2652 22.8946 28.5196 22.7071 28.7071C22.5196 28.8946 22.2652 29 22 29H8C7.73478 29 7.48043 28.8946 7.29289 28.7071C7.10536 28.5196 7 28.2652 7 28V27H2.83C2.3224 26.9766 1.84476 26.753 1.50174 26.3781C1.15871 26.0033 0.978291 25.5077 1 25V5C0.978291 4.49233 1.15871 3.99675 1.50174 3.62186C1.84476 3.24698 2.3224 3.02336 2.83 3H7V2C7 1.73478 7.10536 1.48043 7.29289 1.29289C7.48043 1.10536 7.73478 1 8 1ZM7 5V25H3.51C3.37739 25 3.25022 24.9473 3.15645 24.8536C3.06268 24.7598 3.01 24.6326 3.01 24.5V5.5C3.01 5.36739 3.06268 5.24021 3.15645 5.14645C3.25022 5.05268 3.37739 5 3.51 5H7ZM23 25H26.49C26.6226 25 26.7498 24.9473 26.8436 24.8536C26.9373 24.7598 26.99 24.6326 26.99 24.5V5.5C26.99 5.36739 26.9373 5.24021 26.8436 5.14645C26.7498 5.05268 26.6226 5 26.49 5H23V25ZM14.52 6.38H15.52V7.52C15.746 7.56137 15.9697 7.61478 16.19 7.68C16.426 7.74892 16.6514 7.84986 16.86 7.98L16.45 9.08C16.2355 8.96937 16.0112 8.87897 15.78 8.81001C15.5278 8.72993 15.2646 8.68945 15 8.69C14.807 8.68258 14.616 8.7312 14.45 8.83C14.3798 8.87052 14.3216 8.92884 14.2812 8.99908C14.2408 9.06932 14.2197 9.14898 14.22 9.23C14.2191 9.30637 14.2369 9.38179 14.2717 9.44975C14.3066 9.5177 14.3575 9.57614 14.42 9.62C14.6283 9.75956 14.858 9.86427 15.1 9.93001L15.59 10.1C16.0513 10.2242 16.4676 10.4774 16.79 10.83C17.0646 11.1658 17.2146 11.5862 17.2146 12.02C17.2146 12.4538 17.0646 12.8742 16.79 13.21C16.4155 13.5952 15.9151 13.833 15.38 13.88V15.09H14.38V13.9C13.7692 13.844 13.1818 13.6379 12.67 13.3L13.18 12.23C13.4086 12.3927 13.6618 12.5176 13.93 12.6C14.1774 12.6701 14.4329 12.7071 14.69 12.71C14.9501 12.7383 15.2119 12.6746 15.43 12.53C15.4872 12.4797 15.5335 12.4184 15.5662 12.3496C15.5989 12.2808 15.6172 12.2061 15.62 12.13C15.6195 12.0569 15.6042 11.9846 15.575 11.9176C15.5458 11.8505 15.5033 11.7901 15.45 11.74C15.2683 11.5906 15.057 11.4815 14.83 11.42L14.29 11.24C14.0692 11.1687 13.8551 11.0784 13.65 10.97C13.4589 10.8655 13.2838 10.7342 13.13 10.58C12.981 10.4249 12.8593 10.2457 12.77 10.05C12.6799 9.83131 12.6357 9.59647 12.64 9.36C12.634 9.1127 12.6818 8.86705 12.78 8.64001C12.8745 8.42552 13.0104 8.23182 13.18 8.07001C13.3504 7.90178 13.5551 7.77219 13.78 7.69C14.0206 7.59946 14.2734 7.54553 14.53 7.53L14.52 6.38ZM10.5 17H19.5C19.7761 17 20 17.2239 20 17.5C20 17.7761 19.7761 18 19.5 18H10.5C10.2239 18 10 17.7761 10 17.5C10 17.2239 10.2239 17 10.5 17ZM19.5 20H10.5C10.2239 20 10 20.2239 10 20.5C10 20.7761 10.2239 21 10.5 21H19.5C19.7761 21 20 20.7761 20 20.5C20 20.2239 19.7761 20 19.5 20ZM10.5 23H19.5C19.7761 23 20 23.2239 20 23.5C20 23.7761 19.7761 24 19.5 24H10.5C10.2239 24 10 23.7761 10 23.5C10 23.2239 10.2239 23 10.5 23Z" />
			</svg>
		',
		),

		array(
			'name'   => __( 'Business Reviews', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'businessreviews',
			'id'     => 42,
			'icon'   => '
	<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2323_85)">
<path d="M15.5 17.49L16.87 14.36C16.9281 14.2312 17.0312 14.1281 17.16 14.07L20.29 12.7C20.3894 12.6595 20.4744 12.5903 20.5342 12.5012C20.5941 12.4122 20.626 12.3073 20.626 12.2C20.626 12.0927 20.5941 11.9878 20.5342 11.8988C20.4744 11.8097 20.3894 11.7405 20.29 11.7L17.16 10.33C17.0312 10.2719 16.9281 10.1688 16.87 10.04L15.5 6.91C15.4595 6.81065 15.3903 6.72563 15.3012 6.66578C15.2122 6.60593 15.1073 6.57396 15 6.57396C14.8927 6.57396 14.7878 6.60593 14.6988 6.66578C14.6097 6.72563 14.5405 6.81065 14.5 6.91L13.13 10C13.0719 10.1288 12.9688 10.2319 12.84 10.29L9.71 11.7C9.61065 11.7405 9.52563 11.8097 9.46578 11.8988C9.40593 11.9878 9.37396 12.0927 9.37396 12.2C9.37396 12.3073 9.40593 12.4122 9.46578 12.5012C9.52563 12.5903 9.61065 12.6595 9.71 12.7L12.84 14.07C12.9688 14.1281 13.0719 14.2312 13.13 14.36L14.5 17.49C14.5405 17.5893 14.6097 17.6744 14.6988 17.7342C14.7878 17.7941 14.8927 17.826 15 17.826C15.1073 17.826 15.2122 17.7941 15.3012 17.7342C15.3903 17.6744 15.4595 17.5893 15.5 17.49ZM1 29V3.1C1.00096 2.82818 1.05733 2.55941 1.16565 2.31011C1.27396 2.0608 1.43197 1.83619 1.63 1.65C1.81674 1.44588 2.04381 1.28272 2.29683 1.17083C2.54986 1.05895 2.82334 1.00078 3.1 1H26.9C27.1733 1.00352 27.4429 1.06302 27.6923 1.17482C27.9417 1.28661 28.1655 1.44833 28.35 1.65C28.5517 1.83446 28.7134 2.05829 28.8252 2.30768C28.937 2.55707 28.9965 2.82672 29 3.1V21.3C28.969 21.8468 28.7377 22.3631 28.3504 22.7504C27.9631 23.1377 27.4468 23.369 26.9 23.4H6.6L1 29ZM3.1 23.93L5.73 21.3H26.9V3.1H3.1V23.93Z" class="sp-fill-current"/>
</g>
<defs>
<clipPath id="clip0_2323_85">
<rect width="30" height="30" fill="white"/>
</clipPath>
</defs>
</svg>',
		),

		array(
			'name'   => __( 'Anchor', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'anchor',
			'id'     => 30,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current" ><path d="M17,15l1.55,1.55c-0.96,1.69-3.33,3.04-5.55,3.37V11h3V9h-3V7.82C14.16,7.4,15,6.3,15,5c0-1.65-1.35-3-3-3S9,3.35,9,5 c0,1.3,0.84,2.4,2,2.82V9H8v2h3v8.92c-2.22-0.33-4.59-1.68-5.55-3.37L7,15l-4-3v3c0,3.88,4.92,7,9,7s9-3.12,9-7v-3L17,15z M12,4 c0.55,0,1,0.45,1,1s-0.45,1-1,1s-1-0.45-1-1S11.45,4,12,4z"/></g></svg>',
		),

		array(
			'name'   => __( 'Star Rating', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'starrating',
			'id'     => 31,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M12 7.13l.97 2.29.47 1.11 1.2.1 2.47.21-1.88 1.63-.91.79.27 1.18.56 2.41-2.12-1.28-1.03-.64-1.03.62-2.12 1.28.56-2.41.27-1.18-.91-.79-1.88-1.63 2.47-.21 1.2-.1.47-1.11.97-2.27M12 2L9.19 8.63 2 9.24l5.46 4.73L5.82 21 12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2z"/></svg>',
		),

		array(
			'name'   => __( 'Accordion', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'accordion',
			'id'     => 34,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M21 18H2v2h19v-2zm-2-8v4H4v-4h15m1-2H3c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h17c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm1-4H2v2h19V4z"/></svg>',
		),

		array(
			'name'   => __( 'Shortcode', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'shortcode',
			'id'     => 21,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="sp-w-6 sp-fill-current"><path d="M256 8C119.3 8 8 119.2 8 256c0 136.7 111.3 248 248 248s248-111.3 248-248C504 119.2 392.7 8 256 8zM33 256c0-32.3 6.9-63 19.3-90.7l106.4 291.4C84.3 420.5 33 344.2 33 256zm223 223c-21.9 0-43-3.2-63-9.1l66.9-194.4 68.5 187.8c.5 1.1 1 2.1 1.6 3.1-23.1 8.1-48 12.6-74 12.6zm30.7-327.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-21.9 0-58.7-2.8-58.7-2.8-12-.7-13.4 17.7-1.4 18.4 0 0 11.4 1.4 23.4 2.1l34.7 95.2L200.6 393l-81.2-241.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-4.2 0-9.1-.1-14.4-.3C109.6 73 178.1 33 256 33c58 0 110.9 22.2 150.6 58.5-1-.1-1.9-.2-2.9-.2-21.9 0-37.4 19.1-37.4 39.6 0 18.4 10.6 33.9 21.9 52.3 8.5 14.8 18.4 33.9 18.4 61.5 0 19.1-7.3 41.2-17 72.1l-22.2 74.3-80.7-239.6zm81.4 297.2l68.1-196.9c12.7-31.8 17-57.2 17-79.9 0-8.2-.5-15.8-1.5-22.9 17.4 31.8 27.3 68.2 27.3 107 0 82.3-44.6 154.1-110.9 192.7z"/></svg>',
		),

		array(
			'name'   => __( 'Login Form', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'login',
			'id'     => 33,
			'icon'   => '
		<svg xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current " enable-background="new 0 0 24 24" height="24" viewBox="0 0 24 24" width="24"><g><rect fill="none" /></g><g><path d="M11,7L9.6,8.4l2.6,2.6H2v2h10.2l-2.6,2.6L11,17l5-5L11,7z M20,19h-8v2h8c1.1,0,2-0.9,2-2V5c0-1.1-0.9-2-2-2h-8v2h8V19z"/></g></svg>',
		),

		array(
			'name'   => __( 'Google Maps', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'googlemaps',
			'id'     => 50,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current " enable-background="new 0 0 30 30" viewBox="0 0 30 30" ><g><rect fill="none" /></g><g><path d="M26.3333 3L26.12 3.04L19 5.8L11 3L3.48 5.53333C3.2 5.62667 3 5.86667 3 6.17333V26.3333C3 26.7067 3.29333 27 3.66667 27L3.88 26.96L11 24.2L19 27L26.52 24.4667C26.8 24.3733 27 24.1333 27 23.8267V3.66667C27 3.29333 26.7067 3 26.3333 3ZM19 24.3333L11 21.52V5.66667L19 8.48V24.3333Z"/></g></svg>',
		),

		array(

			'name'   => __( 'Search Form', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'searchform',
			'id'     => 52,
			'icon'   => '
	<svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" class="sp-w-6 sp-fill-current " viewBox="0 0 24 24" ><path d="M0 0h24v24H0V0z" fill="none"></path><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path></svg>',
		),

		array(
			'name'   => __( 'Facebook Like', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'facebooklike',
			'id'     => 35,
			'icon'   => '
				<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M11.66 14.44H7.02C7.00895 14.44 7 14.449 7 14.46V22.5C7 22.511 7.00895 22.52 7.02 22.52H11.66C11.671 22.52 11.68 22.511 11.68 22.5V14.46C11.68 14.449 11.671 14.44 11.66 14.44Z" />
				<path d="M23 16.58C23.0003 16.3506 22.9544 16.1235 22.865 15.9121C22.7757 15.7008 22.6448 15.5097 22.48 15.35C22.7099 15.0664 22.84 14.715 22.85 14.35C22.8473 13.9028 22.6676 13.4749 22.35 13.16C22.0146 12.8194 21.558 12.6252 21.08 12.62H17.35L17.59 11C17.6217 10.8015 17.6384 10.601 17.64 10.4C17.64 8.88001 16.64 7.10001 15.27 7.10001C14.9031 7.10899 14.5503 7.24306 14.27 7.48001L14.13 7.60001L14.32 10.13C14.3352 10.2361 14.3352 10.3439 14.32 10.45C14.2922 10.5545 14.252 10.6552 14.2 10.75L12.2 14.52L12.13 14.58C12.1036 14.4113 12.0172 14.2579 11.8868 14.1477C11.7564 14.0375 11.5907 13.9779 11.42 13.98H7.37C7.27592 13.9787 7.18252 13.9961 7.09522 14.0312C7.00792 14.0662 6.92847 14.1183 6.86148 14.1844C6.79448 14.2505 6.74129 14.3292 6.70499 14.416C6.66868 14.5028 6.64999 14.5959 6.65 14.69V22.13C6.64999 22.2241 6.66868 22.3172 6.70499 22.404C6.74129 22.4908 6.79448 22.5696 6.86148 22.6356C6.92847 22.7017 7.00792 22.7538 7.09522 22.7889C7.18252 22.8239 7.27592 22.8413 7.37 22.84H11.31C11.4983 22.84 11.6789 22.7652 11.812 22.6321C11.9452 22.4989 12.02 22.3183 12.02 22.13V21.75C12.5073 22.1373 13.1077 22.3549 13.73 22.37H19.67C20.1027 22.3498 20.5116 22.1662 20.8141 21.8563C21.1167 21.5463 21.2903 21.1331 21.3 20.7C21.3153 20.5638 21.3153 20.4262 21.3 20.29C21.6037 20.1543 21.8605 19.932 22.0384 19.6509C22.2163 19.3698 22.3073 19.0425 22.3 18.71C22.3024 18.4834 22.2582 18.2587 22.17 18.05C22.4224 17.8963 22.6312 17.6804 22.7765 17.4231C22.9218 17.1658 22.9987 16.8755 23 16.58ZM11.33 22.16H7.37V14.72H11.31V22.16H11.33ZM21.57 17.56L21.1 17.7L21.39 18.1C21.5255 18.2746 21.5993 18.4891 21.6 18.71C21.6027 18.9512 21.5182 19.1852 21.362 19.369C21.2058 19.5527 20.9885 19.6738 20.75 19.71L20.28 19.79L20.5 20.22C20.5745 20.3653 20.6123 20.5267 20.61 20.69C20.61 20.9552 20.5046 21.2096 20.3171 21.3971C20.1296 21.5846 19.8752 21.69 19.61 21.69H13.74C13.2818 21.6743 12.8405 21.5132 12.48 21.23C12.3821 21.1426 12.2707 21.0716 12.15 21.02L12 21V15.52L12.62 14.99L14.7 11.13C14.7912 10.9814 14.8586 10.8194 14.9 10.65C14.9463 10.474 14.9632 10.2915 14.95 10.11L14.8 8.00001C14.9435 7.91855 15.105 7.87389 15.27 7.87001C16.17 7.87001 16.94 9.31001 16.94 10.48C16.9502 10.6432 16.9502 10.8068 16.94 10.97L16.57 13.31H21.09C21.3832 13.3137 21.6632 13.4322 21.87 13.64C21.9645 13.7326 22.0397 13.8431 22.0912 13.9649C22.1427 14.0868 22.1695 14.2177 22.17 14.35C22.1652 14.5068 22.126 14.6607 22.0552 14.8006C21.9843 14.9406 21.8835 15.0633 21.76 15.16L21.34 15.48L21.8 15.74C21.9847 15.8301 22.1372 15.9749 22.2367 16.1548C22.3362 16.3346 22.3779 16.5408 22.356 16.7452C22.3341 16.9495 22.2498 17.1422 22.1146 17.297C21.9793 17.4517 21.7996 17.561 21.6 17.61L21.57 17.56Z" />
				<path d="M25.48 27.93H4.48C3.82043 27.9247 3.18967 27.659 2.72515 27.1907C2.26062 26.7225 1.99998 26.0896 2 25.43V4.42999C2 3.76695 2.26339 3.13107 2.73223 2.66223C3.20107 2.19338 3.83696 1.92999 4.5 1.92999H25.5C26.163 1.92999 26.7989 2.19338 27.2678 2.66223C27.7366 3.13107 28 3.76695 28 4.42999V25.43C28 25.76 27.9347 26.0867 27.8078 26.3913C27.6809 26.696 27.495 26.9725 27.2607 27.2049C27.0264 27.4373 26.7485 27.621 26.4429 27.7455C26.1372 27.8699 25.81 27.9326 25.48 27.93ZM4.48 3.92999C4.34819 3.93255 4.2225 3.98605 4.12928 4.07927C4.03606 4.17249 3.98256 4.29819 3.98 4.42999V25.43C3.98 25.5626 4.03268 25.6898 4.12645 25.7835C4.22021 25.8773 4.34739 25.93 4.48 25.93H25.48C25.6126 25.93 25.7398 25.8773 25.8336 25.7835C25.9273 25.6898 25.98 25.5626 25.98 25.43V4.42999C25.98 4.29738 25.9273 4.17021 25.8336 4.07644C25.7398 3.98267 25.6126 3.92999 25.48 3.92999H4.48Z" />
				</svg>',
		),

		array(
			'name'   => __( 'Facebook Comments', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'facebookcomments',
			'id'     => 36,
			'icon'   => '<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M3.13 28C2.94034 27.9985 2.75305 27.9576 2.58 27.88C2.31249 27.7733 2.08344 27.5882 1.92286 27.3491C1.76227 27.11 1.67762 26.828 1.68 26.54V5.34999C1.68 4.37665 2.06666 3.44317 2.75492 2.75491C3.44318 2.06665 4.37666 1.67999 5.35 1.67999H24.65C25.6225 1.68263 26.5545 2.07014 27.2422 2.75783C27.9299 3.44551 28.3174 4.37746 28.32 5.34999V19.83C28.3174 20.8016 27.9295 21.7325 27.2416 22.4186C26.5536 23.1047 25.6216 23.49 24.65 23.49H8.28L4.16 27.61C3.88133 27.8704 3.51127 28.0105 3.13 28ZM5.35 4.17999C5.19683 4.17999 5.04516 4.21032 4.90377 4.26924C4.76238 4.32816 4.63405 4.4145 4.5262 4.52328C4.41836 4.63205 4.33312 4.76111 4.27542 4.90301C4.21771 5.0449 4.18868 5.19682 4.19 5.34999V24.05L7.24 21H24.65C24.8032 21.0013 24.9551 20.9723 25.097 20.9146C25.2389 20.8569 25.3679 20.7716 25.4767 20.6638C25.5855 20.5559 25.6718 20.4276 25.7308 20.2862C25.7897 20.1448 25.82 19.9932 25.82 19.84V5.34999C25.82 5.03969 25.6967 4.7421 25.4773 4.52268C25.2579 4.30326 24.9603 4.17999 24.65 4.17999H5.35ZM11.59 13.31L11.9 11.31H10V10C9.9827 9.84694 10.001 9.69194 10.0535 9.54712C10.1059 9.40229 10.1911 9.27154 10.3025 9.16506C10.4138 9.05859 10.5482 8.97927 10.6952 8.93329C10.8422 8.88731 10.9979 8.87592 11.15 8.9H12V7.15C11.4746 7.05974 10.943 7.0096 10.41 7C10.0473 6.97295 9.683 7.02603 9.34306 7.15548C9.00313 7.28492 8.69582 7.48756 8.44294 7.74903C8.19006 8.0105 7.99779 8.3244 7.87977 8.66847C7.76175 9.01254 7.72086 9.37836 7.76 9.74V11.28H6V13.28H7.76V18.18C8.12352 18.2406 8.49147 18.2707 8.86 18.27C9.24102 18.2835 9.62249 18.2634 10 18.21V13.31H11.59ZM13.98 8.92999H23.98V9.92999H13.98V8.92999ZM22.98 14.93H13.98V15.93H22.98V14.93ZM13.98 11.93H20.98V12.93H13.98V11.93Z" />
				</svg>
				',
		),

		array(
			'name'   => __( 'Facebook Page', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'facebookpage',
			'id'     => 37,
			'icon'   => '
				<svg class="sp-w-6 sp-fill-current "  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M29 13.32C29 13.8723 28.5523 14.32 28 14.32H13.4V16.94C13.4 17.4923 12.9523 17.94 12.4 17.94H5.3C4.74772 17.94 4.3 17.4923 4.3 16.94V14.32H2C1.44771 14.32 1 13.8723 1 13.32V2C1 1.44771 1.44772 1 2 1H28C28.5523 1 29 1.44772 29 2V13.32ZM27 12.32H13.4V8.61999C13.4 8.06771 12.9523 7.62 12.4 7.62H5.3C4.74772 7.62 4.3 8.06771 4.3 8.62V12.32H3V4C3 3.44772 3.44772 3 4 3H26C26.5523 3 27 3.44771 27 4V12.32ZM14 9.48001L17.46 4.76001C17.4973 4.71033 17.5456 4.67001 17.6011 4.64224C17.6567 4.61446 17.7179 4.60001 17.78 4.60001C17.8421 4.60001 17.9033 4.61446 17.9589 4.64224C18.0144 4.67001 18.0627 4.71033 18.1 4.76001L20.84 8.46001C20.8532 8.48285 20.8721 8.50183 20.8949 8.51503C20.9177 8.52822 20.9436 8.53517 20.97 8.53517C20.9964 8.53517 21.0223 8.52822 21.0451 8.51503C21.0679 8.50183 21.0868 8.48285 21.1 8.46001L22.83 6.00001C22.8656 5.94637 22.9139 5.90238 22.9706 5.87195C23.0273 5.84151 23.0906 5.82559 23.155 5.82559C23.2194 5.82559 23.2827 5.84151 23.3394 5.87195C23.3961 5.90238 23.4444 5.94637 23.48 6.00001L25.92 9.49001C25.9628 9.55127 25.9886 9.62286 25.9945 9.69739C26.0004 9.77191 25.9864 9.84667 25.9537 9.91394C25.9211 9.98121 25.8711 10.0386 25.809 10.0801C25.7468 10.1216 25.6746 10.1457 25.6 10.15H14.32C14.2437 10.1469 14.1697 10.123 14.106 10.081C14.0422 10.0389 13.9911 9.98028 13.9582 9.91137C13.9253 9.84247 13.9118 9.76589 13.9192 9.68989C13.9265 9.61388 13.9545 9.54132 14 9.48001ZM17 24.29L19.43 21.05C19.4553 21.0187 19.4873 20.9934 19.5236 20.9761C19.56 20.9587 19.5997 20.9497 19.64 20.9497C19.6803 20.9497 19.72 20.9587 19.7564 20.9761C19.7927 20.9934 19.8247 21.0187 19.85 21.05L21.74 23.59C21.749 23.6045 21.7615 23.6164 21.7764 23.6247C21.7912 23.633 21.808 23.6373 21.825 23.6373C21.842 23.6373 21.8588 23.633 21.8736 23.6247C21.8885 23.6164 21.901 23.6045 21.91 23.59L23.1 21.87C23.1246 21.8329 23.1581 21.8024 23.1973 21.7813C23.2366 21.7603 23.2804 21.7493 23.325 21.7493C23.3696 21.7493 23.4134 21.7603 23.4527 21.7813C23.4919 21.8024 23.5254 21.8329 23.55 21.87L25.23 24.3C25.2584 24.3429 25.2749 24.3925 25.2778 24.4439C25.2807 24.4952 25.2699 24.5464 25.2465 24.5922C25.2231 24.638 25.1879 24.6768 25.1446 24.7045C25.1013 24.7322 25.0514 24.7479 25 24.75H17.22C17.1678 24.7474 17.1174 24.7307 17.0739 24.7017C17.0304 24.6728 16.9956 24.6326 16.9731 24.5855C16.9505 24.5384 16.9411 24.4861 16.9459 24.434C16.9506 24.382 16.9693 24.3323 17 24.29ZM12.4 8.62V16.94H5.3V8.62H12.4ZM15 15.93H28V16.92H15V15.93ZM27 18.94V27H16V18.94H27ZM28 18.94C28 18.3877 27.5523 17.94 27 17.94H16C15.4477 17.94 15 18.3877 15 18.94V27C15 27.5523 15.4477 28 16 28H27C27.5523 28 28 27.5523 28 27V18.94ZM4.3 18.93H13.4V19.93H4.3V18.93ZM13.4 20.93H4.3V21.93H13.4V20.93ZM4.3 22.93H13.4V23.93H4.3V22.93ZM13.4 24.93H4.3V25.93H13.4V24.93ZM4.3 26.93H13.4V27.93H4.3V26.93ZM10.13 12.3L10 13.16H9.26V15.25C9.10684 15.2603 8.95316 15.2603 8.8 15.25C8.6435 15.2603 8.4865 15.2603 8.33 15.25V13.16H7.57V12.3H8.33V11.64C8.31327 11.4862 8.33074 11.3306 8.38117 11.1843C8.4316 11.038 8.51374 10.9046 8.62172 10.7938C8.72969 10.683 8.86083 10.5974 9.00575 10.5431C9.15067 10.4889 9.30578 10.4673 9.46 10.48C9.68453 10.483 9.9085 10.5031 10.13 10.54V11.27H9.75C9.68555 11.2625 9.62023 11.2692 9.55871 11.2899C9.4972 11.3106 9.44101 11.3446 9.39418 11.3895C9.34735 11.4344 9.31104 11.4891 9.28784 11.5497C9.26464 11.6103 9.25513 11.6753 9.26 11.74V12.3H10.13Z"/>
				</svg>',
		),

		array(
			'name'   => __( 'Facebook Embed', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'facebookembed',
			'id'     => 38,
			'icon'   => '
		<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path fill-rule="evenodd" clip-rule="evenodd" d="M23.6023 7.34992H3.17535V21.4816H11.9735C12.13 21.4816 12.2801 21.5438 12.3908 21.6545C12.5015 21.7651 12.5637 21.9153 12.5637 22.0718V23.2522C12.5637 23.4087 12.5015 23.5588 12.3908 23.6695C12.2801 23.7802 12.13 23.8423 11.9735 23.8423H3.15349C2.52738 23.8423 1.92692 23.5936 1.48419 23.1509C1.04147 22.7082 0.792746 22.1077 0.792746 21.4816V7.34992C0.794172 7.03897 0.857013 6.73137 0.977659 6.44478C1.09831 6.1582 1.27438 5.89827 1.49577 5.67992C1.71716 5.46158 1.97951 5.28912 2.26774 5.17246C2.55597 5.05579 2.86441 4.99722 3.17535 5.0001H23.5805C24.2028 5.00298 24.7988 5.25147 25.2389 5.69152C25.6789 6.13158 25.9274 6.72759 25.9303 7.34992V15.0005C25.9304 15.1533 25.8712 15.3002 25.7652 15.4103C25.6592 15.5203 25.5147 15.585 25.362 15.5907H24.1925C24.036 15.5907 23.8859 15.5285 23.7752 15.4178C23.6645 15.3071 23.6023 15.157 23.6023 15.0005V7.34992ZM29.1873 21.6127V20.6947L28.3238 20.2138L26.2473 19.0771L24.4221 18.0498V19.6127L24.8046 19.7985L27.3402 21.0881L24.6188 22.3232L24.4221 22.4215V23.9626L29.1873 21.6127ZM16.6185 13.9731L11.0882 10.279C11.0056 10.2231 10.9092 10.1909 10.8096 10.1859C10.71 10.1809 10.6109 10.2033 10.5231 10.2506C10.4353 10.2979 10.362 10.3683 10.3114 10.4542C10.2607 10.5401 10.2346 10.6383 10.2357 10.738V18.1044C10.2346 18.2042 10.2607 18.3023 10.3114 18.3882C10.362 18.4742 10.4353 18.5446 10.5231 18.5919C10.6109 18.6392 10.71 18.6615 10.8096 18.6565C10.9092 18.6515 11.0056 18.6193 11.0882 18.5635L16.6185 14.8803C16.6929 14.8303 16.7538 14.7628 16.796 14.6837C16.8381 14.6046 16.8601 14.5163 16.8601 14.4267C16.8601 14.3371 16.8381 14.2488 16.796 14.1697C16.7538 14.0906 16.6929 14.0231 16.6185 13.9731ZM24.0614 17.1098L23.1761 19.7985L22.3564 22.3231L21.482 24.9899H19.8536L20.717 22.3231L21.5476 19.7985L22.422 17.1098H24.0614ZM19.0557 19.7985L16.5201 21.0881L19.2415 22.3232L19.4383 22.4215V23.9626L16.1157 22.3232L14.6621 21.6127V20.6947L16.2906 19.7985L19.4383 18.0498V19.6127L19.0557 19.7985Z" />
		</svg>
		',
		),

		array(
			'name'   => __( 'X (Twitter) Embed Tweet', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'twitterembedtweet',
			'id'     => 40,
			'icon'   => '
			<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.3077 29H3.69231C2.97826 29 2.29346 28.7163 1.78856 28.2114C1.28365 27.7065 1 27.0217 1 26.3077V3.69231C1 2.97826 1.28365 2.29346 1.78856 1.78856C2.29346 1.28365 2.97826 1 3.69231 1H26.3077C27.0217 1 27.7065 1.28365 28.2114 1.78856C28.7163 2.29346 29 2.97826 29 3.69231V26.3077C29 27.0217 28.7163 27.7065 28.2114 28.2114C27.7065 28.7163 27.0217 29 26.3077 29ZM3.69231 3.15385C3.5495 3.15385 3.41254 3.21058 3.31156 3.31156C3.21058 3.41254 3.15385 3.5495 3.15385 3.69231V17.1538H26.8462V3.69231C26.8462 3.5495 26.7894 3.41254 26.6884 3.31156C26.5875 3.21058 26.4505 3.15385 26.3077 3.15385H3.69231ZM26.8462 18.2308H3.15385V26.3077C3.15385 26.4505 3.21058 26.5875 3.31156 26.6884C3.41254 26.7894 3.5495 26.8462 3.69231 26.8462H26.3077C26.4505 26.8462 26.5875 26.7894 26.6884 26.6884C26.7894 26.5875 26.8462 26.4505 26.8462 26.3077V18.2308ZM19.7889 7.66295V7.98856C19.7989 8.90387 19.6276 9.81212 19.2849 10.6609C18.9423 11.5097 18.4349 12.2823 17.7922 12.9341C17.1495 13.5859 16.3841 14.104 15.5402 14.4585C14.6963 14.8131 13.7906 14.9971 12.8752 14.9999C11.5484 15.0073 10.2479 14.6303 9.13076 13.9145H9.71684C10.821 13.912 11.8906 13.5287 12.745 12.8292C12.2333 12.8183 11.7379 12.6468 11.329 12.3391C10.92 12.0314 10.618 11.603 10.4657 11.1144C10.8227 11.1977 11.1941 11.1977 11.5511 11.1144C11.0017 10.995 10.51 10.6905 10.1583 10.2518C9.80663 9.81321 9.61629 9.26709 9.61916 8.70489C9.95245 8.88906 10.324 8.9931 10.7045 9.00878C10.1956 8.65776 9.83578 8.12967 9.6953 7.52761C9.55482 6.92555 9.64377 6.29275 9.94477 5.75274C10.5576 6.51252 11.3239 7.13437 12.1936 7.57763C13.0633 8.02089 14.0167 8.27557 14.9916 8.32501C14.8927 7.90504 14.9058 7.46645 15.0296 7.05313C15.1534 6.6398 15.3836 6.26624 15.6971 5.96981C15.9304 5.74738 16.2056 5.57355 16.5067 5.45846C16.8078 5.34336 17.1288 5.28929 17.4509 5.29939C17.7731 5.30949 18.0901 5.38356 18.3834 5.51729C18.6767 5.65102 18.9405 5.84174 19.1594 6.07835C19.7058 5.96541 20.2301 5.76376 20.7114 5.48141C20.5255 6.04995 20.1399 6.5319 19.6261 6.83809C20.1051 6.75925 20.5697 6.60926 21.0044 6.3931C20.6774 6.8848 20.2658 7.31472 19.7889 7.66295ZM16.5723 19.5123L15.9262 21.5046L15.3123 23.3677L14.6769 25.3385H13.4708L14.1062 23.3677L14.72 21.5046L15.3662 19.5123H16.5723ZM12.8785 21.5046L11.0046 22.4631L13.0077 23.3677L13.1585 23.4323V24.5738L10.7139 23.3677L9.63694 22.84V22.1723L10.8323 21.5046L13.1585 20.2123V21.3646L12.8785 21.5046ZM20.3631 22.84V22.1723L19.7277 21.8061L18.1877 20.9662L16.8415 20.2123V21.3646L17.1215 21.5046L19.0061 22.4631L16.9923 23.3677L16.8415 23.4323V24.5738L20.3631 22.84Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'X (Twitter) Embed Timeline', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'twitterembedtimeline',
			'id'     => 80,
			'icon'   => '
			<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 1H26.5C27.1927 1.03141 27.8447 1.33603 28.3132 1.84714C28.7818 2.35825 29.0288 3.03421 29 3.727V25.273C29.0288 25.9658 28.7818 26.6418 28.3132 27.1529C27.8447 27.664 27.1927 27.9686 26.5 28H22.5C22.3615 27.9938 22.2311 27.9329 22.1374 27.8308C22.0437 27.7286 21.9943 27.5935 22 27.455V26.364C21.994 26.2253 22.0433 26.09 22.1371 25.9876C22.2308 25.8852 22.3613 25.8242 22.5 25.818H26.5C26.6385 25.8118 26.7689 25.7509 26.8626 25.6488C26.9563 25.5466 27.0057 25.4115 27 25.273V3.727C27.0057 3.5885 26.9563 3.45338 26.8626 3.35123C26.7689 3.24908 26.6385 3.18823 26.5 3.182H3.5C3.36152 3.18823 3.23115 3.24908 3.13743 3.35123C3.04372 3.45338 2.9943 3.5885 3 3.727V25.273C2.9943 25.4115 3.04372 25.5466 3.13743 25.6488C3.23115 25.7509 3.36152 25.8118 3.5 25.818H7.5C7.63866 25.8242 7.76919 25.8852 7.86293 25.9876C7.95667 26.09 8.00597 26.2253 8 26.364V27.455C8.00569 27.5935 7.95628 27.7286 7.86256 27.8308C7.76885 27.9329 7.63848 27.9938 7.5 28H3.5C2.80732 27.9686 2.15532 27.664 1.68675 27.1529C1.21819 26.6418 0.971244 25.9658 0.999999 25.273V3.727C0.971244 3.03421 1.21819 2.35825 1.68675 1.84714C2.15532 1.33603 2.80732 1.03141 3.5 1ZM15 7H25V8H15V7ZM15 9H22.423V10H15V9ZM25 11H15V12H25V11ZM15 15.07H25V16.07H15V15.07ZM22.423 17.07H15V18.07H22.423V17.07ZM15 19.07H25V20.07H15V19.07ZM13.015 25.046L11.256 26.031L13.138 26.969L13.279 27.038V28.215L10.979 26.969L9.974 26.423V25.731L11.095 25.046L13.279 23.715V24.9L13.015 25.046ZM15.87 25.046L16.476 23H15.342L14.736 25.046L14.162 26.969L13.563 29H14.697L15.296 26.969L15.87 25.046ZM20.026 25.731V26.423L16.727 28.215V27.038L16.869 26.969L18.75 26.031L16.991 25.046L16.727 24.9V23.715L17.99 24.492L19.427 25.362L20.026 25.731ZM9.69259 12.385C9.19503 12.5893 8.66184 12.6926 8.12401 12.689L8.11901 12.691C7.33901 12.6913 6.57536 12.4674 5.91901 12.046C6.03286 12.0592 6.14738 12.0659 6.26201 12.066C6.9088 12.0668 7.53709 11.8502 8.04601 11.451C7.74598 11.4451 7.45531 11.3455 7.21468 11.1662C6.97405 10.9868 6.7955 10.7368 6.70401 10.451C6.79306 10.4675 6.88343 10.4759 6.97401 10.476C7.10168 10.4763 7.22882 10.4595 7.35201 10.426C7.02703 10.3602 6.73479 10.1841 6.52483 9.9275C6.31487 9.67088 6.20011 9.34956 6.20001 9.01799V8.99999C6.40034 9.11176 6.6247 9.17351 6.85401 9.17999C6.55056 8.97642 6.33599 8.66497 6.2539 8.3089C6.1718 7.95284 6.22833 7.57888 6.41201 7.26299C6.77284 7.7066 7.22292 8.06938 7.73303 8.3278C8.24314 8.58621 8.80189 8.73448 9.37301 8.76299C9.34759 8.65586 9.33483 8.5461 9.33501 8.43599C9.33525 8.14798 9.42203 7.86668 9.58409 7.62859C9.74616 7.3905 9.97603 7.2066 10.2439 7.10075C10.5117 6.9949 10.8052 6.97198 11.0863 7.03497C11.3673 7.09796 11.6229 7.24394 11.82 7.45399C12.1416 7.39029 12.45 7.27227 12.732 7.10499C12.6248 7.43791 12.4002 7.72047 12.1 7.89999C12.3852 7.8653 12.6636 7.78812 12.926 7.67099C12.733 7.95893 12.4906 8.21047 12.21 8.41399V8.59999C12.214 9.13783 12.1111 9.67109 11.9072 10.1688C11.7033 10.6665 11.4025 11.1187 11.0223 11.4992C10.6421 11.8796 10.1901 12.1807 9.69259 12.385ZM8.124 21C8.66158 21.0036 9.19452 20.9004 9.69188 20.6963C10.1892 20.4922 10.6411 20.1914 11.0212 19.8112C11.4014 19.4311 11.7022 18.9792 11.9063 18.4819C12.1104 17.9845 12.2136 17.4516 12.21 16.914V16.728C12.4906 16.5245 12.733 16.2729 12.926 15.985C12.6638 16.101 12.3857 16.1772 12.101 16.211C12.4012 16.0315 12.6258 15.7489 12.733 15.416C12.451 15.5833 12.1426 15.7013 11.821 15.765C11.6049 15.5336 11.3184 15.3802 11.006 15.3285C10.6937 15.2768 10.373 15.3298 10.0939 15.4793C9.81477 15.6287 9.59286 15.8662 9.46267 16.1548C9.33248 16.4433 9.3013 16.7669 9.374 17.075C8.80289 17.0464 8.24417 16.8981 7.73407 16.6397C7.22397 16.3813 6.77388 16.0186 6.413 15.575C6.22961 15.8912 6.17355 16.2655 6.25621 16.6215C6.33887 16.9776 6.55406 17.2889 6.858 17.492C6.63014 17.4852 6.40723 17.4238 6.208 17.313V17.331C6.20811 17.6626 6.32287 17.9839 6.53282 18.2405C6.74278 18.4971 7.03502 18.6732 7.36 18.739C7.23681 18.7725 7.10967 18.7893 6.982 18.789C6.89143 18.7889 6.80105 18.7805 6.712 18.764C6.80349 19.0498 6.98205 19.2999 7.22267 19.4792C7.4633 19.6585 7.75397 19.7581 8.054 19.764C7.54509 20.1632 6.91679 20.3798 6.27 20.379C6.15538 20.3789 6.04086 20.3722 5.927 20.359C6.58336 20.7804 7.34701 21.0043 8.127 21.004L8.124 21Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'X (Twitter) Tweet Button', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'twittertweetbutton',
			'id'     => 82,
			'icon'   => '
			<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6935 3.0749C26.6322 3.04921 26.5664 3.03599 26.5 3.036H3.50001C3.43359 3.03599 3.36782 3.04921 3.30657 3.0749C3.24531 3.10059 3.18979 3.13823 3.14324 3.18561C3.09669 3.233 3.06005 3.28919 3.03546 3.35089C3.01087 3.4126 2.99882 3.47859 3.00001 3.545V26.455C2.99882 26.5214 3.01087 26.5874 3.03546 26.6491C3.06005 26.7108 3.09669 26.767 3.14324 26.8144C3.18979 26.8618 3.24531 26.8994 3.30657 26.9251C3.36782 26.9508 3.43359 26.964 3.50001 26.964H26.5C26.5664 26.964 26.6322 26.9508 26.6935 26.9251C26.7547 26.8994 26.8102 26.8618 26.8568 26.8144C26.9033 26.767 26.94 26.7108 26.9646 26.6491C26.9892 26.5874 27.0012 26.5214 27 26.455V3.545C27.0012 3.47859 26.9892 3.4126 26.9646 3.35089C26.94 3.28919 26.9033 3.233 26.8568 3.18561C26.8102 3.13823 26.7547 3.10059 26.6935 3.0749ZM3.50001 1H26.5C27.1687 1.00686 27.8074 1.27863 28.2761 1.75572C28.7447 2.2328 29.0051 2.87626 29 3.545V26.455C29.0051 27.1237 28.7447 27.7672 28.2761 28.2443C27.8074 28.7214 27.1687 28.9931 26.5 29H3.50001C2.83129 28.9931 2.19257 28.7214 1.72393 28.2443C1.25528 27.7672 0.994946 27.1237 1.00001 26.455V3.545C0.994946 2.87626 1.25528 2.2328 1.72393 1.75572C2.19257 1.27863 2.83129 1.00686 3.50001 1ZM25 22H5V23H25V22ZM25 24H5V25H25V24ZM8.134 5.17701L5.073 10.277C4.896 10.577 5.062 10.999 5.355 10.999H15.279C15.567 10.999 15.734 10.586 15.567 10.288L13.423 6.47801C13.3986 6.42189 13.3584 6.37411 13.3072 6.34055C13.256 6.307 13.1962 6.28912 13.135 6.28912C13.0738 6.28912 13.014 6.307 12.9628 6.34055C12.9116 6.37411 12.8714 6.42189 12.847 6.47801L11.33 9.17801C11.3201 9.19998 11.3041 9.21862 11.2839 9.2317C11.2637 9.24478 11.2401 9.25174 11.216 9.25174C11.1919 9.25174 11.1683 9.24478 11.1481 9.2317C11.1279 9.21862 11.1119 9.19998 11.102 9.17801L8.7 5.17701C8.67496 5.12305 8.63501 5.07738 8.58487 5.04538C8.53473 5.01337 8.47649 4.99637 8.417 4.99637C8.35752 4.99637 8.29927 5.01337 8.24913 5.04538C8.19899 5.07738 8.15905 5.12305 8.134 5.17701ZM17.381 10.143H24.105V11H17.381V10.143ZM23.476 6.71399H17.381V7.57099H23.476V6.71399ZM17.381 8.42899H25V9.28599H17.381V8.42899ZM24.5 13H5.5C5.22386 13 5 13.2239 5 13.5V20.5C5 20.7761 5.22386 21 5.5 21H24.5C24.7761 21 25 20.7761 25 20.5V13.5C25 13.2239 24.7761 13 24.5 13ZM8.08699 19.4C8.47255 19.3944 8.85319 19.3125 9.20688 19.1589C9.56058 19.0053 9.8803 18.7831 10.1476 18.5051C10.4148 18.2271 10.6243 17.8989 10.764 17.5395C10.9036 17.1801 10.9705 16.7965 10.961 16.411V16.275C11.1586 16.1248 11.3279 15.9406 11.461 15.731C11.2774 15.8157 11.0817 15.8713 10.881 15.896C11.0942 15.762 11.2521 15.5558 11.326 15.315C11.1285 15.4367 10.9112 15.523 10.684 15.57C10.5913 15.466 10.4778 15.3827 10.3508 15.3255C10.2239 15.2683 10.0863 15.2385 9.94699 15.238C9.67362 15.2435 9.41361 15.3573 9.22406 15.5544C9.0345 15.7514 8.9309 16.0156 8.93599 16.289C8.93596 16.3694 8.94468 16.4495 8.96199 16.528C8.5564 16.5045 8.16059 16.3942 7.80138 16.2044C7.44217 16.0146 7.12794 15.7498 6.87999 15.428C6.78968 15.5893 6.7425 15.7712 6.74299 15.956C6.74201 16.1272 6.78242 16.296 6.86077 16.4482C6.93912 16.6004 7.0531 16.7314 7.19299 16.83C7.03188 16.8246 6.87459 16.7796 6.73499 16.699V16.712C6.73162 16.9506 6.81035 17.1832 6.95799 17.3707C7.10563 17.5582 7.31322 17.6893 7.54599 17.742C7.45912 17.7666 7.36927 17.7791 7.27899 17.779C7.21519 17.7787 7.15157 17.7724 7.08899 17.76C7.14949 17.9655 7.27331 18.1468 7.44281 18.2778C7.6123 18.4089 7.81883 18.4832 8.03299 18.49C7.67938 18.7808 7.23581 18.9398 6.77799 18.94C6.69742 18.94 6.61693 18.935 6.53699 18.925C6.99512 19.2324 7.53428 19.3967 8.08599 19.397L8.08699 19.4ZM13.891 18.5H13.257V16.551H12.685V16H14.461V16.551H13.891V18.5ZM16.118 17.578L16.323 18.5L16.986 18.494L17.486 16.56H16.886L16.726 17.302C16.7034 17.405 16.6867 17.5091 16.676 17.614L16.673 17.6418L16.673 17.6419C16.6603 17.7586 16.6496 17.8573 16.644 17.937H16.634C16.6131 17.7399 16.5821 17.5439 16.541 17.35L16.387 16.56H15.695L15.527 17.34C15.4843 17.5378 15.4545 17.7383 15.438 17.94H15.429C15.415 17.7268 15.3876 17.5147 15.347 17.305L15.2 16.563H14.579L15.079 18.497H15.738L15.922 17.676C15.94 17.5953 15.9753 17.3953 16.028 17.076H16.037L16.069 17.3L16.118 17.578ZM18.625 18.53C18.4969 18.5373 18.3688 18.5182 18.2484 18.474C18.128 18.4298 18.0179 18.3614 17.925 18.273C17.8368 18.1739 17.7691 18.0582 17.726 17.9327C17.6829 17.8072 17.6652 17.6744 17.674 17.542C17.6555 17.2711 17.7385 17.003 17.907 16.79C18.0823 16.627 18.3111 16.5336 18.5504 16.5271C18.7897 16.5207 19.0231 16.6017 19.207 16.755C19.2863 16.844 19.3471 16.948 19.3855 17.0608C19.424 17.1737 19.4395 17.2931 19.431 17.412V17.712H18.3C18.2995 17.7597 18.309 17.8069 18.328 17.8507C18.3469 17.8944 18.3749 17.9337 18.41 17.966C18.491 18.031 18.5933 18.0638 18.697 18.058C18.7979 18.0588 18.8986 18.0477 18.997 18.025C19.1008 17.9985 19.2015 17.9606 19.297 17.912V18.391C19.2033 18.4433 19.1021 18.4807 18.997 18.502C18.8742 18.5233 18.7496 18.5327 18.625 18.53ZM18.588 16.982C18.5539 16.9808 18.5198 16.9867 18.4881 16.9994C18.4564 17.0122 18.4278 17.0315 18.404 17.056C18.3467 17.1201 18.315 17.203 18.315 17.289H18.854C18.8566 17.2078 18.83 17.1283 18.779 17.065C18.7555 17.0376 18.7261 17.016 18.693 17.0016C18.6599 16.9872 18.624 16.9805 18.588 16.982ZM20.2752 18.475C20.3967 18.5192 20.526 18.538 20.655 18.53C20.7806 18.5328 20.9062 18.5235 21.03 18.502C21.1351 18.4808 21.2364 18.4434 21.33 18.391V17.912C21.2345 17.9608 21.1339 17.9987 21.03 18.025C20.9316 18.0477 20.8309 18.0588 20.73 18.058C20.6262 18.064 20.5239 18.0312 20.443 17.966C20.4079 17.9337 20.3799 17.8944 20.3608 17.8507C20.3417 17.807 20.3319 17.7597 20.332 17.712H21.457V17.412C21.4653 17.293 21.4497 17.1736 21.4111 17.0607C21.3724 16.9479 21.3115 16.8439 21.232 16.755C21.0483 16.6013 20.8148 16.52 20.5754 16.5265C20.3359 16.5329 20.1071 16.6266 19.932 16.79C19.7635 17.003 19.6805 17.2711 19.699 17.542C19.6901 17.6743 19.7076 17.8071 19.7505 17.9326C19.7935 18.0581 19.8609 18.1738 19.949 18.273C20.0427 18.362 20.1538 18.4308 20.2752 18.475ZM20.5177 16.9994C20.5495 16.9867 20.5837 16.9808 20.618 16.982C20.6539 16.9805 20.6897 16.9871 20.7226 17.0015C20.7555 17.0159 20.7847 17.0376 20.808 17.065C20.8593 17.1282 20.8863 17.2077 20.884 17.289H20.345C20.3448 17.2032 20.3761 17.1203 20.433 17.056C20.457 17.0315 20.4858 17.0122 20.5177 16.9994ZM22.7 18C22.8031 17.9953 22.9046 17.9726 23 17.933V18.42C22.9225 18.4559 22.8414 18.4834 22.758 18.502C22.6698 18.5201 22.58 18.5285 22.49 18.527C22.4067 18.5349 22.3226 18.5241 22.244 18.4956C22.1653 18.4671 22.0939 18.4214 22.035 18.362C21.9305 18.215 21.8809 18.0359 21.895 17.856V17.056H21.676V16.79L21.955 16.58L22.117 16.174H22.527V16.562H22.973V17.062H22.527V17.815C22.527 17.94 22.583 18 22.7 18Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'X (Twitter) Follow Button', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'twitterfollowbutton',
			'id'     => 84,
			'icon'   => '
			<svg  class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6935 3.0749C26.6322 3.04921 26.5664 3.03599 26.5 3.036H3.50001C3.43359 3.03599 3.36782 3.04921 3.30657 3.0749C3.24531 3.10059 3.18979 3.13823 3.14324 3.18561C3.09669 3.233 3.06005 3.28919 3.03546 3.35089C3.01087 3.4126 2.99882 3.47859 3.00001 3.545V26.455C2.99882 26.5214 3.01087 26.5874 3.03546 26.6491C3.06005 26.7108 3.09669 26.767 3.14324 26.8144C3.18979 26.8618 3.24531 26.8994 3.30657 26.9251C3.36782 26.9508 3.43359 26.964 3.50001 26.964H26.5C26.5664 26.964 26.6322 26.9508 26.6935 26.9251C26.7547 26.8994 26.8102 26.8618 26.8568 26.8144C26.9033 26.767 26.94 26.7108 26.9646 26.6491C26.9892 26.5874 27.0012 26.5214 27 26.455V3.545C27.0012 3.47859 26.9892 3.4126 26.9646 3.35089C26.94 3.28919 26.9033 3.233 26.8568 3.18561C26.8102 3.13823 26.7547 3.10059 26.6935 3.0749ZM3.50001 1H26.5C27.1687 1.00686 27.8074 1.27863 28.2761 1.75572C28.7447 2.2328 29.0051 2.87626 29 3.545V26.455C29.0051 27.1237 28.7447 27.7672 28.2761 28.2443C27.8074 28.7214 27.1687 28.9931 26.5 29H3.50001C2.83129 28.9931 2.19257 28.7214 1.72393 28.2443C1.25528 27.7672 0.994946 27.1237 1.00001 26.455V3.545C0.994946 2.87626 1.25528 2.2328 1.72393 1.75572C2.19257 1.27863 2.83129 1.00686 3.50001 1ZM25 22H5V23H25V22ZM25 24H5V25H25V24ZM8.134 14.177L5.073 19.277C4.896 19.577 5.062 19.999 5.355 19.999H15.279C15.567 19.999 15.734 19.586 15.567 19.288L13.423 15.478C13.3986 15.4219 13.3584 15.3741 13.3072 15.3406C13.256 15.307 13.1962 15.2891 13.135 15.2891C13.0738 15.2891 13.014 15.307 12.9628 15.3406C12.9116 15.3741 12.8714 15.4219 12.847 15.478L11.33 18.178C11.3201 18.2 11.3041 18.2186 11.2839 18.2317C11.2637 18.2448 11.2401 18.2517 11.216 18.2517C11.1919 18.2517 11.1683 18.2448 11.1481 18.2317C11.1279 18.2186 11.1119 18.2 11.102 18.178L8.7 14.177C8.67496 14.1231 8.63501 14.0774 8.58487 14.0454C8.53473 14.0134 8.47649 13.9964 8.417 13.9964C8.35752 13.9964 8.29927 14.0134 8.24913 14.0454C8.19899 14.0774 8.15905 14.1231 8.134 14.177ZM17.381 19.143H24.105V20H17.381V19.143ZM23.476 15.714H17.381V16.571H23.476V15.714ZM17.381 17.429H25V18.286H17.381V17.429ZM24.5 5H5.5C5.22386 5 5 5.22386 5 5.5V12.5C5 12.7761 5.22386 13 5.5 13H24.5C24.7761 13 25 12.7761 25 12.5V5.5C25 5.22386 24.7761 5 24.5 5ZM9.20689 11.1589C8.8532 11.3125 8.47256 11.3944 8.08699 11.4V11.398C7.53529 11.3977 6.99613 11.2334 6.53799 10.926C6.61794 10.936 6.69843 10.941 6.77899 10.941C7.23681 10.9408 7.68038 10.7818 8.03399 10.491C7.81984 10.4842 7.61331 10.4099 7.44381 10.2788C7.27432 10.1478 7.1505 9.96655 7.08999 9.76101C7.15258 9.77339 7.2162 9.77975 7.27999 9.78001C7.37028 9.78006 7.46013 9.7676 7.54699 9.74301C7.31423 9.6903 7.10664 9.55921 6.95899 9.3717C6.81135 9.18419 6.73262 8.95164 6.73599 8.71301V8.70001C6.87569 8.78025 7.03298 8.8249 7.19399 8.83001C7.05411 8.73137 6.94013 8.60039 6.86178 8.44821C6.78343 8.29603 6.74302 8.12717 6.74399 7.95601C6.7435 7.77115 6.79069 7.5893 6.88099 7.42801C7.12895 7.74983 7.44317 8.0146 7.80239 8.20439C8.1616 8.39418 8.55741 8.50454 8.96299 8.52801C8.94569 8.44952 8.93697 8.36938 8.93699 8.28901C8.9319 8.0158 9.03537 7.75172 9.22471 7.5547C9.41405 7.35768 9.6738 7.24378 9.94699 7.23801C10.0863 7.23851 10.2239 7.26833 10.3508 7.32553C10.4778 7.38274 10.5913 7.46603 10.684 7.57001C10.9112 7.52301 11.1285 7.43671 11.326 7.31501C11.2521 7.55577 11.0942 7.76197 10.881 7.89601C11.0817 7.87133 11.2774 7.81567 11.461 7.73101C11.3279 7.94058 11.1586 8.12479 10.961 8.27501V8.41101C10.9706 8.7965 10.9036 9.18005 10.764 9.5395C10.6243 9.89894 10.4148 10.2271 10.1476 10.5051C9.88031 10.7831 9.56058 11.0053 9.20689 11.1589ZM12.692 10.58H13.315V9.66501H14.015V9.13601H13.315V8.67101H14.077V8.14301H12.692V10.58ZM16.159 9.63101C16.1749 9.89373 16.0894 10.1525 15.92 10.354C15.8329 10.4426 15.7279 10.5115 15.612 10.5562C15.4961 10.6009 15.372 10.6202 15.248 10.613C15.1257 10.6187 15.0037 10.598 14.8901 10.5522C14.7766 10.5065 14.6742 10.4369 14.59 10.348C14.4198 10.1491 14.3325 9.89243 14.346 9.63101C14.33 9.36993 14.4161 9.11286 14.586 8.91401C14.6734 8.82611 14.7786 8.75783 14.8944 8.71369C15.0103 8.66955 15.1342 8.65057 15.258 8.65801C15.4234 8.65448 15.5867 8.69522 15.731 8.77601C15.8676 8.85548 15.9779 8.97337 16.048 9.11501C16.1249 9.27595 16.1629 9.45269 16.159 9.63101ZM14.982 9.63101C14.9754 9.75781 14.9969 9.88451 15.045 10.002C15.0629 10.0421 15.0926 10.0758 15.1301 10.0986C15.1676 10.1215 15.2112 10.1324 15.255 10.13C15.2987 10.1322 15.342 10.1209 15.379 10.0977C15.416 10.0744 15.445 10.0403 15.462 10C15.5065 9.8816 15.5266 9.75539 15.521 9.62901C15.5272 9.50436 15.5064 9.37985 15.46 9.26401C15.4395 9.2269 15.4095 9.19597 15.373 9.17442C15.3365 9.15288 15.2949 9.14152 15.2525 9.14152C15.2101 9.14152 15.1685 9.15288 15.132 9.17442C15.0955 9.19597 15.0655 9.2269 15.045 9.26401C14.9965 9.37999 14.9749 9.50548 14.982 9.63101ZM16.516 10.58H17.143V7.98599H16.516V10.58ZM18.2 10.58H17.573V7.98599H18.2V10.58ZM20.134 10.354C20.3034 10.1525 20.3889 9.89372 20.373 9.631C20.3769 9.45268 20.3388 9.27594 20.262 9.115C20.1918 8.97336 20.0816 8.85547 19.945 8.776C19.8007 8.69521 19.6374 8.65447 19.472 8.658C19.3482 8.65043 19.2242 8.66935 19.1084 8.7135C18.9925 8.75764 18.8874 8.826 18.8 8.914C18.6301 9.11285 18.544 9.36992 18.56 9.631C18.5465 9.89242 18.6338 10.1491 18.804 10.348C18.8882 10.4368 18.9905 10.5065 19.1041 10.5522C19.2176 10.598 19.3397 10.6187 19.462 10.613C19.586 10.6202 19.7101 10.6009 19.826 10.5562C19.9419 10.5115 20.0469 10.4426 20.134 10.354ZM19.259 10.002C19.2109 9.8845 19.1893 9.7578 19.196 9.631H19.2C19.1917 9.50577 19.2118 9.3803 19.259 9.264C19.2795 9.22689 19.3095 9.19596 19.346 9.17441C19.3825 9.15287 19.4241 9.14151 19.4665 9.14151C19.5089 9.14151 19.5505 9.15287 19.587 9.17441C19.6235 9.19596 19.6535 9.22689 19.674 9.264C19.7204 9.37984 19.7412 9.50435 19.735 9.629C19.7406 9.75538 19.7205 9.88159 19.676 10C19.659 10.0403 19.63 10.0744 19.593 10.0976C19.556 10.1209 19.5127 10.1322 19.469 10.13C19.4251 10.1326 19.3815 10.1217 19.344 10.0988C19.3065 10.0759 19.2768 10.0422 19.259 10.002ZM22.293 10.58L22.093 9.68L22.045 9.408L22.013 9.18999H22C21.947 9.5 21.912 9.69699 21.894 9.778L21.71 10.578H21.051L20.551 8.689H21.168L21.316 9.41399C21.3563 9.61871 21.3833 9.8258 21.397 10.034H21.407C21.4233 9.83791 21.4527 9.64314 21.495 9.451L21.663 8.689H22.356L22.51 9.461C22.5587 9.707 22.5897 9.898 22.603 10.034H22.612C22.619 9.95 22.63 9.844 22.644 9.719C22.6554 9.61819 22.6721 9.51805 22.694 9.41899L22.854 8.694H23.459L22.959 10.583L22.293 10.58Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Advanced Gallery', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'seedprodgallery',
			'id'     => 49,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M22.0999 13.17L24.3199 9.84V13.54C24.3217 13.7141 24.3724 13.8842 24.4661 14.0309C24.5598 14.1776 24.6928 14.2951 24.8499 14.37L24.9899 14.44C25.1274 14.5115 25.2808 14.5468 25.4356 14.5424C25.5905 14.5381 25.7416 14.4943 25.8748 14.4152C26.008 14.3361 26.1188 14.2243 26.1967 14.0904C26.2746 13.9565 26.317 13.8049 26.3199 13.65V3.54C26.3249 3.45007 26.3249 3.35993 26.3199 3.27C26.2542 2.78763 26.0149 2.34578 25.6467 2.02729C25.2785 1.7088 24.8068 1.53555 24.3199 1.54H3.31995C2.78951 1.54 2.28081 1.75071 1.90573 2.12579C1.53066 2.50086 1.31995 3.00957 1.31995 3.54V24.54C1.31995 25.0704 1.53066 25.5791 1.90573 25.9542C2.28081 26.3293 2.78951 26.54 3.31995 26.54H13.4199C13.5759 26.5399 13.7292 26.4992 13.8648 26.422C14.0003 26.3448 14.1134 26.2337 14.1931 26.0996C14.2728 25.9655 14.3162 25.813 14.3192 25.657C14.3221 25.5011 14.2845 25.347 14.2099 25.21C14.1905 25.1647 14.1671 25.1212 14.1399 25.08C14.0692 24.921 13.9544 24.7856 13.809 24.6899C13.6637 24.5942 13.494 24.5422 13.3199 24.54H3.51995L10.5199 14.03L13.6399 16.71C13.7416 16.7936 13.8588 16.8564 13.9848 16.8946C14.1107 16.9328 14.243 16.9458 14.374 16.9328C14.505 16.9198 14.6322 16.8811 14.7482 16.8188C14.8642 16.7565 14.9667 16.672 15.0499 16.57C15.2048 16.3691 15.2775 16.1168 15.2534 15.8643C15.2293 15.6118 15.1101 15.3779 14.9199 15.21L10.9199 11.8C10.8138 11.7149 10.6913 11.6523 10.5601 11.6161C10.4289 11.58 10.2917 11.571 10.1569 11.5897C10.022 11.6084 9.89247 11.6545 9.77608 11.725C9.65968 11.7956 9.55892 11.8891 9.47995 12L3.31995 21.24V3.54H24.3199V6.24L20.4399 12.06C20.3356 12.2168 20.2777 12.3999 20.2729 12.5882C20.2681 12.7765 20.3165 12.9623 20.4126 13.1242C20.5088 13.2862 20.6487 13.4177 20.8163 13.5037C20.9839 13.5896 21.1723 13.6265 21.3599 13.61C21.5083 13.5964 21.6517 13.5498 21.7797 13.4737C21.9078 13.3976 22.0172 13.2938 22.0999 13.17ZM16.8199 10.54C18.2007 10.54 19.3199 9.42072 19.3199 8.04001C19.3199 6.6593 18.2007 5.54001 16.8199 5.54001C15.4392 5.54001 14.3199 6.6593 14.3199 8.04001C14.3199 9.42072 15.4392 10.54 16.8199 10.54ZM27.14 21.52C27.1433 21.7444 27.1266 21.9686 27.09 22.19L28.6 23.34C28.6651 23.393 28.709 23.4676 28.7236 23.5502C28.7383 23.6327 28.7228 23.7179 28.68 23.79L27.3 26.18C27.2566 26.2507 27.1901 26.3043 27.1118 26.3316C27.0334 26.359 26.948 26.3584 26.87 26.33L25.15 25.64C24.7896 25.9141 24.3966 26.1425 23.98 26.32L23.72 28.16C23.7082 28.2422 23.6675 28.3176 23.6052 28.3725C23.5429 28.4275 23.4631 28.4585 23.38 28.46H20.61C20.5273 28.4593 20.4472 28.4301 20.3835 28.3772C20.3197 28.3244 20.2761 28.2512 20.26 28.17L20 26.33C19.5836 26.1584 19.1932 25.9295 18.84 25.65L17.12 26.34C17.0423 26.3704 16.9562 26.372 16.8773 26.3445C16.7985 26.317 16.732 26.2622 16.69 26.19L15.3 23.8C15.2561 23.729 15.239 23.6446 15.2518 23.5621C15.2647 23.4795 15.3066 23.4043 15.37 23.35L16.84 22.21C16.79 21.97 16.79 21.75 16.79 21.52V21.52C16.7951 21.2923 16.8152 21.0651 16.85 20.84L15.38 19.7C15.3182 19.6448 15.2768 19.5704 15.2622 19.4888C15.2477 19.4072 15.2611 19.3231 15.3 19.25L16.69 16.86C16.732 16.7878 16.7985 16.733 16.8773 16.7055C16.9562 16.678 17.0423 16.6796 17.12 16.71L18.84 17.4C19.1921 17.1215 19.5829 16.8958 20 16.73L20.22 14.89C20.2321 14.8061 20.2743 14.7294 20.3387 14.6743C20.403 14.6191 20.4853 14.5891 20.57 14.59H23.34C23.4216 14.5911 23.5003 14.6207 23.5623 14.6737C23.6244 14.7266 23.6661 14.7996 23.68 14.88L23.94 16.72C24.3607 16.8892 24.7548 17.1182 25.11 17.4L26.83 16.71C26.908 16.6816 26.9934 16.681 27.0718 16.7084C27.1501 16.7357 27.2166 16.7893 27.26 16.86L28.64 19.25C28.6828 19.3222 28.6983 19.4073 28.6836 19.4899C28.669 19.5725 28.6251 19.647 28.56 19.7L27.09 20.84C27.1216 21.0653 27.1383 21.2925 27.14 21.52ZM20.3333 24.0144C20.8267 24.3441 21.4067 24.52 22 24.52C22.7957 24.52 23.5588 24.2039 24.1214 23.6413C24.684 23.0787 25 22.3157 25 21.52C25 20.9267 24.8241 20.3467 24.4944 19.8533C24.1648 19.36 23.6963 18.9754 23.1481 18.7484C22.5999 18.5213 21.9967 18.4619 21.4148 18.5777C20.8328 18.6934 20.2983 18.9791 19.8787 19.3987C19.4592 19.8183 19.1734 20.3528 19.0577 20.9347C18.9419 21.5167 19.0013 22.1199 19.2284 22.6681C19.4555 23.2162 19.84 23.6848 20.3333 24.0144Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Gallery', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'seedprodbasicgallery',
			'id'     => 50,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.1901 21.87L28.0001 7.42001C28.0407 7.14406 28.0251 6.86275 27.9543 6.59296C27.8835 6.32317 27.7589 6.07048 27.588 5.85005C27.417 5.62963 27.2033 5.44603 26.9597 5.31026C26.716 5.1745 26.4474 5.08938 26.1701 5.06001L9.66005 3.06001C9.38886 3.02643 9.11371 3.04662 8.85032 3.11942C8.58693 3.19223 8.34047 3.31621 8.12502 3.4843C7.90956 3.65239 7.72935 3.86129 7.59466 4.09906C7.45997 4.33682 7.37346 4.5988 7.34005 4.87001L6.93006 8.23999H3.15005C2.83261 8.25556 2.53405 8.3954 2.31887 8.6293C2.10368 8.86319 1.98916 9.17236 2.00005 9.48999V25.71C1.98923 25.8706 2.01055 26.0317 2.06276 26.1839C2.11497 26.3361 2.19702 26.4764 2.30411 26.5965C2.4112 26.7167 2.54118 26.8142 2.68643 26.8835C2.83169 26.9528 2.9893 26.9924 3.15005 27H21.6401C21.9575 26.9844 22.2561 26.8446 22.4712 26.6107C22.6864 26.3768 22.8009 26.0676 22.7901 25.75V23.54L23.8801 23.67C24.1501 23.705 24.4245 23.686 24.6872 23.6143C24.9499 23.5425 25.1958 23.4194 25.4106 23.252C25.6254 23.0846 25.8049 22.8763 25.9387 22.639C26.0725 22.4018 26.1579 22.1405 26.1901 21.87ZM22.7901 21.44V9.48999C22.8009 9.17236 22.6864 8.86319 22.4712 8.6293C22.2561 8.3954 21.9575 8.25556 21.6401 8.23999H9.00006L9.40005 5.17001L25.9101 7.21001L24.1401 21.61L22.7901 21.44ZM5.49005 24.46H20.4901L20.5501 16.24L16.4601 20.65L14.7701 22.48L12.6301 20.18L10.2701 17.62L8.83005 19.71L7.49005 21.64L5.49005 24.46ZM14.7101 19L20.4901 12.73V10.74H4.31005V22L5.43005 20.36L8.00005 16.69L10.0001 13.84L14.7101 19Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Business Hours', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'businesshours',
			'id'     => 23,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.96 29.96" class="sp-w-6 sp-fill-current"><defs></defs><g id="Layer_2" data-name="Layer 2"><g id="Layer_1-2" data-name="Layer 1"><path class="cls-2" d="M27,15H24.12l-.12-.2L18,4.4A3,3,0,1,0,12,4a1.34,1.34,0,0,0,0,.2.83.83,0,0,0,0,.22L6,14.78l-.12.2H3a2,2,0,0,0-2,2V27a2,2,0,0,0,2,2H27a2,2,0,0,0,2-2V17A2,2,0,0,0,27,15Zm-19.27-.2L12.81,6a3,3,0,0,0,4.34,0l5.1,8.74.12.2H7.59ZM27,26.83H3v-9.7H27Z"/><path class="cls-2" d="M7.55,25a2.41,2.41,0,0,0,1.9-.75,3.21,3.21,0,0,0,.66-2.16,3.22,3.22,0,0,0-.65-2.16,2.41,2.41,0,0,0-1.91-.75,2.4,2.4,0,0,0-1.9.74A3.12,3.12,0,0,0,5,22.06a3.22,3.22,0,0,0,.66,2.17A2.43,2.43,0,0,0,7.55,25Zm-1-4.34a1.16,1.16,0,0,1,1-.49c.91,0,1.36.64,1.36,1.92S8.46,24,7.55,24a1.18,1.18,0,0,1-1-.48,2.42,2.42,0,0,1-.34-1.42A2.36,2.36,0,0,1,6.53,20.64Z"/><path class="cls-2" d="M12.37,22.89h.49a2.27,2.27,0,0,0,1.55-.48A1.79,1.79,0,0,0,15,21a1.64,1.64,0,0,0-.51-1.32A2.18,2.18,0,0,0,13,19.24H11.23V24.9h1.14Zm0-2.66h.52a1,1,0,0,1,.69.2.78.78,0,0,1,.22.62.81.81,0,0,1-.26.64,1.26,1.26,0,0,1-.79.22h-.38Z"/><polygon class="cls-2" points="19.07 23.91 17.11 23.91 17.11 22.45 18.94 22.45 18.94 21.47 17.11 21.47 17.11 20.23 19.07 20.23 19.07 19.24 15.97 19.24 15.97 24.9 19.07 24.9 19.07 23.91"/><path class="cls-2" d="M21.22,22.24c0-.32,0-.86-.07-1.62h0l2.35,4.28H25V19.24H24v2.68q0,.46.06,1.56h0l-2.34-4.24H20.2V24.9h1Z"/></g></g></svg>',
		),
		array(
			'name'   => __( 'Team Member', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'teammembers',
			'id'     => 28,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M22.14 18.13C21.7076 18.1313 21.2792 18.0471 20.8794 17.8823C20.4797 17.7174 20.1165 17.4751 19.8107 17.1693C19.5049 16.8636 19.2626 16.5003 19.0978 16.1006C18.9329 15.7008 18.8487 15.2724 18.85 14.84C18.8512 14.2995 18.9856 13.7676 19.2412 13.2913C19.4968 12.8151 19.8658 12.4091 20.3156 12.1094C20.7654 11.8097 21.2822 11.6254 21.8201 11.5728C22.3581 11.5203 22.9007 11.6011 23.4 11.8081C23.8993 12.0151 24.3399 12.3419 24.6829 12.7597C25.0259 13.1775 25.2606 13.6733 25.3664 14.2034C25.4722 14.7335 25.4457 15.2814 25.2894 15.7989C25.133 16.3163 24.8516 16.7872 24.47 17.17C24.1676 17.4803 23.8048 17.7256 23.4042 17.8907C23.0035 18.0557 22.5733 18.1372 22.14 18.13ZM15.28 25.87V24.08C15.2644 23.5385 15.3957 23.0029 15.66 22.53C15.9147 22.0947 16.2945 21.7463 16.75 21.53C17.5812 21.0874 18.4648 20.7515 19.38 20.53C20.2843 20.3155 21.2107 20.2081 22.14 20.21C23.0716 20.2071 24.0006 20.3077 24.91 20.51C25.8254 20.7205 26.7072 21.057 27.53 21.51C27.9931 21.7148 28.3761 22.0662 28.62 22.51C28.881 22.9914 29.012 23.5325 29 24.08V25.87H15.28ZM12.2 14.63C11.5052 14.6587 10.812 14.5422 10.1648 14.2878C9.51757 14.0335 8.93052 13.6469 8.44114 13.1528C7.95177 12.6588 7.57084 12.068 7.3227 11.4184C7.07456 10.7688 6.96466 10.0745 7.00002 9.38C6.96466 8.68549 7.07456 7.99123 7.3227 7.3416C7.57084 6.69197 7.95177 6.10124 8.44114 5.60716C8.93052 5.11308 9.51757 4.72652 10.1648 4.47217C10.812 4.21782 11.5052 4.10129 12.2 4.13C12.8989 4.09409 13.5976 4.20529 14.2508 4.45643C14.9041 4.70756 15.4973 5.09302 15.9921 5.58788C16.487 6.08274 16.8725 6.67597 17.1236 7.32919C17.3747 7.98242 17.4859 8.68109 17.45 9.38C17.4859 10.0789 17.3747 10.7776 17.1236 11.4308C16.8725 12.084 16.487 12.6773 15.9921 13.1721C15.4973 13.667 14.9041 14.0524 14.2508 14.3036C13.5976 14.5547 12.8989 14.6659 12.2 14.63V14.63ZM1.00002 25.87V22.58C0.993664 21.8099 1.19766 21.0527 1.59002 20.39C2.01384 19.7093 2.63962 19.1779 3.38002 18.87C4.8287 18.2072 6.33498 17.6783 7.88002 17.29C9.29387 16.9436 10.7444 16.769 12.2 16.77C12.8681 16.7643 13.5359 16.7977 14.2 16.87C14.78 16.94 15.41 17.05 16.09 17.19L15.19 18.08L14.3 19C14 19 13.67 18.93 13.3 18.91H12.2C10.8929 18.9064 9.58904 19.0404 8.31002 19.31C6.89055 19.6364 5.51682 20.1369 4.22002 20.8C3.88299 20.9718 3.60455 21.2398 3.42002 21.57C3.2262 21.8689 3.11573 22.2141 3.10002 22.57V23.76H13.18V25.86L1.00002 25.87ZM12.2 12.53C12.6176 12.5444 13.0337 12.4728 13.4224 12.3197C13.8112 12.1666 14.1644 11.9352 14.46 11.64C14.7533 11.343 14.9834 10.9895 15.1364 10.6011C15.2894 10.2127 15.362 9.79729 15.35 9.38C15.3716 8.96065 15.3048 8.54145 15.1542 8.14952C15.0035 7.75758 14.7722 7.40164 14.4753 7.10473C14.1784 6.80781 13.8224 6.57653 13.4305 6.42586C13.0386 6.27518 12.6194 6.20845 12.2 6.23C11.7807 6.20845 11.3615 6.27518 10.9695 6.42586C10.5776 6.57653 10.2217 6.80781 9.92475 7.10473C9.62783 7.40164 9.39656 7.75758 9.24588 8.14952C9.0952 8.54145 9.02847 8.96065 9.05002 9.38C9.02847 9.79935 9.0952 10.2186 9.24588 10.6105C9.39656 11.0024 9.62783 11.3584 9.92475 11.6553C10.2217 11.9522 10.5776 12.1835 10.9695 12.3341C11.3615 12.4848 11.7807 12.5515 12.2 12.53V12.53Z"/>
</svg>',
		),

		array(
			'name'   => __( 'Price List', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'pricelist',
			'id'     => 29,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 29.96 29.96" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M4 1H26C26.5304 1 27.0391 1.21071 27.4142 1.58579C27.7893 1.96086 28 2.46957 28 3V15.45C27.9948 15.6239 27.9443 15.7935 27.8535 15.942C27.7626 16.0904 27.6346 16.2126 27.4821 16.2964C27.3296 16.3803 27.1579 16.4228 26.9839 16.42C26.8099 16.4171 26.6396 16.3688 26.49 16.28C26.3422 16.1924 26.2194 16.0681 26.1336 15.9192C26.0478 15.7703 26.0018 15.6018 26 15.43V3H4V25.02H14.33C14.5157 25.0215 14.6974 25.0747 14.8545 25.1736C15.0117 25.2725 15.1383 25.4132 15.22 25.58C15.2899 25.731 15.3211 25.8971 15.3106 26.0632C15.3001 26.2293 15.2483 26.3902 15.1599 26.5312C15.0715 26.6722 14.9493 26.789 14.8044 26.8708C14.6595 26.9527 14.4964 26.9971 14.33 27H4C3.46957 27 2.96086 26.7893 2.58579 26.4142C2.21071 26.0391 2 25.5304 2 25V3C2 2.46957 2.21071 1.96086 2.58579 1.58579C2.96086 1.21071 3.46957 1 4 1ZM22.33 5.83H12.67C12.4923 5.83 12.3219 5.90059 12.1962 6.02624C12.0706 6.15189 12 6.32231 12 6.5C12 6.6777 12.0706 6.84811 12.1962 6.97376C12.3219 7.09941 12.4923 7.17 12.67 7.17H22.33C22.418 7.17 22.5051 7.15267 22.5864 7.119C22.6677 7.08533 22.7415 7.03598 22.8038 6.97376C22.866 6.91155 22.9153 6.83769 22.949 6.7564C22.9827 6.67511 23 6.58799 23 6.5C23 6.41202 22.9827 6.32489 22.949 6.2436C22.9153 6.16232 22.866 6.08846 22.8038 6.02624C22.7415 5.96403 22.6677 5.91467 22.5864 5.881C22.5051 5.84733 22.418 5.83 22.33 5.83ZM8.5 8C9.32843 8 10 7.32843 10 6.5C10 5.67157 9.32843 5 8.5 5C7.67157 5 7 5.67157 7 6.5C7 7.32843 7.67157 8 8.5 8ZM22.33 9.83H12.67C12.4923 9.83 12.3219 9.90059 12.1962 10.0262C12.0706 10.1519 12 10.3223 12 10.5C12 10.6777 12.0706 10.8481 12.1962 10.9738C12.3219 11.0994 12.4923 11.17 12.67 11.17H22.33C22.418 11.17 22.5051 11.1527 22.5864 11.119C22.6677 11.0853 22.7415 11.036 22.8038 10.9738C22.866 10.9115 22.9153 10.8377 22.949 10.7564C22.9827 10.6751 23 10.588 23 10.5C23 10.412 22.9827 10.3249 22.949 10.2436C22.9153 10.1623 22.866 10.0885 22.8038 10.0262C22.7415 9.96403 22.6677 9.91467 22.5864 9.881C22.5051 9.84733 22.418 9.83 22.33 9.83ZM8.5 12C9.32843 12 10 11.3284 10 10.5C10 9.67157 9.32843 9 8.5 9C7.67157 9 7 9.67157 7 10.5C7 11.3284 7.67157 12 8.5 12ZM22.33 13.83H12.67C12.4923 13.83 12.3219 13.9006 12.1962 14.0262C12.0706 14.1519 12 14.3223 12 14.5C12 14.6777 12.0706 14.8481 12.1962 14.9738C12.3219 15.0994 12.4923 15.17 12.67 15.17H22.33C22.418 15.17 22.5051 15.1527 22.5864 15.119C22.6677 15.0853 22.7415 15.036 22.8038 14.9738C22.866 14.9115 22.9153 14.8377 22.949 14.7564C22.9827 14.6751 23 14.588 23 14.5C23 14.412 22.9827 14.3249 22.949 14.2436C22.9153 14.1623 22.866 14.0885 22.8038 14.0262C22.7415 13.964 22.6677 13.9147 22.5864 13.881C22.5051 13.8473 22.418 13.83 22.33 13.83ZM8.5 16C9.32843 16 10 15.3284 10 14.5C10 13.6716 9.32843 13 8.5 13C7.67157 13 7 13.6716 7 14.5C7 15.3284 7.67157 16 8.5 16ZM15.33 17.83H12.67C12.4923 17.83 12.3219 17.9006 12.1962 18.0262C12.0706 18.1519 12 18.3223 12 18.5C12 18.588 12.0173 18.6751 12.051 18.7564C12.0847 18.8377 12.134 18.9115 12.1962 18.9738C12.2585 19.036 12.3323 19.0853 12.4136 19.119C12.4949 19.1527 12.582 19.17 12.67 19.17H15.33C15.418 19.17 15.5051 19.1527 15.5864 19.119C15.6677 19.0853 15.7415 19.036 15.8038 18.9738C15.866 18.9115 15.9153 18.8377 15.949 18.7564C15.9827 18.6751 16 18.588 16 18.5C16 18.3223 15.9294 18.1519 15.8038 18.0262C15.6781 17.9006 15.5077 17.83 15.33 17.83ZM8.5 20C9.32843 20 10 19.3284 10 18.5C10 17.6716 9.32843 17 8.5 17C7.67157 17 7 17.6716 7 18.5C7 19.3284 7.67157 20 8.5 20ZM29 23C29 26.3137 26.3137 29 23 29C19.6863 29 17 26.3137 17 23C17 19.6863 19.6863 17 23 17C26.3137 17 29 19.6863 29 23Z" /><path d="M22.76 18.61H23.76V19.81C23.9971 19.85 24.231 19.9068 24.46 19.98C24.7084 20.0541 24.9468 20.1582 25.17 20.29L24.74 21.45C24.5143 21.3285 24.2763 21.2313 24.03 21.16C23.7742 21.0794 23.5082 21.0357 23.24 21.03C23.0359 21.0196 22.8335 21.0719 22.66 21.18C22.5857 21.2217 22.5241 21.2828 22.4818 21.3568C22.4396 21.4307 22.4182 21.5148 22.42 21.6C22.4192 21.6792 22.4369 21.7574 22.4716 21.8286C22.5063 21.8998 22.5572 21.9619 22.62 22.01C22.845 22.1501 23.087 22.261 23.34 22.34L23.85 22.51C24.3344 22.649 24.7728 22.9148 25.12 23.28C25.4076 23.6322 25.56 24.0754 25.55 24.53C25.5525 24.9903 25.3852 25.4354 25.08 25.78C24.6892 26.187 24.1625 26.4361 23.6 26.48V27.75H22.6V26.51C21.9589 26.4513 21.343 26.2311 20.81 25.87L21.34 24.75C21.5797 24.9233 21.8467 25.0552 22.13 25.14C22.3895 25.2182 22.659 25.2587 22.93 25.26C23.2013 25.2927 23.4751 25.2251 23.7 25.07C23.7601 25.0172 23.8088 24.9527 23.8432 24.8805C23.8775 24.8083 23.8969 24.7299 23.9 24.65C23.9 24.4962 23.8388 24.3487 23.73 24.24C23.5387 24.078 23.313 23.9617 23.07 23.9L22.51 23.71C22.2779 23.6393 22.0534 23.5455 21.84 23.43C21.6353 23.3247 21.4494 23.1862 21.29 23.02C21.1289 22.8592 20.9999 22.6691 20.91 22.46C20.8147 22.2321 20.7671 21.987 20.77 21.74C20.7675 21.4823 20.8186 21.2269 20.92 20.99C21.0177 20.7635 21.1606 20.5593 21.34 20.39C21.5309 20.216 21.7556 20.0833 22 20C22.2522 19.9003 22.5191 19.8429 22.79 19.83L22.76 18.61Z" fill="gray"/></svg>',
		),

		array(
			'name'   => __( 'Add To Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wcaddtocart',
			'id'     => 34,
			'icon'   => '<svg class="sp-w-6 sp-fill-current "  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.48 11.69H17.09C17.22 11.69 17.3446 11.6384 17.4365 11.5465C17.5284 11.4546 17.58 11.3299 17.58 11.2V7.71999H21C21.0639 7.72 21.1271 7.70726 21.186 7.68252C21.2449 7.65777 21.2983 7.62152 21.343 7.57588C21.3877 7.53025 21.4228 7.47614 21.4464 7.41675C21.4699 7.35735 21.4813 7.29386 21.48 7.22999V5.55999C21.48 5.43175 21.4298 5.3086 21.34 5.21699C21.2503 5.12538 21.1282 5.07261 21 5.06999H17.58V1.58999C17.5774 1.45912 17.5236 1.3345 17.4301 1.24288C17.3366 1.15127 17.2109 1.09996 17.08 1.09999H15.47C15.4061 1.09866 15.3426 1.11009 15.2832 1.13362C15.2238 1.15714 15.1697 1.19229 15.1241 1.23699C15.0785 1.2817 15.0422 1.33506 15.0175 1.39396C14.9927 1.45286 14.98 1.5161 14.98 1.57999V5.06999H11.58C11.4518 5.06996 11.3286 5.12021 11.237 5.20995C11.1454 5.29969 11.0926 5.42177 11.09 5.54999V7.21999C11.0926 7.35179 11.1461 7.47749 11.2393 7.57071C11.3325 7.66393 11.4582 7.71743 11.59 7.71999H15V11.19C14.9999 11.3192 15.0498 11.4434 15.1393 11.5367C15.2288 11.6299 15.3509 11.6848 15.48 11.69ZM9.8 23.61C9.27349 23.5981 8.75536 23.7433 8.31175 24.0271C7.86814 24.311 7.51916 24.7206 7.30936 25.2036C7.09956 25.6867 7.03844 26.2213 7.13382 26.7392C7.22919 27.2572 7.47673 27.7349 7.84483 28.1116C8.21294 28.4882 8.68492 28.7466 9.20054 28.8538C9.71616 28.961 10.252 28.9122 10.7398 28.7135C11.2275 28.5148 11.645 28.1753 11.9389 27.7383C12.2328 27.3013 12.3899 26.7866 12.39 26.26C12.3953 25.9156 12.3323 25.5735 12.2048 25.2535C12.0773 24.9335 11.8876 24.642 11.6469 24.3956C11.4061 24.1493 11.1189 23.953 10.802 23.8182C10.485 23.6833 10.1445 23.6126 9.8 23.61ZM22.8 23.61C22.2735 23.5981 21.7554 23.7433 21.3118 24.0271C20.8681 24.311 20.5192 24.7206 20.3094 25.2036C20.0996 25.6867 20.0384 26.2213 20.1338 26.7392C20.2292 27.2572 20.4767 27.7349 20.8448 28.1116C21.2129 28.4882 21.6849 28.7466 22.2005 28.8538C22.7162 28.961 23.252 28.9122 23.7398 28.7135C24.2275 28.5148 24.645 28.1753 24.9389 27.7383C25.2328 27.3013 25.3899 26.7866 25.39 26.26C25.394 25.9126 25.3288 25.5678 25.1983 25.2458C25.0678 24.9238 24.8745 24.631 24.6298 24.3844C24.385 24.1378 24.0937 23.9423 23.7727 23.8094C23.4517 23.6764 23.1074 23.6086 22.76 23.61H22.8ZM10 19.3V19.14L11.22 17H20.88C21.3469 16.9984 21.8047 16.8702 22.2044 16.6289C22.6042 16.3876 22.931 16.0424 23.15 15.63L27.92 6.77999C27.9817 6.66553 27.9963 6.53155 27.9608 6.40647C27.9254 6.28139 27.8426 6.17505 27.73 6.10999L26.21 5.25999C26.1521 5.22778 26.0882 5.2079 26.0223 5.2016C25.9563 5.1953 25.8898 5.20272 25.8269 5.22338C25.7639 5.24405 25.706 5.27751 25.6566 5.32167C25.6072 5.36584 25.5675 5.41974 25.54 5.47999L24.46 7.71999L20.88 14.34H11.78L11.61 14L8.71 7.71999L7.48 5.06999L6.39 2.70999C6.35668 2.63132 6.30297 2.56296 6.23442 2.51198C6.16586 2.461 6.08493 2.42924 6 2.41999H2.51C2.4448 2.41997 2.38026 2.43297 2.32015 2.45822C2.26004 2.48347 2.20557 2.52046 2.15993 2.56702C2.1143 2.61359 2.07842 2.66879 2.05439 2.7294C2.03036 2.79001 2.01867 2.8548 2.02 2.91999V4.56999C2.01867 4.63517 2.03036 4.69997 2.05439 4.76058C2.07842 4.82118 2.1143 4.87639 2.15993 4.92295C2.20557 4.96952 2.26004 5.00651 2.32015 5.03176C2.38026 5.057 2.4448 5.07 2.51 5.06999H4.61L9.28 15.07L7.53 18.36C7.26814 18.8355 7.15985 19.3805 7.22 19.92C7.3034 20.5774 7.62564 21.1811 8.12536 21.6163C8.62508 22.0515 9.26739 22.2877 9.93 22.28H24.87C24.9339 22.28 24.9971 22.2673 25.056 22.2425C25.1149 22.2178 25.1683 22.1815 25.213 22.1359C25.2577 22.0902 25.2928 22.0361 25.3164 21.9767C25.3399 21.9174 25.3513 21.8539 25.35 21.79V20.13C25.3513 20.0656 25.34 20.0017 25.3166 19.9417C25.2931 19.8817 25.2582 19.827 25.2136 19.7805C25.169 19.7341 25.1157 19.6969 25.0568 19.6711C24.9978 19.6453 24.9344 19.6313 24.87 19.63H10.34C10.2955 19.6328 10.2508 19.6263 10.209 19.6108C10.1671 19.5954 10.129 19.5713 10.097 19.5403C10.0649 19.5092 10.0398 19.4717 10.0231 19.4304C10.0064 19.389 9.99852 19.3446 10 19.3Z" />
</svg>',
		),

		array(
			'name'   => __( 'Checkout', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wccheckout',
			'id'     => 35,
			'icon'   => '<svg class="sp-w-6 sp-fill-current "   viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.90441 23.8719C9.36486 23.5642 9.90621 23.4 10.46 23.4C10.8281 23.3987 11.1928 23.4702 11.5331 23.6104C11.8734 23.7507 12.1826 23.9569 12.4428 24.2172C12.7031 24.4774 12.9093 24.7866 13.0496 25.1269C13.1898 25.4672 13.2613 25.8319 13.26 26.2C13.26 26.7538 13.0958 27.2951 12.7881 27.7556C12.4804 28.2161 12.0431 28.5749 11.5315 28.7869C11.0199 28.9988 10.4569 29.0542 9.91375 28.9462C9.3706 28.8382 8.87169 28.5715 8.4801 28.1799C8.08852 27.7883 7.82184 27.2894 7.7138 26.7463C7.60576 26.2031 7.66121 25.6401 7.87314 25.1285C8.08506 24.6169 8.44395 24.1796 8.90441 23.8719ZM22.9044 23.8719C23.3649 23.5642 23.9062 23.4 24.46 23.4H24.43C24.8002 23.396 25.1675 23.4655 25.5107 23.6045C25.8539 23.7434 26.1661 23.949 26.4293 24.2094C26.6925 24.4698 26.9014 24.7798 27.044 25.1215C27.1866 25.4632 27.26 25.8298 27.26 26.2C27.26 26.7538 27.0958 27.2951 26.7881 27.7556C26.4804 28.2161 26.0431 28.5749 25.5315 28.7869C25.0199 28.9988 24.4569 29.0542 23.9137 28.9462C23.3706 28.8382 22.8717 28.5715 22.4801 28.1799C22.0885 27.7883 21.8218 27.2894 21.7138 26.7463C21.6058 26.2031 21.6612 25.6401 21.8731 25.1285C22.0851 24.6169 22.4439 24.1796 22.9044 23.8719ZM23.9752 11.3809L25.26 7.43C25.2955 7.32386 25.2881 7.20807 25.2396 7.10724C25.191 7.00641 25.1051 6.92848 25 6.89L13.85 3.27C13.7426 3.23505 13.6257 3.24377 13.5246 3.2943C13.4236 3.34482 13.3465 3.43308 13.31 3.54L11.18 10.09C11.1613 10.1431 11.1535 10.1995 11.157 10.2557C11.1605 10.3119 11.1753 10.3669 11.2005 10.4172C11.2257 10.4676 11.2608 10.5124 11.3036 10.549C11.3465 10.5855 11.3963 10.6131 11.45 10.63L20.5645 13.6H12.6L12.42 13.22L9.29 6.6L8 3.8L6.79 1.3C6.74823 1.21053 6.68184 1.13481 6.59861 1.08168C6.51538 1.02855 6.41874 1.00022 6.32 1H2.61C2.46944 1 2.33463 1.05584 2.23524 1.15523C2.13584 1.25463 2.08 1.38944 2.08 1.53V3.28C2.07999 3.34914 2.09376 3.41758 2.12052 3.48133C2.14727 3.54508 2.18647 3.60286 2.23582 3.65128C2.28518 3.6997 2.34369 3.73779 2.40793 3.76332C2.47218 3.78886 2.54088 3.80133 2.61 3.8H4.88L9.88 14.43L8 17.86C7.71649 18.357 7.60077 18.9321 7.67 19.5C7.76359 20.2049 8.11465 20.8503 8.6556 21.3119C9.19656 21.7734 9.88914 22.0185 10.6 22H26.7C26.8406 22 26.9754 21.9442 27.0748 21.8448C27.1742 21.7454 27.23 21.6106 27.23 21.47V19.72C27.23 19.6509 27.2162 19.5824 27.1895 19.5187C27.1627 19.4549 27.1235 19.3971 27.0742 19.3487C27.0248 19.3003 26.9663 19.2622 26.9021 19.2367C26.8378 19.2111 26.7691 19.1987 26.7 19.2H11.05C10.9572 19.2 10.8682 19.1631 10.8025 19.0975C10.7369 19.0318 10.7 18.9428 10.7 18.85V18.68L12 16.4H22.41C22.9047 16.4058 23.3919 16.279 23.8209 16.0328C24.25 15.7866 24.6054 15.43 24.85 15L27.85 10.89C27.8833 10.8297 27.9043 10.7634 27.9118 10.6949C27.9192 10.6264 27.913 10.5571 27.8935 10.4911C27.8739 10.425 27.8415 10.3635 27.798 10.3101C27.7544 10.2567 27.7008 10.2125 27.64 10.18L26 9.24C25.9381 9.2058 25.8698 9.18461 25.7994 9.17771C25.7289 9.17082 25.6579 9.17837 25.5905 9.1999C25.5231 9.22143 25.4608 9.25649 25.4074 9.30293C25.354 9.34938 25.3107 9.40623 25.28 9.47L23.9752 11.3809ZM22.45 13.31L24.31 7.58L14 4.22L12.13 10L22.45 13.31ZM21.49 8.9C21.5728 8.92615 21.6496 8.96839 21.7161 9.02429C21.7825 9.08019 21.8373 9.14865 21.8773 9.22576C21.9172 9.30286 21.9415 9.38709 21.9489 9.47362C21.9562 9.56015 21.9464 9.64727 21.92 9.73C21.863 9.89385 21.7446 10.0292 21.5898 10.1075C21.435 10.1858 21.2558 10.2011 21.09 10.15C21.0489 10.137 21.0092 10.1201 20.9716 10.0994C21.1255 9.93458 21.2415 9.73681 21.31 9.52C21.3718 9.32899 21.3948 9.12758 21.3778 8.92756C21.3762 8.90889 21.3743 8.89026 21.372 8.87169C21.412 8.87741 21.4515 8.88687 21.49 8.9ZM20.9954 8.0451C21.2484 7.99146 21.5117 8.00314 21.76 8.08C21.9514 8.13924 22.1292 8.23573 22.2832 8.36393C22.4372 8.49214 22.5643 8.64952 22.6572 8.82703C22.7502 9.00453 22.8071 9.19867 22.8247 9.39825C22.8424 9.59784 22.8204 9.79894 22.76 9.99C22.6695 10.2763 22.4963 10.5294 22.2622 10.7173C22.0281 10.9053 21.7435 11.0197 21.4444 11.0462C21.1453 11.0726 20.8451 11.0098 20.5817 10.8659C20.43 10.783 20.2944 10.6753 20.1801 10.5481C20.1191 10.5612 20.0571 10.5706 19.9944 10.5762C19.6953 10.6026 19.3951 10.5398 19.1317 10.3959C18.8682 10.2519 18.6533 10.0331 18.514 9.76708C18.3748 9.50109 18.3174 9.19981 18.3492 8.90125C18.3809 8.60269 18.5004 8.32022 18.6925 8.08948C18.8846 7.85874 19.1408 7.69007 19.4286 7.60474C19.7165 7.5194 20.0232 7.52123 20.31 7.61C20.5022 7.66806 20.6808 7.76386 20.8355 7.89179C20.8926 7.93902 20.946 7.99028 20.9954 8.0451ZM20.1964 8.49823C20.1508 8.46973 20.1017 8.44675 20.05 8.43C19.968 8.402 19.8812 8.39069 19.7947 8.39673C19.7083 8.40277 19.6239 8.42604 19.5465 8.46517C19.4692 8.50431 19.4005 8.55851 19.3444 8.6246C19.2883 8.69069 19.246 8.76732 19.22 8.85C19.1788 8.97408 19.1754 9.10758 19.2103 9.23359C19.2451 9.35959 19.3165 9.47242 19.4156 9.55776C19.5146 9.64311 19.6367 9.69712 19.7665 9.71296C19.7783 9.71439 19.79 9.71551 19.8018 9.71631C19.7879 9.60234 19.7869 9.48659 19.7992 9.37125C19.8309 9.07269 19.9504 8.79022 20.1425 8.55948C20.1599 8.53855 20.1779 8.51813 20.1964 8.49823ZM16.29 6.75L14.7 6.23C14.5846 6.19544 14.4603 6.20749 14.3537 6.26359C14.2472 6.31968 14.1668 6.41533 14.13 6.53C14.1113 6.58646 14.104 6.64606 14.1085 6.70536C14.113 6.76466 14.1292 6.82248 14.1561 6.87548C14.1831 6.92849 14.2203 6.97562 14.2656 7.01416C14.3109 7.05269 14.3634 7.08187 14.42 7.1L16 7.62C16.1154 7.65541 16.24 7.64504 16.348 7.59105C16.456 7.53706 16.5391 7.44358 16.58 7.33C16.6149 7.2133 16.6031 7.0876 16.5472 6.9794C16.4912 6.8712 16.3954 6.78896 16.28 6.75H16.29ZM14.16 7.89999L16.58 8.67999C16.6828 8.72665 16.7648 8.80947 16.8105 8.9127C16.8562 9.01592 16.8623 9.13233 16.8277 9.23978C16.7932 9.34723 16.7203 9.43822 16.623 9.49542C16.5257 9.55262 16.4107 9.57204 16.3 9.54999L13.88 8.75999C13.8235 8.74161 13.7712 8.71228 13.7261 8.67369C13.681 8.63509 13.6439 8.58798 13.617 8.53506C13.59 8.48213 13.5738 8.42441 13.5692 8.3652C13.5645 8.306 13.5716 8.24646 13.59 8.18999C13.6084 8.13352 13.6377 8.08123 13.6763 8.0361C13.7149 7.99096 13.762 7.95387 13.8149 7.92694C13.8679 7.90001 13.9256 7.88377 13.9848 7.87915C14.044 7.87453 14.1035 7.88161 14.16 7.89999Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wccart',
			'id'     => 36,
			'icon'   => '<svg class="sp-w-6 sp-fill-current "  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path d="M23.37 23.33C22.8166 23.332 22.2763 23.4979 21.8171 23.8067C21.358 24.1156 21.0007 24.5536 20.7903 25.0654C20.5799 25.5772 20.5259 26.1399 20.635 26.6824C20.7442 27.2248 21.0117 27.7228 21.4036 28.1134C21.7956 28.504 22.2945 28.7697 22.8374 28.8769C23.3803 28.9841 23.9428 28.9281 24.4538 28.7159C24.9649 28.5037 25.4016 28.1448 25.7088 27.6846C26.016 27.2243 26.18 26.6834 26.18 26.13C26.1813 25.7611 26.1095 25.3956 25.9686 25.0546C25.8277 24.7136 25.6207 24.404 25.3593 24.1436C25.098 23.8832 24.7876 23.6772 24.4461 23.5375C24.1047 23.3979 23.7389 23.3274 23.37 23.33ZM9.37 23.33C8.81621 23.33 8.27486 23.4942 7.8144 23.8019C7.35395 24.1096 6.99506 24.5469 6.78314 25.0585C6.57121 25.5701 6.51576 26.1331 6.6238 26.6762C6.73184 27.2194 6.99851 27.7183 7.3901 28.1099C7.78169 28.5015 8.2806 28.7682 8.82375 28.8762C9.36689 28.9842 9.92988 28.9288 10.4415 28.7169C10.9531 28.5049 11.3904 28.1461 11.6981 27.6856C12.0058 27.2251 12.17 26.6838 12.17 26.13C12.1713 25.7636 12.1005 25.4005 11.9615 25.0615C11.8225 24.7225 11.6181 24.4142 11.3599 24.1542C11.1018 23.8942 10.7949 23.6876 10.4569 23.5462C10.1189 23.4048 9.75639 23.3313 9.39 23.33H9.37Z" />
	<path d="M29 4.70999V5.18999C29.016 5.27592 29.016 5.36406 29 5.44999C28.9861 5.49589 28.9659 5.53963 28.94 5.57999C28.9206 5.62532 28.8972 5.66883 28.87 5.70999L23.82 14.88C23.5806 15.3174 23.2275 15.6821 22.798 15.9356C22.3686 16.189 21.8787 16.3218 21.38 16.32H11H10.94L9.67 18.61L9.62 18.78C9.6226 18.8737 9.66168 18.9627 9.72891 19.0281C9.79615 19.0935 9.88623 19.13 9.98 19.13H25.65C25.7906 19.13 25.9254 19.1858 26.0248 19.2852C26.1242 19.3846 26.18 19.5194 26.18 19.66V21.4C26.18 21.5406 26.1242 21.6754 26.0248 21.7748C25.9254 21.8742 25.7906 21.93 25.65 21.93H9.53C8.82028 21.9441 8.13004 21.6974 7.59003 21.2367C7.05003 20.7759 6.69777 20.1331 6.6 19.43C6.54061 18.8581 6.65936 18.2818 6.94 17.78L8.81 14.38V14.33L3.8 3.75999H1.52C1.44865 3.75876 1.37828 3.74313 1.31311 3.71404C1.24794 3.68496 1.18932 3.64301 1.14076 3.59071C1.09221 3.53842 1.05471 3.47685 1.03052 3.40971C1.00633 3.34257 0.99595 3.27124 1 3.19999V1.45999C0.999975 1.32114 1.05444 1.18783 1.15168 1.08872C1.24892 0.989606 1.38118 0.932612 1.52 0.929993H5.18C5.29158 0.930348 5.40093 0.961303 5.49614 1.01949C5.59135 1.07767 5.66878 1.16086 5.72 1.25999L6.88 3.72999L8.21 6.52999L11.35 13.16L11.53 13.53H21.32H21.37L25.21 6.52999L25.41 6.11999H13.41C13.3404 6.12132 13.2712 6.10892 13.2064 6.0835C13.1416 6.05808 13.0824 6.02015 13.0323 5.97186C12.9821 5.92357 12.942 5.86587 12.9141 5.80207C12.8863 5.73826 12.8713 5.6696 12.87 5.59999V4.18999C12.8713 4.12039 12.8863 4.05172 12.9141 3.98792C12.942 3.92411 12.9821 3.86642 13.0323 3.81813C13.0824 3.76984 13.1416 3.7319 13.2064 3.70648C13.2712 3.68107 13.3404 3.66867 13.41 3.66999H27.93C28.2094 3.66731 28.4786 3.77507 28.679 3.96983C28.8794 4.16459 28.9947 4.4306 29 4.70999Z" />
	</svg>
	',
		),

		array(
			'name'   => __( 'Products Grid', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wccustomproductsgrid',
			'id'     => 37,
			'icon'   => '
	<svg class="sp-w-6 sp-fill-current " viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path d="M16.44 8.50999H23.65V11.39H16.44V8.50999ZM16.44 18.61H23.65V21.49H16.44V18.61ZM25.1 1.99999H4.9C4.5188 1.99867 4.1411 2.07277 3.78866 2.21804C3.43622 2.36332 3.116 2.57688 2.84645 2.84643C2.57689 3.11598 2.36333 3.4362 2.21806 3.78864C2.07279 4.14108 1.99868 4.51878 2 4.89999V25.1C1.99868 25.4812 2.07279 25.8589 2.21806 26.2113C2.36333 26.5638 2.57689 26.884 2.84645 27.1535C3.116 27.4231 3.43622 27.6367 3.78866 27.7819C4.1411 27.9272 4.5188 28.0013 4.9 28H25.1C25.4812 28.0013 25.8589 27.9272 26.2113 27.7819C26.5638 27.6367 26.884 27.4231 27.1536 27.1535C27.4231 26.884 27.6367 26.5638 27.7819 26.2113C27.9272 25.8589 28.0013 25.4812 28 25.1V4.89999C28.0013 4.51878 27.9272 4.14108 27.7819 3.78864C27.6367 3.4362 27.4231 3.11598 27.1536 2.84643C26.884 2.57688 26.5638 2.36332 26.2113 2.21804C25.8589 2.07277 25.4812 1.99867 25.1 1.99999ZM25.1 25.08H4.9V4.89999H25.1V25.08ZM13.56 6.34999H6.35V13.56H13.56V6.34999ZM12.12 12.12H7.79V7.78999H12.12V12.12ZM13.56 16.44H6.35V23.65H13.56V16.44ZM12.12 22.21H7.79V17.88H12.12V22.21Z" />
	</svg>',
		),

		array(
			'name'   => __( 'Recent Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wcrecentproductsgrid',
			'id'     => 42,
			'icon'   => '<svg class="sp-w-6 sp-fill-current "  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path d="M13.56 9.23V16.44L19.69 20.08L20.8 18.23L15.72 15.23V9.23H13.56ZM28 12.12V2L24.17 5.83C22.0377 3.70809 19.2355 2.39134 16.2411 2.10417C13.2467 1.817 10.2453 2.57719 7.74859 4.25518C5.25189 5.93316 3.41438 8.42507 2.54927 11.3062C1.68416 14.1873 1.84499 17.2792 3.00434 20.055C4.1637 22.8308 6.24982 25.1186 8.90714 26.5285C11.5645 27.9384 14.6285 28.3831 17.577 27.7868C20.5255 27.1906 23.1759 25.5902 25.0765 23.2585C26.9772 20.9268 28.0104 18.0082 28 15H25.1C25.111 17.3387 24.3109 19.6088 22.8359 21.4237C21.361 23.2387 19.3024 24.4861 17.0109 24.9536C14.7195 25.421 12.3368 25.0796 10.2688 23.9875C8.20076 22.8953 6.57536 21.1199 5.66944 18.9639C4.76351 16.8078 4.6331 14.4043 5.30043 12.1628C5.96775 9.92135 7.39152 7.98057 9.32922 6.67107C11.2669 5.36156 13.5987 4.76434 15.9273 4.98113C18.2559 5.19792 20.4373 6.21531 22.1 7.86L17.84 12.12H28Z" />
	</svg>',
		),

		array(
			'name'   => __( 'Sale Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wcsaleproductsgrid',
			'id'     => 40,
			'icon'   => '<svg class="sp-w-6 sp-fill-current "  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M15 4.3C17.1163 4.3 19.185 4.92754 20.9446 6.10328C22.7042 7.27901 24.0757 8.95012 24.8855 10.9053C25.6954 12.8605 25.9073 15.0119 25.4944 17.0875C25.0815 19.1631 24.0625 21.0696 22.566 22.566C21.0696 24.0625 19.1631 25.0815 17.0875 25.4944C15.0119 25.9073 12.8605 25.6954 10.9053 24.8855C8.95012 24.0757 7.27901 22.7042 6.10328 20.9446C4.92755 19.185 4.30001 17.1163 4.30001 15C4.30265 12.163 5.43082 9.44295 7.43689 7.43688C9.44295 5.43082 12.163 4.30265 15 4.3ZM15 2C12.4288 2 9.91543 2.76244 7.77759 4.1909C5.63975 5.61935 3.97351 7.64968 2.98957 10.0251C2.00563 12.4006 1.74819 15.0144 2.2498 17.5362C2.75141 20.0579 3.98953 22.3743 5.80762 24.1924C7.6257 26.0105 9.94208 27.2486 12.4638 27.7502C14.9856 28.2518 17.5995 27.9944 19.9749 27.0104C22.3503 26.0265 24.3807 24.3603 25.8091 22.2224C27.2376 20.0846 28 17.5712 28 15C28 11.5522 26.6304 8.24558 24.1924 5.80761C21.7544 3.36964 18.4478 2 15 2ZM6.23 17.16V15.91C6.44916 16.0962 6.69975 16.2418 6.97 16.34C7.22889 16.4379 7.50322 16.4887 7.78 16.49C7.91827 16.4914 8.05611 16.4745 8.19 16.44C8.29585 16.4165 8.39717 16.376 8.49 16.32C8.56399 16.2758 8.62584 16.214 8.67 16.14C8.7216 16.065 8.74923 15.9761 8.74923 15.885C8.74923 15.7939 8.7216 15.705 8.67 15.63C8.60472 15.5331 8.51948 15.4513 8.42 15.39C8.2992 15.3041 8.1682 15.2336 8.03 15.18L7.55 14.97C7.16689 14.8318 6.82353 14.6018 6.55 14.3C6.3687 14.0587 6.25934 13.771 6.23457 13.4702C6.2098 13.1694 6.27062 12.8677 6.41 12.6C6.51312 12.3853 6.67199 12.2023 6.87 12.07C7.0811 11.9245 7.31906 11.8225 7.57 11.77C7.84529 11.7048 8.12711 11.6713 8.41 11.67C8.67124 11.6677 8.93219 11.6878 9.19 11.73C9.40179 11.7611 9.60964 11.8147 9.81 11.89V13.07C9.71588 12.998 9.61135 12.9406 9.5 12.9C9.39086 12.8464 9.27705 12.8029 9.16 12.77L8.81 12.7C8.70022 12.6903 8.58979 12.6903 8.48 12.7C8.35048 12.6842 8.21952 12.6842 8.09 12.7C7.98376 12.7166 7.88182 12.754 7.79 12.81C7.71602 12.8542 7.65416 12.916 7.61 12.99C7.56303 13.0574 7.53854 13.1379 7.54 13.22C7.53564 13.3087 7.56021 13.3965 7.61 13.47C7.66638 13.5552 7.73749 13.6297 7.82 13.69C7.92967 13.7597 8.04318 13.8231 8.16 13.88L8.6 14.08C8.80768 14.1638 9.00832 14.2642 9.2 14.38C9.36895 14.4746 9.52379 14.5924 9.66 14.73C9.78755 14.8615 9.88631 15.0182 9.95 15.19C10.0221 15.3816 10.0561 15.5854 10.05 15.79C10.0624 16.0604 10.0038 16.3293 9.88 16.57C9.76295 16.7818 9.59443 16.9606 9.39 17.09C9.17547 17.2248 8.93832 17.3197 8.69 17.37C8.40743 17.431 8.11908 17.4612 7.83 17.46C7.53817 17.46 7.24695 17.4332 6.96 17.38C6.70689 17.3443 6.46072 17.2701 6.23 17.16ZM14.53 17.38H15.9L13.88 11.78H12.38L10.38 17.38H11.73L12.13 16.13H14.13L14.53 17.38ZM13.24 13.28L13.84 15.16H12.44L13.05 13.27C13.0984 13.1106 13.1286 12.9462 13.14 12.78C13.1539 12.95 13.1875 13.1177 13.24 13.28ZM19.94 17.38H16.6V11.78H17.86V16.36H19.94V17.38ZM20.69 17.38H24.08V16.36H21.95V15.07H23.76V14.07H21.92V12.78H23.92V11.78H20.69V17.38Z" />
        </svg>',
		),

		array(
			'name'   => __( 'Best Selling Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wcbestsellingproductsgrid',
			'id'     => 41,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zM7 10h2v7H7zm4-3h2v10h-2zm4 6h2v4h-2z"/></svg> ',
		),

		array(
			'name'   => __( 'Featured Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wcfeaturedproductsgrid',
			'id'     => 39,
			'icon'   => ' <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24"  viewBox="0 0 24 24" class="sp-w-6 sp-fill-current "><rect fill="none" height="24" width="24"/><path d="M19,5h-2V3H7v2H5C3.9,5,3,5.9,3,7v1c0,2.55,1.92,4.63,4.39,4.94c0.63,1.5,1.98,2.63,3.61,2.96V19H7v2h10v-2h-4v-3.1 c1.63-0.33,2.98-1.46,3.61-2.96C19.08,12.63,21,10.55,21,8V7C21,5.9,20.1,5,19,5z M5,8V7h2v3.82C5.84,10.4,5,9.3,5,8z M12,14 c-1.65,0-3-1.35-3-3V5h6v6C15,12.65,13.65,14,12,14z M19,8c0,1.3-0.84,2.4-2,2.82V7h2V8z"/></svg>',
		),

		array(
			'name'   => __( 'Top Rated Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'wc',
			'type'   => 'wctopratedproductsgrid',
			'id'     => 43,
			'icon'   => '
        <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" class="sp-w-6 sp-fill-current " viewBox="0 0 24 24" ><g><rect fill="none" height="24" width="24"/></g><g><g><path d="M17,10.43V2H7v8.43c0,0.35,0.18,0.68,0.49,0.86l4.18,2.51l-0.99,2.34l-3.41,0.29l2.59,2.24L9.07,22L12,20.23L14.93,22 l-0.78-3.33l2.59-2.24l-3.41-0.29l-0.99-2.34l4.18-2.51C16.82,11.11,17,10.79,17,10.43z M11,11.07l-2-1.2V4h2V11.07z M15,9.87 l-2,1.2V4h2V9.87z"/></g></g></svg>',
		),

		array(
			'name'   => __( 'Posts', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'posts',
			'id'     => 136,
			'icon'   => '
			<svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current">
			<path clip-rule="evenodd" d="m1 27.5v-25c0-.39782.15804-.77936.43934-1.06066s.66284-.43934 1.06066-.43934h25c.3978 0 .7794.15804 1.0607.43934s.4393.66284.4393 1.06066v25c0 .3978-.158.7794-.4393 1.0607s-.6629.4393-1.0607.4393h-25c-.39782 0-.77936-.158-1.06066-.4393s-.43934-.6629-.43934-1.0607zm26-.5v-19h-24v19zm-24-21v-3h24v3zm12.67 5.11h7.66c.176 0 .3449.0692.4703.1927.1253.1235.1971.2913.1997.4673 0 .1777-.0706.3481-.1962.4738-.1257.1256-.2961.1962-.4738.1962h-7.66c-.1777 0-.3481-.0706-.4738-.1962-.1256-.1257-.1962-.2961-.1962-.4738.0026-.176.0744-.3438.1997-.4673.1254-.1235.2943-.1927.4703-.1927zm7.66 2.66h-7.66c-.1777 0-.3481.0706-.4738.1962-.1256.1257-.1962.2961-.1962.4738s.0706.3481.1962.4738c.1257.1256.2961.1962.4738.1962h7.66c.1777 0 .3481-.0706.4738-.1962.1256-.1257.1962-.2961.1962-.4738s-.0706-.3481-.1962-.4738c-.1257-.1256-.2961-.1962-.4738-.1962zm-10.72-3.66h-6.21999c-.05159-.0014-.10292.0078-.15086.0269s-.09148.0478-.12797.0843c-.0365.0365-.06517.08-.08428.1279-.01912.048-.02826.0993-.02689.1509v5.11c0 .1034.04109.2026.11423.2758.07314.0731.17234.1142.27577.1142h6.21999c.1034 0 .2026-.0411.2758-.1142.0731-.0732.1142-.1724.1142-.2758v-5.11c.0014-.0516-.0078-.1029-.0269-.1509-.0191-.0479-.0478-.0914-.0843-.1279-.0364-.0365-.08-.0652-.1279-.0843-.048-.0191-.0993-.0283-.1509-.0269zm-5.82999.78h5.43999v.63l-1.94 2-1.59999-1.61-1.9 2.56zm.39 4.32 1.59-2.15 1.51999 1.53 1.94-2v2.58zm16.15999 4.79h-7.66c-.1777 0-.3481.0706-.4738.1962-.1256.1257-.1962.2961-.1962.4738.0026.176.0744.3438.1997.4673.1254.1235.2943.1927.4703.1927h7.66c.176 0 .3449-.0692.4703-.1927.1253-.1235.1971-.2913.1997-.4673 0-.088-.0173-.1751-.051-.2564s-.083-.1551-.1452-.2174c-.0623-.0622-.1361-.1115-.2174-.1452s-.1684-.051-.2564-.051zm-7.66 2.67h7.66c.176 0 .3449.0692.4703.1927.1253.1235.1971.2913.1997.4673 0 .1777-.0706.3481-.1962.4738-.1257.1256-.2961.1962-.4738.1962h-7.66c-.1777 0-.3481-.0706-.4738-.1962-.1256-.1257-.1962-.2961-.1962-.4738.0026-.176.0744-.3438.1997-.4673.1254-.1235.2943-.1927.4703-.1927zm-9.27999-3.67h6.21999c.1034 0 .2026.0411.2758.1142.0731.0732.1142.1724.1142.2758v5.11c.0014.0516-.0078.1029-.0269.1509-.0191.0479-.0478.0914-.0843.1279-.0364.0365-.08.0652-.1279.0843-.048.0191-.0993.0283-.1509.0269h-6.21999c-.05159.0014-.10292-.0078-.15086-.0269s-.09148-.0478-.12797-.0843c-.0365-.0365-.06517-.08-.08428-.1279-.01912-.048-.02826-.0993-.02689-.1509v-5.11c0-.1034.04109-.2026.11423-.2758.07314-.0731.17234-.1142.27577-.1142zm5.82999.79h-5.43999v3.57l1.9-2.56 1.59999 1.61 1.94-2zm-3.45999 2.21-1.59 2.11 5.04999.01v-2.59l-1.94 2z"  fill-rule="evenodd"/></svg>',
		),

		array(
			'name'   => __( 'Posts', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'posts',
			'id'     => 47,
			'icon'   => '
            <svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current">
            <path clip-rule="evenodd" d="m1 27.5v-25c0-.39782.15804-.77936.43934-1.06066s.66284-.43934 1.06066-.43934h25c.3978 0 .7794.15804 1.0607.43934s.4393.66284.4393 1.06066v25c0 .3978-.158.7794-.4393 1.0607s-.6629.4393-1.0607.4393h-25c-.39782 0-.77936-.158-1.06066-.4393s-.43934-.6629-.43934-1.0607zm26-.5v-19h-24v19zm-24-21v-3h24v3zm12.67 5.11h7.66c.176 0 .3449.0692.4703.1927.1253.1235.1971.2913.1997.4673 0 .1777-.0706.3481-.1962.4738-.1257.1256-.2961.1962-.4738.1962h-7.66c-.1777 0-.3481-.0706-.4738-.1962-.1256-.1257-.1962-.2961-.1962-.4738.0026-.176.0744-.3438.1997-.4673.1254-.1235.2943-.1927.4703-.1927zm7.66 2.66h-7.66c-.1777 0-.3481.0706-.4738.1962-.1256.1257-.1962.2961-.1962.4738s.0706.3481.1962.4738c.1257.1256.2961.1962.4738.1962h7.66c.1777 0 .3481-.0706.4738-.1962.1256-.1257.1962-.2961.1962-.4738s-.0706-.3481-.1962-.4738c-.1257-.1256-.2961-.1962-.4738-.1962zm-10.72-3.66h-6.21999c-.05159-.0014-.10292.0078-.15086.0269s-.09148.0478-.12797.0843c-.0365.0365-.06517.08-.08428.1279-.01912.048-.02826.0993-.02689.1509v5.11c0 .1034.04109.2026.11423.2758.07314.0731.17234.1142.27577.1142h6.21999c.1034 0 .2026-.0411.2758-.1142.0731-.0732.1142-.1724.1142-.2758v-5.11c.0014-.0516-.0078-.1029-.0269-.1509-.0191-.0479-.0478-.0914-.0843-.1279-.0364-.0365-.08-.0652-.1279-.0843-.048-.0191-.0993-.0283-.1509-.0269zm-5.82999.78h5.43999v.63l-1.94 2-1.59999-1.61-1.9 2.56zm.39 4.32 1.59-2.15 1.51999 1.53 1.94-2v2.58zm16.15999 4.79h-7.66c-.1777 0-.3481.0706-.4738.1962-.1256.1257-.1962.2961-.1962.4738.0026.176.0744.3438.1997.4673.1254.1235.2943.1927.4703.1927h7.66c.176 0 .3449-.0692.4703-.1927.1253-.1235.1971-.2913.1997-.4673 0-.088-.0173-.1751-.051-.2564s-.083-.1551-.1452-.2174c-.0623-.0622-.1361-.1115-.2174-.1452s-.1684-.051-.2564-.051zm-7.66 2.67h7.66c.176 0 .3449.0692.4703.1927.1253.1235.1971.2913.1997.4673 0 .1777-.0706.3481-.1962.4738-.1257.1256-.2961.1962-.4738.1962h-7.66c-.1777 0-.3481-.0706-.4738-.1962-.1256-.1257-.1962-.2961-.1962-.4738.0026-.176.0744-.3438.1997-.4673.1254-.1235.2943-.1927.4703-.1927zm-9.27999-3.67h6.21999c.1034 0 .2026.0411.2758.1142.0731.0732.1142.1724.1142.2758v5.11c.0014.0516-.0078.1029-.0269.1509-.0191.0479-.0478.0914-.0843.1279-.0364.0365-.08.0652-.1279.0843-.048.0191-.0993.0283-.1509.0269h-6.21999c-.05159.0014-.10292-.0078-.15086-.0269s-.09148-.0478-.12797-.0843c-.0365-.0365-.06517-.08-.08428-.1279-.01912-.048-.02826-.0993-.02689-.1509v-5.11c0-.1034.04109-.2026.11423-.2758.07314-.0731.17234-.1142.27577-.1142zm5.82999.79h-5.43999v3.57l1.9-2.56 1.59999 1.61 1.94-2zm-3.45999 2.21-1.59 2.11 5.04999.01v-2.59l-1.94 2z"  fill-rule="evenodd"/></svg>',
		),

		array(
			'name'   => __( 'Alert', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'alertbox',
			'id'     => 10,
			'icon'   => '
<svg width="30" height="30" class="sp-w-14px  sp-fill-current sp-pb-2" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
<path d="M14.9845 3.00154C15.2434 2.98792 15.5011 3.04492 15.7301 3.16646C15.9591 3.288 16.1507 3.4695 16.2845 3.69154L26.8845 25.6915C26.9499 25.7984 26.9845 25.9213 26.9845 26.0465C26.9845 26.1718 26.9499 26.2947 26.8845 26.4015C26.7406 26.5981 26.5502 26.7559 26.3302 26.8606C26.1103 26.9653 25.8678 27.0138 25.6245 27.0015H4.36449C4.1212 27.0138 3.87868 26.9653 3.65875 26.8606C3.43881 26.7559 3.24835 26.5981 3.10449 26.4015C3.03908 26.2947 3.00447 26.1718 3.00447 26.0465C3.00447 25.9213 3.03908 25.7984 3.10449 25.6915L13.7045 3.69154C13.8366 3.47258 14.0251 3.29306 14.2502 3.1717C14.4754 3.05034 14.7289 2.99158 14.9845 3.00154V3.00154ZM14.9845 1.00154C14.3495 0.986112 13.7229 1.14832 13.1752 1.46988C12.6275 1.79144 12.1804 2.25958 11.8845 2.82154L1.28449 24.8215C0.30449 26.8015 1.98449 29.0015 4.36449 29.0015H25.6045C28.0245 29.0015 29.6645 26.8015 28.7045 24.8215L18.1045 2.82154C17.807 2.25647 17.3569 1.78628 16.8053 1.46451C16.2537 1.14275 15.6228 0.982336 14.9845 1.00154V1.00154Z"/>
<path d="M14.9845 7.00156C14.7193 7.00156 14.4649 7.10691 14.2774 7.29445C14.0899 7.48199 13.9845 7.73634 13.9845 8.00156V19.0016C13.9845 19.2668 14.0899 19.5211 14.2774 19.7087C14.4649 19.8962 14.7193 20.0016 14.9845 20.0016C15.2497 20.0016 15.5041 19.8962 15.6916 19.7087C15.8791 19.5211 15.9845 19.2668 15.9845 19.0016V8.00156C15.9845 7.73634 15.8791 7.48199 15.6916 7.29445C15.5041 7.10691 15.2497 7.00156 14.9845 7.00156Z" />
<path d="M14.9845 21.0016C14.7867 21.0016 14.5934 21.0602 14.4289 21.1701C14.2645 21.28 14.1363 21.4361 14.0606 21.6189C13.9849 21.8016 13.9651 22.0027 14.0037 22.1966C14.0423 22.3906 14.1375 22.5688 14.2774 22.7087C14.4172 22.8485 14.5954 22.9438 14.7894 22.9823C14.9834 23.0209 15.1845 23.0011 15.3672 22.9254C15.5499 22.8497 15.7061 22.7216 15.816 22.5571C15.9258 22.3927 15.9845 22.1993 15.9845 22.0016C15.9845 21.7363 15.8791 21.482 15.6916 21.2944C15.5041 21.1069 15.2497 21.0016 14.9845 21.0016Z"/>
</svg> ',
		),

		// $wordpress_widgets

		array(
			'name'   => __( 'Post Title', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'posttitle',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 23V2.42999C7.00003 2.03217 7.15806 1.65064 7.43937 1.36933C7.72067 1.08803 8.10221 0.929993 8.50003 0.929993H26.5C26.8979 0.929993 27.2794 1.08803 27.5607 1.36933C27.842 1.65064 28 2.03217 28 2.42999V24.82C28.0134 25.3536 27.9198 25.8846 27.7247 26.3815C27.5297 26.8784 27.2372 27.3312 26.8644 27.7133C26.4916 28.0954 26.0462 28.399 25.5542 28.6062C25.0623 28.8135 24.5338 28.9202 24 28.92H5.00003C4.46622 28.9202 3.93778 28.8135 3.44584 28.6062C2.9539 28.399 2.50842 28.0954 2.13565 27.7133C1.76288 27.3312 1.47036 26.8784 1.27532 26.3815C1.08029 25.8846 0.986684 25.3536 1.00003 24.82V24H6.00003C6.26525 24 6.5196 23.8946 6.70714 23.7071C6.89467 23.5196 7.00003 23.2652 7.00003 23ZM3.61477 26.8323C4.01691 27.1267 4.50164 27.2869 5.00003 27.29H21C20.5576 26.8178 20.2172 26.2595 20 25.65H2.76003C2.91354 26.1242 3.21262 26.5379 3.61477 26.8323ZM25.4142 25.3442C25.7893 24.9691 26 24.4604 26 23.93V23.53V2.92999H9.00003V22.93C9.00003 23.1952 9.10539 23.4496 9.29292 23.6371C9.48046 23.8246 9.73481 23.93 10 23.93H22C22 24.4604 22.2107 24.9691 22.5858 25.3442C22.9609 25.7193 23.4696 25.93 24 25.93C24.5305 25.93 25.0392 25.7193 25.4142 25.3442ZM14.7 19.18V19.88C14.7881 19.8758 14.8757 19.8716 14.9626 19.8674C15.9116 19.822 16.7871 19.78 17.52 19.78C18.32 19.78 19.21 19.81 20.32 19.86V19.16C19.9086 19.1764 19.497 19.1292 19.1 19.02C19.0357 18.9866 18.9793 18.9399 18.9344 18.883C18.8896 18.8261 18.8574 18.7603 18.84 18.69C18.7542 17.9533 18.7241 17.2112 18.75 16.47V11.35V9.01H20.02C20.3913 9.01 20.7179 9.03087 21.0144 9.04982C21.2609 9.06557 21.4866 9.08 21.7 9.08C21.7968 9.0771 21.8929 9.09769 21.98 9.14C22.0421 9.25428 22.0796 9.38033 22.09 9.51C22.1532 9.93061 22.1899 10.3548 22.2 10.78H22.91V8.91C22.9246 8.60533 22.9546 8.30161 23 8L22.91 7.91C22.65 7.97 22.38 7.97 22.12 7.97H19.23H15.77H12.93C12.7916 7.96538 12.6576 7.95227 12.5267 7.93947C12.3739 7.92451 12.2255 7.91 12.08 7.91L12 8C12 8.17593 12.0354 8.35187 12.0661 8.50404C12.0894 8.62 12.11 8.72215 12.11 8.8V10.8H12.82C12.8242 10.3504 12.8677 9.90202 12.95 9.46C12.9585 9.35337 12.9928 9.25041 13.05 9.16C13.1537 9.11318 13.2662 9.0893 13.38 9.09C13.8533 9.05 14.3933 9.03 15 9.03H16.27V11.37V16.45C16.2964 17.1879 16.2663 17.9267 16.18 18.66C16.1652 18.7326 16.1341 18.801 16.0891 18.8599C16.0441 18.9188 15.9862 18.9667 15.92 19C15.5251 19.1227 15.1135 19.1834 14.7 19.18Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Post Content', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postcontent',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 2.42999V23C7.00003 23.2652 6.89467 23.5196 6.70714 23.7071C6.5196 23.8946 6.26525 24 6.00003 24H1.00003V24.82C0.986684 25.3536 1.08029 25.8846 1.27532 26.3815C1.47036 26.8784 1.76288 27.3312 2.13565 27.7133C2.50842 28.0954 2.9539 28.399 3.44584 28.6062C3.93778 28.8135 4.46622 28.9202 5.00003 28.92H24C24.5338 28.9202 25.0623 28.8135 25.5542 28.6062C26.0462 28.399 26.4916 28.0954 26.8644 27.7133C27.2372 27.3312 27.5297 26.8784 27.7247 26.3815C27.9198 25.8846 28.0134 25.3536 28 24.82V2.42999C28 2.03217 27.842 1.65064 27.5607 1.36933C27.2794 1.08803 26.8979 0.929993 26.5 0.929993H8.50003C8.10221 0.929993 7.72067 1.08803 7.43937 1.36933C7.15806 1.65064 7.00003 2.03217 7.00003 2.42999ZM5.00003 27.29C4.50164 27.2869 4.01691 27.1267 3.61477 26.8323C3.21262 26.5379 2.91354 26.1242 2.76003 25.65H20C20.2172 26.2595 20.5576 26.8178 21 27.29H5.00003ZM26 23.93C26 24.4604 25.7893 24.9691 25.4142 25.3442C25.0392 25.7193 24.5305 25.93 24 25.93C23.4696 25.93 22.9609 25.7193 22.5858 25.3442C22.2107 24.9691 22 24.4604 22 23.93H10C9.73481 23.93 9.48046 23.8246 9.29292 23.6371C9.10539 23.4496 9.00003 23.1952 9.00003 22.93V2.92999H26V23.53V23.93ZM22.33 16.93H12.67C12.582 16.93 12.4949 16.9473 12.4136 16.981C12.3323 17.0147 12.2585 17.064 12.1962 17.1262C12.134 17.1884 12.0847 17.2623 12.051 17.3436C12.0173 17.4249 12 17.512 12 17.6C12 17.7777 12.0706 17.9481 12.1962 18.0738C12.3219 18.1994 12.4923 18.27 12.67 18.27H22.33C22.418 18.27 22.5051 18.2527 22.5864 18.219C22.6677 18.1853 22.7415 18.136 22.8038 18.0738C22.866 18.0115 22.9153 17.9377 22.949 17.8564C22.9827 17.7751 23 17.688 23 17.6C23 17.512 22.9827 17.4249 22.949 17.3436C22.9153 17.2623 22.866 17.1884 22.8038 17.1262C22.7415 17.064 22.6677 17.0147 22.5864 16.981C22.5051 16.9473 22.418 16.93 22.33 16.93ZM22.33 19.6H12.67C12.4923 19.6 12.3219 19.6706 12.1962 19.7962C12.0706 19.9219 12 20.0923 12 20.27C12.0026 20.446 12.0744 20.6138 12.1997 20.7373C12.3251 20.8608 12.494 20.93 12.67 20.93H22.33C22.506 20.93 22.6749 20.8608 22.8003 20.7373C22.9256 20.6138 22.9974 20.446 23 20.27C23 20.182 22.9827 20.0949 22.949 20.0136C22.9153 19.9323 22.866 19.8585 22.8038 19.7962C22.7415 19.734 22.6677 19.6847 22.5864 19.651C22.5051 19.6173 22.418 19.6 22.33 19.6ZM13 14.05V14.62L14.59 14.58H16.19V14.01C15.8516 14.0273 15.5128 13.9833 15.19 13.88C15.1527 13.8551 15.1228 13.8205 15.1035 13.7801C15.0841 13.7396 15.076 13.6947 15.08 13.65C15.1452 13.2411 15.2593 12.8416 15.42 12.46L15.73 11.64H19.34L19.93 13.15C20.0171 13.3289 20.0713 13.5219 20.09 13.72C20.0922 13.7589 20.0829 13.7977 20.0634 13.8314C20.0439 13.8652 20.0149 13.8925 19.98 13.91C19.6715 13.9793 19.3561 14.0128 19.04 14.01V14.58H21.46H23.37V14.01H23.07C22.9371 14.026 22.8029 14.026 22.67 14.01C22.6183 13.9934 22.5706 13.9661 22.53 13.93C22.4774 13.8773 22.4335 13.8165 22.4 13.75L22.12 13.12L21.69 12.12L18.61 4.92999H17.61L17 6.49999L15.16 10.93C15.1291 11.0064 15.0991 11.0806 15.07 11.1525C14.7791 11.8713 14.5791 12.3655 14.47 12.62C14.21 13.23 14.06 13.58 14 13.67C13.9593 13.7462 13.9089 13.8168 13.85 13.88C13.7966 13.9217 13.7354 13.9522 13.67 13.97C13.4485 14.0102 13.2247 14.0369 13 14.05ZM17.47 10.89H16L17.46 7.33999L18.89 10.89H17.47Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Post Excerpt', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postexcerpt',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 23V2.42999C7.00003 2.03217 7.15806 1.65064 7.43937 1.36933C7.72067 1.08803 8.10221 0.929993 8.50003 0.929993H26.5C26.8979 0.929993 27.2794 1.08803 27.5607 1.36933C27.842 1.65064 28 2.03217 28 2.42999V24.82C28.0134 25.3536 27.9198 25.8846 27.7247 26.3815C27.5297 26.8784 27.2372 27.3312 26.8644 27.7133C26.4916 28.0954 26.0462 28.399 25.5542 28.6062C25.0623 28.8135 24.5338 28.9202 24 28.92H5.00003C4.46622 28.9202 3.93778 28.8135 3.44584 28.6062C2.9539 28.399 2.50842 28.0954 2.13565 27.7133C1.76288 27.3312 1.47036 26.8784 1.27532 26.3815C1.08029 25.8846 0.986684 25.3536 1.00003 24.82V24H6.00003C6.26525 24 6.5196 23.8946 6.70714 23.7071C6.89467 23.5196 7.00003 23.2652 7.00003 23ZM3.61477 26.8323C4.01691 27.1267 4.50164 27.2869 5.00003 27.29H21C20.5576 26.8178 20.2172 26.2595 20 25.65H2.76003C2.91354 26.1242 3.21262 26.5379 3.61477 26.8323ZM25.4142 25.3442C25.7893 24.9691 26 24.4604 26 23.93V23.53V2.92999H9.00003V22.93C9.00003 23.1952 9.10539 23.4496 9.29292 23.6371C9.48046 23.8246 9.73481 23.93 10 23.93H22C22 24.4604 22.2107 24.9691 22.5858 25.3442C22.9609 25.7193 23.4696 25.93 24 25.93C24.5305 25.93 25.0392 25.7193 25.4142 25.3442ZM11.67 5.92999H21.33C21.418 5.92999 21.5051 5.94732 21.5864 5.98099C21.6677 6.01466 21.7415 6.06402 21.8038 6.12623C21.866 6.18845 21.9153 6.26231 21.949 6.3436C21.9827 6.42488 22 6.51201 22 6.59999C22 6.68798 21.9827 6.7751 21.949 6.85639C21.9153 6.93768 21.866 7.01154 21.8038 7.07375C21.7415 7.13597 21.6677 7.18532 21.5864 7.21899C21.5051 7.25266 21.418 7.26999 21.33 7.26999H11.67C11.4923 7.26999 11.3219 7.1994 11.1962 7.07375C11.0706 6.9481 11 6.77769 11 6.59999C11 6.4223 11.0706 6.25188 11.1962 6.12623C11.3219 6.00058 11.4923 5.92999 11.67 5.92999ZM11.67 8.92999H23.33C23.5077 8.92999 23.6781 9.00058 23.8038 9.12623C23.9294 9.25188 24 9.4223 24 9.59999C24 9.77769 23.9294 9.9481 23.8038 10.0738C23.6781 10.1994 23.5077 10.27 23.33 10.27H11.67C11.4923 10.27 11.3219 10.1994 11.1962 10.0738C11.0706 9.9481 11 9.77769 11 9.59999C11 9.4223 11.0706 9.25188 11.1962 9.12623C11.3219 9.00058 11.4923 8.92999 11.67 8.92999ZM21.33 11.93H11.67C11.4923 11.93 11.3219 12.0006 11.1962 12.1262C11.0706 12.2519 11 12.4223 11 12.6C11 12.7777 11.0706 12.9481 11.1962 13.0738C11.3219 13.1994 11.4923 13.27 11.67 13.27H21.33C21.418 13.27 21.5051 13.2527 21.5864 13.219C21.6677 13.1853 21.7415 13.136 21.8038 13.0738C21.866 13.0115 21.9153 12.9377 21.949 12.8564C21.9827 12.7751 22 12.688 22 12.6C22 12.512 21.9827 12.4249 21.949 12.3436C21.9153 12.2623 21.866 12.1884 21.8038 12.1262C21.7415 12.064 21.6677 12.0147 21.5864 11.981C21.5051 11.9473 21.418 11.93 21.33 11.93ZM11.67 14.93H23.33C23.5077 14.93 23.6781 15.0006 23.8038 15.1262C23.9294 15.2519 24 15.4223 24 15.6C24 15.7777 23.9294 15.9481 23.8038 16.0738C23.6781 16.1994 23.5077 16.27 23.33 16.27H11.67C11.4923 16.27 11.3219 16.1994 11.1962 16.0738C11.0706 15.9481 11 15.7777 11 15.6C11 15.4223 11.0706 15.2519 11.1962 15.1262C11.3219 15.0006 11.4923 14.93 11.67 14.93ZM12.5 20.93C13.3284 20.93 14 20.2584 14 19.43C14 18.6016 13.3284 17.93 12.5 17.93C11.6716 17.93 11 18.6016 11 19.43C11 20.2584 11.6716 20.93 12.5 20.93ZM19 19.43C19 20.2584 18.3284 20.93 17.5 20.93C16.6716 20.93 16 20.2584 16 19.43C16 18.6016 16.6716 17.93 17.5 17.93C18.3284 17.93 19 18.6016 19 19.43Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Featured Image', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postfeaturedimage',
			'id'     => 43,
			'icon'   => '
			<svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 23V2.42999C7.00003 2.03217 7.15806 1.65064 7.43937 1.36933C7.72067 1.08803 8.1022 0.929993 8.50003 0.929993H26.5C26.8979 0.929993 27.2794 1.08803 27.5607 1.36933C27.842 1.65064 28 2.03217 28 2.42999V24.82C28.0134 25.3536 27.9198 25.8846 27.7247 26.3815C27.5297 26.8784 27.2372 27.3312 26.8644 27.7133C26.4916 28.0954 26.0462 28.399 25.5542 28.6062C25.0623 28.8135 24.5338 28.9202 24 28.92H5.00003C4.46622 28.9202 3.93778 28.8135 3.44584 28.6062C2.9539 28.399 2.50842 28.0954 2.13565 27.7133C1.76288 27.3312 1.47036 26.8784 1.27532 26.3815C1.08029 25.8846 0.986684 25.3536 1.00003 24.82V24H6.00003C6.26525 24 6.5196 23.8946 6.70714 23.7071C6.89467 23.5196 7.00003 23.2652 7.00003 23ZM3.61477 26.8323C4.01691 27.1267 4.50164 27.2869 5.00003 27.29H21C20.5576 26.8178 20.2172 26.2595 20 25.65H2.76003C2.91354 26.1242 3.21262 26.5379 3.61477 26.8323ZM25.4142 25.3442C25.7893 24.9691 26 24.4604 26 23.93V23.53V2.92999H9.00003V22.93C9.00003 23.1952 9.10539 23.4496 9.29292 23.6371C9.48046 23.8246 9.73481 23.93 10 23.93H22C22 24.4604 22.2107 24.9691 22.5858 25.3442C22.9609 25.7193 23.4696 25.93 24 25.93C24.5305 25.93 25.0392 25.7193 25.4142 25.3442ZM23.33 18H11.67C11.4923 18 11.3219 18.0706 11.1962 18.1962C11.0706 18.3219 11 18.4923 11 18.67C11.0026 18.846 11.0744 19.0138 11.1997 19.1373C11.3251 19.2608 11.494 19.33 11.67 19.33H23.33C23.506 19.33 23.6749 19.2608 23.8003 19.1373C23.9256 19.0138 23.9974 18.846 24 18.67C24 18.4923 23.9294 18.3219 23.8038 18.1962C23.6781 18.0706 23.5077 18 23.33 18ZM11.67 20.67H23.33C23.506 20.67 23.6749 20.7392 23.8003 20.8627C23.9256 20.9862 23.9974 21.154 24 21.33C24 21.5077 23.9294 21.6781 23.8038 21.8038C23.6781 21.9294 23.5077 22 23.33 22H11.67C11.4923 22 11.3219 21.9294 11.1962 21.8038C11.0706 21.6781 11 21.5077 11 21.33C11.0026 21.154 11.0744 20.9862 11.1997 20.8627C11.3251 20.7392 11.494 20.67 11.67 20.67ZM11.62 5.16H23.27C23.4636 5.16 23.6493 5.23691 23.7862 5.37382C23.9231 5.51072 24 5.6964 24 5.89V15.46C24 15.6545 23.9234 15.8413 23.7868 15.9798C23.6502 16.1183 23.4645 16.1974 23.27 16.2H11.62C11.4255 16.1974 11.2398 16.1183 11.1032 15.9798C10.9666 15.8413 10.89 15.6545 10.89 15.46V5.89C10.89 5.6964 10.9669 5.51072 11.1038 5.37382C11.2407 5.23691 11.4264 5.16 11.62 5.16ZM22.54 6.63H12.35L12.34 13.28L15.9 8.48L18.9 11.48L22.54 7.8V6.63ZM16.07 10.72L13.07 14.72L22.54 14.75V9.9L18.9 13.58L16.07 10.72Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Author Box', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postauthorbox',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M27 1H3C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V27C1 27.5304 1.21071 28.0391 1.58579 28.4142C1.96086 28.7893 2.46957 29 3 29H27C27.5304 29 28.0391 28.7893 28.4142 28.4142C28.7893 28.0391 29 27.5304 29 27V3C29 2.46957 28.7893 1.96086 28.4142 1.58579C28.0391 1.21071 27.5304 1 27 1ZM26.81 26.82H3.19V3.18H26.81V26.82ZM15 17C11.71 17 5.84003 18.75 5.84003 22.25V24.2H24.16V22.21C24.16 18.71 18.29 17 15 17ZM7.59003 22L8.72003 21.08C10.5834 19.842 12.7631 19.1652 15 19.13C17.233 19.1623 19.4108 19.8282 21.28 21.05L22.42 21.97L7.59003 22ZM12.5999 6.56805C13.3104 6.09336 14.1456 5.84 15 5.84C16.1449 5.84264 17.2422 6.29863 18.0518 7.10821C18.8614 7.9178 19.3174 9.01507 19.32 10.16C19.32 11.0144 19.0666 11.8496 18.5919 12.5601C18.1173 13.2705 17.4426 13.8242 16.6532 14.1512C15.8638 14.4781 14.9952 14.5637 14.1572 14.397C13.3192 14.2303 12.5495 13.8189 11.9453 13.2147C11.3411 12.6105 10.9297 11.8408 10.763 11.0028C10.5963 10.1648 10.6819 9.29618 11.0088 8.5068C11.3358 7.71743 11.8895 7.04274 12.5999 6.56805ZM13.8166 11.931C14.1669 12.1651 14.5787 12.29 15 12.29C15.5649 12.29 16.1067 12.0656 16.5061 11.6661C16.9056 11.2667 17.13 10.7249 17.13 10.16C17.13 9.73872 17.0051 9.32691 16.771 8.97663C16.537 8.62635 16.2043 8.35335 15.8151 8.19213C15.4259 8.03092 14.9976 7.98874 14.5845 8.07092C14.1713 8.15311 13.7917 8.35597 13.4939 8.65386C13.196 8.95175 12.9931 9.33127 12.9109 9.74445C12.8287 10.1576 12.8709 10.5859 13.0321 10.9751C13.1933 11.3643 13.4664 11.697 13.8166 11.931Z" />
            </svg>
            ',
		),

		array(
			'name'   => __( 'Author Bio', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'authorbio',
			'id'     => 47,
			'icon'   => '			
				<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path fill-rule="evenodd" clip-rule="evenodd" d="M12.6419 2.715C13.3399 2.24872 14.1605 1.99989 15 2C16.1251 2.00138 17.2038 2.44899 17.9993 3.24463C18.7948 4.04028 19.2423 5.11898 19.2436 6.24412C19.2436 7.08355 18.9946 7.90413 18.5282 8.60208C18.0619 9.30004 17.399 9.84402 16.6235 10.1652C15.8479 10.4864 14.9945 10.5705 14.1712 10.4067C13.348 10.2428 12.5917 9.83858 11.9982 9.24498C11.4047 8.65137 11.0005 7.89509 10.8368 7.07178C10.6731 6.24846 10.7572 5.39509 11.0785 4.61959C11.3998 3.84409 11.9439 3.18129 12.6419 2.715ZM13.835 7.98762C14.1798 8.21803 14.5853 8.341 15 8.341C15.5559 8.34043 16.0889 8.11933 16.482 7.72622C16.8751 7.33311 17.0962 6.8001 17.0968 6.24416C17.0968 5.82944 16.9738 5.42404 16.7434 5.07921C16.513 4.73438 16.1855 4.46562 15.8024 4.30692C15.4192 4.14821 14.9976 4.10668 14.5909 4.18759C14.1841 4.2685 13.8105 4.4682 13.5173 4.76145C13.224 5.0547 13.0243 5.42833 12.9434 5.83508C12.8625 6.24183 12.904 6.66344 13.0627 7.04658C13.2214 7.42973 13.4902 7.75722 13.835 7.98762ZM6 18.0859C6 14.6479 11.7699 12.9223 15 12.9223C18.2301 12.9223 24 14.6479 24 18.0859V19.9998H6V18.0859ZM8.82545 16.9456L7.72312 17.8526H22.2928L21.1714 16.9436C19.335 15.7413 17.1947 15.0859 15 15.0537C12.8032 15.0809 10.6604 15.7374 8.82545 16.9456ZM7 22C6.44772 22 6 22.4477 6 23C6 23.5523 6.44772 24 7 24H23C23.5523 24 24 23.5523 24 23C24 22.4477 23.5523 22 23 22H7ZM10 27C10 26.4477 10.4477 26 11 26H19C19.5523 26 20 26.4477 20 27C20 27.5523 19.5523 28 19 28H11C10.4477 28 10 27.5523 10 27Z"/>
				</svg>
			',
		),

		array(
			'name'   => __( 'Post Comments', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postcomments',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path d="M1 3.63999C0.999998 3.29246 1.06861 2.94834 1.20191 2.62739C1.33521 2.30644 1.53056 2.01496 1.77677 1.76968C2.02298 1.5244 2.31519 1.33015 2.63664 1.19807C2.9581 1.06599 3.30247 0.998672 3.65 0.999988H26.36C27.0602 0.999988 27.7317 1.27813 28.2268 1.77323C28.7219 2.26832 29 2.93982 29 3.63999V20.64C29 21.3402 28.7219 22.0117 28.2268 22.5068C27.7317 23.0018 27.0602 23.28 26.36 23.28H6.36L1.85 27.86C1.78011 27.9285 1.69166 27.975 1.59559 27.9937C1.49952 28.0125 1.40008 28.0026 1.30958 27.9653C1.21908 27.928 1.1415 27.865 1.08647 27.7841C1.03144 27.7032 1.00137 27.6079 1 27.51V3.63999ZM3.43 22.83L5.38 20.89H26.57V3.43999H3.43V22.83ZM9.52 17.64V17C9.51949 16.4802 9.67185 15.9718 9.95812 15.5379C10.2444 15.1041 10.6519 14.764 11.13 14.56C12.3503 14.0258 13.6679 13.75 15 13.75C16.3321 13.75 17.6497 14.0258 18.87 14.56C19.3481 14.764 19.7556 15.1041 20.0419 15.5379C20.3282 15.9718 20.4805 16.4802 20.48 17V17.61L9.52 17.64ZM15 12C14.4779 12 13.9674 11.8452 13.5333 11.5551C13.0991 11.265 12.7608 10.8527 12.561 10.3703C12.3611 9.88788 12.3089 9.35706 12.4107 8.84495C12.5126 8.33284 12.764 7.86244 13.1332 7.49323C13.5024 7.12402 13.9729 6.87258 14.485 6.77072C14.9971 6.66885 15.5279 6.72113 16.0103 6.92095C16.4927 7.12076 16.905 7.45914 17.1951 7.89328C17.4852 8.32743 17.64 8.83785 17.64 9.35999C17.64 10.0602 17.3619 10.7317 16.8668 11.2268C16.3717 11.7218 15.7002 12 15 12Z" />
            </svg>
            ',
		),

		array(
			'name'   => __( 'Post Navigation', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postnavigation',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M24.2352 0H4.26372C4.09909 0.00140102 3.93636 0.0352148 3.7848 0.0995105C3.63325 0.163806 3.49584 0.257325 3.38043 0.374726C3.26502 0.492127 3.17387 0.631111 3.11217 0.783742C3.05047 0.936374 3.01945 1.09966 3.02086 1.26429V17.6464C3.01945 17.8111 3.05047 17.9743 3.11217 18.127C3.17387 18.2796 3.26502 18.4186 3.38043 18.536C3.49584 18.6534 3.63325 18.7469 3.7848 18.8112C3.93636 18.8755 4.09909 18.9093 4.26372 18.9107H24.2352C24.3998 18.9093 24.5625 18.8755 24.7141 18.8112C24.8656 18.7469 25.003 18.6534 25.1184 18.536C25.2339 18.4186 25.325 18.2796 25.3867 18.127C25.4484 17.9743 25.4794 17.8111 25.478 17.6464V1.26429C25.4794 1.09966 25.4484 0.936374 25.3867 0.783742C25.325 0.631111 25.2339 0.492127 25.1184 0.374726C25.003 0.257325 24.8656 0.163806 24.7141 0.0995105C24.5625 0.0352148 24.3998 0.00140102 24.2352 0ZM5.51729 2.51786H22.9816V4.52143L16.7459 10.8321L11.6137 5.64643L5.51729 13.8643V2.51786ZM6.74943 16.3929L11.8923 9.48215L16.7459 14.3893L22.9816 8.07858V16.3929H6.74943ZM11.903 25.2107H5.47442L7.91728 22.7464L6.95299 21.7821L3.23514 25.4786C3.17874 25.5326 3.13386 25.5974 3.1032 25.6692C3.07255 25.7411 3.05674 25.8183 3.05674 25.8964C3.05674 25.9745 3.07255 26.0518 3.1032 26.1236C3.13386 26.1954 3.17874 26.2603 3.23514 26.3143L6.92085 30L7.88514 29.0357L5.44228 26.5714H11.8709L11.903 25.2107ZM23.0565 26.5714H16.6279L16.703 25.2107H23.1315L20.678 22.7464L21.6422 21.7821L25.3387 25.4786C25.4473 25.5905 25.5081 25.7404 25.5081 25.8964C25.5081 26.0524 25.4473 26.2023 25.3387 26.3143L21.6422 30L20.678 29.0357L23.0565 26.5714Z" />
            </svg>
            
            ',
		),

		array(
			'name'   => __( 'Post Info', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'postinfo',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.50003 23.08V2.49998C7.50003 2.10216 7.65806 1.72063 7.93937 1.43932C8.22067 1.15802 8.60221 0.999984 9.00003 0.999984H27C27.2004 0.99464 27.3997 1.02949 27.5863 1.10248C27.773 1.17546 27.9431 1.2851 28.0866 1.42493C28.2302 1.56475 28.3443 1.73192 28.4221 1.91657C28.5 2.10121 28.5401 2.29959 28.54 2.49998V24.9C28.5534 25.4336 28.4598 25.9645 28.2647 26.4614C28.0697 26.9584 27.7772 27.4112 27.4044 27.7933C27.0316 28.1754 26.5862 28.479 26.0942 28.6862C25.6023 28.8935 25.0738 29.0002 24.54 29H5.50003C4.96622 29.0002 4.43778 28.8935 3.94584 28.6862C3.4539 28.479 3.00842 28.1754 2.63565 27.7933C2.26288 27.4112 1.97036 26.9584 1.77532 26.4614C1.58029 25.9645 1.48668 25.4336 1.50003 24.9V24.08H6.50003C6.76525 24.08 7.0196 23.9746 7.20714 23.7871C7.39467 23.5996 7.50003 23.3452 7.50003 23.08ZM4.11614 26.9004C4.51799 27.1946 5.00201 27.3554 5.50003 27.36H21.5C21.0596 26.8863 20.7195 26.3284 20.5 25.72H3.26003C3.41486 26.1933 3.71429 26.6062 4.11614 26.9004ZM25.9142 25.4142C26.2893 25.0391 26.5 24.5304 26.5 24V23.59V2.99998H9.50003V23C9.50003 23.2652 9.60539 23.5196 9.79292 23.7071C9.98046 23.8946 10.2348 24 10.5 24H22.5C22.5 24.5304 22.7107 25.0391 23.0858 25.4142C23.4609 25.7893 23.9696 26 24.5 26C25.0305 26 25.5392 25.7893 25.9142 25.4142ZM14 10.5C14 11.3284 13.3284 12 12.5 12C11.6716 12 11 11.3284 11 10.5C11 9.67156 11.6716 8.99998 12.5 8.99998C13.3284 8.99998 14 9.67156 14 10.5ZM18 12C18.8284 12 19.5 11.3284 19.5 10.5C19.5 9.67156 18.8284 8.99998 18 8.99998C17.1716 8.99998 16.5 9.67156 16.5 10.5C16.5 11.3284 17.1716 12 18 12ZM25 10.5C25 11.3284 24.3284 12 23.5 12C22.6716 12 22 11.3284 22 10.5C22 9.67156 22.6716 8.99998 23.5 8.99998C24.3284 8.99998 25 9.67156 25 10.5ZM11.67 5.99998H22.33C22.418 5.99998 22.5051 6.01731 22.5864 6.05099C22.6677 6.08466 22.7415 6.13401 22.8038 6.19622C22.866 6.25844 22.9153 6.3323 22.949 6.41359C22.9827 6.49487 23 6.582 23 6.66998C22.9974 6.84594 22.9256 7.0138 22.8003 7.13729C22.6749 7.26079 22.506 7.33 22.33 7.32998H11.67C11.494 7.33 11.3251 7.26079 11.1997 7.13729C11.0744 7.0138 11.0026 6.84594 11 6.66998C11 6.49229 11.0706 6.32187 11.1962 6.19622C11.3219 6.07057 11.4923 5.99998 11.67 5.99998ZM24.8038 14.1962C24.6781 14.0706 24.5077 14 24.33 14H11.67C11.4923 14 11.3219 14.0706 11.1962 14.1962C11.0706 14.3219 11 14.4923 11 14.67C11.0026 14.8459 11.0744 15.0138 11.1997 15.1373C11.3251 15.2608 11.494 15.33 11.67 15.33H24.33C24.506 15.33 24.6749 15.2608 24.8003 15.1373C24.9256 15.0138 24.9974 14.8459 25 14.67C25 14.4923 24.9294 14.3219 24.8038 14.1962ZM11.67 17H22.33C22.418 17 22.5051 17.0173 22.5864 17.051C22.6677 17.0847 22.7415 17.134 22.8038 17.1962C22.866 17.2584 22.9153 17.3323 22.949 17.4136C22.9827 17.4949 23 17.582 23 17.67C22.9974 17.8459 22.9256 18.0138 22.8003 18.1373C22.6749 18.2608 22.506 18.33 22.33 18.33H11.67C11.494 18.33 11.3251 18.2608 11.1997 18.1373C11.0744 18.0138 11.0026 17.8459 11 17.67C11 17.4923 11.0706 17.3219 11.1962 17.1962C11.3219 17.0706 11.4923 17 11.67 17ZM18.33 20H11.67C11.4923 20 11.3219 20.0706 11.1962 20.1962C11.0706 20.3219 11 20.4923 11 20.67C11.0026 20.8459 11.0744 21.0138 11.1997 21.1373C11.3251 21.2608 11.494 21.33 11.67 21.33H18.33C18.506 21.33 18.6749 21.2608 18.8003 21.1373C18.9256 21.0138 18.9974 20.8459 19 20.67C19 20.582 18.9827 20.4949 18.949 20.4136C18.9153 20.3323 18.866 20.2584 18.8038 20.1962C18.7415 20.134 18.6677 20.0847 18.5864 20.051C18.5051 20.0173 18.418 20 18.33 20Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Archive Title', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'archivetitle',
			'id'     => 43,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 2.5V27.5C1 27.8978 1.15804 28.2794 1.43934 28.5607C1.72064 28.842 2.10218 29 2.5 29H27.5C27.8978 29 28.2794 28.842 28.5607 28.5607C28.842 28.2794 29 27.8978 29 27.5V2.5C29 2.10218 28.842 1.72064 28.5607 1.43934C28.2794 1.15804 27.8978 1 27.5 1H2.5C2.10218 1 1.72064 1.15804 1.43934 1.43934C1.15804 1.72064 1 2.10218 1 2.5ZM27 27H3V8H27V27ZM27 6V3H3V6H27ZM12.89 19V18.47C13.2 18.4844 13.5104 18.4507 13.81 18.37C13.8594 18.3428 13.9027 18.3056 13.9371 18.2609C13.9716 18.2161 13.9964 18.1648 14.01 18.11C14.0733 17.5591 14.0967 17.0043 14.08 16.45V12.59V10.83H13.16H11.93C11.79 10.83 11.71 10.83 11.68 10.89C11.65 10.95 11.63 10.99 11.61 11.11C11.549 11.44 11.5156 11.7745 11.51 12.11H11V10.63C11 10.52 11 10.23 10.94 10.01L11 9.95L11.64 10H18.56L19.16 9.95L19.22 10.01C19.22 10.1716 19.1985 10.3189 19.1791 10.4518L19.1791 10.4518C19.1661 10.541 19.154 10.6237 19.15 10.7V12.11H18.61C18.61 11.78 18.61 11.46 18.53 11.11C18.52 11.0145 18.4893 10.9224 18.44 10.84H18.24C18.0748 10.84 17.8969 10.8295 17.7044 10.8181L17.7044 10.8181C17.4775 10.8046 17.2305 10.79 16.96 10.79H16.04V12.55V16.41C15.9854 16.9783 15.972 17.5498 16 18.12C16.0149 18.173 16.0402 18.2224 16.0746 18.2653C16.109 18.3083 16.1516 18.3439 16.2 18.37C16.4997 18.4507 16.81 18.4844 17.12 18.47V19C16.6661 19 16.2481 18.9821 15.8693 18.9658H15.8693H15.8693H15.8692C15.5553 18.9523 15.2683 18.94 15.01 18.94C14.7491 18.94 14.4505 18.9526 14.1266 18.9662C13.7429 18.9824 13.3238 19 12.89 19ZM16.67 21H24.33C24.418 21 24.5051 21.0173 24.5864 21.051C24.6677 21.0847 24.7415 21.134 24.8038 21.1962C24.866 21.2585 24.9153 21.3323 24.949 21.4136C24.9827 21.4949 25 21.582 25 21.67C24.9974 21.846 24.9256 22.0138 24.8003 22.1373C24.6749 22.2608 24.506 22.33 24.33 22.33H16.67C16.494 22.33 16.3251 22.2608 16.1997 22.1373C16.0744 22.0138 16.0026 21.846 16 21.67C16 21.582 16.0173 21.4949 16.051 21.4136C16.0847 21.3323 16.134 21.2585 16.1962 21.1962C16.2585 21.134 16.3323 21.0847 16.4136 21.051C16.4949 21.0173 16.582 21 16.67 21ZM24.33 23.67H16.67C16.494 23.67 16.3251 23.7392 16.1997 23.8627C16.0744 23.9862 16.0026 24.154 16 24.33C16 24.418 16.0173 24.5051 16.051 24.5864C16.0847 24.6677 16.134 24.7415 16.1962 24.8038C16.2585 24.866 16.3323 24.9153 16.4136 24.949C16.4949 24.9827 16.582 25 16.67 25H24.33C24.5077 25 24.6781 24.9294 24.8038 24.8038C24.9294 24.6781 25 24.5077 25 24.33C24.9974 24.154 24.9256 23.9862 24.8003 23.8627C24.6749 23.7392 24.506 23.67 24.33 23.67ZM5.67 21H13.33C13.418 21 13.5051 21.0173 13.5864 21.051C13.6677 21.0847 13.7415 21.134 13.8038 21.1962C13.866 21.2585 13.9153 21.3323 13.949 21.4136C13.9827 21.4949 14 21.582 14 21.67C13.9974 21.846 13.9256 22.0138 13.8003 22.1373C13.6749 22.2608 13.506 22.33 13.33 22.33H5.67C5.49402 22.33 5.32511 22.2608 5.19974 22.1373C5.07437 22.0138 5.00263 21.846 5 21.67C5 21.582 5.01733 21.4949 5.051 21.4136C5.08467 21.3323 5.13402 21.2585 5.19624 21.1962C5.25845 21.134 5.33231 21.0847 5.4136 21.051C5.49489 21.0173 5.58201 21 5.67 21ZM13.33 23.67H5.67C5.49402 23.67 5.32511 23.7392 5.19974 23.8627C5.07437 23.9862 5.00263 24.154 5 24.33C5 24.5077 5.07059 24.6781 5.19624 24.8038C5.32189 24.9294 5.4923 25 5.67 25H13.33C13.5077 25 13.6781 24.9294 13.8038 24.8038C13.9294 24.6781 14 24.5077 14 24.33C13.9974 24.154 13.9256 23.9862 13.8003 23.8627C13.6749 23.7392 13.506 23.67 13.33 23.67Z" />
</svg>
',
		),

		// array(
		// 'name'   => __('Template Tags', 'coming-soon'),
		// 'is_pro' => true,
		// 'cat'    => 'templatetag',
		// 'type'   => 'templatetag',
		// 'id'     => 43,
		// 'icon'   => '
		// <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="sp-w-6 sp-fill-current"><path d="M256 8C119.3 8 8 119.2 8 256c0 136.7 111.3 248 248 248s248-111.3 248-248C504 119.2 392.7 8 256 8zM33 256c0-32.3 6.9-63 19.3-90.7l106.4 291.4C84.3 420.5 33 344.2 33 256zm223 223c-21.9 0-43-3.2-63-9.1l66.9-194.4 68.5 187.8c.5 1.1 1 2.1 1.6 3.1-23.1 8.1-48 12.6-74 12.6zm30.7-327.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-21.9 0-58.7-2.8-58.7-2.8-12-.7-13.4 17.7-1.4 18.4 0 0 11.4 1.4 23.4 2.1l34.7 95.2L200.6 393l-81.2-241.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-4.2 0-9.1-.1-14.4-.3C109.6 73 178.1 33 256 33c58 0 110.9 22.2 150.6 58.5-1-.1-1.9-.2-2.9-.2-21.9 0-37.4 19.1-37.4 39.6 0 18.4 10.6 33.9 21.9 52.3 8.5 14.8 18.4 33.9 18.4 61.5 0 19.1-7.3 41.2-17 72.1l-22.2 74.3-80.7-239.6zm81.4 297.2l68.1-196.9c12.7-31.8 17-57.2 17-79.9 0-8.2-.5-15.8-1.5-22.9 17.4 31.8 27.3 68.2 27.3 107 0 82.3-44.6 154.1-110.9 192.7z"/></svg>',
		// ),

		array(
			'name'   => __( 'Site Logo', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'sitelogo',
			'id'     => 81,
			'icon'   => '
            <svg  viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.33 1.75999H26.67C27.106 1.75868 27.538 1.84325 27.9413 2.00888C28.3446 2.17452 28.7113 2.41796 29.0205 2.72532C29.3297 3.03269 29.5754 3.39794 29.7435 3.80024C29.9115 4.20254 29.9987 4.63401 30 5.06999V24.93C29.9947 25.8097 29.6415 26.6516 29.0176 27.2717C28.3937 27.8919 27.5497 28.24 26.67 28.24H3.33C2.89401 28.2413 2.46203 28.1567 2.05873 27.9911C1.65542 27.8255 1.28869 27.582 0.979472 27.2747C0.670253 26.9673 0.424601 26.602 0.256543 26.1997C0.0884846 25.7974 0.00131124 25.366 0 24.93V5.06999C0.00131124 4.63401 0.0884846 4.20254 0.256543 3.80024C0.424601 3.39794 0.670253 3.03269 0.979472 2.72532C1.28869 2.41796 1.65542 2.17452 2.05873 2.00888C2.46203 1.84325 2.89401 1.75868 3.33 1.75999ZM2.94 25.29H27.06V8.37999H2.94V25.29ZM5.88 4.70999H3.53C3.37524 4.70996 3.22667 4.77075 3.11631 4.87924C3.00594 4.98774 2.94263 5.13525 2.94 5.28999C2.94 5.44646 3.00216 5.59653 3.11281 5.70718C3.22346 5.81783 3.37352 5.87999 3.53 5.87999H5.88C5.95786 5.88134 6.03518 5.867 6.10738 5.83783C6.17957 5.80866 6.24515 5.76525 6.30021 5.7102C6.35527 5.65514 6.39868 5.58956 6.42785 5.51736C6.45702 5.44517 6.47136 5.36784 6.47 5.28999C6.47001 5.21297 6.45469 5.13672 6.42492 5.0657C6.39514 4.99467 6.35153 4.93028 6.2966 4.87629C6.24168 4.8223 6.17656 4.77979 6.10503 4.75123C6.0335 4.72268 5.95701 4.70866 5.88 4.70999ZM18.23 4.70999H15.88C15.7252 4.70996 15.5767 4.77075 15.4663 4.87924C15.3559 4.98774 15.2926 5.13525 15.29 5.28999C15.29 5.44646 15.3522 5.59653 15.4628 5.70718C15.5735 5.81783 15.7235 5.87999 15.88 5.87999H18.23C18.3079 5.88134 18.3852 5.867 18.4574 5.83783C18.5296 5.80866 18.5952 5.76525 18.6502 5.7102C18.7053 5.65514 18.7487 5.58956 18.7778 5.51736C18.807 5.44517 18.8214 5.36784 18.82 5.28999C18.82 5.21297 18.8047 5.13672 18.7749 5.0657C18.7451 4.99467 18.7015 4.93028 18.6466 4.87629C18.5917 4.8223 18.5266 4.77979 18.455 4.75123C18.3835 4.72268 18.307 4.70866 18.23 4.70999ZM20 4.70999H22.35C22.427 4.70866 22.5035 4.72268 22.575 4.75123C22.6466 4.77979 22.7117 4.8223 22.7666 4.87629C22.8215 4.93028 22.8651 4.99467 22.8949 5.0657C22.9247 5.13672 22.94 5.21297 22.94 5.28999C22.9414 5.36784 22.927 5.44517 22.8979 5.51736C22.8687 5.58956 22.8253 5.65514 22.7702 5.7102C22.7152 5.76525 22.6496 5.80866 22.5774 5.83783C22.5052 5.867 22.4279 5.88134 22.35 5.87999H20C19.8435 5.87999 19.6935 5.81783 19.5828 5.70718C19.4722 5.59653 19.41 5.44646 19.41 5.28999C19.4126 5.13525 19.4759 4.98774 19.5863 4.87924C19.6967 4.77075 19.8452 4.70996 20 4.70999ZM26.47 4.70999H24.12C24.043 4.70866 23.9665 4.72268 23.895 4.75123C23.8234 4.77979 23.7583 4.8223 23.7034 4.87629C23.6485 4.93028 23.6049 4.99467 23.5751 5.0657C23.5453 5.13672 23.53 5.21297 23.53 5.28999C23.5286 5.36784 23.543 5.44517 23.5722 5.51736C23.6013 5.58956 23.6447 5.65514 23.6998 5.7102C23.7549 5.76525 23.8204 5.80866 23.8926 5.83783C23.9648 5.867 24.0422 5.88134 24.12 5.87999H26.47C26.6265 5.87999 26.7765 5.81783 26.8872 5.70718C26.9978 5.59653 27.06 5.44646 27.06 5.28999C27.0574 5.13525 26.9941 4.98774 26.8837 4.87924C26.7733 4.77075 26.6248 4.70996 26.47 4.70999ZM24.33 12.67H17.67C17.494 12.67 17.3251 12.7392 17.1997 12.8627C17.0744 12.9862 17.0026 13.1541 17 13.33C17 13.418 17.0173 13.5051 17.051 13.5864C17.0847 13.6677 17.134 13.7416 17.1962 13.8038C17.2585 13.866 17.3323 13.9153 17.4136 13.949C17.4949 13.9827 17.582 14 17.67 14H24.33C24.5077 14 24.6781 13.9294 24.8038 13.8038C24.9294 13.6781 25 13.5077 25 13.33C24.9974 13.1541 24.9256 12.9862 24.8003 12.8627C24.6749 12.7392 24.506 12.67 24.33 12.67ZM17.67 19H24.33C24.5077 19 24.6781 19.0706 24.8038 19.1962C24.9294 19.3219 25 19.4923 25 19.67C24.9974 19.846 24.9256 20.0138 24.8003 20.1373C24.6749 20.2608 24.506 20.33 24.33 20.33H17.67C17.494 20.33 17.3251 20.2608 17.1997 20.1373C17.0744 20.0138 17.0026 19.846 17 19.67C17 19.582 17.0173 19.4949 17.051 19.4136C17.0847 19.3323 17.134 19.2585 17.1962 19.1962C17.2585 19.134 17.3323 19.0847 17.4136 19.051C17.4949 19.0173 17.582 19 17.67 19ZM24.33 15.67H17.67C17.494 15.67 17.3251 15.7392 17.1997 15.8627C17.0744 15.9862 17.0026 16.1541 17 16.33C17 16.418 17.0173 16.5051 17.051 16.5864C17.0847 16.6677 17.134 16.7416 17.1962 16.8038C17.2585 16.866 17.3323 16.9153 17.4136 16.949C17.4949 16.9827 17.582 17 17.67 17H24.33C24.5077 17 24.6781 16.9294 24.8038 16.8038C24.9294 16.6781 25 16.5077 25 16.33C24.9974 16.1541 24.9256 15.9862 24.8003 15.8627C24.6749 15.7392 24.506 15.67 24.33 15.67ZM5 22V21.39C4.99966 20.8677 5.1531 20.3568 5.44119 19.9211C5.72928 19.4854 6.13926 19.1442 6.62 18.94C7.84381 18.4059 9.16471 18.1302 10.5 18.1302C11.8353 18.1302 13.1562 18.4059 14.38 18.94C14.8592 19.1463 15.2677 19.4881 15.5555 19.9233C15.8433 20.3585 15.9978 20.8682 16 21.39V22H5ZM9.02772 15.8534C9.46351 16.1446 9.97586 16.3 10.5 16.3C11.2028 16.3 11.8768 16.0208 12.3738 15.5238C12.8708 15.0269 13.15 14.3528 13.15 13.65C13.15 13.1259 12.9946 12.6135 12.7034 12.1777C12.4122 11.742 11.9983 11.4023 11.5141 11.2017C11.0299 11.0011 10.497 10.9487 9.98299 11.0509C9.46894 11.1532 8.99675 11.4056 8.62614 11.7762C8.25554 12.1468 8.00315 12.619 7.9009 13.133C7.79865 13.6471 7.85112 14.1799 8.0517 14.6641C8.25227 15.1483 8.59192 15.5622 9.02772 15.8534Z" />
</svg>
',
		),

		array(
			'name'   => __( 'Template Parts', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'templatetag',
			'type'   => 'seedprodtemplateparts',
			'id'     => 47,
			'icon'   => '
			<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 2.5V27.5C1 27.8978 1.15804 28.2794 1.43934 28.5607C1.72064 28.842 2.10218 29 2.5 29H27.5C27.8978 29 28.2794 28.842 28.5607 28.5607C28.842 28.2794 29 27.8978 29 27.5V2.5C29 2.10218 28.842 1.72064 28.5607 1.43934C28.2794 1.15804 27.8978 1 27.5 1H2.5C2.10218 1 1.72064 1.15804 1.43934 1.43934C1.15804 1.72064 1 2.10218 1 2.5ZM27 27H3V3H27V27ZM23.5 6.5V9.5H6.5V6.5H23.5ZM24 5H6C5.73478 5 5.48043 5.10536 5.29289 5.29289C5.10536 5.48043 5 5.73478 5 6V10C5 10.2652 5.10536 10.5196 5.29289 10.7071C5.48043 10.8946 5.73478 11 6 11H24C24.2652 11 24.5196 10.8946 24.7071 10.7071C24.8946 10.5196 25 10.2652 25 10V6C25 5.73478 24.8946 5.48043 24.7071 5.29289C24.5196 5.10536 24.2652 5 24 5ZM14.86 14.66C9.89997 14 9.65997 14 9.65997 14C9.65997 14 9.83997 15.66 10.27 19.23C10.4311 20.7189 11.1157 22.1021 12.2018 23.1332C13.2878 24.1643 14.7048 24.7763 16.2 24.86L17.25 24.91C17.25 24.91 17.35 21.09 16.37 19.29C15.39 17.49 12.81 17.18 12.81 17.18C13.568 17.7485 14.2414 18.4219 14.81 19.18C15.7 20.37 15.6 23.09 15.6 23.09C14.6622 22.8322 13.816 22.3156 13.1582 21.5994C12.5003 20.8832 12.0573 19.9962 11.88 19.04C11.56 16.72 11.5 15.86 11.5 15.86C11.5 15.86 11.2 15.8 14.65 16.26C18.1 16.72 18.96 20.8 18.49 24.72C21.53 20.34 19.82 15.32 14.86 14.66Z" />
            </svg>
            ',
		),

		array(
			'name'   => __( 'Menu Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'menucart',
			'id'     => 51,
			'icon'   => '
			<svg viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg" class="sp-w-6 sp-fill-current">
			 <path fill-rule="evenodd" clip-rule="evenodd" d="M7.00003 23V2.42999C7.00003 2.03217 7.15806 1.65064 7.43937 1.36933C7.72067 1.08803 8.1022 0.929993 8.50003 0.929993H26.5C26.8979 0.929993 27.2794 1.08803 27.5607 1.36933C27.842 1.65064 28 2.03217 28 2.42999V24.82C28.0134 25.3536 27.9198 25.8846 27.7247 26.3815C27.5297 26.8784 27.2372 27.3312 26.8644 27.7133C26.4916 28.0954 26.0462 28.399 25.5542 28.6062C25.0623 28.8135 24.5338 28.9202 24 28.92H5.00003C4.46622 28.9202 3.93778 28.8135 3.44584 28.6062C2.9539 28.399 2.50842 28.0954 2.13565 27.7133C1.76288 27.3312 1.47036 26.8784 1.27532 26.3815C1.08029 25.8846 0.986684 25.3536 1.00003 24.82V24H6.00003C6.26525 24 6.5196 23.8946 6.70714 23.7071C6.89467 23.5196 7.00003 23.2652 7.00003 23ZM3.61477 26.8323C4.01691 27.1267 4.50164 27.2869 5.00003 27.29H21C20.5576 26.8178 20.2172 26.2595 20 25.65H2.76003C2.91354 26.1242 3.21262 26.5379 3.61477 26.8323ZM25.4142 25.3442C25.7893 24.9691 26 24.4604 26 23.93V23.53V2.92999H9.00003V22.93C9.00003 23.1952 9.10539 23.4496 9.29292 23.6371C9.48046 23.8246 9.73481 23.93 10 23.93H22C22 24.4604 22.2107 24.9691 22.5858 25.3442C22.9609 25.7193 23.4696 25.93 24 25.93C24.5305 25.93 25.0392 25.7193 25.4142 25.3442ZM23.33 18H11.67C11.4923 18 11.3219 18.0706 11.1962 18.1962C11.0706 18.3219 11 18.4923 11 18.67C11.0026 18.846 11.0744 19.0138 11.1997 19.1373C11.3251 19.2608 11.494 19.33 11.67 19.33H23.33C23.506 19.33 23.6749 19.2608 23.8003 19.1373C23.9256 19.0138 23.9974 18.846 24 18.67C24 18.4923 23.9294 18.3219 23.8038 18.1962C23.6781 18.0706 23.5077 18 23.33 18ZM11.67 20.67H23.33C23.506 20.67 23.6749 20.7392 23.8003 20.8627C23.9256 20.9862 23.9974 21.154 24 21.33C24 21.5077 23.9294 21.6781 23.8038 21.8038C23.6781 21.9294 23.5077 22 23.33 22H11.67C11.4923 22 11.3219 21.9294 11.1962 21.8038C11.0706 21.6781 11 21.5077 11 21.33C11.0026 21.154 11.0744 20.9862 11.1997 20.8627C11.3251 20.7392 11.494 20.67 11.67 20.67ZM11.62 5.16H23.27C23.4636 5.16 23.6493 5.23691 23.7862 5.37382C23.9231 5.51072 24 5.6964 24 5.89V15.46C24 15.6545 23.9234 15.8413 23.7868 15.9798C23.6502 16.1183 23.4645 16.1974 23.27 16.2H11.62C11.4255 16.1974 11.2398 16.1183 11.1032 15.9798C10.9666 15.8413 10.89 15.6545 10.89 15.46V5.89C10.89 5.6964 10.9669 5.51072 11.1038 5.37382C11.2407 5.23691 11.4264 5.16 11.62 5.16ZM22.54 6.63H12.35L12.34 13.28L15.9 8.48L18.9 11.48L22.54 7.8V6.63ZM16.07 10.72L13.07 14.72L22.54 14.75V9.9L18.9 13.58L16.07 10.72Z"/>
			 </svg>',
		),

		array(
			'name'   => __( 'Product Title', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'producttitle',
			'id'     => 52,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3.00002 27.5V8H3V6H3.00002V2.5C2.97512 2.1307 3.09644 1.76641 3.33779 1.48578C3.57913 1.20514 3.92115 1.03066 4.29002 1H25.71C26.0789 1.03066 26.4209 1.20514 26.6623 1.48578C26.9036 1.76641 27.0249 2.1307 27 2.5V27.5C27.0249 27.8693 26.9036 28.2336 26.6623 28.5142C26.4209 28.7949 26.0789 28.9693 25.71 29H4.29002C3.92115 28.9693 3.57913 28.7949 3.33779 28.5142C3.09644 28.2336 2.97512 27.8693 3.00002 27.5ZM4.71002 27V8H25.29V27H4.71002ZM25.29 3V6H4.71002V3H25.29ZM22.33 13H16.67C16.582 13 16.4949 13.0173 16.4136 13.051C16.3323 13.0847 16.2585 13.134 16.1962 13.1962C16.134 13.2585 16.0847 13.3323 16.051 13.4136C16.0173 13.4949 16 13.582 16 13.67C16.0026 13.846 16.0744 14.0138 16.1997 14.1373C16.3251 14.2608 16.494 14.33 16.67 14.33H22.33C22.506 14.33 22.6749 14.2608 22.8003 14.1373C22.9256 14.0138 22.9974 13.846 23 13.67C23 13.4923 22.9294 13.3219 22.8038 13.1962C22.6781 13.0706 22.5077 13 22.33 13ZM22.33 16.83H16.67C16.582 16.83 16.4949 16.8473 16.4136 16.881C16.3323 16.9147 16.2585 16.964 16.1962 17.0262C16.134 17.0884 16.0847 17.1623 16.051 17.2436C16.0173 17.3249 16 17.412 16 17.5C16 17.588 16.0173 17.6751 16.051 17.7564C16.0847 17.8377 16.134 17.9115 16.1962 17.9737C16.2585 18.036 16.3323 18.0853 16.4136 18.119C16.4949 18.1527 16.582 18.17 16.67 18.17H22.33C22.5077 18.17 22.6781 18.0994 22.8038 17.9737C22.9294 17.8481 23 17.6777 23 17.5C23 17.412 22.9827 17.3249 22.949 17.2436C22.9153 17.1623 22.866 17.0884 22.8038 17.0262C22.7415 16.964 22.6677 16.9147 22.5864 16.881C22.5051 16.8473 22.418 16.83 22.33 16.83ZM16.67 20.67H22.33C22.506 20.67 22.6749 20.7392 22.8003 20.8627C22.9256 20.9862 22.9974 21.1541 23 21.33C23 21.5077 22.9294 21.6781 22.8038 21.8038C22.6781 21.9294 22.5077 22 22.33 22H16.67C16.582 22 16.4949 21.9827 16.4136 21.949C16.3323 21.9153 16.2585 21.866 16.1962 21.8038C16.134 21.7416 16.0847 21.6677 16.051 21.5864C16.0173 21.5051 16 21.418 16 21.33C16.0026 21.1541 16.0744 20.9862 16.1997 20.8627C16.3251 20.7392 16.494 20.67 16.67 20.67ZM8.00004 21.47V22C8.45391 22 8.88984 21.9821 9.28494 21.9658C9.61236 21.9523 9.91174 21.94 10.17 21.94C10.4283 21.94 10.7154 21.9523 11.0293 21.9658C11.4082 21.9821 11.8262 22 12.28 22V21.48C11.9701 21.4937 11.6598 21.4599 11.36 21.38C11.3131 21.3533 11.2721 21.3174 11.2395 21.2744C11.2068 21.2315 11.1832 21.1823 11.17 21.13C11.1017 20.5761 11.0783 20.0176 11.1 19.46V15.6V13.84H12.03C12.2956 13.84 12.5435 13.8547 12.7707 13.8681C12.9626 13.8795 13.1397 13.89 13.3 13.89C13.3687 13.8708 13.4414 13.8708 13.51 13.89C13.5545 13.9738 13.5817 14.0656 13.59 14.16C13.6391 14.4912 13.6658 14.8252 13.67 15.16H14.21V13.75C14.2141 13.6721 14.2283 13.5875 14.2436 13.4962C14.2657 13.3647 14.29 13.2193 14.29 13.06L14.22 13L13.62 13.05H6.70004L6.06004 13L6.04004 13.01C6.10004 13.23 6.10004 13.52 6.10004 13.63V15.11H6.63004C6.63563 14.7745 6.66908 14.44 6.73004 14.11C6.73355 14.0302 6.76147 13.9534 6.81004 13.89C6.83004 13.83 6.91004 13.83 7.05004 13.83H8.28004H9.19004V15.59V19.45C9.21233 20.0043 9.18892 20.5595 9.12004 21.11C9.10639 21.1648 9.08159 21.2161 9.04717 21.2609C9.01276 21.3056 8.96948 21.3428 8.92004 21.37C8.62057 21.4517 8.31006 21.4854 8.00004 21.47Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Featured Image', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productfeaturedimage',
			'id'     => 53,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3 1H27C27.5304 1 28.0391 1.21071 28.4142 1.58579C28.7893 1.96086 29 2.46957 29 3V27C29 27.5304 28.7893 28.0391 28.4142 28.4142C28.0391 28.7893 27.5304 29 27 29H3C2.46957 29 1.96086 28.7893 1.58579 28.4142C1.21071 28.0391 1 27.5304 1 27V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1ZM3.19 26.82H26.81V3.18H3.19V26.82ZM9.99999 23C9.78586 22.9924 9.58274 22.9033 9.43216 22.7508C9.28159 22.5984 9.19496 22.3942 9.18999 22.18V14L7.44999 13.92C7.28233 13.9037 7.12446 13.8335 6.99999 13.72C6.85915 13.6077 6.7576 13.4537 6.70999 13.28L5.99999 10.33C5.95528 10.1417 5.97778 9.94367 6.06361 9.7702C6.14943 9.59672 6.29318 9.4587 6.46999 9.37999L12 7.06999C12.0923 7.03137 12.1902 7.00773 12.29 6.99999H17.71C17.826 6.98259 17.944 6.98259 18.06 6.99999L23.5 9.31999C23.6718 9.38874 23.8136 9.51634 23.9 9.67999C23.9874 9.84918 24.0189 10.0417 23.99 10.23L23.34 13.23C23.3056 13.4102 23.2104 13.5731 23.0702 13.6915C22.93 13.8098 22.7534 13.8764 22.57 13.88H20.82V22.12C20.8217 22.3359 20.7392 22.5439 20.59 22.7C20.5187 22.7892 20.4292 22.8621 20.3275 22.9139C20.2257 22.9656 20.114 22.995 20 23H9.99999ZM19.18 21.35V13.12C19.1787 13.0136 19.1983 12.908 19.2378 12.8092C19.2773 12.7105 19.3359 12.6204 19.4101 12.5443C19.4844 12.4681 19.573 12.4074 19.6708 12.3655C19.7685 12.3235 19.8736 12.3013 19.98 12.3H21.86L22.22 10.6L18.34 8.93999C18.1287 9.6622 17.689 10.2965 17.0868 10.7477C16.4847 11.1989 15.7525 11.4428 15 11.4428C14.2475 11.4428 13.5153 11.1989 12.9131 10.7477C12.311 10.2965 11.8713 9.6622 11.66 8.93999L7.73999 10.63L8.14999 12.27L10.08 12.36C10.2836 12.3723 10.4753 12.4601 10.6177 12.6061C10.7601 12.7522 10.8429 12.9461 10.85 13.15V21.35H19.18ZM17.34 8.14999H12.65C12.6889 8.74546 12.9529 9.30383 13.3884 9.71179C13.8239 10.1198 14.3983 10.3468 14.995 10.3468C15.5918 10.3468 16.1661 10.1198 16.6016 9.71179C17.0371 9.30383 17.3011 8.74546 17.34 8.14999Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Price', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productprice',
			'id'     => 54,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M9.67999 3L20.38 1L26.19 10.25L18.85 27.13C18.5334 27.7471 17.9859 28.2141 17.3267 28.4295C16.6674 28.6449 15.9498 28.5912 15.33 28.28L3.89999 23.28C3.24931 23.039 2.72024 22.5506 2.42804 21.9213C2.13584 21.2919 2.10419 20.5726 2.33999 19.92L9.67999 3ZM17.1483 25.5346C17.3503 25.3502 17.514 25.1278 17.63 24.88L23.66 11.13L19.24 3.75L10.82 5.55L4.81999 19.3C4.6202 19.8147 4.63215 20.3875 4.85323 20.8933C5.07432 21.3992 5.48657 21.797 5.99999 22L14.86 25.88C15.1075 25.9965 15.3755 26.0631 15.6488 26.0759C15.922 26.0887 16.1951 26.0475 16.4524 25.9546C16.7097 25.8617 16.9462 25.719 17.1483 25.5346ZM8.83997 17.32C9.65972 18.2023 10.6363 18.9246 11.72 19.45L11.08 20.91L12.65 21.59L13.28 20.13C14.185 20.4349 15.1649 20.4349 16.07 20.13C16.4553 19.9739 16.8048 19.7408 17.0971 19.4451C17.3894 19.1494 17.6183 18.7972 17.77 18.41C17.9325 18.0776 18.0261 17.7158 18.045 17.3463C18.0639 16.9768 18.0077 16.6072 17.88 16.26C17.4851 15.3889 16.901 14.6168 16.17 14C15.8178 13.686 15.484 13.3521 15.17 13C14.985 12.8151 14.8298 12.6025 14.71 12.37C14.6755 12.2929 14.6577 12.2094 14.6577 12.125C14.6577 12.0406 14.6755 11.9571 14.71 11.88C14.7461 11.79 14.8062 11.7117 14.8838 11.6535C14.9614 11.5953 15.0535 11.5595 15.15 11.55C15.4667 11.548 15.7791 11.6235 16.06 11.77C17.0427 12.2474 17.9121 12.9293 18.61 13.77L20.45 12C19.683 11.1507 18.7724 10.4433 17.76 9.91001L18.21 8.91001L16.64 8.23001L16.19 9.23001C15.2991 8.89148 14.327 8.82876 13.4 9.05001C13.0232 9.17994 12.6798 9.39169 12.3945 9.67006C12.1092 9.94842 11.8891 10.2865 11.75 10.66C11.5275 11.1249 11.4146 11.6346 11.42 12.15C11.4519 12.6313 11.5994 13.0978 11.85 13.51C12.1821 14.0575 12.5894 14.5557 13.06 14.99C13.54 15.44 13.91 15.82 14.19 16.11C14.4005 16.3218 14.5821 16.5605 14.73 16.82C14.7711 16.8987 14.7927 16.9862 14.7927 17.075C14.7927 17.1638 14.7711 17.2513 14.73 17.33C14.6928 17.4418 14.6218 17.5394 14.5269 17.6092C14.432 17.6791 14.3178 17.7178 14.2 17.72C13.8524 17.7104 13.5111 17.6251 13.2 17.47C12.5925 17.1857 12.0272 16.8189 11.52 16.38C10.9439 15.9054 10.4118 15.38 9.92997 14.81L8.83997 17.32Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Add To Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'addtocart',
			'id'     => 55,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.18 19.66V21.4C26.18 21.5406 26.1242 21.6754 26.0248 21.7748C25.9254 21.8742 25.7906 21.93 25.65 21.93H9.53C8.82028 21.9441 8.13004 21.6974 7.59003 21.2367C7.05003 20.7759 6.69777 20.1331 6.6 19.43C6.54061 18.8581 6.65936 18.2818 6.94 17.78L8.81 14.38V14.33L3.8 3.75999H1.52C1.44865 3.75876 1.37828 3.74313 1.31311 3.71404C1.24794 3.68496 1.18932 3.64301 1.14076 3.59071C1.09221 3.53842 1.05471 3.47685 1.03052 3.40971C1.00633 3.34257 0.99595 3.27124 1 3.19999V1.45999C0.999975 1.32114 1.05444 1.18783 1.15168 1.08872C1.24892 0.989606 1.38118 0.932612 1.52 0.929993H5.18C5.29158 0.930348 5.40093 0.961303 5.49614 1.01949C5.59135 1.07767 5.66878 1.16086 5.72 1.25999L6.88 3.72999L8.21 6.52999L11.35 13.16L11.53 13.53H21.32H21.37L22 12.38C22.6385 12.6244 23.3163 12.7498 24 12.75C24.3359 12.751 24.671 12.7175 25 12.65L23.77 14.89C23.5306 15.3274 23.1775 15.6921 22.748 15.9456C22.3186 16.199 21.8287 16.3318 21.33 16.33H11H10.94L9.67 18.61L9.62 18.78C9.6226 18.8737 9.66168 18.9627 9.72891 19.0281C9.79615 19.0935 9.88623 19.13 9.98 19.13H25.65C25.7906 19.13 25.9254 19.1858 26.0248 19.2852C26.1242 19.3846 26.18 19.5194 26.18 19.66ZM12.84 4.18999V5.59999C12.8413 5.66959 12.8563 5.73826 12.8841 5.80206C12.912 5.86587 12.9521 5.92356 13.0022 5.97185C13.0524 6.02014 13.1116 6.05808 13.1764 6.0835C13.2412 6.10891 13.3104 6.12131 13.38 6.11999H18.33C18.4578 5.23379 18.8011 4.3925 19.33 3.66999H13.38C13.3104 3.66866 13.2412 3.68106 13.1764 3.70648C13.1116 3.7319 13.0524 3.76983 13.0022 3.81812C12.9521 3.86641 12.912 3.92411 12.8841 3.98791C12.8563 4.05172 12.8413 4.12038 12.84 4.18999ZM7.83437 23.8019C8.29483 23.4942 8.83618 23.33 9.38997 23.33C9.75804 23.3287 10.1227 23.4002 10.463 23.5404C10.8034 23.6807 11.1125 23.8869 11.3728 24.1472C11.6331 24.4074 11.8393 24.7166 11.9795 25.0569C12.1198 25.3972 12.1913 25.7619 12.19 26.13C12.19 26.6838 12.0258 27.2251 11.7181 27.6856C11.4104 28.1461 10.9731 28.5049 10.4615 28.7169C9.94985 28.9288 9.38686 28.9842 8.84371 28.8762C8.30057 28.7682 7.80165 28.5015 7.41007 28.1099C7.01848 27.7183 6.75181 27.2194 6.64377 26.6763C6.53573 26.1331 6.59118 25.5701 6.8031 25.0585C7.01503 24.5469 7.37391 24.1096 7.83437 23.8019ZM21.8371 23.8067C22.2962 23.4979 22.8366 23.332 23.39 23.33H23.37C23.7406 23.3247 24.1085 23.3933 24.4523 23.5317C24.7961 23.6702 25.1088 23.8757 25.3723 24.1364C25.6357 24.3971 25.8446 24.7076 25.9867 25.0499C26.1288 25.3922 26.2013 25.7594 26.2 26.13C26.2 26.6834 26.036 27.2243 25.7288 27.6846C25.4215 28.1448 24.9848 28.5037 24.4738 28.7159C23.9627 28.9281 23.4003 28.9841 22.8574 28.8769C22.3145 28.7697 21.8156 28.504 21.4236 28.1134C21.0316 27.7228 20.7642 27.2248 20.655 26.6824C20.5458 26.1399 20.5999 25.5772 20.8102 25.0654C21.0206 24.5536 21.378 24.1156 21.8371 23.8067ZM24 11.4C26.43 11.4 28.4 9.43006 28.4 7.00001C28.4 4.56995 26.43 2.60001 24 2.60001C21.5699 2.60001 19.6 4.56995 19.6 7.00001C19.6 9.43006 21.5699 11.4 24 11.4ZM23.2401 7.83H21.0601V6.21H23.2401V4H24.8201V6.21H27.0001V7.83H24.8201V10H23.2401V7.83Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Meta', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productmeta',
			'id'     => 56,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M2.99997 22.5V7.5C2.98063 6.99792 3.15785 6.50808 3.49398 6.13461C3.8301 5.76114 4.29863 5.53347 4.79997 5.5H25.2C25.7102 5.52081 26.1914 5.74298 26.5381 6.11785C26.8849 6.49271 27.0689 6.98971 27.05 7.5V22.5C27.0689 23.0103 26.8849 23.5073 26.5381 23.8822C26.1914 24.257 25.7102 24.4792 25.2 24.5H4.84997C4.33975 24.4792 3.85857 24.257 3.51183 23.8822C3.16508 23.5073 2.98102 23.0103 2.99997 22.5ZM4.99997 22H25V8H4.99997V22ZM15 10C15.5523 10 16 10.4477 16 11V17C16 17.5523 15.5523 18 15 18C14.4477 18 14 17.5523 14 17V11C14 10.4477 14.4477 10 15 10ZM12 10H11C10.4477 10 10 10.4477 10 11V17C10 17.5523 10.4477 18 11 18H12C12.5523 18 13 17.5523 13 17V11C13 10.4477 12.5523 10 12 10ZM18 10H19C19.5523 10 20 10.4477 20 11V17C20 17.5523 19.5523 18 19 18H18C17.4477 18 17 17.5523 17 17V11C17 10.4477 17.4477 10 18 10ZM9 10C9 9.44772 8.55228 9 8 9C7.44772 9 7 9.44772 7 10V20C7 20.5523 7.44772 21 8 21C8.55228 21 9 20.5523 9 20V10ZM22 9C22.5523 9 23 9.44772 23 10V20C23 20.5523 22.5523 21 22 21C21.4477 21 21 20.5523 21 20V10C21 9.44772 21.4477 9 22 9Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Content', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productcontent',
			'id'     => 57,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M27 1H3C2.46957 1 1.96086 1.21071 1.58579 1.58579C1.21071 1.96086 1 2.46957 1 3V27C1 27.5304 1.21071 28.0391 1.58579 28.4142C1.96086 28.7893 2.46957 29 3 29H27C27.5304 29 28.0391 28.7893 28.4142 28.4142C28.7893 28.0391 29 27.5304 29 27V3C29 2.46957 28.7893 1.96086 28.4142 1.58579C28.0391 1.21071 27.5304 1 27 1ZM26.81 26.82H3.19V3.18H26.81V26.82ZM11.4822 15.6247C11.5476 15.6539 11.6183 15.6694 11.69 15.67L18.31 15.6C18.3793 15.5999 18.4478 15.5856 18.5114 15.5581C18.5751 15.5306 18.6324 15.4904 18.68 15.44C18.7314 15.389 18.7721 15.3283 18.7996 15.2613C18.8271 15.1943 18.8408 15.1224 18.84 15.05V9.56H20.01C20.1262 9.56164 20.2393 9.52274 20.33 9.45C20.4306 9.37043 20.4983 9.25645 20.52 9.13L20.95 7.13C20.9707 7.00342 20.9497 6.87353 20.89 6.76C20.8352 6.64883 20.7432 6.56037 20.63 6.51L17 5C16.9417 5.01055 16.8847 5.02732 16.83 5.05H13.23C13.1541 5.03409 13.0758 5.03409 13 5.05L9.31997 6.58C9.20275 6.63553 9.10752 6.72876 9.04951 6.84477C8.99151 6.96079 8.97406 7.0929 8.99997 7.22L9.49997 9.19C9.53038 9.30462 9.59703 9.40634 9.68997 9.48C9.77902 9.54862 9.88758 9.58714 9.99997 9.59L11.16 9.64V15.12C11.1583 15.2614 11.2121 15.3979 11.31 15.5C11.3582 15.553 11.4168 15.5954 11.4822 15.6247ZM17.81 9.08V14.57L12.23 14.55V9.08C12.2254 8.94428 12.1708 8.81506 12.0766 8.71721C11.9824 8.61936 11.8554 8.5598 11.72 8.55L10.43 8.49L10.16 7.4L12.77 6.3C12.9089 6.78416 13.2016 7.20996 13.6039 7.51305C14.0062 7.81615 14.4963 7.98008 15 7.98008C15.5037 7.98008 15.9937 7.81615 16.396 7.51305C16.7983 7.20996 17.091 6.78416 17.23 6.3L19.84 7.4L19.6 8.53H18.35C18.2058 8.53262 18.0685 8.59172 17.9675 8.69458C17.8665 8.79745 17.8099 8.93585 17.81 9.08ZM13.4301 5.76999H16.5601C16.5286 6.16345 16.35 6.53062 16.06 6.79836C15.77 7.06611 15.3898 7.21479 14.9951 7.21479C14.6003 7.21479 14.2201 7.06611 13.9301 6.79836C13.6401 6.53062 13.4615 6.16345 13.4301 5.76999ZM23.33 17.67H6.67C6.49402 17.67 6.32511 17.7392 6.19974 17.8627C6.07437 17.9862 6.00263 18.1541 6 18.33C6 18.418 6.01733 18.5051 6.051 18.5864C6.08467 18.6677 6.13402 18.7416 6.19624 18.8038C6.25845 18.866 6.33231 18.9153 6.4136 18.949C6.49489 18.9827 6.58201 19 6.67 19H23.33C23.5077 19 23.6781 18.9294 23.8038 18.8038C23.9294 18.6781 24 18.5077 24 18.33C23.9974 18.1541 23.9256 17.9862 23.8003 17.8627C23.6749 17.7392 23.506 17.67 23.33 17.67ZM8.00004 20.67H22.1C22.2751 20.67 22.443 20.7395 22.5667 20.8633C22.6905 20.9871 22.76 21.155 22.76 21.33C22.7614 21.4175 22.7453 21.5044 22.7127 21.5857C22.6801 21.6669 22.6317 21.7408 22.5703 21.8032C22.5089 21.8655 22.4357 21.915 22.3549 21.9488C22.2742 21.9826 22.1876 22 22.1 22H8.00004C7.91252 22 7.82587 21.9826 7.74514 21.9488C7.66441 21.915 7.5912 21.8655 7.52978 21.8032C7.46836 21.7408 7.41996 21.6669 7.38738 21.5857C7.35481 21.5044 7.33872 21.4175 7.34004 21.33C7.34004 21.155 7.40958 20.9871 7.53335 20.8633C7.65712 20.7395 7.825 20.67 8.00004 20.67ZM19.52 23.67H10.52C10.3457 23.6726 10.1794 23.743 10.0562 23.8662C9.93293 23.9894 9.86257 24.1558 9.85999 24.33C9.85997 24.506 9.92918 24.6749 10.0527 24.8003C10.1762 24.9256 10.344 24.9974 10.52 25H19.52C19.6959 24.9974 19.8638 24.9256 19.9873 24.8003C20.1108 24.6749 20.18 24.506 20.18 24.33C20.1774 24.1558 20.107 23.9894 19.9838 23.8662C19.8606 23.743 19.6942 23.6726 19.52 23.67Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Short Description', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'shortdescription',
			'id'     => 58,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.89 2.65001H4.11C3.7029 2.64869 3.29953 2.72758 2.92292 2.88215C2.5463 3.03673 2.20383 3.26397 1.91504 3.5509C1.62624 3.83784 1.3968 4.17884 1.2398 4.55445C1.08279 4.93006 1.00131 5.33291 1 5.74001V24.26C1.00131 24.6671 1.08279 25.07 1.2398 25.4456C1.3968 25.8212 1.62624 26.1622 1.91504 26.4491C2.20383 26.736 2.5463 26.9633 2.92292 27.1179C3.29953 27.2724 3.7029 27.3513 4.11 27.35H25.89C26.7114 27.35 27.4994 27.0251 28.0821 26.4462C28.6647 25.8673 28.9947 25.0814 29 24.26V5.74001C28.9987 5.33291 28.9172 4.93006 28.7602 4.55445C28.6032 4.17884 28.3738 3.83784 28.085 3.5509C27.7962 3.26397 27.4537 3.03673 27.0771 2.88215C26.7005 2.72758 26.2971 2.64869 25.89 2.65001ZM26.25 24.65H3.75V8.82001H26.25V24.65ZM4.29 5.39001H6.49C6.62883 5.40014 6.75865 5.46252 6.8533 5.56459C6.94795 5.66666 7.00037 5.80082 7 5.94001C7.00037 6.07921 6.94795 6.21337 6.8533 6.31544C6.75865 6.41751 6.62883 6.47989 6.49 6.49001H4.29C4.14587 6.48739 4.00853 6.42829 3.90754 6.32543C3.80655 6.22257 3.74998 6.08417 3.75 5.94001C3.74998 5.79586 3.80655 5.65746 3.90754 5.5546C4.00853 5.45174 4.14587 5.39264 4.29 5.39001ZM15.8 5.39001H18C18.1459 5.39001 18.2858 5.44796 18.3889 5.55111C18.4921 5.65425 18.55 5.79415 18.55 5.94001C18.55 6.08588 18.4921 6.22578 18.3889 6.32892C18.2858 6.43207 18.1459 6.49001 18 6.49001H15.8C15.6541 6.49001 15.5142 6.43207 15.4111 6.32892C15.3079 6.22578 15.25 6.08588 15.25 5.94001C15.25 5.79415 15.3079 5.65425 15.4111 5.55111C15.5142 5.44796 15.6541 5.39001 15.8 5.39001ZM21.84 5.39001H19.67C19.5241 5.39001 19.3842 5.44796 19.2811 5.55111C19.1779 5.65425 19.12 5.79415 19.12 5.94001C19.12 6.08588 19.1779 6.22578 19.2811 6.32892C19.3842 6.43207 19.5241 6.49001 19.67 6.49001H21.84C21.9859 6.49001 22.1258 6.43207 22.2289 6.32892C22.3321 6.22578 22.39 6.08588 22.39 5.94001C22.39 5.79415 22.3321 5.65425 22.2289 5.55111C22.1258 5.44796 21.9859 5.39001 21.84 5.39001ZM23.45 5.39001H25.65H25.71C25.8434 5.40713 25.966 5.47255 26.0544 5.5739C26.1429 5.67525 26.1911 5.8055 26.19 5.94001C26.19 6.08417 26.1335 6.22257 26.0325 6.32543C25.9315 6.42829 25.7941 6.48739 25.65 6.49001H23.45C23.3041 6.49001 23.1642 6.43207 23.0611 6.32892C22.9579 6.22578 22.9 6.08588 22.9 5.94001C22.9 5.79415 22.9579 5.65425 23.0611 5.55111C23.1642 5.44796 23.3041 5.39001 23.45 5.39001ZM6.80998 20.87C6.88897 20.9511 6.99677 20.9978 7.10998 21L12.32 20.97C12.431 20.9667 12.5373 20.9242 12.62 20.85C12.6588 20.8104 12.6894 20.7635 12.71 20.712C12.7306 20.6605 12.7408 20.6054 12.74 20.55V16.34H13.65C13.7449 16.3343 13.8357 16.2994 13.91 16.24C13.9862 16.1797 14.0393 16.0949 14.06 16L14.35 14.47C14.3791 14.3789 14.3791 14.2811 14.35 14.19C14.3034 14.1048 14.2294 14.0378 14.14 14L11.3 12.83C11.2371 12.8188 11.1728 12.8188 11.11 12.83H8.28998C8.23052 12.8186 8.16945 12.8186 8.10998 12.83L5.25998 14C5.15719 14.0385 5.073 14.1148 5.02466 14.2133C4.97632 14.3119 4.96749 14.4251 4.99998 14.53L5.37998 16C5.39044 16.1153 5.44401 16.2224 5.52998 16.3C5.60063 16.3576 5.68883 16.3893 5.77998 16.39H6.68998V20.58C6.68818 20.6341 6.69795 20.6879 6.71865 20.738C6.73934 20.788 6.77048 20.833 6.80998 20.87ZM11.9 16V20.16L7.58998 20.19V16C7.58745 15.8947 7.54449 15.7944 7.47002 15.72C7.39555 15.6455 7.29527 15.6025 7.18998 15.6L6.18998 15.55L5.97998 14.71L7.97998 13.87C8.09419 14.2424 8.32485 14.5683 8.63806 14.7998C8.95126 15.0314 9.33049 15.1563 9.71998 15.1563C10.1095 15.1563 10.4887 15.0314 10.8019 14.7998C11.1151 14.5683 11.3458 14.2424 11.46 13.87L13.51 14.71L13.32 15.58H12.32C12.2086 15.58 12.1018 15.6242 12.023 15.703C11.9442 15.7818 11.9 15.8886 11.9 16ZM8.46997 13.42H10.92C10.8923 13.7258 10.7513 14.0102 10.5246 14.2173C10.2979 14.4244 10.002 14.5392 9.69497 14.5392C9.38793 14.5392 9.092 14.4244 8.86532 14.2173C8.63865 14.0102 8.49763 13.7258 8.46997 13.42ZM24.33 13.12H16.67C16.494 13.12 16.3251 13.1892 16.1997 13.3127C16.0744 13.4362 16.0026 13.604 16 13.78C16 13.868 16.0173 13.9551 16.051 14.0364C16.0847 14.1177 16.134 14.1915 16.1962 14.2538C16.2585 14.316 16.3323 14.3653 16.4136 14.399C16.4949 14.4327 16.582 14.45 16.67 14.45H24.33C24.5077 14.45 24.6781 14.3794 24.8038 14.2538C24.9294 14.1281 25 13.9577 25 13.78C24.9974 13.604 24.9256 13.4362 24.8003 13.3127C24.6749 13.1892 24.506 13.12 24.33 13.12ZM16.67 16.12H23.05C23.225 16.12 23.3929 16.1895 23.5167 16.3133C23.6405 16.4371 23.71 16.605 23.71 16.78C23.7113 16.8675 23.6952 16.9544 23.6627 17.0356C23.6301 17.1169 23.5817 17.1908 23.5203 17.2532C23.4588 17.3155 23.3856 17.365 23.3049 17.3988C23.2242 17.4326 23.1375 17.45 23.05 17.45H16.67C16.582 17.45 16.4949 17.4327 16.4136 17.399C16.3323 17.3653 16.2585 17.316 16.1962 17.2538C16.134 17.1915 16.0847 17.1177 16.051 17.0364C16.0173 16.9551 16 16.868 16 16.78C16.0026 16.604 16.0744 16.4362 16.1997 16.3127C16.3251 16.1892 16.494 16.12 16.67 16.12ZM20.48 19.12H16.67C16.494 19.12 16.3251 19.1892 16.1997 19.3127C16.0744 19.4362 16.0026 19.604 16 19.78C16 19.868 16.0173 19.9551 16.051 20.0364C16.0847 20.1177 16.134 20.1915 16.1962 20.2538C16.2585 20.316 16.3323 20.3653 16.4136 20.399C16.4949 20.4327 16.582 20.45 16.67 20.45H20.48C20.656 20.4474 20.8238 20.3756 20.9473 20.2503C21.0708 20.1249 21.14 19.956 21.14 19.78C21.1374 19.6058 21.0671 19.4394 20.9438 19.3162C20.8206 19.1929 20.6542 19.1226 20.48 19.12Z"/>
</svg>
',
		),

		array(
			'name'   => __( 'Additional Information', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'additionalinformation',
			'id'     => 59,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3 1H27C27.5304 1 28.0391 1.21071 28.4142 1.58579C28.7893 1.96086 29 2.46957 29 3V27C29 27.5304 28.7893 28.0391 28.4142 28.4142C28.0391 28.7893 27.5304 29 27 29H3C2.46957 29 1.96086 28.7893 1.58579 28.4142C1.21071 28.0391 1 27.5304 1 27V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1ZM3.19 26.82H26.81V3.18H3.19V26.82ZM11.69 15.67C11.6183 15.6694 11.5476 15.6539 11.4822 15.6247C11.4168 15.5954 11.3582 15.553 11.31 15.5C11.2121 15.3979 11.1583 15.2614 11.16 15.12V9.64L9.99997 9.59C9.88758 9.58714 9.77902 9.54862 9.68997 9.48C9.59703 9.40634 9.53038 9.30462 9.49997 9.19L8.99997 7.22C8.97406 7.0929 8.99151 6.96079 9.04951 6.84477C9.10752 6.72876 9.20275 6.63553 9.31997 6.58L13 5.05C13.0758 5.03409 13.1541 5.03409 13.23 5.05H16.83C16.8847 5.02732 16.9417 5.01055 17 5L20.63 6.51C20.7432 6.56037 20.8352 6.64883 20.89 6.76C20.9497 6.87353 20.9707 7.00342 20.95 7.13L20.52 9.13C20.4983 9.25645 20.4306 9.37043 20.33 9.45C20.2393 9.52274 20.1262 9.56164 20.01 9.56H18.84V15.05C18.8408 15.1224 18.8271 15.1943 18.7996 15.2613C18.7721 15.3283 18.7314 15.389 18.68 15.44C18.6324 15.4904 18.5751 15.5306 18.5114 15.5581C18.4478 15.5856 18.3793 15.5999 18.31 15.6L11.69 15.67ZM17.81 14.57V9.08C17.8099 8.93585 17.8665 8.79745 17.9675 8.69458C18.0685 8.59172 18.2058 8.53262 18.35 8.53H19.6L19.84 7.4L17.23 6.3C17.091 6.78416 16.7983 7.20996 16.396 7.51305C15.9937 7.81615 15.5037 7.98008 15 7.98008C14.4963 7.98008 14.0062 7.81615 13.6039 7.51305C13.2016 7.20996 12.9089 6.78416 12.77 6.3L10.16 7.4L10.43 8.49L11.72 8.55C11.8554 8.5598 11.9824 8.61936 12.0766 8.71721C12.1708 8.81506 12.2254 8.94428 12.23 9.08V14.55L17.81 14.57ZM22.33 18.83H12.67C12.4923 18.83 12.3219 18.9006 12.1962 19.0262C12.0706 19.1519 12 19.3223 12 19.5C12 19.6777 12.0706 19.8481 12.1962 19.9737C12.3219 20.0994 12.4923 20.17 12.67 20.17H22.33C22.418 20.17 22.5051 20.1527 22.5864 20.119C22.6677 20.0853 22.7415 20.036 22.8038 19.9737C22.866 19.9115 22.9153 19.8377 22.949 19.7564C22.9827 19.6751 23 19.588 23 19.5C23 19.412 22.9827 19.3249 22.949 19.2436C22.9153 19.1623 22.866 19.0884 22.8038 19.0262C22.7415 18.964 22.6677 18.9147 22.5864 18.881C22.5051 18.8473 22.418 18.83 22.33 18.83ZM8.5 21C9.32843 21 10 20.3284 10 19.5C10 18.6716 9.32843 18 8.5 18C7.67157 18 7 18.6716 7 19.5C7 20.3284 7.67157 21 8.5 21ZM22.33 22.83H12.67C12.4923 22.83 12.3219 22.9006 12.1962 23.0262C12.0706 23.1519 12 23.3223 12 23.5C12 23.6777 12.0706 23.8481 12.1962 23.9737C12.3219 24.0994 12.4923 24.17 12.67 24.17H22.33C22.418 24.17 22.5051 24.1527 22.5864 24.119C22.6677 24.0853 22.7415 24.036 22.8038 23.9737C22.866 23.9115 22.9153 23.8377 22.949 23.7564C22.9827 23.6751 23 23.588 23 23.5C23 23.412 22.9827 23.3249 22.949 23.2436C22.9153 23.1623 22.866 23.0884 22.8038 23.0262C22.7415 22.964 22.6677 22.9147 22.5864 22.881C22.5051 22.8473 22.418 22.83 22.33 22.83ZM8.5 25C9.32843 25 10 24.3284 10 23.5C10 22.6716 9.32843 22 8.5 22C7.67157 22 7 22.6716 7 23.5C7 24.3284 7.67157 25 8.5 25Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Products Related', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productrelated',
			'id'     => 60,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M22.4141 8H18.77C18.5293 6.95849 18.0315 5.99205 17.3201 5.19C17.0502 4.90158 16.7575 4.63548 16.4449 4.39426C16.787 4.32393 17.1371 4.28863 17.49 4.29001C18.6521 4.29631 19.778 4.69469 20.6855 5.42066C21.5147 6.08404 22.1171 6.98547 22.4141 8ZM23.8289 8C23.5074 6.62938 22.7473 5.39775 21.6601 4.49489C20.4967 3.52879 19.0322 2.99997 17.52 3.00001C16.641 2.99817 15.777 3.17728 14.9794 3.52006C14.1913 3.18164 13.3383 3.0027 12.4701 3C10.9596 3.00228 9.49736 3.53216 8.33608 4.49807C7.25091 5.40067 6.49224 6.63103 6.17112 8H4.85C3.83 8 3 9.14 3 10.53V24.47C3 25.86 3.83 27 4.85 27H25.15C26.15 27 27 25.86 27 24.47V10.53C27 9.14 26.17 8 25.15 8H23.8289ZM13.5029 4.39905C13.1522 4.32453 12.7926 4.28755 12.4301 4.29C11.2684 4.29807 10.1434 4.69711 9.23628 5.42278C8.40743 6.08586 7.8047 6.98635 7.50635 8H11.1993C11.4369 6.96222 11.9316 5.99899 12.64 5.20001C12.8983 4.90285 13.1876 4.63435 13.5029 4.39905ZM12.5046 8H13.74H13.79H15.85H17.4154C17.2069 7.27929 16.84 6.61282 16.3401 6.05C16.0455 5.72396 15.7093 5.43816 15.3401 5.2C15.2151 5.11215 15.0866 5.03033 14.955 4.9547C14.827 5.02745 14.7019 5.10593 14.58 5.19001C14.2121 5.43384 13.8761 5.72275 13.58 6.05001C13.0801 6.61283 12.7132 7.2793 12.5046 8ZM21.8124 10.53L21.8 10.55H15.04H14.56L14.5476 10.53H13.77H4.85V24.47H25.15V10.53H22.65H21.8124ZM11.6899 22.89C11.6183 22.8894 11.5476 22.8739 11.4822 22.8447C11.4168 22.8154 11.3582 22.773 11.3099 22.72C11.2596 22.6708 11.2202 22.6115 11.1944 22.546C11.1685 22.4805 11.1568 22.4103 11.1599 22.34V16.87L9.99995 16.81C9.88756 16.8071 9.779 16.7686 9.68995 16.7C9.59701 16.6264 9.53036 16.5246 9.49995 16.41L9.01995 14.41C8.99278 14.2842 9.00976 14.1529 9.06804 14.0382C9.12632 13.9234 9.22234 13.8323 9.33995 13.78L12.9999 12.27C13.0758 12.2541 13.1541 12.2541 13.2299 12.27H16.8299C16.909 12.253 16.9909 12.253 17.07 12.27L20.7 13.78C20.8146 13.8231 20.9079 13.9092 20.9599 14.02C21.0189 14.1338 21.04 14.2634 21.02 14.39L20.59 16.39C20.5683 16.5165 20.5006 16.6304 20.3999 16.71C20.3081 16.7818 20.1964 16.8237 20.0799 16.83H18.8799V22.32C18.8814 22.3925 18.8679 22.4645 18.8404 22.5316C18.8129 22.5987 18.7719 22.6594 18.7199 22.71C18.6724 22.7604 18.615 22.8006 18.5514 22.8281C18.4878 22.8556 18.4193 22.8699 18.3499 22.87L11.6899 22.89ZM17.8099 21.79V16.3C17.8099 16.1568 17.8668 16.0194 17.9681 15.9182C18.0694 15.8169 18.2067 15.76 18.3499 15.76H19.5999L19.84 14.63L17.2299 13.54C17.091 14.0242 16.7983 14.45 16.396 14.7531C15.9937 15.0562 15.5037 15.2201 14.9999 15.2201C14.4962 15.2201 14.0062 15.0562 13.6039 14.7531C13.2016 14.45 12.9089 14.0242 12.7699 13.54L10.1599 14.64L10.4299 15.74H11.7199C11.8554 15.7498 11.9824 15.8094 12.0766 15.9072C12.1707 16.0051 12.2254 16.1343 12.2299 16.27V21.74L17.8099 21.79ZM16.5601 13H13.4301C13.4615 13.3935 13.6401 13.7606 13.9301 14.0284C14.2201 14.2961 14.6003 14.4448 14.9951 14.4448C15.3898 14.4448 15.77 14.2961 16.06 14.0284C16.35 13.7606 16.5286 13.3935 16.5601 13Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Upsells', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'upsells',
			'id'     => 61,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M21.85 4.63L19.34 2H25.92V8.9L23.41 6.27L16.51 13.5L12.12 10.05L6.55 15.42L5 13.8L12.12 6.8L16.51 10.25L21.85 4.63ZM24.5 11.8C24.8978 11.8 25.2794 11.958 25.5607 12.2393C25.842 12.5206 26 12.9022 26 13.3V27.8H23V13.3C23 12.9022 23.158 12.5206 23.4393 12.2393C23.7206 11.958 24.1022 11.8 24.5 11.8ZM19.5607 15.2393C19.2794 14.958 18.8978 14.8 18.5 14.8C18.1022 14.8 17.7206 14.958 17.4393 15.2393C17.158 15.5206 17 15.9022 17 16.3V27.8H20V16.3C20 15.9022 19.842 15.5206 19.5607 15.2393ZM12.5 17.8C12.8978 17.8 13.2794 17.958 13.5607 18.2393C13.842 18.5206 14 18.9022 14 19.3V27.8H11V19.3C11 18.9022 11.158 18.5206 11.4393 18.2393C11.7206 17.958 12.1022 17.8 12.5 17.8ZM6.5 20.8C6.89782 20.8 7.27936 20.958 7.56066 21.2393C7.84196 21.5206 8 21.9022 8 22.3V27.8H5V22.3C5 21.9022 5.15804 21.5206 5.43934 21.2393C5.72064 20.958 6.10218 20.8 6.5 20.8Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Rating', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productrating',
			'id'     => 62,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M21.38 8H24C24.5304 8 25.0391 8.21071 25.4142 8.58578C25.7893 8.96086 26 9.46956 26 10V25C26 25.5304 25.7893 26.0391 25.4142 26.4142C25.0391 26.7893 24.5304 27 24 27H6C5.46957 27 4.96086 26.7893 4.58579 26.4142C4.21071 26.0391 4 25.5304 4 25V10C4 9.46956 4.21071 8.96086 4.58579 8.58578C4.96086 8.21071 5.46957 8 6 8H8.64C8.63518 7.96014 8.63518 7.91985 8.64 7.88C8.91739 6.39521 9.70475 5.05397 10.866 4.08807C12.0273 3.12216 13.4895 2.59228 15 2.59C16.3979 2.58553 17.7583 3.04249 18.87 3.89C19.232 4.1528 19.567 4.45093 19.87 4.78C20.647 5.65601 21.1693 6.72814 21.38 7.88C21.3848 7.91985 21.3848 7.96014 21.38 8ZM11.8045 5.01064C10.8971 5.7366 10.2612 6.74763 10 7.88C9.97547 7.914 9.95531 7.95096 9.94 7.99H20V7.88C19.8294 7.05403 19.4547 6.28393 18.91 5.64C18.6186 5.31072 18.2819 5.02452 17.91 4.79C17.0584 4.1911 16.0411 3.87297 15 3.88C13.8379 3.88629 12.712 4.28468 11.8045 5.01064ZM6.32 10V25H23.68V10H6.32ZM20.9782 16.4045C20.9614 16.4648 20.9272 16.5189 20.88 16.56L18.07 18.91C18.0381 18.9358 18.0145 18.9704 18.002 19.0095C17.9896 19.0486 17.9889 19.0905 18 19.13L18.83 22.64C18.8465 22.7012 18.8445 22.766 18.8242 22.8261C18.8038 22.8863 18.766 22.9389 18.7157 22.9775C18.6653 23.0161 18.6046 23.0389 18.5412 23.0429C18.4779 23.0469 18.4148 23.0319 18.36 23L15.16 21.12C15.1265 21.1007 15.0886 21.0905 15.05 21.0905C15.0114 21.0905 14.9734 21.1007 14.94 21.12L11.7 23C11.6445 23.0301 11.5814 23.0437 11.5184 23.0391C11.4554 23.0345 11.395 23.0119 11.3445 22.974C11.2939 22.9361 11.2553 22.8845 11.2332 22.8253C11.2112 22.766 11.2066 22.7017 11.22 22.64L12.07 19.13C12.0787 19.0904 12.0769 19.0492 12.0646 19.0106C12.0523 18.9719 12.03 18.9372 12 18.91L9.11998 16.57C9.06926 16.5297 9.03196 16.475 9.01302 16.413C8.99407 16.3511 8.99437 16.2848 9.01388 16.2231C9.03338 16.1613 9.07118 16.1069 9.12226 16.0671C9.17335 16.0272 9.23532 16.0038 9.29998 16L13.05 15.68C13.0883 15.6794 13.1257 15.6677 13.1576 15.6464C13.1895 15.6251 13.2146 15.5951 13.23 15.56L14.7 12.22C14.7263 12.1625 14.7686 12.1139 14.8218 12.0797C14.8749 12.0456 14.9368 12.0275 15 12.0275C15.0632 12.0275 15.125 12.0456 15.1782 12.0797C15.2314 12.1139 15.2737 12.1625 15.3 12.22L16.82 15.55C16.8366 15.5851 16.8618 15.6154 16.8933 15.6381C16.9247 15.6609 16.9614 15.6753 17 15.68L20.7 16C20.7623 16.0059 20.8216 16.0299 20.8704 16.0691C20.9192 16.1084 20.9554 16.161 20.9746 16.2207C20.9937 16.2803 20.995 16.3442 20.9782 16.4045ZM15.11 20L17.45 21.31L16.83 18.78C16.8201 18.7404 16.8214 18.6989 16.8338 18.6601C16.8462 18.6212 16.8691 18.5866 16.9 18.56L19 16.85L16.29 16.62C16.2516 16.6194 16.2143 16.6077 16.1824 16.5864C16.1504 16.5651 16.1253 16.5351 16.11 16.5L15.25 14.55C15.2324 14.5117 15.2043 14.4792 15.1688 14.4565C15.1334 14.4337 15.0921 14.4216 15.05 14.4216C15.0079 14.4216 14.9666 14.4337 14.9312 14.4565C14.8957 14.4792 14.8675 14.5117 14.85 14.55L14 16.49C13.9834 16.5251 13.9582 16.5554 13.9267 16.5781C13.8952 16.6009 13.8585 16.6153 13.82 16.62L11.1 16.84L13.1 18.55C13.1337 18.5746 13.1592 18.6086 13.1735 18.6478C13.1877 18.687 13.19 18.7295 13.18 18.77L12.56 21.3L14.89 20C14.9234 19.9807 14.9614 19.9705 15 19.9705C15.0386 19.9705 15.0765 19.9807 15.11 20Z"/>
			</svg>
			',
		),

		array(
			'name'   => __( 'Product Stock', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productstock',
			'id'     => 63,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.578 11.2781C18.7539 11.3784 18.8997 11.5241 19 11.7C19.0992 11.8718 19.1514 12.0666 19.1514 12.265C19.1514 12.4634 19.0992 12.6582 19 12.83C18.8961 13.002 18.7493 13.144 18.574 13.2421C18.3987 13.3403 18.2009 13.3912 18 13.39H11.16C10.8637 13.4182 10.5681 13.3294 10.3364 13.1425C10.1046 12.9557 9.95522 12.6856 9.92 12.39C9.89146 12.0914 9.97968 11.7934 10.1661 11.5585C10.3526 11.3236 10.6228 11.17 10.92 11.13H18C18.2025 11.1267 18.4021 11.1779 18.578 11.2781ZM23.1 2L23.49 2.46L27.1 7.32V13.91C27.0953 14.0774 27.0487 14.2409 26.9644 14.3856C26.8801 14.5303 26.7608 14.6515 26.6175 14.7381C26.4742 14.8247 26.3114 14.8739 26.1441 14.8812C25.9768 14.8886 25.8103 14.8537 25.66 14.78L25.43 14.67C25.2508 14.5941 25.0977 14.4676 24.9893 14.306C24.8809 14.1443 24.8221 13.9546 24.82 13.76V8.85H4.28V24.85H12.87C13.0201 24.85 13.1687 24.8799 13.3071 24.9379C13.4455 24.9959 13.571 25.081 13.6762 25.188C13.7814 25.2951 13.8642 25.422 13.9198 25.5614C13.9754 25.7008 14.0027 25.8499 14 26C13.9974 26.2953 13.8782 26.5776 13.6684 26.7855C13.4587 26.9934 13.1753 27.11 12.88 27.11H3C2.73478 27.11 2.48043 27.0046 2.29289 26.8171C2.10536 26.6296 2 26.3752 2 26.11V7.32L5.64 2.46L6 2H23.1ZM7.19 4.28L5.49 6.56H23.71L22 4.28H7.19ZM22.05 27.75C25.3361 27.75 28 25.0861 28 21.8C28 18.5139 25.3361 15.85 22.05 15.85C18.7639 15.85 16.1 18.5139 16.1 21.8C16.1 25.0861 18.7639 27.75 22.05 27.75ZM19.77 24.96C19.8311 25.0214 19.9134 25.0572 20 25.06L24.03 25.1C24.1168 25.0982 24.1995 25.0623 24.26 25C24.3229 24.9358 24.3587 24.8499 24.36 24.76V21.4H25.07C25.1069 21.4005 25.1437 21.3937 25.178 21.38C25.2123 21.3663 25.2436 21.3459 25.27 21.32C25.3323 21.2739 25.3752 21.2061 25.39 21.13L25.65 19.89C25.6736 19.8186 25.6736 19.7414 25.65 19.67C25.6183 19.601 25.5609 19.5471 25.49 19.52L23.27 18.6C23.224 18.5889 23.176 18.5889 23.13 18.6H20.92C20.8739 18.5897 20.8261 18.5897 20.78 18.6L18.56 19.53C18.4884 19.5639 18.43 19.6205 18.3938 19.691C18.3576 19.7616 18.3457 19.842 18.36 19.92L18.65 21.12C18.6653 21.1932 18.7083 21.2577 18.77 21.3C18.8233 21.3446 18.8904 21.3694 18.96 21.37H19.67V24.72C19.6713 24.8099 19.7071 24.8958 19.77 24.96ZM23.74 21V24.39L20.36 24.34V20.99C20.3576 20.9076 20.3245 20.829 20.2671 20.7698C20.2098 20.7105 20.1323 20.6749 20.05 20.67H19.26L19.1 20L20.67 19.34C20.755 19.6364 20.9341 19.8971 21.1804 20.0827C21.4267 20.2682 21.7266 20.3686 22.035 20.3686C22.3433 20.3686 22.6433 20.2682 22.8896 20.0827C23.1358 19.8971 23.315 19.6364 23.4 19.34L25 20L24.84 20.67H24.07C23.9833 20.6725 23.9008 20.7081 23.8394 20.7694C23.7781 20.8308 23.7425 20.9133 23.74 21ZM21.1 19H23C22.9342 19.2001 22.807 19.3743 22.6364 19.4978C22.4658 19.6213 22.2606 19.6877 22.05 19.6877C21.8394 19.6877 21.6342 19.6213 21.4636 19.4978C21.293 19.3743 21.1657 19.2001 21.1 19Z"/>
</svg>',
		),

		array(
			'name'   => __( 'Archive Products', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'archiveproducts',
			'id'     => 64,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M23.92 9.11042L24.28 9.58042L27.88 15.6004V27.0004C27.88 27.5309 27.6693 28.0396 27.2942 28.4146C26.9191 28.7897 26.4104 29.0004 25.88 29.0004H5C4.46957 29.0004 3.96086 28.7897 3.58579 28.4146C3.21071 28.0396 3 27.5309 3 27.0004V15.5804L6.6 9.58042L6.96 9.11042H9.17V8.63042L7.27 8.54042C7.08286 8.53949 6.90213 8.47216 6.76 8.35042C6.60194 8.23572 6.48879 8.06952 6.44 7.88042L5.61 4.65042C5.55981 4.44099 5.58552 4.22055 5.68257 4.02829C5.77961 3.83603 5.9417 3.68442 6.14 3.60042L12.07 1.07042C12.1867 1.02036 12.313 0.996465 12.44 1.00042H18.44C18.5457 1.00483 18.6502 1.02506 18.75 1.06042L24.75 3.54042C24.9324 3.62339 25.0857 3.75927 25.19 3.93042C25.2838 4.12625 25.3152 4.34616 25.28 4.56042L24.57 7.88042C24.5335 8.07576 24.4313 8.25273 24.2803 8.3819C24.1293 8.51107 23.9386 8.58466 23.74 8.59042H21.82V9.11042H23.92ZM24.49 14.8504L22.79 11.3704H21.82V14.8504H24.49ZM17.1947 3.96325C17.6713 3.51554 17.9592 2.90302 18 2.25042H12.87C12.9108 2.90302 13.1987 3.51554 13.6753 3.96325C14.1518 4.41096 14.7811 4.66019 15.435 4.66019C16.0889 4.66019 16.7182 4.41096 17.1947 3.96325ZM7.5 5.00042L7.94 6.77042L10.07 6.87042C10.2912 6.88296 10.4995 6.97848 10.6534 7.1379C10.8073 7.29732 10.8953 7.50891 10.9 7.73042V14.8704H20.07V7.73042C20.0673 7.61402 20.0878 7.49826 20.1302 7.38983C20.1726 7.2814 20.2361 7.18246 20.3171 7.09875C20.398 7.01504 20.4947 6.94822 20.6016 6.90216C20.7086 6.8561 20.8236 6.83172 20.94 6.83042H23L23.38 4.97042L19.1 3.19042C18.8677 3.97974 18.3861 4.67265 17.7273 5.16552C17.0685 5.65839 16.2678 5.92475 15.445 5.92475C14.6222 5.92475 13.8215 5.65839 13.1627 5.16552C12.5039 4.67265 12.0223 3.97974 11.79 3.19042L7.5 5.00042ZM9.09 11.3704H8.09L6.44 14.8504H9.09V11.3704ZM5.26 26.7404H25.62V17.1204H5.26V26.7404ZM19.4062 19.5843C19.582 19.6827 19.7283 19.8264 19.8299 20.0004C19.9273 20.1682 19.9811 20.3577 19.9864 20.5516C19.9916 20.7455 19.9481 20.9377 19.8599 21.1104C19.7583 21.2845 19.612 21.4282 19.4362 21.5266C19.2604 21.6251 19.0614 21.6747 18.8599 21.6704H12.0799C11.783 21.6963 11.4875 21.6068 11.2548 21.4206C11.022 21.2344 10.8699 20.9658 10.8299 20.6704C10.8045 20.3757 10.8945 20.0827 11.0811 19.8532C11.2677 19.6237 11.5362 19.4757 11.8299 19.4404H18.8299C19.0314 19.4361 19.2304 19.4858 19.4062 19.5843Z"/>
			</svg>',
		),

		array(
			'name'   => __( 'Product Data Tabs', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productdatatabs',
			'id'     => 65,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.39 1H25.61C25.7925 1 25.9733 1.03595 26.1419 1.10581C26.3106 1.17566 26.4638 1.27805 26.5929 1.40712C26.722 1.5362 26.8243 1.68943 26.8942 1.85807C26.964 2.02671 27 2.20746 27 2.39V14.92C26.9975 15.0786 26.9545 15.234 26.875 15.3713C26.7956 15.5086 26.6823 15.6233 26.546 15.7045C26.4097 15.7856 26.2549 15.8306 26.0963 15.8351C25.9377 15.8395 25.7807 15.8034 25.64 15.73C25.4885 15.6535 25.3614 15.5362 25.273 15.3913C25.1847 15.2464 25.1386 15.0797 25.14 14.91V7.5H11.28C11.0333 7.5 10.7968 7.40202 10.6224 7.22761C10.448 7.0532 10.35 6.81665 10.35 6.57V2.86H2.92V25.16H14.92C15.0897 25.1586 15.2564 25.2047 15.4013 25.293C15.5462 25.3814 15.6635 25.5085 15.74 25.66C15.8091 25.8007 15.8416 25.9565 15.8343 26.1131C15.8271 26.2697 15.7804 26.4219 15.6986 26.5555C15.6168 26.6892 15.5025 26.8001 15.3664 26.8778C15.2303 26.9555 15.0767 26.9975 14.92 27H2.39C2.20746 27 2.02671 26.964 1.85807 26.8942C1.68943 26.8243 1.5362 26.722 1.40712 26.5929C1.27805 26.4638 1.17566 26.3106 1.10581 26.1419C1.03595 25.9733 1 25.7925 1 25.61V2.39C1 2.02135 1.14645 1.6678 1.40712 1.40712C1.6678 1.14645 2.02135 1 2.39 1ZM12.14 5.64H17.71V2.86H12.14V5.64ZM19.57 5.64H25.14V2.86H19.57V5.64ZM22.45 28.9C26.0122 28.9 28.9 26.0122 28.9 22.45C28.9 18.8878 26.0122 16 22.45 16C18.8878 16 16 18.8878 16 22.45C16 26.0122 18.8878 28.9 22.45 28.9ZM20 25.89C20.0657 25.9581 20.1555 25.9976 20.25 26L24.64 26.05C24.6871 26.0506 24.7338 26.0411 24.7769 26.0221C24.8201 26.0031 24.8586 25.9751 24.89 25.94C24.9581 25.8744 24.9976 25.7845 25 25.69V22.05H25.75C25.8304 22.0494 25.908 22.0211 25.97 21.97C26.0324 21.9154 26.0747 21.8415 26.09 21.76L26.38 20.42C26.4042 20.3384 26.4042 20.2516 26.38 20.17C26.3379 20.0994 26.275 20.0436 26.2 20.01L23.8 19.01H23.64H21.25C21.2006 18.9998 21.1495 18.9998 21.1 19.01L18.69 20.01C18.6106 20.0433 18.5457 20.104 18.5071 20.181C18.4686 20.2581 18.459 20.3464 18.48 20.43L18.79 21.73C18.8078 21.8102 18.854 21.8812 18.92 21.93C18.9799 21.9768 19.0541 22.0015 19.13 22H19.9V25.64C19.9008 25.7329 19.9365 25.8222 20 25.89ZM24.31 21.62V25.27L20.56 25.21V21.59C20.5601 21.4989 20.5246 21.4113 20.4611 21.346C20.3976 21.2806 20.3111 21.2426 20.22 21.24H19.37L19.19 20.51L20.92 19.78C20.9988 20.1188 21.1899 20.4209 21.4623 20.6372C21.7346 20.8535 22.0722 20.9713 22.42 20.9713C22.7678 20.9713 23.1054 20.8535 23.3778 20.6372C23.6501 20.4209 23.8413 20.1188 23.92 19.78L25.65 20.5L25.5 21.25H24.66C24.5654 21.2551 24.4764 21.2964 24.4112 21.3652C24.3461 21.434 24.3099 21.5252 24.31 21.62ZM21.41 19.42H23.48C23.48 19.6945 23.371 19.9578 23.1769 20.1519C22.9828 20.346 22.7195 20.455 22.445 20.455C22.1705 20.455 21.9073 20.346 21.7132 20.1519C21.5191 19.9578 21.41 19.6945 21.41 19.42Z"/>
</svg>
',
		),

		array(
			'name'   => __( 'Hotspot', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'hotspot',
			'id'     => 70,
			'icon'   => '
			<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M28 5C28 3.89543 27.1046 3 26 3H4C2.89543 3 2 3.89543 2 5V25C2 26.1046 2.89543 27 4 27H26C27.1046 27 28 26.1046 28 25V5ZM26 5H4V25H26V5Z" />
			<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5683 9L16.6873 17.5316L22.1406 13.0234L28 22.1853V24.7778C28 26.0051 27.1046 27 26 27H4C2.89543 27 2 26.0051 2 24.7778V21.0588L10.5683 9ZM4 21.8303V24.7778H26V22.926L21.8594 16.3105L16.3127 20.9132L10.4317 12.7784L4 21.8303Z" />
			<path d="M16 13.5C16 16.5376 13.5376 19 10.5 19C7.46243 19 5 16.5376 5 13.5C5 10.4624 7.46243 8 10.5 8C13.5376 8 16 10.4624 16 13.5Z" fill="#E5E5E5"/>
			<path fill-rule="evenodd" clip-rule="evenodd" d="M10.5 17.9C12.9301 17.9 14.9 15.9301 14.9 13.5C14.9 11.0699 12.9301 9.1 10.5 9.1C8.06995 9.1 6.1 11.0699 6.1 13.5C6.1 15.9301 8.06995 17.9 10.5 17.9ZM10.5 19C13.5376 19 16 16.5376 16 13.5C16 10.4624 13.5376 8 10.5 8C7.46243 8 5 10.4624 5 13.5C5 16.5376 7.46243 19 10.5 19Z" />
			<path d="M12.8 13.5C12.8 14.7703 11.7702 15.8 10.5 15.8C9.2297 15.8 8.19995 14.7703 8.19995 13.5C8.19995 12.2297 9.2297 11.2 10.5 11.2C11.7702 11.2 12.8 12.2297 12.8 13.5Z" fill="white" stroke="black" stroke-width="2"/>
			<path d="M24 15C24 16.6569 22.6569 18 21 18C19.3431 18 18 16.6569 18 15C18 13.3431 19.3431 12 21 12C22.6569 12 24 13.3431 24 15Z" fill="#E5E5E5"/>
			<path fill-rule="evenodd" clip-rule="evenodd" d="M21 17.1429C22.1835 17.1429 23.1429 16.1835 23.1429 15C23.1429 13.8165 22.1835 12.8571 21 12.8571C19.8165 12.8571 18.8571 13.8165 18.8571 15C18.8571 16.1835 19.8165 17.1429 21 17.1429ZM21 18C22.6569 18 24 16.6569 24 15C24 13.3431 22.6569 12 21 12C19.3431 12 18 13.3431 18 15C18 16.6569 19.3431 18 21 18Z" />
			<path d="M22.3001 15C22.3001 15.718 21.718 16.3 21.0001 16.3C20.2821 16.3 19.7001 15.718 19.7001 15C19.7001 14.282 20.2821 13.7 21.0001 13.7C21.718 13.7 22.3001 14.282 22.3001 15Z" fill="white" stroke="black"/>
			</svg>',
		),

		array(
			'name'   => __( 'Counter', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'counter',
			'id'     => 66,
			'icon'   => '
			<svg viewBox="0 0 30 30" fill="none" class="sp-w-6 sp-fill-current" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M3.48 1.23H26.48C26.81 1.22736 27.1372 1.29008 27.4429 1.41453C27.7485 1.53898 28.0264 1.72271 28.2607 1.95512C28.495 2.18753 28.6809 2.46402 28.8078 2.76865C28.9347 3.07327 29 3.40001 29 3.73V26.23C29 26.893 28.7366 27.5289 28.2678 27.9978C27.7989 28.4666 27.163 28.73 26.5 28.73H3.5C2.83696 28.73 2.20107 28.4666 1.73223 27.9978C1.26339 27.5289 1 26.893 1 26.23V3.73C0.999979 3.07041 1.26062 2.43754 1.72515 1.96927C2.18967 1.501 2.82043 1.23528 3.48 1.23ZM3.48 26.73H14V3.23H3.48C3.34739 3.23 3.22021 3.28268 3.12645 3.37645C3.03268 3.47021 2.98 3.59739 2.98 3.73V26.23C2.98 26.3626 3.03268 26.4898 3.12645 26.5836C3.22021 26.6773 3.34739 26.73 3.48 26.73ZM26.8536 26.5836C26.9473 26.4898 27 26.3626 27 26.23V3.73C27 3.59739 26.9473 3.47021 26.8536 3.37645C26.7598 3.28268 26.6326 3.23 26.5 3.23H16V26.73H26.5C26.6326 26.73 26.7598 26.6773 26.8536 26.5836ZM22.17 7.59999C21.7359 7.5704 21.3023 7.66349 20.9185 7.8686C20.5348 8.07372 20.2165 8.3826 20 8.75998H19.91C19.9193 8.12827 20.0588 7.50527 20.32 6.92999C20.5378 6.49826 20.8883 6.14776 21.32 5.92998C21.8792 5.69426 22.4836 5.58499 23.09 5.60998C23.4936 5.60867 23.8961 5.65227 24.29 5.73998V4.07998C23.8631 4.01649 23.4315 3.98973 23 3.99998C22.3065 3.94785 21.61 4.05094 20.9614 4.30177C20.3127 4.55259 19.728 4.94484 19.25 5.44998C18.323 6.72115 17.8797 8.2813 18 9.84999C17.9789 10.6631 18.1323 11.4712 18.45 12.22C18.7174 12.8335 19.159 13.355 19.72 13.72C20.3026 14.068 20.9715 14.2448 21.65 14.23C22.1107 14.2564 22.5719 14.1864 23.0041 14.0245C23.4362 13.8627 23.8299 13.6125 24.16 13.29C24.4701 12.9489 24.7094 12.5496 24.8639 12.1153C25.0184 11.681 25.085 11.2203 25.06 10.76C25.1065 9.91872 24.8353 9.09068 24.3 8.43998C24.0282 8.15246 23.6965 7.92818 23.3284 7.78302C22.9604 7.63787 22.5649 7.57538 22.17 7.59999ZM22.62 12.08C22.5 12.2285 22.3473 12.3472 22.1738 12.427C22.0004 12.5068 21.8108 12.5455 21.62 12.54C21.4032 12.5498 21.1873 12.5056 20.9918 12.4113C20.7964 12.3171 20.6273 12.1757 20.5 12C20.2046 11.6057 20.0529 11.1223 20.07 10.63C20.0675 10.4396 20.1074 10.251 20.187 10.078C20.2666 9.90502 20.3838 9.75196 20.53 9.62998C20.8273 9.35864 21.2175 9.21186 21.62 9.21999C21.8075 9.21039 21.9948 9.24316 22.1679 9.31587C22.341 9.38858 22.4956 9.49936 22.62 9.63999C22.875 9.96831 23.0091 10.3744 23 10.79C23.026 11.251 22.8918 11.7067 22.62 12.08ZM12.1625 18.3117C11.9885 18.7014 11.7236 19.0438 11.39 19.31C10.5112 19.9059 9.45986 20.1942 8.40001 20.13C7.40995 20.1487 6.42651 19.965 5.51001 19.59V17.79C5.91794 17.9961 6.34715 18.1571 6.79001 18.27C7.24068 18.3875 7.70429 18.4479 8.17001 18.45C8.70986 18.4884 9.24904 18.3667 9.72001 18.1C9.89069 17.9658 10.0256 17.7915 10.1128 17.5927C10.2 17.3938 10.2369 17.1765 10.22 16.96C10.2388 16.7538 10.193 16.5468 10.0892 16.3677C9.98526 16.1886 9.82832 16.0461 9.64001 15.96C9.06045 15.7233 8.43483 15.6207 7.81001 15.66H7.05001V14.04H7.86001C8.4426 14.079 9.02594 13.976 9.56001 13.74C9.73329 13.6391 9.87538 13.4924 9.97065 13.316C10.0659 13.1395 10.1107 12.9402 10.1 12.74C10.1 11.98 9.63001 11.6 8.68001 11.6C8.3398 11.6037 8.00231 11.661 7.68001 11.77C7.2817 11.9094 6.90216 12.0975 6.55001 12.33L5.55001 10.88C6.50047 10.204 7.64396 9.85321 8.81001 9.88001C9.67195 9.83388 10.5272 10.0538 11.26 10.51C11.5482 10.6994 11.7824 10.96 11.9401 11.2667C12.0978 11.5733 12.1736 11.9155 12.16 12.26C12.1752 12.8456 11.972 13.4159 11.59 13.86C11.1665 14.318 10.6107 14.6326 10 14.76C10.651 14.8157 11.269 15.0706 11.77 15.49C11.9829 15.6918 12.1488 15.9379 12.2559 16.211C12.3631 16.484 12.4088 16.7773 12.39 17.07C12.4145 17.4961 12.3365 17.9219 12.1625 18.3117ZM22.79 17.76H17.86V15.98H25.03V17.31L21.25 25.96H19.03L22.79 17.76Z"/>
</svg>
            ',
		),

		array(
			'name'   => __( 'Product Gallery Images', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'woocommercetemplatetag',
			'type'   => 'productgalleryimages',
			'id'     => 71,
			'icon'   => '
			<svg viewBox="0 0 30 30" fill="none" class="sp-w-6 sp-fill-current" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M5 5.5V20.5C5 20.8978 5.15804 21.2794 5.43934 21.5607C5.72064 21.842 6.10218 22 6.5 22H23.5C23.8978 22 24.2794 21.842 24.5607 21.5607C24.842 21.2794 25 20.8978 25 20.5V5.5C25 5.10218 24.842 4.72064 24.5607 4.43934C24.2794 4.15804 23.8978 4 23.5 4H6.5C6.10218 4 5.72064 4.15804 5.43934 4.43934C5.15804 4.72064 5 5.10218 5 5.5ZM23 20H7V6H23V20ZM1 13.91L3.15 16.06V11.76L1 13.91ZM26.85 11.76L29 13.91L26.85 16.06V11.76ZM11 25.76C11.5523 25.76 12 25.3123 12 24.76C12 24.2077 11.5523 23.76 11 23.76C10.4477 23.76 10 24.2077 10 24.76C10 25.3123 10.4477 25.76 11 25.76ZM15 25.76C15.5523 25.76 16 25.3123 16 24.76C16 24.2077 15.5523 23.76 15 23.76C14.4477 23.76 14 24.2077 14 24.76C14 25.3123 14.4477 25.76 15 25.76ZM20 24.76C20 25.3123 19.5523 25.76 19 25.76C18.4477 25.76 18 25.3123 18 24.76C18 24.2077 18.4477 23.76 19 23.76C19.5523 23.76 20 24.2077 20 24.76ZM11.6901 18C11.6187 18 11.5481 17.9857 11.4825 17.9578C11.4169 17.9299 11.3575 17.8891 11.308 17.8378C11.2585 17.7864 11.2199 17.7256 11.1945 17.659C11.1691 17.5923 11.1574 17.5213 11.1601 17.45V12L10.0001 11.93C9.88455 11.9217 9.77444 11.8779 9.6849 11.8044C9.59535 11.731 9.53077 11.6316 9.50005 11.52L9.00005 9.55999C8.97229 9.43281 8.9889 9.29996 9.04712 9.18353C9.10533 9.06709 9.20165 8.97409 9.32005 8.91999L13.0001 7.37999C13.0759 7.36408 13.1542 7.36408 13.2301 7.37999H16.8301C16.9091 7.36296 16.991 7.36296 17.0701 7.37999L20.7001 8.88999C20.8126 8.9364 20.9048 9.02153 20.9601 9.12999C21.0191 9.24375 21.0401 9.37342 21.0201 9.49999L20.5901 11.5C20.5684 11.6264 20.5007 11.7404 20.4001 11.82C20.3108 11.8963 20.1975 11.9388 20.0801 11.94H18.8801V17.43C18.8827 17.5026 18.8698 17.575 18.8422 17.6423C18.8146 17.7096 18.773 17.7701 18.7201 17.82C18.6725 17.8704 18.6151 17.9106 18.5515 17.9381C18.4879 17.9656 18.4194 17.9799 18.3501 17.98L11.6901 18ZM17.8101 16.9V11.42C17.8087 11.3482 17.8217 11.2769 17.8483 11.2103C17.8748 11.1436 17.9144 11.0829 17.9646 11.0317C18.0149 10.9805 18.0749 10.9398 18.1411 10.912C18.2072 10.8843 18.2783 10.87 18.3501 10.87H19.6001L19.8401 9.73999L17.2301 8.62999C17.0911 9.11415 16.7984 9.53995 16.3961 9.84304C15.9938 10.1461 15.5038 10.3101 15.0001 10.3101C14.4963 10.3101 14.0063 10.1461 13.604 9.84304C13.2017 9.53995 12.909 9.11415 12.7701 8.62999L10.1601 9.74999L10.4301 10.85L11.7201 10.91C11.8554 10.9151 11.9837 10.9718 12.0785 11.0685C12.1734 11.1652 12.2276 11.2946 12.2301 11.43V16.9H17.8101ZM16.5601 8.10001H13.4301C13.4615 8.49347 13.6401 8.86063 13.9301 9.12838C14.2201 9.39613 14.6003 9.54481 14.9951 9.54481C15.3898 9.54481 15.77 9.39613 16.06 9.12838C16.35 8.86063 16.5286 8.49347 16.5601 8.10001Z"/>
</svg>
',
		),

		array(
			'name'   => __( 'Downloads Grid', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'edd',
			'type'   => 'edddownloadsgrid',
			'id'     => 72,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M27 2.00001H3C2.4887 1.98178 1.99104 2.16704 1.61613 2.51517C1.24121 2.86331 1.01964 3.3459 1 3.85715V26.1429C1.01965 26.6541 1.24123 27.1367 1.61614 27.4848C1.99105 27.833 2.48871 28.0182 3 28H27C27.5113 28.0182 28.0089 27.833 28.3839 27.4848C28.7588 27.1367 28.9803 26.6541 29 26.1429V3.85715C28.9804 3.3459 28.7588 2.86331 28.3839 2.51517C28.009 2.16704 27.5113 1.98178 27 2.00001ZM26.8149 25.9711H3.18506V4.02894H26.8149V25.9711ZM5 7V19H14V7H5ZM12 17H7V9H12V17ZM16 19V7H25V19H16ZM18 17H23V9H18V17ZM14 21H5V23H14V21ZM16 21H25V23H16V21Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'edd',
			'type'   => 'eddcart',
			'id'     => 73,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.07 16.39V26.86H5.92989V8.72H9.76996V11.86C9.76664 11.9979 9.79055 12.1352 9.84031 12.2639C9.89007 12.3926 9.96471 12.5102 10.06 12.61C10.2068 12.7599 10.3953 12.8624 10.601 12.9042C10.8067 12.946 11.0202 12.9252 11.2139 12.8445C11.4077 12.7638 11.5728 12.6268 11.6879 12.4513C11.8031 12.2758 11.863 12.0699 11.86 11.86V8.72H13.01C13.0279 8.01036 13.1458 7.30678 13.36 6.63H11.86V6.28C11.8556 5.86644 11.9339 5.45618 12.0901 5.07326C12.2464 4.69033 12.4775 4.34245 12.77 4.05001C13.0624 3.75756 13.4103 3.52643 13.7932 3.37017C14.1761 3.21391 14.5864 3.13566 15 3.14C15.1741 3.138 15.3481 3.15139 15.52 3.18C16.1502 2.59144 16.8689 2.10552 17.65 1.74C16.8459 1.27315 15.9296 1.03458 15 1.05C14.3104 1.04021 13.626 1.16879 12.987 1.42816C12.348 1.68752 11.7676 2.07239 11.28 2.56001C10.7924 3.04762 10.4075 3.62808 10.1481 4.26704C9.88875 4.90599 9.76016 5.59048 9.76996 6.28V6.63H5.92989C5.65617 6.62821 5.38507 6.68328 5.13375 6.79173C4.88243 6.90018 4.65637 7.05963 4.46989 7.26C4.26955 7.4465 4.11012 7.67256 4.00169 7.92388C3.89327 8.1752 3.83821 8.4463 3.84001 8.72V26.86C3.83911 27.135 3.89454 27.4074 4.00288 27.6602C4.11122 27.913 4.27018 28.1409 4.46996 28.33C4.65836 28.5272 4.88501 28.6839 5.13604 28.7905C5.38708 28.8971 5.65723 28.9514 5.92996 28.95H24.07C24.6207 28.9384 25.1455 28.7145 25.535 28.325C25.9244 27.9355 26.1483 27.4106 26.1599 26.86V15.1C25.5311 15.6314 24.8268 16.0661 24.07 16.39ZM21 2C19.6155 2 18.2622 2.41054 17.111 3.17971C15.9599 3.94888 15.0627 5.04213 14.5328 6.32122C14.003 7.6003 13.8644 9.00776 14.1345 10.3656C14.4046 11.7235 15.0713 12.9708 16.0503 13.9497C17.0292 14.9287 18.2765 15.5954 19.6344 15.8655C20.9922 16.1356 22.3997 15.997 23.6788 15.4672C24.9579 14.9373 26.0511 14.0401 26.8203 12.889C27.5895 11.7378 28 10.3845 28 9C27.998 7.14411 27.2598 5.36482 25.9475 4.0525C24.6352 2.74019 22.8559 2.00204 21 2ZM21 14C20.0111 14 19.0444 13.7068 18.2222 13.1573C17.3999 12.6079 16.759 11.827 16.3806 10.9134C16.0022 9.99979 15.9031 8.99445 16.0961 8.02455C16.289 7.05464 16.7652 6.16373 17.4645 5.46447C18.1637 4.7652 19.0546 4.289 20.0246 4.09607C20.9945 3.90315 21.9998 4.00216 22.9134 4.3806C23.8271 4.75904 24.6079 5.3999 25.1574 6.22215C25.7068 7.04439 26 8.01109 26 9C25.9995 10.3259 25.4726 11.5974 24.535 12.535C23.5974 13.4726 22.3259 13.9995 21 14ZM20.8097 12H22.3977V6H20.9879L19 7.67852L19.8118 8.71272L20.4691 8.15048C20.6002 8.03139 20.722 7.90242 20.8334 7.76471C20.8176 8.08482 20.8097 8.43776 20.8097 8.82353V12Z" fill="evenodd"/>
</svg>
',
		),

		array(
			'name'   => __( 'Checkout', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'edd',
			'type'   => 'eddcheckout',
			'id'     => 74,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 2H26C26.53 2.00155 27.0378 2.21276 27.4125 2.5875C27.7872 2.96224 27.9984 3.47004 28 4V10C27.9984 10.53 27.7872 11.0378 27.4125 11.4125C27.0378 11.7872 26.53 11.9984 26 12H23.3301L21.9199 14.53C21.6736 14.9747 21.3095 15.3429 20.8676 15.5942C20.5111 15.7927 20.1089 15.8946 19.7009 15.89H9.88L8.06006 19.28H23.065C23.3236 19.28 23.5716 19.3827 23.7544 19.5656C23.9373 19.7484 24.04 19.9964 24.04 20.255C24.04 20.5136 23.9373 20.7616 23.7544 20.9444C23.5716 21.1273 23.3236 21.23 23.065 21.23H8.35C7.97218 21.2636 7.59217 21.1972 7.24813 21.0375C6.90409 20.8778 6.60814 20.6303 6.39 20.32C6.19051 20.0149 6.08511 19.658 6.08688 19.2935C6.08866 18.929 6.19755 18.5731 6.4 18.27L8.48 14.43L3.54 3.95H1.975C1.71641 3.95 1.46842 3.84728 1.28557 3.66443C1.19503 3.57389 1.12322 3.46641 1.07422 3.34812C1.02522 3.22982 1 3.10304 1 2.975C1 2.71641 1.10272 2.46842 1.28557 2.28557C1.46842 2.10272 1.71641 2 1.975 2H4.81006L6.01 4.57L6.99 6.52L10.5701 13.94H19.9399L21 12H14C13.47 11.9984 12.9622 11.7872 12.5875 11.4125C12.2128 11.0378 12.0016 10.53 12 10V4C12.0016 3.47004 12.2128 2.96224 12.5875 2.5875C12.9622 2.21276 13.47 2.00155 14 2ZM14 10H26V4H14V5H22V7H14V10ZM24.04 25.66C24.0448 25.9692 23.986 26.2761 23.8673 26.5616C23.7487 26.8472 23.5726 27.1053 23.35 27.32C23.1334 27.5393 22.8747 27.7126 22.5894 27.8295C22.3042 27.9464 21.9982 28.0043 21.69 28C21.3822 28.0057 21.0765 27.9473 20.7925 27.8285C20.5084 27.7097 20.2522 27.5331 20.0401 27.31C19.8175 27.0953 19.6414 26.8372 19.5227 26.5516C19.404 26.2661 19.3452 25.9592 19.35 25.65C19.3443 25.3422 19.4027 25.0365 19.5215 24.7524C19.6403 24.4684 19.8168 24.2121 20.04 24C20.2576 23.7813 20.5163 23.6078 20.8011 23.4894C21.086 23.3709 21.3915 23.31 21.7 23.31C22.0085 23.31 22.314 23.3709 22.5988 23.4894C22.8837 23.6078 23.1424 23.7813 23.36 24C23.5786 24.2172 23.7514 24.476 23.8682 24.7611C23.985 25.0462 24.0434 25.3519 24.04 25.66ZM11.02 25.66C11.0248 25.9692 10.966 26.2761 10.8474 26.5616C10.7287 26.8472 10.5526 27.1053 10.3301 27.32C10.1134 27.5393 9.85471 27.7126 9.56943 27.8295C9.28414 27.9463 8.97819 28.0043 8.66993 28C8.3626 27.9988 8.05852 27.9371 7.77506 27.8184C7.4916 27.6996 7.23432 27.5261 7.01794 27.3079C6.80157 27.0896 6.63033 26.8309 6.51402 26.5464C6.39771 26.2619 6.33861 25.9573 6.3401 25.65C6.33439 25.3422 6.39278 25.0365 6.51155 24.7525C6.63032 24.4684 6.80688 24.2121 7.03001 24C7.24663 23.7814 7.50442 23.6078 7.78849 23.4894C8.07256 23.371 8.37728 23.31 8.68504 23.31C8.99281 23.31 9.29753 23.371 9.5816 23.4894C9.86566 23.6078 10.1235 23.7814 10.3401 24C10.5586 24.2172 10.7314 24.476 10.8482 24.7611C10.965 25.0462 11.0234 25.3519 11.02 25.66Z" fill="evenodd"/>
</svg>',
		),

		array(
			'name'   => __( 'Buy Now Button', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'edd',
			'type'   => 'eddbuynowbutton',
			'id'     => 75,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15 13L9.81335 7.6301H13.2542V3.76867C13.2542 3.09431 14.0386 2.51986 15 2.51986C15.9614 2.51986 16.7457 3.06934 16.7457 3.76867V7.6301H20.1866L15 13Z" fill="evenodd"/>
<path d="M4.04639 19.4145H5.15483C5.48676 19.3926 5.81857 19.459 6.11657 19.6068C6.21864 19.6684 6.30187 19.7568 6.35723 19.8624C6.41259 19.9679 6.43796 20.0866 6.43057 20.2056C6.43523 20.3685 6.38722 20.5286 6.29365 20.662C6.20735 20.7847 6.07893 20.8714 5.93286 20.9055V20.9272C6.10159 20.9616 6.25302 21.0539 6.36103 21.188C6.4547 21.3277 6.50116 21.4938 6.4936 21.6618C6.49869 21.7932 6.47178 21.9239 6.41518 22.0426C6.35858 22.1613 6.27399 22.2644 6.16868 22.3432C5.90915 22.5219 5.59764 22.6094 5.28302 22.592H4.04639V19.4145ZM4.90489 20.6338H5.16353C5.26452 20.64 5.3648 20.6133 5.44933 20.5577C5.4833 20.5311 5.51026 20.4966 5.52787 20.4572C5.54548 20.4179 5.55321 20.3748 5.55039 20.3317C5.55039 20.1535 5.41564 20.0644 5.14614 20.0644H4.90489V20.6338ZM4.90489 21.2619V21.9291H5.20702C5.47508 21.9291 5.60911 21.8161 5.60911 21.5901C5.61177 21.5439 5.60341 21.4977 5.5847 21.4554C5.56599 21.4131 5.53748 21.3758 5.50152 21.3467C5.40996 21.2843 5.30015 21.2545 5.18964 21.2619H4.90489Z" fill="evenodd"/>
<path d="M9.74723 19.4145V21.3271C9.75818 21.5038 9.73246 21.6808 9.67168 21.8471C9.6109 22.0133 9.51637 22.1652 9.39405 22.2932C9.11339 22.5377 8.74717 22.6608 8.37581 22.6355C8.01221 22.6597 7.65363 22.5403 7.37713 22.303C7.25561 22.1766 7.16182 22.0263 7.10175 21.8616C7.04168 21.697 7.01666 21.5216 7.02829 21.3467V19.4145H7.89114V21.2793C7.87651 21.4519 7.92096 21.6243 8.0172 21.7683C8.11827 21.8657 8.25313 21.9202 8.39352 21.9204C8.5339 21.9206 8.66892 21.8665 8.77028 21.7694C8.86188 21.6216 8.90341 21.4482 8.88873 21.2749V19.4145H9.74723Z" fill="evenodd"/>
<path d="M11.549 20.5925L12.0967 19.4145H13.0269L11.9815 21.351V22.592H11.1165V21.3771L10.071 19.4145H11.0056L11.549 20.5925Z" fill="evenodd"/>
<path d="M17.552 22.592H16.4261L15.2655 20.3534H15.246C15.2735 20.7055 15.2873 20.9743 15.2873 21.1597V22.592H14.5266V19.4145H15.648L16.8043 21.6227H16.8173C16.797 21.3025 16.7868 21.0453 16.7868 20.8511V19.4145H17.5518L17.552 22.592Z" fill="evenodd"/>
<path d="M21.212 20.9989C21.2434 21.4409 21.1024 21.8779 20.8186 22.2182C20.6665 22.3645 20.4854 22.4772 20.2869 22.5491C20.0885 22.621 19.8772 22.6504 19.6667 22.6355C19.4569 22.6494 19.2465 22.6193 19.0491 22.547C18.8516 22.4748 18.6715 22.362 18.5202 22.216C18.2333 21.8764 18.0902 21.4381 18.1214 20.9946C18.0909 20.5545 18.2332 20.1198 18.5181 19.7829C18.6704 19.6366 18.8517 19.5238 19.0503 19.4517C19.2489 19.3796 19.4603 19.3499 19.6711 19.3645C19.8811 19.3494 20.0919 19.3786 20.29 19.4501C20.488 19.5216 20.6688 19.6339 20.8208 19.7796C21.1037 20.1203 21.2439 20.5572 21.212 20.9989ZM19.0255 20.9989C19.0255 21.6133 19.2393 21.9204 19.6667 21.9204C19.7597 21.927 19.8528 21.91 19.9373 21.8708C20.0219 21.8317 20.0952 21.7717 20.1503 21.6966C20.2544 21.4777 20.3082 21.2383 20.3079 20.9959C20.3075 20.7536 20.2529 20.5144 20.1481 20.2958C20.0944 20.2206 20.0224 20.1603 19.9389 20.1206C19.8555 20.0808 19.7633 20.063 19.6711 20.0687C19.2407 20.0687 19.0255 20.3788 19.0255 20.9989Z" fill="evenodd"/>
<path d="M25.2175 22.592H24.2025L23.8982 21.2075C23.8838 21.1496 23.8609 21.0344 23.8298 20.862C23.7986 20.6896 23.7758 20.5454 23.7613 20.4295C23.7497 20.5236 23.7309 20.6406 23.7048 20.7805C23.6787 20.9203 23.653 21.0489 23.6277 21.1662C23.6023 21.2836 23.4969 21.7588 23.3114 22.592H22.2964L21.5096 19.4145H22.3377L22.6833 21.0076C22.7615 21.3597 22.8151 21.64 22.8441 21.8487C22.863 21.7009 22.8966 21.5002 22.9452 21.2467C22.9937 20.9932 23.039 20.7831 23.081 20.6164L23.3614 19.4145H24.1569L24.4285 20.6164C24.4749 20.8091 24.522 21.0315 24.5698 21.2836C24.6176 21.5358 24.6495 21.7241 24.6654 21.8487C24.6843 21.6879 24.7357 21.409 24.8198 21.012L25.1718 19.4145H26L25.2175 22.592Z" fill="evenodd"/>
<path d="M1 17V25C1 25.5304 1.21071 26.0391 1.58579 26.4142C1.96086 26.7893 2.46957 27 3 27H27C27.5304 27 28.0391 26.7893 28.4142 26.4142C28.7893 26.0391 29 25.5304 29 25V17C29 16.4696 28.7893 15.9609 28.4142 15.5858C28.0391 15.2107 27.5304 15 27 15H3C2.46957 15 1.96086 15.2107 1.58579 15.5858C1.21071 15.9609 1 16.4696 1 17ZM27 25H3V17H27V25Z" fill="evenodd"/>
</svg>',
		),

		array(
			'name'   => __( 'Download Title', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadtitle',
			'id'     => 76,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M26.1699 16H27C27.53 16.0015 28.0378 16.2128 28.4125 16.5875C28.7872 16.9622 28.9985 17.47 29 18V26C28.9984 26.5299 28.7872 27.0378 28.4125 27.4125C28.0378 27.7872 27.53 27.9984 27 28H3C2.47005 27.9984 1.96224 27.7872 1.58751 27.4125C1.21277 27.0378 1.00156 26.5299 1 26V18C1.00155 17.47 1.21276 16.9622 1.5875 16.5875C1.96223 16.2128 2.47004 16.0015 3 16H3.83008L11.65 8.18C11.2562 7.57646 11.0328 6.87776 11.0033 6.15772C10.9739 5.43768 11.1395 4.72306 11.4827 4.08939C11.8259 3.45571 12.3339 2.92651 12.953 2.55772C13.5721 2.18893 14.2794 1.99425 15 1.99425C15.7206 1.99425 16.4279 2.18893 17.047 2.55772C17.6661 2.92651 18.1741 3.45571 18.5173 4.08939C18.8605 4.72306 19.0261 5.43768 18.9967 6.15772C18.9672 6.87776 18.7438 7.57646 18.35 8.18L26.1699 16ZM16.3334 4.00447C15.9387 3.74075 15.4747 3.6 15 3.6C14.6846 3.59923 14.3722 3.66079 14.0807 3.78113C13.7891 3.90147 13.5243 4.07822 13.3012 4.30124C13.0782 4.52425 12.9015 4.78913 12.7811 5.08065C12.6608 5.37218 12.5992 5.68461 12.6 6C12.6 6.47467 12.7408 6.93869 13.0045 7.33337C13.2682 7.72804 13.643 8.03566 14.0816 8.21731C14.5201 8.39896 15.0027 8.44649 15.4682 8.35388C15.9338 8.26128 16.3614 8.0327 16.6971 7.69705C17.0327 7.36141 17.2613 6.93377 17.3539 6.46821C17.4465 6.00266 17.399 5.5201 17.2173 5.08156C17.0357 4.64301 16.728 4.26818 16.3334 4.00447ZM15 10C14.3487 10 13.707 9.84217 13.13 9.54L6.66992 16H23.3301L16.87 9.54C16.293 9.84217 15.6513 10 15 10ZM3.33008 18V26H26.6699V18H3.33008ZM8.28125 24H9.43945L9.43942 20.6631H10.4824V19.7168H7.23633V20.6631H8.28125V24ZM11.0938 24V19.7168H12.2578V24H11.0938ZM13.916 24H15.0723L15.0722 20.6631H16.1152V19.7168H12.8691V20.6631H13.916V24ZM16.7285 24V19.7168H17.8848V23.0654H19.5352V24H16.7285ZM20.2207 24H22.7637L22.7638 23.0566H21.377V22.248H22.6602V21.3193H21.377V20.6455H22.7637V19.7168H20.2207V24Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Download Featured Image', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadfeaturedimage',
			'id'     => 77,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M14.0005 7.24255L20.9973 14.4866L27.9941 7.24255H23.3523V3.67105C23.3523 2.72764 22.2942 1.98639 20.9973 1.98639C19.7004 1.98639 18.6423 2.76133 18.6423 3.67105V7.24255H14.0005ZM3.34635 27.8456C3.60708 27.9574 3.88792 28.0145 4.17158 28.0136L25.8285 28.0136C26.3985 27.9997 26.9413 27.7671 27.3445 27.3639C27.7476 26.9607 27.9803 26.4179 27.9942 25.8479V18.6804C27.9942 18.5382 27.9662 18.3974 27.9117 18.266C27.8573 18.1347 27.7776 18.0153 27.677 17.9148C27.5256 17.7633 27.3326 17.6602 27.1226 17.6184C26.9125 17.5766 26.6948 17.5981 26.4969 17.68C26.2991 17.762 26.13 17.9008 26.011 18.0788C25.892 18.2569 25.8285 18.4663 25.8285 18.6804V25.8479H4.17158V4.191H10.7769C10.9191 4.191 11.06 4.16299 11.1913 4.10857C11.3227 4.05415 11.4421 3.97439 11.5426 3.87384C11.6432 3.77328 11.7229 3.65391 11.7774 3.52253C11.8318 3.39115 11.8598 3.25034 11.8598 3.10814C11.8598 2.82096 11.7457 2.54553 11.5426 2.34246C11.3396 2.13939 11.0641 2.0253 10.7769 2.0253H4.17158C3.88792 2.02438 3.60708 2.08154 3.34635 2.19328C3.08563 2.30502 2.85054 2.46896 2.65558 2.675C2.44953 2.86996 2.28559 3.10505 2.17385 3.36577C2.06211 3.6265 2.00495 3.90734 2.00587 4.191V25.8479C2.00495 26.1316 2.06211 26.4124 2.17385 26.6731C2.28559 26.9339 2.44953 27.169 2.65558 27.3639C2.85054 27.57 3.08563 27.7339 3.34635 27.8456ZM10.4521 17.5497L6.22903 23H23.8072L18.5013 15.9616L13.8089 22.0977L10.4521 17.5497Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Download Price', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadprice',
			'id'     => 78,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3.52588 8.11024L8.71252 13.4801L13.8991 8.11024H10.4583V4.24881C10.4583 3.54948 9.67398 3 8.71252 3C7.75106 3 6.96677 3.57445 6.96677 4.24881V8.11024H3.52588ZM14.899 22.0603C16.2288 23.2363 17.8847 23.9801 19.6471 24.1931V27H22.2649V24.081C23.505 23.8544 24.6188 23.1804 25.3947 22.1869C26.1706 21.1933 26.5547 19.9495 26.4741 18.6914C26.4741 15.5476 24.0241 14.3246 21.9976 13.313C20.5685 12.5996 19.3501 11.9913 19.3501 10.8888C19.3501 9.62546 20.0519 9.09225 21.3476 9.09225C21.908 9.11088 22.4585 9.2451 22.9646 9.48649C23.4708 9.72787 23.9215 10.0712 24.2887 10.495L26.2849 8.13659C25.2652 6.91119 23.838 6.0943 22.2649 5.83559V3H19.6471V5.89165C18.445 6.10215 17.3624 6.74787 16.6059 7.70561C15.8494 8.66335 15.472 9.86607 15.5457 11.0843C15.5457 13.9615 17.8552 15.1823 19.8443 16.2337C21.3486 17.0289 22.6697 17.7271 22.6697 18.9717C22.6697 20.2638 21.9942 20.9091 20.4842 20.9091C19.0495 20.8244 17.6874 20.2496 16.6258 19.2807L14.899 22.0603Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Download Content', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadcontent',
			'id'     => 79,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M3.95002 25C3.69412 25.0006 3.44085 24.9485 3.20599 24.8469C2.97114 24.7453 2.75975 24.5963 2.58502 24.4094C2.39914 24.2314 2.25143 24.0174 2.15086 23.7805C2.05029 23.5435 1.99897 23.2886 2.00002 23.0313V5.96876C1.99897 5.71138 2.05029 5.45648 2.15086 5.21956C2.25143 4.98264 2.39914 4.76866 2.58502 4.59063C2.75975 4.40368 2.97114 4.25475 3.20599 4.15313C3.44085 4.05151 3.69412 3.99939 3.95002 4.00001H10.025C10.2848 4.00125 10.5335 4.10566 10.7164 4.29027C10.8992 4.47487 11.0013 4.72455 11 4.98438C11.0013 5.2442 10.8992 5.49388 10.7164 5.67848C10.5335 5.86309 10.2848 5.9675 10.025 5.96875H3.95002V23.0312H26.05V5.96876H19.975C19.7152 5.96751 19.4665 5.8631 19.2837 5.67849C19.1008 5.49389 18.9988 5.24421 19 4.98439C18.9988 4.72456 19.1008 4.47488 19.2836 4.29027C19.4665 4.10567 19.7152 4.00125 19.975 4.00001H26.05C26.3059 3.99939 26.5592 4.05151 26.794 4.15313C27.0289 4.25475 27.2403 4.40368 27.415 4.59063C27.6009 4.76866 27.7486 4.98264 27.8492 5.21956C27.9497 5.45648 28.0011 5.71138 28 5.96876V23.0313C28.0011 23.2886 27.9497 23.5435 27.8492 23.7805C27.7486 24.0174 27.6009 24.2314 27.415 24.4094C27.2403 24.5963 27.0289 24.7453 26.794 24.8469C26.5592 24.9485 26.3059 25.0006 26.05 25H3.95002ZM15.1866 18.7196L10 13.4501H13.4409V5.22548C13.4409 4.56373 14.2252 4.00001 15.1867 4.00001C16.1481 4.00001 16.9324 4.53922 16.9324 5.22548V13.4501H20.3733L15.1866 18.7196Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Download Add To Cart', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'eddaddtocart',
			'id'     => 80,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.6267 6.09339L17.8133 11.4633L23 6.09339H19.5591V2.232C19.5591 1.53266 18.7748 0.983189 17.8133 0.983189C16.8519 0.983189 16.0676 1.55764 16.0676 2.232V6.09339H12.6267ZM8.90779 23.8868C9.36895 23.5799 9.9108 23.4167 10.4647 23.4179C10.8321 23.4178 11.1959 23.4902 11.5353 23.6309C11.8746 23.7717 12.1829 23.9779 12.4423 24.238C12.7018 24.4981 12.9075 24.8068 13.0475 25.1464C13.1875 25.4861 13.2591 25.85 13.2582 26.2174C13.2582 26.7713 13.0939 27.3128 12.786 27.7733C12.4781 28.2338 12.0405 28.5926 11.5286 28.8043C11.0167 29.0161 10.4535 29.0711 9.91033 28.9626C9.36712 28.8541 8.86831 28.5869 8.47703 28.1948C8.08576 27.8027 7.81961 27.3033 7.71227 26.7598C7.60493 26.2164 7.66123 25.6533 7.87405 25.1419C8.08686 24.6304 8.44662 24.1936 8.90779 23.8868ZM22.875 23.8867C23.3362 23.5799 23.878 23.4167 24.432 23.4179C24.7993 23.4178 25.1631 23.4902 25.5025 23.6309C25.8418 23.7717 26.1501 23.978 26.4096 24.238C26.6691 24.4981 26.8747 24.8068 27.0147 25.1464C27.1547 25.4861 27.2263 25.85 27.2254 26.2174C27.2254 26.7713 27.0611 27.3128 26.7532 27.7733C26.4453 28.2338 26.0077 28.5926 25.4958 28.8043C24.984 29.0161 24.4208 29.0711 23.8775 28.9626C23.3343 28.8541 22.8355 28.5869 22.4442 28.1948C22.053 27.8027 21.7868 27.3033 21.6795 26.7598C21.5722 26.2164 21.6285 25.6533 21.8413 25.1419C22.0541 24.6304 22.4138 24.1936 22.875 23.8867ZM12.6018 13.6197H22.4068L25.2852 9.49252C25.3162 9.4289 25.3599 9.37225 25.4135 9.32598C25.467 9.27972 25.5295 9.24481 25.5969 9.22337C25.6644 9.20193 25.7355 9.1944 25.806 9.20124C25.8764 9.20808 25.9448 9.22915 26.0068 9.26317L27.6441 10.1609C27.7043 10.194 27.7575 10.2387 27.8005 10.2923C27.8435 10.346 27.8755 10.4076 27.8947 10.4736C27.9139 10.5397 27.9199 10.6088 27.9123 10.6772C27.9048 10.7455 27.8839 10.8117 27.8508 10.872L24.8511 14.9774C24.6107 15.4152 24.2569 15.78 23.8268 16.0337C23.3967 16.2874 22.9061 16.4206 22.4068 16.4191H12.0012L10.7442 18.7007L10.7022 18.8687C10.7036 18.961 10.7408 19.0491 10.806 19.1144C10.8711 19.1797 10.9592 19.2171 11.0514 19.2187H26.7017C26.7706 19.2187 26.8387 19.2324 26.9023 19.2588C26.9659 19.2852 27.0237 19.3239 27.0723 19.3726C27.1209 19.4214 27.1595 19.4792 27.1858 19.5429C27.2121 19.6065 27.2255 19.6747 27.2255 19.7436V21.4932C27.2256 21.6323 27.1705 21.7657 27.0723 21.8641C26.9741 21.9626 26.8408 22.018 26.7017 22.0181L10.6047 22.0182C9.89712 22.031 9.20946 21.7839 8.67184 21.3238C8.13422 20.8636 7.78396 20.2223 7.68737 19.5213C7.62345 18.9511 7.74 18.3753 8.02062 17.8749L9.90612 14.4455L4.87799 3.82151H2.60824C2.46918 3.82136 2.33587 3.76597 2.23765 3.66753C2.13942 3.5691 2.08432 3.43567 2.08447 3.29661V1.54693C2.0844 1.47807 2.09789 1.40988 2.12417 1.34623C2.15045 1.28259 2.18901 1.22474 2.23765 1.176C2.28628 1.12726 2.34404 1.08858 2.40763 1.06216C2.47122 1.03574 2.53939 1.0221 2.60824 1.02203H6.31924C6.41874 1.02208 6.51617 1.05048 6.60011 1.1039C6.68406 1.15732 6.75105 1.23355 6.79324 1.32366L7.96469 3.82151L9.2915 6.621L12.4202 13.2418L12.6018 13.6197Z" fill="evenodd"/>
</svg>',
		),

		array(
			'name'   => __( 'Download Excerpt', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadexcerpt',
			'id'     => 81,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 4H22.5C23.3284 4 24 4.67157 24 5.5C24 6.32843 23.3284 7 22.5 7H3.5C2.67157 7 2 6.32843 2 5.5C2 4.67157 2.67157 4 3.5 4ZM14.5 10H3.5C2.67157 10 2 10.6716 2 11.5C2 12.3284 2.67157 13 3.5 13H14.5C15.3284 13 16 12.3284 16 11.5C16 10.6716 15.3284 10 14.5 10ZM3.5 16H26.5C27.3284 16 28 16.6716 28 17.5C28 18.3284 27.3284 19 26.5 19H3.5C2.67157 19 2 18.3284 2 17.5C2 16.6716 2.67157 16 3.5 16ZM3.5 23C4.32843 23 5 23.6716 5 24.5C5 25.3284 4.32843 26 3.5 26C2.67157 26 2 25.3284 2 24.5C2 23.6716 2.67157 23 3.5 23ZM11 24.5C11 23.6716 10.3284 23 9.5 23C8.67157 23 8 23.6716 8 24.5C8 25.3284 8.67157 26 9.5 26C10.3284 26 11 25.3284 11 24.5ZM15.5 23C16.3284 23 17 23.6716 17 24.5C17 25.3284 16.3284 26 15.5 26C14.6716 26 14 25.3284 14 24.5C14 23.6716 14.6716 23 15.5 23Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Download Instructions', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'eddtemplatetag',
			'type'   => 'edddownloadinstructions',
			'id'     => 82,
			'icon'   => '<svg class="sp-w-6 sp-fill-current" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path fill-rule="evenodd" clip-rule="evenodd" d="M3 1H27C27.5304 1 28.0391 1.21071 28.4142 1.58579C28.7893 1.96086 29 2.46957 29 3V27C29 27.5304 28.7893 28.0391 28.4142 28.4142C28.0391 28.7893 27.5304 29 27 29H3C2.46957 29 1.96087 28.7893 1.5858 28.4142C1.21073 28.0391 1.00002 27.5304 1 27V3C1 2.46957 1.21071 1.96086 1.58579 1.58579C1.96086 1.21071 2.46957 1 3 1ZM3.18506 26.815H26.8149V3.185H3.18506V26.815ZM6.5 8H20.4231C21.2515 8 21.9231 8.67157 21.9231 9.5C21.9231 10.3284 21.2515 11 20.4231 11H6.5C5.67157 11 5 10.3284 5 9.5C5 8.67157 5.67157 8 6.5 8ZM6.5 20H23.5C24.3284 20 25 20.6716 25 21.5C25 22.3284 24.3284 23 23.5 23H6.5C5.67157 23 5 22.3284 5 21.5C5 20.6716 5.67157 20 6.5 20ZM14.2692 14H6.5C5.67157 14 5 14.6716 5 15.5C5 16.3284 5.67157 17 6.5 17H14.2692C15.0977 17 15.7692 16.3284 15.7692 15.5C15.7692 14.6716 15.0977 14 14.2692 14Z" fill="evenodd"/>
			</svg>',
		),

		array(
			'name'   => __( 'Push Notifications', 'coming-soon' ),
			'is_pro' => false,
			'cat'    => 'adv',
			'type'   => 'pushnotification',
			'id'     => 83,
			'icon'   => '<svg xmlns="http://www.w3.org/2000/svg" class="sp-w-5 sp-fill-current" viewBox="0 0 16 16">
			<path fill="currentColor" d="M13 2.5a1.5 1.5 0 0 1 3 0v11a1.5 1.5 0 0 1-3 0v-.214c-2.162-1.241-4.49-1.843-6.912-2.083l.405 2.712A1 1 0 0 1 5.51 15.1h-.548a1 1 0 0 1-.916-.599l-1.85-3.49-.202-.003A2.014 2.014 0 0 1 0 9V7a2.02 2.02 0 0 1 1.992-2.013 75 75 0 0 0 2.483-.075c3.043-.154 6.148-.849 8.525-2.199zm1 0v11a.5.5 0 0 0 1 0v-11a.5.5 0 0 0-1 0m-1 1.35c-2.344 1.205-5.209 1.842-8 2.033v4.233q.27.015.537.036c2.568.189 5.093.744 7.463 1.993zm-9 6.215v-4.13a95 95 0 0 1-1.992.052A1.02 1.02 0 0 0 1 7v2c0 .55.448 1.002 1.006 1.009A61 61 0 0 1 4 10.065m-.657.975 1.609 3.037.01.024h.548l-.002-.014-.443-2.966a68 68 0 0 0-1.722-.082z"/>
		  </svg>',
		),

	);

	global $wp_widget_factory;
	$all_widgets = $wp_widget_factory->widgets;
	// print_r($all_widgets);

	$wordpress_widgets = array();

	$random_id = 300;
	foreach ( $all_widgets as $k => $value ) {
		if ( 'WP_Widget_Custom_HTML' != $k ) {
			$random_id       = $random_id + 10;
			$block_options[] = array(
				'name'    => $value->name,
				'is_pro'  => true,
				'cat'     => 'wpw',
				'type'    => 'wpwidgetblock-' . $k,
				'base_id' => $value->id_base,
				'id'      => $random_id,
				'icon'    => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="sp-w-6 sp-fill-current"><path d="M256 8C119.3 8 8 119.2 8 256c0 136.7 111.3 248 248 248s248-111.3 248-248C504 119.2 392.7 8 256 8zM33 256c0-32.3 6.9-63 19.3-90.7l106.4 291.4C84.3 420.5 33 344.2 33 256zm223 223c-21.9 0-43-3.2-63-9.1l66.9-194.4 68.5 187.8c.5 1.1 1 2.1 1.6 3.1-23.1 8.1-48 12.6-74 12.6zm30.7-327.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-21.9 0-58.7-2.8-58.7-2.8-12-.7-13.4 17.7-1.4 18.4 0 0 11.4 1.4 23.4 2.1l34.7 95.2L200.6 393l-81.2-241.5c13.4-.7 25.5-2.1 25.5-2.1 12-1.4 10.6-19.1-1.4-18.4 0 0-36.1 2.8-59.4 2.8-4.2 0-9.1-.1-14.4-.3C109.6 73 178.1 33 256 33c58 0 110.9 22.2 150.6 58.5-1-.1-1.9-.2-2.9-.2-21.9 0-37.4 19.1-37.4 39.6 0 18.4 10.6 33.9 21.9 52.3 8.5 14.8 18.4 33.9 18.4 61.5 0 19.1-7.3 41.2-17 72.1l-22.2 74.3-80.7-239.6zm81.4 297.2l68.1-196.9c12.7-31.8 17-57.2 17-79.9 0-8.2-.5-15.8-1.5-22.9 17.4 31.8 27.3 68.2 27.3 107 0 82.3-44.6 154.1-110.9 192.7z"/></svg>',
			);
		}
	}

	// print_r($wordpress_widgets);
	// filter out some options
	$theme_enabled = get_option( 'seedprod_theme_enabled' );
	$theme_builder = seedprod_lite_cu( 'themebuilder' );

	if ( ! empty( $theme_builder ) && ! empty( $theme_enabled ) ) {
		$block_options[] = array(
			'name'   => __( 'Template Parts', 'coming-soon' ),
			'is_pro' => true,
			'cat'    => 'adv',
			'type'   => 'seedprodtemplateparts',
			'id'     => 47,
			'icon'   => '
			   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 30 30" class="sp-w-6 sp-fill-current"><path fill-rule="evenodd" clip-rule="evenodd" d="M1 2.5V27.5C1 27.8978 1.15804 28.2794 1.43934 28.5607C1.72064 28.842 2.10218 29 2.5 29H27.5C27.8978 29 28.2794 28.842 28.5607 28.5607C28.842 28.2794 29 27.8978 29 27.5V2.5C29 2.10218 28.842 1.72064 28.5607 1.43934C28.2794 1.15804 27.8978 1 27.5 1H2.5C2.10218 1 1.72064 1.15804 1.43934 1.43934C1.15804 1.72064 1 2.10218 1 2.5ZM27 27H3V3H27V27ZM23.5 6.5V9.5H6.5V6.5H23.5ZM24 5H6C5.73478 5 5.48043 5.10536 5.29289 5.29289C5.10536 5.48043 5 5.73478 5 6V10C5 10.2652 5.10536 10.5196 5.29289 10.7071C5.48043 10.8946 5.73478 11 6 11H24C24.2652 11 24.5196 10.8946 24.7071 10.7071C24.8946 10.5196 25 10.2652 25 10V6C25 5.73478 24.8946 5.48043 24.7071 5.29289C24.5196 5.10536 24.2652 5 24 5ZM14.86 14.66C9.89997 14 9.65997 14 9.65997 14C9.65997 14 9.83997 15.66 10.27 19.23C10.4311 20.7189 11.1157 22.1021 12.2018 23.1332C13.2878 24.1643 14.7048 24.7763 16.2 24.86L17.25 24.91C17.25 24.91 17.35 21.09 16.37 19.29C15.39 17.49 12.81 17.18 12.81 17.18C13.568 17.7485 14.2414 18.4219 14.81 19.18C15.7 20.37 15.6 23.09 15.6 23.09C14.6622 22.8322 13.816 22.3156 13.1582 21.5994C12.5003 20.8832 12.0573 19.9962 11.88 19.04C11.56 16.72 11.5 15.86 11.5 15.86C11.5 15.86 11.2 15.8 14.65 16.26C18.1 16.72 18.96 20.8 18.49 24.72C21.53 20.34 19.82 15.32 14.86 14.66Z" />
			   </svg>
			   ',
		);
	}

	return $block_options;
}

/**
 * Get times
 */
function seedprod_lite_get_default_timezone() {
	$general_settings = get_option( 'seedprod_settings' );
	$timezone         = 'UTC';

	if ( ! empty( $general_settings ) ) {
		$general_settings = json_decode( $general_settings );
		if ( ! empty( $general_settings->default_timezone ) ) {
			$timezone = $general_settings->default_timezone;
		}
	}

	return $timezone;
}



/**
 * Get times
 */
function seedprod_lite_get_times() {
	$times   = array();
	$times[] = array(
		'v' => '',
		'l' => __( 'Select Time', 'coming-soon' ),
	);
	$times[] = array(
		'v' => '00:00',
		'l' => '12:00 A.M.',
	);
	$times[] = array(
		'v' => '00:30',
		'l' => '12:30 A.M.',
	);
	$times[] = array(
		'v' => '01:00',
		'l' => '1:00 A.M.',
	);
	$times[] = array(
		'v' => '01:30',
		'l' => '1:30 A.M.',
	);
	$times[] = array(
		'v' => '02:00',
		'l' => '2:00 A.M.',
	);
	$times[] = array(
		'v' => '02:30',
		'l' => '2:30 A.M.',
	);
	$times[] = array(
		'v' => '03:00',
		'l' => '3:00 A.M.',
	);
	$times[] = array(
		'v' => '03:30',
		'l' => '3:30 A.M.',
	);
	$times[] = array(
		'v' => '04:00',
		'l' => '4:00 A.M.',
	);
	$times[] = array(
		'v' => '04:30',
		'l' => '4:30 A.M.',
	);
	$times[] = array(
		'v' => '05:00',
		'l' => '5:00 A.M.',
	);
	$times[] = array(
		'v' => '05:30',
		'l' => '5:30 A.M.',
	);
	$times[] = array(
		'v' => '06:00',
		'l' => '6:00 A.M.',
	);
	$times[] = array(
		'v' => '06:30',
		'l' => '6:30 A.M.',
	);
	$times[] = array(
		'v' => '07:00',
		'l' => '7:00 A.M.',
	);
	$times[] = array(
		'v' => '07:30',
		'l' => '7:30 A.M.',
	);
	$times[] = array(
		'v' => '08:00',
		'l' => '8:00 A.M.',
	);
	$times[] = array(
		'v' => '08:30',
		'l' => '8:30 A.M.',
	);
	$times[] = array(
		'v' => '09:00',
		'l' => '9:00 A.M.',
	);
	$times[] = array(
		'v' => '09:30',
		'l' => '9:30 A.M.',
	);
	$times[] = array(
		'v' => '10:00',
		'l' => '10:00 A.M.',
	);
	$times[] = array(
		'v' => '10:30',
		'l' => '10:30 A.M.',
	);
	$times[] = array(
		'v' => '11:00',
		'l' => '11:00 A.M.',
	);
	$times[] = array(
		'v' => '11:30',
		'l' => '11:30 A.M.',
	);
	$times[] = array(
		'v' => '12:00',
		'l' => '12:00 P.M.',
	);
	$times[] = array(
		'v' => '12:30',
		'l' => '12:30 P.M.',
	);
	$times[] = array(
		'v' => '13:00',
		'l' => '1:00 P.M.',
	);
	$times[] = array(
		'v' => '13:30',
		'l' => '1:30 P.M.',
	);
	$times[] = array(
		'v' => '14:00',
		'l' => '2:00 P.M.',
	);
	$times[] = array(
		'v' => '14:30',
		'l' => '2:30 P.M.',
	);
	$times[] = array(
		'v' => '15:00',
		'l' => '3:00 P.M.',
	);
	$times[] = array(
		'v' => '15:30',
		'l' => '3:30 P.M.',
	);
	$times[] = array(
		'v' => '16:00',
		'l' => '4:00 P.M.',
	);
	$times[] = array(
		'v' => '16:30',
		'l' => '4:30 P.M.',
	);
	$times[] = array(
		'v' => '17:00',
		'l' => '5:00 P.M.',
	);
	$times[] = array(
		'v' => '17:30',
		'l' => '5:30 P.M.',
	);
	$times[] = array(
		'v' => '18:00',
		'l' => '6:00 P.M.',
	);
	$times[] = array(
		'v' => '18:30',
		'l' => '6:30 P.M.',
	);
	$times[] = array(
		'v' => '19:00',
		'l' => '7:00 P.M.',
	);
	$times[] = array(
		'v' => '19:30',
		'l' => '7:30 P.M.',
	);
	$times[] = array(
		'v' => '20:00',
		'l' => '8:00 P.M.',
	);
	$times[] = array(
		'v' => '20:30',
		'l' => '8:30 P.M.',
	);
	$times[] = array(
		'v' => '21:00',
		'l' => '9:00 P.M.',
	);
	$times[] = array(
		'v' => '21:30',
		'l' => '9:30 P.M.',
	);
	$times[] = array(
		'v' => '22:00',
		'l' => '10:00 P.M.',
	);
	$times[] = array(
		'v' => '22:30',
		'l' => '10:30 P.M.',
	);
	$times[] = array(
		'v' => '23:00',
		'l' => '11:00 P.M.',
	);
	$times[] = array(
		'v' => '23:30',
		'l' => '11:30 P.M.',
	);

	return $times;
}

/**
 * Check per
 */
function seedprod_lite_get_api_key() {
	$seedprod_api_key = '';

	if ( defined( 'SEEDPROD_API_KEY' ) ) {
		$seedprod_api_key = SEEDPROD_API_KEY;
	}

	if ( empty( $seedprod_api_key ) ) {
		$seedprod_api_key = get_option( 'seedprod_api_key ' );
	}

	return $seedprod_api_key;
}

/**
 * Get timezones
 */
function seedprod_lite_get_timezones() {
	// timezones
	$zonen      = array();
	$continents = array( 'Africa', 'America', 'Antarctica', 'Arctic', 'Asia', 'Atlantic', 'Australia', 'Europe', 'Indian', 'Pacific' );

	foreach ( timezone_identifiers_list() as $zone ) {
		$zone = explode( '/', $zone );
		if ( ! in_array( $zone[0], $continents ) ) {
			continue;
		}

		// This determines what gets set and translated - we don't translate Etc/* strings here, they are done later
		$exists    = array(
			0 => ( isset( $zone[0] ) && $zone[0] ),
			1 => ( isset( $zone[1] ) && $zone[1] ),
			2 => ( isset( $zone[2] ) && $zone[2] ),
		);
		$exists[3] = ( $exists[0] && 'Etc' !== $zone[0] );
		$exists[4] = ( $exists[1] && $exists[3] );
		$exists[5] = ( $exists[2] && $exists[3] );

		$zonen[] = array(
			'continent'   => ( $exists[0] ? $zone[0] : '' ),
			'city'        => ( $exists[1] ? $zone[1] : '' ),
			'subcity'     => ( $exists[2] ? $zone[2] : '' ),
			't_continent' => ( $exists[3] ? __( str_replace( '_', ' ', $zone[0] ), 'continents-cities' ) : '' ), // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralText
			't_city'      => ( $exists[4] ? __( str_replace( '_', ' ', $zone[1] ), 'continents-cities' ) : '' ), // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralText
			't_subcity'   => ( $exists[5] ? __( str_replace( '_', ' ', $zone[2] ), 'continents-cities' ) : '' ), // phpcs:ignore WordPress.WP.I18n.NonSingularStringLiteralText
		);
	}
	usort( $zonen, '_wp_timezone_choice_usort_callback' );

	$structure = array();

	foreach ( $zonen as $key => $zone ) {
		// Build value in an array to join later
		$value = array( $zone['continent'] );

		if ( empty( $zone['city'] ) ) {
			// It's at the continent level (generally won't happen)
			$display = $zone['t_continent'];
		} else {
			// It's inside a continent group

			// Continent optgroup
			if ( ! isset( $zonen[ $key - 1 ] ) || $zonen[ $key - 1 ]['continent'] !== $zone['continent'] ) {
				$label = $zone['t_continent'];
				// $structure[] = $label ;
			}

			// Add the city to the value
			$value[] = $zone['city'];

			// get offset
			// $timezone = $label.'/'.str_replace(' ', '_', $zone['t_city']);
			// $time = new \DateTime('now', new DateTimeZone($timezone));
			// $timezone_offset = $time->format('P');

			$display = $zone['t_city'];
			if ( ! empty( $zone['subcity'] ) ) {
				// Add the subcity to the value
				$value[]  = $zone['subcity'];
				$display .= ' - ' . $zone['t_subcity'];
			}
		}

		// Build the value
		$value = join( '/', $value );

		// get offset
		$time                  = new \DateTime( 'now', new DateTimeZone( $value ) );
		$timezone_offset       = $time->format( 'P' );
		$structure[ $label ][] = array(
			'v' => $value,
			'l' => $display . ' (' . $timezone_offset . ' GMT)',
		);
	}

	$structure['UTC'][] = array(
		'v' => 'UTC',
		'l' => 'UTC',
	);

	return $structure;
}

/**
 * Add to array if value does not exist
 */
function seedprod_lite_array_add( $arr, $key, $value ) {
	if ( ! array_key_exists( $key, $arr ) ) {
		$arr[ $key ] = $value;
	}
	return $arr;
}

/**
 * Check per
 */
function seedprod_lite_cu( $rper = null ) {
	if ( ! empty( $rper ) ) {
		$uper = explode( ',', get_option( 'seedprod_per' ) );
		if ( in_array( $rper, $uper ) ) {
			return true;
		} else {
			return false;
		}
	} else {
		$a = get_option( 'seedprod_a' );
		if ( $a ) {
			return true;
		} else {
			return false;
		}
	}
}

/**
 * Upgrade link.
 *
 * @param string $medium Medium.
 * @return string SeedProd Upgrade Link.
 */
function seedprod_lite_upgrade_link( $medium = 'link' ) {
	$referrer    = '';
	$referred_by = get_option( 'seedprod_referred_by' );
	if ( ! empty( $referred_by ) ) {
		$referrer = '-' . $referred_by;
	}
	return apply_filters( 'seedprod_lite_upgrade_link', 'https://seedprod.com/lite-upgrade/?utm_source=WordPress' . $referrer . '&utm_campaign=liteplugin&utm_medium=' . sanitize_key( apply_filters( 'seedprod_lite_upgrade_link_medium', $medium ) ) );
}

/**
 * Disable admin notices.
 *
 * @return void
 */
function seedprod_lite_disable_admin_notices() {
	global $wp_filter;
	if ( is_user_admin() ) {
		if ( isset( $wp_filter['user_admin_notices'] ) ) {
			unset( $wp_filter['user_admin_notices'] );
		}
	} elseif ( isset( $wp_filter['admin_notices'] ) ) {
		unset( $wp_filter['admin_notices'] );
	}
	if ( isset( $wp_filter['all_admin_notices'] ) ) {
		unset( $wp_filter['all_admin_notices'] );
	}
}

$get_page = ! empty( $_GET['page'] ) ? sanitize_text_field( wp_unslash( $_GET['page'] ) ) : null; // phpcs:ignore WordPress.Security.NonceVerification.Recommended

if ( null !== $get_page && strpos( $get_page, 'seedprod' ) !== false ) {
	add_action( 'admin_print_scripts', 'seedprod_lite_disable_admin_notices' );
}

/**
 * Generate plugin nonce.
 *
 * @return void
 */
function seedprod_lite_plugin_nonce() {
	check_ajax_referer( 'seedprod_lite_plugin_nonce', 'nonce' );

	if ( ! current_user_can( 'install_plugins' ) ) {
		wp_send_json_error();
	}

	$plugin = ! empty( $_POST['plugin'] ) ? sanitize_text_field( wp_unslash( $_POST['plugin'] ) ) : null;

	$install_plugin_nonce = wp_create_nonce( 'install-plugin_' . sanitize_text_field( $plugin ) );

	wp_send_json( $install_plugin_nonce );
}

/**
 * Check if is dev URL.
 *
 * @param string $url URL being analysed.
 * @return boolean $is_local_url Return true/false whether the URL is from a dev env or not.
 */
function seedprod_lite_is_dev_url( $url = '' ) {
	$is_local_url = false;
	// Trim it up
	$url = strtolower( trim( $url ) );
	// Need to get the host...so let's add the scheme so we can use parse_url
	if ( false === strpos( $url, 'http://' ) && false === strpos( $url, 'https://' ) ) {
		$url = 'http://' . $url;
	}
	$url_parts = wp_parse_url( $url );
	$host      = ! empty( $url_parts['host'] ) ? $url_parts['host'] : false;
	if ( ! empty( $url ) && ! empty( $host ) ) {
		if ( false !== ip2long( $host ) ) {
			if ( ! filter_var( $host, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
				$is_local_url = true;
			}
		} elseif ( 'localhost' === $host ) {
			$is_local_url = true;
		}

		$tlds_to_check = array( '.local', ':8888', ':8080', ':8081', '.invalid', '.example', '.test' );
		foreach ( $tlds_to_check as $tld ) {
			if ( false !== strpos( $host, $tld ) ) {
				$is_local_url = true;
				break;
			}
		}
		if ( substr_count( $host, '.' ) > 1 ) {
			$subdomains_to_check = array( 'dev.', '*.staging.', 'beta.', 'test.' );
			foreach ( $subdomains_to_check as $subdomain ) {
				$subdomain = str_replace( '.', '(.)', $subdomain );
				$subdomain = str_replace( array( '*', '(.)' ), '(.*)', $subdomain );
				if ( preg_match( '/^(' . $subdomain . ')/', $host ) ) {
					$is_local_url = true;
					break;
				}
			}
		}
	}
	return $is_local_url;
}

/**
 * Find fonts in doc.
 *
 * @param Array/Object $some_array An array/object containing possible fonts in the doc.
 * @return array An array with fonts in the doc.
 */
function seedprod_lite_find_fonts_in_doc( $some_array ) {
	if ( empty( $some_array ) ) {
		return false;
	}
	$load_fonts    = array();
	$load_variants = array();
	$iterator      = new RecursiveIteratorIterator( new RecursiveArrayIterator( $some_array ), RecursiveIteratorIterator::SELF_FIRST );
	foreach ( $iterator as $k => $v ) {
		$indent = str_repeat( '&nbsp;', 10 * $iterator->getDepth() );

		// Not at end: show key only
		// if ( $iterator->callHasChildren() ) {
		// echo "$indent$k :<br>";
		// At end: show key, value and path
		// }

		if ( ! $iterator->callHasChildren() ) {
			for ( $p = array(), $i = 0, $z = $iterator->getDepth(); $i <= $z; $i++ ) {
				$p[] = $iterator->getSubIterator( $i )->key();
			}
			$path = implode( ',', $p );
			// echo "$indent$k : $v : path -> $path<br>";
			// get font
			if ( stripos( $k, 'font' ) !== false && stripos( $k, 'variant' ) === false && ! empty( $v ) && strpos( $v, ',' ) === false && stripos( $k, 'fontSize' ) === false ) {
				$load_fonts[] = array(
					'k' => $k,
					'v' => $v,
					'p' => $path,
				);
			}
			// get variant
			if ( stripos( $k, 'font' ) !== false && stripos( $k, 'variant' ) !== false && ! empty( $v ) && strpos( $v, ',' ) === false ) {
				$load_variants[] = array(
					'k' => $k,
					'v' => $v,
					'p' => $path,
				);
			}
		}
	}
	return array_merge( $load_fonts, $load_variants );
}

/**
 * Construct font string.
 *
 * @param array $doc_settings Array of fonts in the doc.
 * @return string $google_fonts_str Google fonts string.
 */
function seedprod_lite_construct_font_str( $doc_settings ) {
	$fonts      = seedprod_lite_find_fonts_in_doc( $doc_settings );
	$myfonts    = array();
	$myvariants = array();
	if ( ! empty( $fonts ) ) {
		foreach ( $fonts as $k => $v ) {
			if ( stripos( $v['k'], 'font' ) !== false && stripos( $v['k'], 'variant' ) === false ) {
				if ( empty( $myfonts[ $v['v'] ] ) ) {
					$myfonts[ $v['v'] ] = array();
				}

				foreach ( $fonts as $k2 => $v2 ) {
					if ( $v['p'] . 'Variant' === $v2['p'] ) {
						$myfonts[ $v['v'] ][] = $v2['v'];
					}
				}
			}
		}

		foreach ( $myfonts as $k3 => $v3 ) {
			$myfonts[ $k3 ] = array_unique( $v3 );
		}
	}
	$google_fonts_str = '';
	if ( ! empty( $myfonts ) ) {
		$google_fonts_str = 'https://fonts.googleapis.com/css?family=';
		$c                = 1;
		foreach ( $myfonts as $k4 => $v4 ) {
			$end = '|';
			if ( count( $myfonts ) == $c ) {
				$end = '';
			}
			$google_fonts_str .= urlencode( $k4 ); // phpcs:ignore WordPress.PHP.DiscouragedPHPFunctions.urlencode_urlencode
			if ( ! empty( $v4 ) ) {
				$google_fonts_str .= ':' . implode( ',', $v4 );
			}

			$google_fonts_str .= $end;
			++$c;
		}
		$google_fonts_str .= '&display=swap';
	}
	return $google_fonts_str;
}

add_filter( '_wp_post_revision_fields', 'seedprod_lite_wp_post_revision_fields', 11, 2 );

/**
 * WP Post revision fields.
 *
 * @param array $fields List of fields to revision. Contains 'post_title', 'post_content', and 'post_excerpt' by default.
 * @param array $post   A post array being processed for insertion as a post revision.
 * @return array $fields List of fields after revision.
 */
function seedprod_lite_wp_post_revision_fields( $fields, $post ) {
	if ( ! empty( $post['post_content_filtered'] ) && strpos( $post['post_content'], 'sp-page' ) !== false ) {
		$fields['post_content_filtered'] = 'Content Filtered';
		return $fields;
	} elseif ( ( ! empty( $post['post_content_filtered'] ) && strpos( $post['post_content'], 'sp-theme-template' ) !== false ) || strpos( $post['post_title'], 'Global CSS' ) !== false ) {
		$fields['post_content_filtered'] = 'Content Filtered';
		return $fields;
	} elseif ( ! empty( $post['post_content_filtered'] ) && 'seedprod' == $post['post_type'] ) {
		$fields['post_content_filtered'] = 'Content Filtered';
		return $fields;
	} elseif ( ! empty( $post['post_content_filtered'] ) && strpos( $post['post_content_filtered'], 'template_id' ) !== false ) {
		$fields['post_content_filtered'] = 'Content Filtered';
		return $fields;
	} else {
		return $fields;
	}
}

// add_filter( 'get_edit_post_link', 'seedprod_lite_filter_get_edit_post_link', 11, 3 );

/**
 * Get edit post link.
 *
 * @param string          $link    Link(depreciated).
 * @param integer|WP_Post $id      Post ID or post object. Default is the global $post.
 * @param string          $context How to output the '&' character. Default '&'.
 * @return string|null $link The edit post link for the given post.
 */
function seedprod_lite_filter_get_edit_post_link( $link, $id, $context ) {
	$has_settings = get_post_meta( $id, '_seedprod_page', true );
	if ( ! empty( $has_settings ) ) {
		$link = admin_url() . 'admin.php?page=seedprod_lite_builder&id=' . $id . '#/setup/' . $id;
	}
	return $link;
}

/**
 * Dismiss Settings Lite CTA
 */
function seedprod_lite_dismiss_settings_lite_cta() {
	if ( check_ajax_referer( 'seedprod_lite_dismiss_settings_lite_cta' ) ) {
		if ( ! empty( $_POST['dismiss'] ) ) {
			update_option( 'seedprod_dismiss_settings_lite_cta', true );

			$response = array(
				'status' => 'true',

			);
		}

		// Send Response
		wp_send_json( $response );
		exit;
	}
}

/**
 * Dismiss Lite Banners
 */
function seedprod_lite_dismiss_upsell() {
	if ( check_ajax_referer( 'seedprod_lite_dismiss_upsell' ) ) {
		if ( ! empty( $_POST['id'] ) ) {
			$ts = time();
			update_option( 'seedprod_dismiss_upsell_' . absint( $_POST['id'] ), $ts );
			$response = array(
				'status' => 'true',

			);
		}

		// Send Response
		wp_send_json( $response );
		exit;
	}
}

/**
 * Get expire times
 *
 * @return array Expire times.
 */
function seedprod_lite_get_expire_times() {
	return array(
		'1'    => __( '1 Hour', 'coming-soon' ),
		'2'    => __( '2 Hours', 'coming-soon' ),
		'3'    => __( '3 Hours', 'coming-soon' ),
		'4'    => __( '4 Hours', 'coming-soon' ),
		'5'    => __( '5 Hours', 'coming-soon' ),
		'6'    => __( '6 Hours', 'coming-soon' ),
		'7'    => __( '7 Hours', 'coming-soon' ),
		'8'    => __( '8 Hours', 'coming-soon' ),
		'9'    => __( '9 Hours', 'coming-soon' ),
		'10'   => __( '10 Hours', 'coming-soon' ),
		'11'   => __( '11 Hours', 'coming-soon' ),
		'12'   => __( '12 Hours', 'coming-soon' ),
		'13'   => __( '13 Hours', 'coming-soon' ),
		'14'   => __( '14 Hours', 'coming-soon' ),
		'15'   => __( '15 Hours', 'coming-soon' ),
		'16'   => __( '16 Hours', 'coming-soon' ),
		'17'   => __( '17 Hours', 'coming-soon' ),
		'18'   => __( '18 Hours', 'coming-soon' ),
		'19'   => __( '19 Hours', 'coming-soon' ),
		'20'   => __( '20 Hours', 'coming-soon' ),
		'21'   => __( '21 Hours', 'coming-soon' ),
		'22'   => __( '22 Hours', 'coming-soon' ),
		'23'   => __( '23 Hours', 'coming-soon' ),
		'24'   => __( '1 Day', 'coming-soon' ),
		'48'   => __( '2 Days', 'coming-soon' ),
		'72'   => __( '3 Days', 'coming-soon' ),
		'96'   => __( '4 Days', 'coming-soon' ),
		'120'  => __( '5 Days', 'coming-soon' ),
		'144'  => __( '6 Days', 'coming-soon' ),
		'168'  => __( '7 Days', 'coming-soon' ),
		'192'  => __( '8 Days', 'coming-soon' ),
		'216'  => __( '9 Days', 'coming-soon' ),
		'240'  => __( '10 Days', 'coming-soon' ),
		'264'  => __( '11 Days', 'coming-soon' ),
		'288'  => __( '12 Days', 'coming-soon' ),
		'312'  => __( '13 Days', 'coming-soon' ),
		'336'  => __( '14 Days', 'coming-soon' ),
		'360'  => __( '15 Days', 'coming-soon' ),
		'384'  => __( '16 Days', 'coming-soon' ),
		'408'  => __( '17 Days', 'coming-soon' ),
		'432'  => __( '18 Days', 'coming-soon' ),
		'456'  => __( '19 Days', 'coming-soon' ),
		'480'  => __( '20 Days', 'coming-soon' ),
		'504'  => __( '21 Days', 'coming-soon' ),
		'528'  => __( '22 Days', 'coming-soon' ),
		'552'  => __( '23 Days', 'coming-soon' ),
		'576'  => __( '24 Days', 'coming-soon' ),
		'600'  => __( '25 Days', 'coming-soon' ),
		'624'  => __( '26 Days', 'coming-soon' ),
		'648'  => __( '27 Days', 'coming-soon' ),
		'672'  => __( '28 Days', 'coming-soon' ),
		'696'  => __( '29 Days', 'coming-soon' ),
		'720'  => __( '30 Days', 'coming-soon' ),
		'8760' => __( '1 Year', 'coming-soon' ),
	);
}



/**
 * Get system info.
 *
 * @return string $return String with all the system info appended.
 */
function seedprod_lite_get_system_info() {
	global $wpdb;

	// Get theme info.
	$theme_data = wp_get_theme();
	$theme      = $theme_data->Name . ' ' . $theme_data->Version; //phpcs:ignore WordPress.NamingConventions.ValidVariableName.UsedPropertyNotSnakeCase

	$return = '### Begin System Info ###' . "\n\n";

	// WPForms info.
	$return .= '-- SeedProd Info' . "\n\n";

	// Now the basics...
	$return .= "\n" . '-- Site Info' . "\n\n";
	$return .= 'Site URL:                 ' . site_url() . "\n";
	$return .= 'Home URL:                 ' . home_url() . "\n";
	$return .= 'Multisite:                ' . ( is_multisite() ? 'Yes' : 'No' ) . "\n";

	// WordPress configuration.
	$return .= "\n" . '-- WordPress Configuration' . "\n\n";
	$return .= 'Version:                  ' . get_bloginfo( 'version' ) . "\n";
	$return .= 'Language:                 ' . ( defined( 'WPLANG' ) && WPLANG ? WPLANG : 'en_US' ) . "\n";
	$return .= 'Permalink Structure:      ' . ( get_option( 'permalink_structure' ) ? get_option( 'permalink_structure' ) : 'Default' ) . "\n";
	$return .= 'Active Theme:             ' . $theme . "\n";
	$return .= 'Show On Front:            ' . get_option( 'show_on_front' ) . "\n";
	// Only show page specs if front page is set to 'page'.
	if ( get_option( 'show_on_front' ) === 'page' ) {
		$front_page_id = get_option( 'page_on_front' );
		$blog_page_id  = get_option( 'page_for_posts' );

		$return .= 'Page On Front:            ' . ( 0 != $front_page_id ? get_the_title( $front_page_id ) . ' (#' . $front_page_id . ')' : 'Unset' ) . "\n";
		$return .= 'Page For Posts:           ' . ( 0 != $blog_page_id ? get_the_title( $blog_page_id ) . ' (#' . $blog_page_id . ')' : 'Unset' ) . "\n";
	}
	$return .= 'ABSPATH:                  ' . ABSPATH . "\n";
	$return .= 'Table Prefix:             Length: ' . strlen( $wpdb->prefix ) . '   Status: ' . ( strlen( $wpdb->prefix ) > 16 ? 'ERROR: Too long' : 'Acceptable' ) . "\n";
	$return .= 'WP_DEBUG:                 ' . ( defined( 'WP_DEBUG' ) ? WP_DEBUG ? 'Enabled' : 'Disabled' : 'Not set' ) . "\n";
	$return .= 'Memory Limit:             ' . WP_MEMORY_LIMIT . "\n";
	$return .= 'Registered Post Stati:    ' . implode( ', ', get_post_stati() ) . "\n";

	// @todo WPForms configuration/specific details.
	$return .= "\n" . '-- WordPress Uploads/Constants' . "\n\n";
	$return .= 'WP_CONTENT_DIR:           ' . ( defined( 'WP_CONTENT_DIR' ) ? WP_CONTENT_DIR ? WP_CONTENT_DIR : 'Disabled' : 'Not set' ) . "\n";
	$return .= 'WP_CONTENT_URL:           ' . ( defined( 'WP_CONTENT_URL' ) ? WP_CONTENT_URL ? WP_CONTENT_URL : 'Disabled' : 'Not set' ) . "\n";
	$return .= 'UPLOADS:                  ' . ( defined( 'UPLOADS' ) ? UPLOADS ? UPLOADS : 'Disabled' : 'Not set' ) . "\n";

	$uploads_dir = wp_upload_dir();

	$return .= 'wp_uploads_dir() path:    ' . $uploads_dir['path'] . "\n";
	$return .= 'wp_uploads_dir() url:     ' . $uploads_dir['url'] . "\n";
	$return .= 'wp_uploads_dir() basedir: ' . $uploads_dir['basedir'] . "\n";
	$return .= 'wp_uploads_dir() baseurl: ' . $uploads_dir['baseurl'] . "\n";

	// Get plugins that have an update.
	$updates = get_plugin_updates();

	// Must-use plugins.
	// NOTE: MU plugins can't show updates!
	$muplugins = get_mu_plugins();
	if ( count( $muplugins ) > 0 && ! empty( $muplugins ) ) {
		$return .= "\n" . '-- Must-Use Plugins' . "\n\n";

		foreach ( $muplugins as $plugin => $plugin_data ) {
			$return .= $plugin_data['Name'] . ': ' . $plugin_data['Version'] . "\n";
		}
	}

	// WordPress active plugins.
	$return .= "\n" . '-- WordPress Active Plugins' . "\n\n";

	$plugins        = get_plugins();
	$active_plugins = get_option( 'active_plugins', array() );

	foreach ( $plugins as $plugin_path => $plugin ) {
		if ( ! in_array( $plugin_path, $active_plugins, true ) ) {
			continue;
		}
		$update  = ( array_key_exists( $plugin_path, $updates ) ) ? ' (needs update - ' . $updates[ $plugin_path ]->update->new_version . ')' : '';
		$return .= $plugin['Name'] . ': ' . $plugin['Version'] . $update . "\n";
	}

	// WordPress inactive plugins.
	$return .= "\n" . '-- WordPress Inactive Plugins' . "\n\n";

	foreach ( $plugins as $plugin_path => $plugin ) {
		if ( in_array( $plugin_path, $active_plugins, true ) ) {
			continue;
		}
		$update  = ( array_key_exists( $plugin_path, $updates ) ) ? ' (needs update - ' . $updates[ $plugin_path ]->update->new_version . ')' : '';
		$return .= $plugin['Name'] . ': ' . $plugin['Version'] . $update . "\n";
	}

	if ( is_multisite() ) {
		// WordPress Multisite active plugins.
		$return .= "\n" . '-- Network Active Plugins' . "\n\n";

		$plugins        = wp_get_active_network_plugins();
		$active_plugins = get_site_option( 'active_sitewide_plugins', array() );

		foreach ( $plugins as $plugin_path ) {
			$plugin_base = plugin_basename( $plugin_path );
			if ( ! array_key_exists( $plugin_base, $active_plugins ) ) {
				continue;
			}
			$update  = ( array_key_exists( $plugin_path, $updates ) ) ? ' (needs update - ' . $updates[ $plugin_path ]->update->new_version . ')' : '';
			$plugin  = get_plugin_data( $plugin_path );
			$return .= $plugin['Name'] . ': ' . $plugin['Version'] . $update . "\n";
		}
	}

	// Server configuration (really just versions).
	$server_version = ! empty( $_SERVER['SERVER_SOFTWARE'] ) ? sanitize_text_field( wp_unslash( $_SERVER['SERVER_SOFTWARE'] ) ) : '';
	$return        .= "\n" . '-- Webserver Configuration' . "\n\n";
	$return        .= 'PHP Version:              ' . PHP_VERSION . "\n";
	$return        .= 'MySQL Version:            ' . $wpdb->db_version() . "\n";
	$return        .= 'Webserver Info:           ' . $server_version . "\n";

	// PHP configs... now we're getting to the important stuff.
	$return .= "\n" . '-- PHP Configuration' . "\n\n";
	$return .= 'Memory Limit:             ' . ini_get( 'memory_limit' ) . "\n";
	$return .= 'Upload Max Size:          ' . ini_get( 'upload_max_filesize' ) . "\n";
	$return .= 'Post Max Size:            ' . ini_get( 'post_max_size' ) . "\n";
	$return .= 'Upload Max Filesize:      ' . ini_get( 'upload_max_filesize' ) . "\n";
	$return .= 'Time Limit:               ' . ini_get( 'max_execution_time' ) . "\n";
	$return .= 'Max Input Vars:           ' . ini_get( 'max_input_vars' ) . "\n";
	$return .= 'Display Errors:           ' . ( ini_get( 'display_errors' ) ? 'On (' . ini_get( 'display_errors' ) . ')' : 'N/A' ) . "\n";

	// PHP extensions and such.
	$return .= "\n" . '-- PHP Extensions' . "\n\n";
	$return .= 'cURL:                     ' . ( function_exists( 'curl_init' ) ? 'Supported' : 'Not Supported' ) . "\n";
	$return .= 'fsockopen:                ' . ( function_exists( 'fsockopen' ) ? 'Supported' : 'Not Supported' ) . "\n";
	$return .= 'SOAP Client:              ' . ( class_exists( 'SoapClient', false ) ? 'Installed' : 'Not Installed' ) . "\n";
	$return .= 'Suhosin:                  ' . ( extension_loaded( 'suhosin' ) ? 'Installed' : 'Not Installed' ) . "\n";

	// Session stuff.
	$return .= "\n" . '-- Session Configuration' . "\n\n";
	$return .= 'Session:                  ' . ( isset( $_SESSION ) ? 'Enabled' : 'Disabled' ) . "\n";

	// The rest of this is only relevant if session is enabled.
	if ( isset( $_SESSION ) ) {
		$return .= 'Session Name:             ' . esc_html( ini_get( 'session.name' ) ) . "\n";
		$return .= 'Cookie Path:              ' . esc_html( ini_get( 'session.cookie_path' ) ) . "\n";
		$return .= 'Save Path:                ' . esc_html( ini_get( 'session.save_path' ) ) . "\n";
		$return .= 'Use Cookies:              ' . ( ini_get( 'session.use_cookies' ) ? 'On' : 'Off' ) . "\n";
		$return .= 'Use Only Cookies:         ' . ( ini_get( 'session.use_only_cookies' ) ? 'On' : 'Off' ) . "\n";
	}

	$return .= "\n" . '### End System Info ###';

	return $return;
}

/**
 * If site uses WP Rocket, disable minify for CSS & JS
 */
function seedprod_lite_wprocket_disable_minify() {
	if ( function_exists( 'rocket_define_donotminify_constant' ) ) {
		rocket_define_donotminify_constant( 'true' );
	}
}




