/**
 * Minified by jsDelivr using UglifyJS v3.3.21.
 * Original file: /npm/jquery-numerator@0.2.1/jquery-numerator.js
 * 
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
 !function(i,t,n,e){var s="numerator",r={easing:"swing",duration:500,delimiter:e,rounding:0,toValue:e,fromValue:e,queue:!1,onStart:function(){},onStep:function(){},onProgress:function(){},onComplete:function(){}};function o(t,n){this.element=t,this.settings=i.extend({},r,n),this._defaults=r,this._name=s,this.init()}o.prototype={init:function(){this.parseElement(),this.setValue()},parseElement:function(){var t=i.trim(i(this.element).text());this.settings.fromValue=this.settings.fromValue||this.format(t)},setValue:function(){var e=this;i({value:e.settings.fromValue}).animate({value:e.settings.toValue},{duration:parseInt(e.settings.duration,10),easing:e.settings.easing,start:e.settings.onStart,step:function(t,n){i(e.element).text(e.format(t)),e.settings.onStep(t,n)},progress:e.settings.onProgress,complete:e.settings.onComplete})},format:function(t){return t=parseInt(this.settings.rounding)<1?parseInt(t,10):parseFloat(t).toFixed(parseInt(this.settings.rounding)),this.settings.delimiter?this.delimit(t):t},delimit:function(t){var n=this;if(t=t.toString(),n.settings.rounding&&0<parseInt(n.settings.rounding,10)){var e=t.substring(t.length-(n.settings.rounding+1),t.length),i=t.substring(0,t.length-(n.settings.rounding+1));return n.addDelimiter(i)+e}return n.addDelimiter(t)},addDelimiter:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.settings.delimiter)}},i.fn[s]=function(t){return this.each(function(){i.data(this,"plugin_"+s)&&i.data(this,"plugin_"+s,null),i.data(this,"plugin_"+s,new o(this,t))})}}(jQuery,window,document);
 //# sourceMappingURL=/sm/d71f2f06da50ad69dddbfcb951e124d12292261aa506e23734ca98f950f3eea5.map